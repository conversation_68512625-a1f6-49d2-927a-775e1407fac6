package com.rato.addons.gui.modern;

import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.ScaledResolution;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.Tessellator;
import net.minecraft.client.renderer.WorldRenderer;
import net.minecraft.client.renderer.vertex.DefaultVertexFormats;
import org.lwjgl.opengl.GL11;
import org.lwjgl.opengl.GL13;
import org.lwjgl.opengl.GL20;

import java.awt.*;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;

/**
 * Modern OpenGL Renderer with Shaders and Effects
 * Provides blur, shadow, glow, and smooth animations
 */
public class ModernRenderer {
    private static ModernRenderer instance;
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Shader Programs
    private int blurShaderProgram = -1;
    private int shadowShaderProgram = -1;
    private int glowShaderProgram = -1;
    
    // Framebuffers for effects
    private int framebuffer = -1;
    private int colorTexture = -1;
    private int depthTexture = -1;
    
    private boolean shadersSupported = false;
    
    public static ModernRenderer getInstance() {
        if (instance == null) {
            instance = new ModernRenderer();
        }
        return instance;
    }
    
    private ModernRenderer() {
        initializeShaders();
        initializeFramebuffers();
    }
    
    /**
     * Initialize shader programs
     */
    private void initializeShaders() {
        try {
            // Check if shaders are supported
            if (GL20.glCreateProgram() != 0) {
                shadersSupported = true;
                
                // Create blur shader
                blurShaderProgram = createShaderProgram(
                    getVertexShader(),
                    getBlurFragmentShader()
                );
                
                // Create shadow shader
                shadowShaderProgram = createShaderProgram(
                    getVertexShader(),
                    getShadowFragmentShader()
                );
                
                // Create glow shader
                glowShaderProgram = createShaderProgram(
                    getVertexShader(),
                    getGlowFragmentShader()
                );
            }
        } catch (Exception e) {
            shadersSupported = false;
            System.out.println("Shaders not supported, using fallback rendering");
        }
    }
    
    /**
     * Initialize framebuffers for post-processing effects
     */
    private void initializeFramebuffers() {
        if (!shadersSupported) return;
        
        try {
            ScaledResolution sr = new ScaledResolution(mc);
            int width = sr.getScaledWidth();
            int height = sr.getScaledHeight();
            
            // Generate framebuffer
            framebuffer = GL11.glGenTextures();
            
            // Generate color texture
            colorTexture = GL11.glGenTextures();
            GL11.glBindTexture(GL11.GL_TEXTURE_2D, colorTexture);
            GL11.glTexImage2D(GL11.GL_TEXTURE_2D, 0, GL11.GL_RGBA8, width, height, 0, GL11.GL_RGBA, GL11.GL_UNSIGNED_BYTE, (IntBuffer) null);
            GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_MIN_FILTER, GL11.GL_LINEAR);
            GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_MAG_FILTER, GL11.GL_LINEAR);
            
            // Generate depth texture
            depthTexture = GL11.glGenTextures();
            GL11.glBindTexture(GL11.GL_TEXTURE_2D, depthTexture);
            GL11.glTexImage2D(GL11.GL_TEXTURE_2D, 0, GL11.GL_DEPTH_COMPONENT, width, height, 0, GL11.GL_DEPTH_COMPONENT, GL11.GL_FLOAT, (FloatBuffer) null);
            GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_MIN_FILTER, GL11.GL_LINEAR);
            GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_MAG_FILTER, GL11.GL_LINEAR);
            
        } catch (Exception e) {
            System.out.println("Failed to initialize framebuffers: " + e.getMessage());
        }
    }
    
    /**
     * Draw a rounded rectangle with modern effects
     */
    public void drawRoundedRect(float x, float y, float width, float height, float radius, Color color, boolean blur, boolean shadow, boolean glow) {
        // Setup OpenGL state
        GlStateManager.enableBlend();
        GlStateManager.blendFunc(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA);
        GlStateManager.disableTexture2D();
        
        if (shadow && shadersSupported) {
            drawShadow(x, y, width, height, radius, 10, 0.3f);
        }
        
        if (glow && shadersSupported) {
            drawGlow(x, y, width, height, radius, color, 15, 0.8f);
        }
        
        // Draw main rectangle
        drawRoundedRectangle(x, y, width, height, radius, color);
        
        if (blur && shadersSupported) {
            applyBlur(x, y, width, height, 5.0f);
        }
        
        // Restore OpenGL state
        GlStateManager.enableTexture2D();
        GlStateManager.disableBlend();
    }
    
    /**
     * Draw a basic rounded rectangle
     */
    private void drawRoundedRectangle(float x, float y, float width, float height, float radius, Color color) {
        float alpha = color.getAlpha() / 255.0f;
        float red = color.getRed() / 255.0f;
        float green = color.getGreen() / 255.0f;
        float blue = color.getBlue() / 255.0f;
        
        GL11.glColor4f(red, green, blue, alpha);
        
        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer worldRenderer = tessellator.getWorldRenderer();
        
        worldRenderer.begin(GL11.GL_TRIANGLE_FAN, DefaultVertexFormats.POSITION);
        
        // Center point
        worldRenderer.pos(x + width / 2, y + height / 2, 0).endVertex();
        
        // Draw rounded corners with smooth curves
        int segments = 16;
        
        // Top-left corner
        for (int i = 0; i <= segments; i++) {
            double angle = Math.PI + (Math.PI / 2) * i / segments;
            double cornerX = x + radius + Math.cos(angle) * radius;
            double cornerY = y + radius + Math.sin(angle) * radius;
            worldRenderer.pos(cornerX, cornerY, 0).endVertex();
        }
        
        // Top-right corner
        for (int i = 0; i <= segments; i++) {
            double angle = 3 * Math.PI / 2 + (Math.PI / 2) * i / segments;
            double cornerX = x + width - radius + Math.cos(angle) * radius;
            double cornerY = y + radius + Math.sin(angle) * radius;
            worldRenderer.pos(cornerX, cornerY, 0).endVertex();
        }
        
        // Bottom-right corner
        for (int i = 0; i <= segments; i++) {
            double angle = 0 + (Math.PI / 2) * i / segments;
            double cornerX = x + width - radius + Math.cos(angle) * radius;
            double cornerY = y + height - radius + Math.sin(angle) * radius;
            worldRenderer.pos(cornerX, cornerY, 0).endVertex();
        }
        
        // Bottom-left corner
        for (int i = 0; i <= segments; i++) {
            double angle = Math.PI / 2 + (Math.PI / 2) * i / segments;
            double cornerX = x + radius + Math.cos(angle) * radius;
            double cornerY = y + height - radius + Math.sin(angle) * radius;
            worldRenderer.pos(cornerX, cornerY, 0).endVertex();
        }
        
        // Close the shape
        double angle = Math.PI;
        double cornerX = x + radius + Math.cos(angle) * radius;
        double cornerY = y + radius + Math.sin(angle) * radius;
        worldRenderer.pos(cornerX, cornerY, 0).endVertex();
        
        tessellator.draw();
    }
    
    /**
     * Apply blur effect using shaders
     */
    private void applyBlur(float x, float y, float width, float height, float intensity) {
        if (!shadersSupported || blurShaderProgram == -1) return;
        
        // Implementation would use framebuffer and blur shader
        // This is a simplified version for demonstration
    }
    
    /**
     * Draw shadow effect
     */
    private void drawShadow(float x, float y, float width, float height, float radius, float shadowSize, float opacity) {
        Color shadowColor = new Color(0, 0, 0, (int)(opacity * 255));
        drawRoundedRectangle(x + shadowSize/2, y + shadowSize/2, width, height, radius, shadowColor);
    }
    
    /**
     * Draw glow effect
     */
    private void drawGlow(float x, float y, float width, float height, float radius, Color color, float glowSize, float intensity) {
        for (int i = 0; i < glowSize; i++) {
            float alpha = intensity * (1.0f - (float)i / glowSize);
            Color glowColor = new Color(color.getRed(), color.getGreen(), color.getBlue(), (int)(alpha * 255));
            drawRoundedRectangle(x - i, y - i, width + 2*i, height + 2*i, radius + i, glowColor);
        }
    }
    
    /**
     * Create shader program from vertex and fragment shaders
     */
    private int createShaderProgram(String vertexSource, String fragmentSource) {
        int vertexShader = compileShader(GL20.GL_VERTEX_SHADER, vertexSource);
        int fragmentShader = compileShader(GL20.GL_FRAGMENT_SHADER, fragmentSource);
        
        int program = GL20.glCreateProgram();
        GL20.glAttachShader(program, vertexShader);
        GL20.glAttachShader(program, fragmentShader);
        GL20.glLinkProgram(program);
        
        // Check for linking errors
        if (GL20.glGetProgrami(program, GL20.GL_LINK_STATUS) == GL11.GL_FALSE) {
            System.out.println("Shader program linking failed: " + GL20.glGetProgramInfoLog(program, 1024));
            return -1;
        }
        
        GL20.glDeleteShader(vertexShader);
        GL20.glDeleteShader(fragmentShader);
        
        return program;
    }
    
    /**
     * Compile individual shader
     */
    private int compileShader(int type, String source) {
        int shader = GL20.glCreateShader(type);
        GL20.glShaderSource(shader, source);
        GL20.glCompileShader(shader);
        
        if (GL20.glGetShaderi(shader, GL20.GL_COMPILE_STATUS) == GL11.GL_FALSE) {
            System.out.println("Shader compilation failed: " + GL20.glGetShaderInfoLog(shader, 1024));
            return -1;
        }
        
        return shader;
    }
    
    // Shader source code
    private String getVertexShader() {
        return "#version 120\n" +
               "attribute vec2 position;\n" +
               "attribute vec2 texCoord;\n" +
               "varying vec2 fragTexCoord;\n" +
               "void main() {\n" +
               "    gl_Position = vec4(position, 0.0, 1.0);\n" +
               "    fragTexCoord = texCoord;\n" +
               "}";
    }
    
    private String getBlurFragmentShader() {
        return "#version 120\n" +
               "uniform sampler2D texture;\n" +
               "uniform float blurSize;\n" +
               "varying vec2 fragTexCoord;\n" +
               "void main() {\n" +
               "    vec4 sum = vec4(0.0);\n" +
               "    for(int x = -4; x <= 4; x++) {\n" +
               "        for(int y = -4; y <= 4; y++) {\n" +
               "            sum += texture2D(texture, fragTexCoord + vec2(x, y) * blurSize) / 81.0;\n" +
               "        }\n" +
               "    }\n" +
               "    gl_FragColor = sum;\n" +
               "}";
    }
    
    private String getShadowFragmentShader() {
        return "#version 120\n" +
               "uniform sampler2D texture;\n" +
               "uniform float shadowOpacity;\n" +
               "varying vec2 fragTexCoord;\n" +
               "void main() {\n" +
               "    vec4 color = texture2D(texture, fragTexCoord);\n" +
               "    gl_FragColor = vec4(0.0, 0.0, 0.0, color.a * shadowOpacity);\n" +
               "}";
    }
    
    private String getGlowFragmentShader() {
        return "#version 120\n" +
               "uniform sampler2D texture;\n" +
               "uniform vec3 glowColor;\n" +
               "uniform float glowIntensity;\n" +
               "varying vec2 fragTexCoord;\n" +
               "void main() {\n" +
               "    vec4 color = texture2D(texture, fragTexCoord);\n" +
               "    vec3 glow = glowColor * glowIntensity;\n" +
               "    gl_FragColor = vec4(color.rgb + glow, color.a);\n" +
               "}";
    }
    
    /**
     * Cleanup resources
     */
    public void cleanup() {
        if (shadersSupported) {
            if (blurShaderProgram != -1) GL20.glDeleteProgram(blurShaderProgram);
            if (shadowShaderProgram != -1) GL20.glDeleteProgram(shadowShaderProgram);
            if (glowShaderProgram != -1) GL20.glDeleteProgram(glowShaderProgram);
            if (framebuffer != -1) GL11.glDeleteTextures(framebuffer);
            if (colorTexture != -1) GL11.glDeleteTextures(colorTexture);
            if (depthTexture != -1) GL11.glDeleteTextures(depthTexture);
        }
    }
}
