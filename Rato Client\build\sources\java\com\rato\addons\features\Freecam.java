package com.rato.addons.features;

import com.rato.addons.util.Logger;
import net.minecraft.client.Minecraft;
import net.minecraft.client.entity.EntityOtherPlayerMP;
import net.minecraft.entity.Entity;
import net.minecraft.network.Packet;
import net.minecraft.network.play.client.C03PacketPlayer;
import net.minecraftforge.client.event.EntityViewRenderEvent;
import net.minecraftforge.event.entity.living.LivingEvent;
import net.minecraftforge.event.world.WorldEvent;
import net.minecraftforge.fml.common.eventhandler.EventPriority;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.InputEvent;
import org.lwjgl.input.Keyboard;
import org.lwjgl.input.Mouse;

/**
 * Sistema de Freecam simples e eficiente
 * Baseado no exemplo fornecido - muito mais estável
 */
public class Freecam {
    
    private static Freecam instance;
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Estado do freecam
    private boolean isActive = false;
    private EntityOtherPlayerMP playerEntity;

    // Posição e rotação originais
    private double originalX = Double.MAX_VALUE;
    private double originalY, originalZ;
    private float originalPitch, originalYaw;

    // Posição da câmera livre
    private double cameraX, cameraY, cameraZ;
    private float cameraYaw, cameraPitch;

    // Configurações
    private double speed = 15.0; // Velocidade mais rápida para câmera
    
    public static Freecam getInstance() {
        if (instance == null) {
            instance = new Freecam();
        }
        return instance;
    }
    
    /**
     * Ativa o freecam
     */
    public void onEnable() {
        if (mc.thePlayer == null || mc.theWorld == null) return;

        // Salvar posição e rotação originais
        originalX = mc.thePlayer.posX;
        originalY = mc.thePlayer.posY;
        originalZ = mc.thePlayer.posZ;
        originalPitch = mc.thePlayer.rotationPitch;
        originalYaw = mc.thePlayer.rotationYaw;

        // Criar boneco fake na posição atual
        playerEntity = new EntityOtherPlayerMP(mc.theWorld, mc.thePlayer.getGameProfile());
        playerEntity.clonePlayer(mc.thePlayer, true);
        playerEntity.onGround = mc.thePlayer.onGround;

        // Adicionar boneco fake ao mundo
        mc.theWorld.addEntityToWorld(-2137, playerEntity);

        // Inicializar câmera na posição do player
        cameraX = originalX;
        cameraY = originalY + 1.62; // Altura dos olhos
        cameraZ = originalZ;
        cameraYaw = originalYaw;
        cameraPitch = originalPitch;

        isActive = true;
        Logger.sendMessage("§aFreecam ativado - Boneco fake criado");
    }
    
    /**
     * Desativa o freecam
     */
    public void onDisable() {
        if (mc.thePlayer == null || mc.theWorld == null) {
            isActive = false;
            return;
        }

        // Verificar se temos posição original salva
        if (originalX != Double.MAX_VALUE) {
            // Teleportar de volta para posição do boneco fake
            if (playerEntity != null) {
                mc.thePlayer.setPositionAndRotation(playerEntity.posX, playerEntity.posY, playerEntity.posZ, originalYaw, originalPitch);

                // Remover boneco fake
                mc.theWorld.removeEntityFromWorld(-2137);
                playerEntity = null;
            } else {
                // Fallback para posição original
                mc.thePlayer.setPositionAndRotation(originalX, originalY, originalZ, originalYaw, originalPitch);
            }

            // Resetar marcador
            originalX = Double.MAX_VALUE;
        }

        isActive = false;
        Logger.sendMessage("§cFreecam desativado - Voltou para boneco");
    }
    
    /**
     * Toggle freecam
     */
    public void toggle() {
        if (originalX == Double.MAX_VALUE) {
            // Ativar freecam
            onEnable();
        } else {
            // Desativar freecam
            onDisable();
        }
    }
    
    /**
     * Verifica se freecam está ativo
     */
    public boolean isFreecamActive() {
        return originalX != Double.MAX_VALUE;
    }
    
    @SubscribeEvent
    public void onKeyInput(InputEvent.KeyInputEvent event) {
        if (Keyboard.isKeyDown(Keyboard.KEY_G) && Keyboard.getEventKeyState()) {
            toggle();
        }

        // Ajustar velocidade durante freecam
        if (isActive && Keyboard.getEventKeyState()) {
            if (Keyboard.isKeyDown(Keyboard.KEY_EQUALS)) { // Tecla +
                increaseSpeed();
            } else if (Keyboard.isKeyDown(Keyboard.KEY_MINUS)) { // Tecla -
                decreaseSpeed();
            }
        }
    }
    
    @SubscribeEvent
    public void onLivingUpdate(LivingEvent.LivingUpdateEvent event) {
        if (event.entity != mc.thePlayer) return;

        if (originalX != Double.MAX_VALUE) {
            // Freecam está ativo - manter player parado e mover apenas câmera

            // Manter player na posição original (parado)
            mc.thePlayer.setPosition(originalX, originalY, originalZ);
            mc.thePlayer.motionX = 0.0;
            mc.thePlayer.motionY = 0.0;
            mc.thePlayer.motionZ = 0.0;
            mc.thePlayer.rotationYaw = originalYaw;
            mc.thePlayer.rotationPitch = originalPitch;

            // Mover apenas a câmera
            updateCameraMovement();
        }
    }

    /**
     * Atualiza movimento da câmera livre
     */
    private void updateCameraMovement() {
        // Velocidade da câmera
        double moveSpeed = speed * 0.1;

        // Calcular direções baseadas na rotação da câmera
        float yawRad = (float) Math.toRadians(cameraYaw);
        float pitchRad = (float) Math.toRadians(cameraPitch);

        // Movimento horizontal
        double motionX = 0, motionZ = 0, motionY = 0;

        if (mc.gameSettings.keyBindForward.isKeyDown()) {
            motionX -= Math.sin(yawRad) * Math.cos(pitchRad) * moveSpeed;
            motionZ += Math.cos(yawRad) * Math.cos(pitchRad) * moveSpeed;
        }
        if (mc.gameSettings.keyBindBack.isKeyDown()) {
            motionX += Math.sin(yawRad) * Math.cos(pitchRad) * moveSpeed;
            motionZ -= Math.cos(yawRad) * Math.cos(pitchRad) * moveSpeed;
        }
        if (mc.gameSettings.keyBindLeft.isKeyDown()) {
            motionX -= Math.cos(yawRad) * moveSpeed;
            motionZ -= Math.sin(yawRad) * moveSpeed;
        }
        if (mc.gameSettings.keyBindRight.isKeyDown()) {
            motionX += Math.cos(yawRad) * moveSpeed;
            motionZ += Math.sin(yawRad) * moveSpeed;
        }

        // Movimento vertical
        if (mc.gameSettings.keyBindJump.isKeyDown()) {
            motionY += moveSpeed;
        }
        if (mc.gameSettings.keyBindSneak.isKeyDown()) {
            motionY -= moveSpeed;
        }

        // Aplicar movimento à câmera
        cameraX += motionX;
        cameraY += motionY;
        cameraZ += motionZ;

        // Capturar movimento do mouse
        updateCameraRotation();
    }

    /**
     * Atualiza rotação da câmera com mouse
     */
    private void updateCameraRotation() {
        int deltaX = Mouse.getDX();
        int deltaY = Mouse.getDY();

        if (deltaX != 0 || deltaY != 0) {
            float sensitivity = mc.gameSettings.mouseSensitivity * 0.6f + 0.2f;

            cameraYaw += deltaX * sensitivity;
            cameraPitch -= deltaY * sensitivity;

            // Limitar pitch
            if (cameraPitch > 90.0f) cameraPitch = 90.0f;
            if (cameraPitch < -90.0f) cameraPitch = -90.0f;

            // Normalizar yaw
            while (cameraYaw > 180.0f) cameraYaw -= 360.0f;
            while (cameraYaw < -180.0f) cameraYaw += 360.0f;
        }
    }

    /**
     * Substitui posição da câmera durante renderização
     */
    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public void onCameraSetup(EntityViewRenderEvent.CameraSetup event) {
        if (originalX == Double.MAX_VALUE) return; // Freecam não ativo

        // Substituir posição da câmera
        event.entity.posX = cameraX;
        event.entity.posY = cameraY;
        event.entity.posZ = cameraZ;
        event.entity.prevPosX = cameraX;
        event.entity.prevPosY = cameraY;
        event.entity.prevPosZ = cameraZ;
        event.entity.lastTickPosX = cameraX;
        event.entity.lastTickPosY = cameraY;
        event.entity.lastTickPosZ = cameraZ;

        // Aplicar rotação da câmera
        event.yaw = cameraYaw;
        event.pitch = cameraPitch;
    }
    
    @SubscribeEvent
    public void onWorldChange(WorldEvent.Load event) {
        if (isActive) {
            toggle(); // Desativar freecam ao mudar de mundo
        }
    }
    

    
    /**
     * Verifica se player está se movendo
     */
    private boolean isMoving() {
        return mc.gameSettings.keyBindForward.isKeyDown() ||
               mc.gameSettings.keyBindBack.isKeyDown() ||
               mc.gameSettings.keyBindLeft.isKeyDown() ||
               mc.gameSettings.keyBindRight.isKeyDown();
    }
    
    /**
     * Aumenta velocidade
     */
    public void increaseSpeed() {
        speed = Math.min(speed + 1.0, 15.0);
        Logger.sendMessage("§7Velocidade Freecam: §e" + String.format("%.1f", speed));
    }

    /**
     * Diminui velocidade
     */
    public void decreaseSpeed() {
        speed = Math.max(speed - 1.0, 1.0);
        Logger.sendMessage("§7Velocidade Freecam: §e" + String.format("%.1f", speed));
    }
    
    /**
     * Define velocidade
     */
    public void setSpeed(double speed) {
        this.speed = Math.max(0.1, Math.min(speed, 10.0));
    }
    
    /**
     * Obtém velocidade atual
     */
    public double getSpeed() {
        return speed;
    }
}
