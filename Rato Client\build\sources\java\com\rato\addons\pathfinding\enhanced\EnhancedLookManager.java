package com.rato.addons.pathfinding.enhanced;

import net.minecraft.client.Minecraft;
import net.minecraft.util.MathHelper;
import net.minecraft.util.Vec3;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;

/**
 * Sistema de rotação suave melhorado baseado no LookManager do Mucifex
 */
public class EnhancedLookManager {
    
    private static EnhancedLookManager instance;
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Estado da rotação
    private boolean active = false;
    private double yawTarget;
    private double pitchTarget;
    private long lastUpdate;
    private boolean registered = false;
    
    // Configurações de suavidade (baseado no Mucifex)
    private double rotationSpeed = 200.0; // ms para rotação completa
    private double precision = 1.0; // Precisão em graus
    
    // Configurações avançadas
    private boolean smoothRotation = true;
    private boolean limitPitch = true;
    private double maxPitchUp = -30.0;
    private double maxPitchDown = 30.0;
    
    public EnhancedLookManager() {
        instance = this;
    }
    
    public static EnhancedLookManager getInstance() {
        if (instance == null) {
            instance = new EnhancedLookManager();
        }
        return instance;
    }
    
    /**
     * Define alvo de rotação (baseado no Mucifex)
     */
    public void setTarget(double yaw, double pitch) {
        this.yawTarget = normalizeYaw(yaw);
        this.pitchTarget = limitPitch ? MathHelper.clamp_double(pitch, maxPitchUp, maxPitchDown) : pitch;
        this.active = true;
        this.lastUpdate = System.currentTimeMillis();
        
        if (!registered) {
            MinecraftForge.EVENT_BUS.register(this);
            registered = true;
        }
    }
    
    /**
     * Define alvo baseado em posição
     */
    public void lookAt(Vec3 target) {
        if (mc.thePlayer == null) return;
        
        Vec3 playerPos = mc.thePlayer.getPositionEyes(1.0f);
        Vec3 direction = target.subtract(playerPos).normalize();
        
        double yaw = Math.toDegrees(Math.atan2(-direction.xCoord, direction.zCoord));
        double pitch = Math.toDegrees(Math.asin(-direction.yCoord));
        
        setTarget(yaw, pitch);
    }
    
    /**
     * Cancela rotação atual
     */
    public void cancel() {
        this.active = false;
        if (registered) {
            MinecraftForge.EVENT_BUS.unregister(this);
            registered = false;
        }
    }
    
    /**
     * Evento de tick para rotação suave (baseado no Mucifex)
     */
    @SubscribeEvent
    public void onRenderTick(TickEvent.RenderTickEvent event) {
        if (event.phase != TickEvent.Phase.END || mc.thePlayer == null || !active) {
            return;
        }
        
        double currentYaw = mc.thePlayer.rotationYaw;
        double currentPitch = mc.thePlayer.rotationPitch;
        
        // Verificar se chegou ao alvo (baseado no Mucifex)
        double yawDiff = Math.abs(normalizeYawDifference(yawTarget - currentYaw));
        double pitchDiff = Math.abs(pitchTarget - currentPitch);
        
        if (yawDiff < precision && pitchDiff < precision) {
            mc.thePlayer.rotationYaw = (float) yawTarget;
            mc.thePlayer.rotationPitch = (float) pitchTarget;
            cancel();
            return;
        }
        
        // Calcular velocidade de rotação baseada no tempo (como no Mucifex)
        long msElapsed = System.currentTimeMillis() - lastUpdate;
        double speedFactor = (double) msElapsed / rotationSpeed;
        
        if (smoothRotation) {
            // Rotação suave com aceleração/desaceleração
            speedFactor = applySmoothingCurve(speedFactor, yawDiff, pitchDiff);
        }
        
        // Aplicar rotação de yaw
        double yawChange = normalizeYawDifference(yawTarget - currentYaw) * speedFactor;
        mc.thePlayer.rotationYaw = (float) normalizeYaw(currentYaw + yawChange);
        
        // Aplicar rotação de pitch
        double pitchChange = (pitchTarget - currentPitch) * speedFactor;
        mc.thePlayer.rotationPitch = (float) (currentPitch + pitchChange);
        
        this.lastUpdate = System.currentTimeMillis();
    }
    
    /**
     * Aplica curva de suavização para movimento mais natural
     */
    private double applySmoothingCurve(double speedFactor, double yawDiff, double pitchDiff) {
        double maxDiff = Math.max(yawDiff, pitchDiff);
        
        // Acelerar no início, desacelerar no final
        if (maxDiff > 45) {
            // Movimento rápido para grandes diferenças
            return Math.min(speedFactor * 2.0, 1.0);
        } else if (maxDiff > 15) {
            // Movimento normal
            return speedFactor;
        } else {
            // Movimento lento para precisão final
            return speedFactor * 0.5;
        }
    }
    
    /**
     * Normaliza ângulo yaw para -180 a 180
     */
    private double normalizeYaw(double yaw) {
        while (yaw > 180) yaw -= 360;
        while (yaw < -180) yaw += 360;
        return yaw;
    }
    
    /**
     * Normaliza diferença de yaw para menor caminho
     */
    private double normalizeYawDifference(double diff) {
        while (diff > 180) diff -= 360;
        while (diff < -180) diff += 360;
        return diff;
    }
    
    /**
     * Verifica se está rotacionando
     */
    public boolean isActive() {
        return active;
    }
    
    /**
     * Verifica se está próximo do alvo
     */
    public boolean isNearTarget() {
        if (!active || mc.thePlayer == null) return true;
        
        double yawDiff = Math.abs(normalizeYawDifference(yawTarget - mc.thePlayer.rotationYaw));
        double pitchDiff = Math.abs(pitchTarget - mc.thePlayer.rotationPitch);
        
        return yawDiff < precision * 2 && pitchDiff < precision * 2;
    }
    
    /**
     * Força rotação instantânea
     */
    public void setInstant(double yaw, double pitch) {
        if (mc.thePlayer == null) return;
        
        mc.thePlayer.rotationYaw = (float) normalizeYaw(yaw);
        mc.thePlayer.rotationPitch = (float) (limitPitch ? MathHelper.clamp_double(pitch, maxPitchUp, maxPitchDown) : pitch);
        cancel();
    }
    
    /**
     * Rotação instantânea para posição
     */
    public void lookAtInstant(Vec3 target) {
        if (mc.thePlayer == null) return;
        
        Vec3 playerPos = mc.thePlayer.getPositionEyes(1.0f);
        Vec3 direction = target.subtract(playerPos).normalize();
        
        double yaw = Math.toDegrees(Math.atan2(-direction.xCoord, direction.zCoord));
        double pitch = Math.toDegrees(Math.asin(-direction.yCoord));
        
        setInstant(yaw, pitch);
    }
    
    // Getters e Setters
    public double getRotationSpeed() { return rotationSpeed; }
    public void setRotationSpeed(double speed) { this.rotationSpeed = Math.max(50, speed); }
    
    public double getPrecision() { return precision; }
    public void setPrecision(double precision) { this.precision = Math.max(0.1, precision); }
    
    public boolean isSmoothRotation() { return smoothRotation; }
    public void setSmoothRotation(boolean smooth) { this.smoothRotation = smooth; }
    
    public boolean isLimitPitch() { return limitPitch; }
    public void setLimitPitch(boolean limit) { this.limitPitch = limit; }
    
    public double getMaxPitchUp() { return maxPitchUp; }
    public void setMaxPitchUp(double maxPitchUp) { this.maxPitchUp = Math.max(-90, maxPitchUp); }
    
    public double getMaxPitchDown() { return maxPitchDown; }
    public void setMaxPitchDown(double maxPitchDown) { this.maxPitchDown = Math.min(90, maxPitchDown); }
    
    /**
     * Obtém informações de debug
     */
    public String getDebugInfo() {
        if (!active || mc.thePlayer == null) {
            return "Inativo";
        }
        
        double currentYaw = mc.thePlayer.rotationYaw;
        double currentPitch = mc.thePlayer.rotationPitch;
        double yawDiff = normalizeYawDifference(yawTarget - currentYaw);
        double pitchDiff = pitchTarget - currentPitch;
        
        return String.format("Yaw: %.1f→%.1f (%.1f°), Pitch: %.1f→%.1f (%.1f°)", 
            currentYaw, yawTarget, yawDiff, currentPitch, pitchTarget, pitchDiff);
    }
    
    /**
     * Limpa recursos
     */
    public void shutdown() {
        cancel();
    }
}
