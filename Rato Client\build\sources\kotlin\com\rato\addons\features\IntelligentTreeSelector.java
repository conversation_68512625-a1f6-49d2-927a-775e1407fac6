package com.rato.addons.features;

import net.minecraft.client.Minecraft;
import net.minecraft.util.Vec3;
import net.minecraft.util.BlockPos;
import net.minecraft.block.Block;
import net.minecraft.init.Blocks;

import java.util.*;

/**
 * Sistema inteligente de seleção de árvores e blocos de quebra
 * Implementa clusterização, análise geométrica e seleção ótima de pontos de quebra
 */
public class IntelligentTreeSelector {
    
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Configurações de análise
    private static final int MAX_CLUSTER_SIZE = 200; // Máximo de blocos por árvore
    private static final double MAX_BREAK_DISTANCE = 4.5; // Alcance máximo para quebra
    private static final int MIN_TREE_SIZE = 3; // Mínimo de troncos para ser considerado árvore
    
    /**
     * Cluster de árvore com análise geométrica
     */
    public static class TreeCluster {
        public final List<BlockPos> logBlocks;
        public final List<BlockPos> baseBlocks; // Blocos acessíveis do chão
        public final BlockPos optimalBreakPoint; // Melhor bloco para quebrar
        public final BlockPos standingPoint; // Posição ideal para ficar
        public final Vec3 centroid; // Centro geométrico
        public final double utility; // Pontuação de utilidade
        public final boolean isAccessible; // Se é acessível
        
        public TreeCluster(List<BlockPos> logBlocks, List<BlockPos> baseBlocks, 
                          BlockPos optimalBreakPoint, BlockPos standingPoint,
                          Vec3 centroid, double utility, boolean isAccessible) {
            this.logBlocks = logBlocks;
            this.baseBlocks = baseBlocks;
            this.optimalBreakPoint = optimalBreakPoint;
            this.standingPoint = standingPoint;
            this.centroid = centroid;
            this.utility = utility;
            this.isAccessible = isAccessible;
        }
        
        public int getSize() { return logBlocks.size(); }
        public boolean isValid() { return isAccessible && optimalBreakPoint != null && standingPoint != null; }
    }
    
    /**
     * Encontra e analisa todas as árvores em uma área
     */
    public List<TreeCluster> findTreeClusters(BlockPos center, int radius, Block logType) {
        if (mc.theWorld == null) return new ArrayList<>();
        
        // Fase 1: Coleta de dados brutos - encontrar todos os blocos de log
        Set<BlockPos> allLogBlocks = scanForLogBlocks(center, radius, logType);
        
        // Fase 2: Clusterização - agrupar logs em árvores individuais
        List<List<BlockPos>> rawClusters = clusterLogBlocks(allLogBlocks);
        
        // Fase 3: Análise geométrica e seleção de pontos ótimos
        List<TreeCluster> analyzedClusters = new ArrayList<>();
        for (List<BlockPos> cluster : rawClusters) {
            TreeCluster analyzed = analyzeTreeCluster(cluster, logType);
            if (analyzed != null && analyzed.isValid()) {
                analyzedClusters.add(analyzed);
            }
        }
        
        // Fase 4: Ordenar por utilidade
        analyzedClusters.sort((a, b) -> Double.compare(b.utility, a.utility));
        
        return analyzedClusters;
    }
    
    /**
     * Fase 1: Escaneamento de área para encontrar blocos de log
     */
    private Set<BlockPos> scanForLogBlocks(BlockPos center, int radius, Block logType) {
        Set<BlockPos> logBlocks = new HashSet<>();
        
        for (int x = -radius; x <= radius; x++) {
            for (int z = -radius; z <= radius; z++) {
                for (int y = -10; y <= 20; y++) { // Escaneamento vertical amplo
                    BlockPos checkPos = center.add(x, y, z);
                    Block block = mc.theWorld.getBlockState(checkPos).getBlock();
                    
                    if (block == logType) {
                        logBlocks.add(checkPos);
                    }
                }
            }
        }
        
        return logBlocks;
    }
    
    /**
     * Fase 2: Clusterização usando Flood Fill (BFS)
     */
    private List<List<BlockPos>> clusterLogBlocks(Set<BlockPos> allLogBlocks) {
        List<List<BlockPos>> clusters = new ArrayList<>();
        Set<BlockPos> unprocessed = new HashSet<>(allLogBlocks);
        
        while (!unprocessed.isEmpty()) {
            BlockPos startBlock = unprocessed.iterator().next();
            List<BlockPos> cluster = floodFillCluster(startBlock, unprocessed);
            
            if (cluster.size() >= MIN_TREE_SIZE && cluster.size() <= MAX_CLUSTER_SIZE) {
                clusters.add(cluster);
            }
        }
        
        return clusters;
    }
    
    /**
     * Flood Fill para encontrar todos os blocos conectados
     */
    private List<BlockPos> floodFillCluster(BlockPos start, Set<BlockPos> unprocessed) {
        List<BlockPos> cluster = new ArrayList<>();
        Queue<BlockPos> queue = new LinkedList<>();
        Set<BlockPos> visited = new HashSet<>();
        
        queue.add(start);
        visited.add(start);
        
        while (!queue.isEmpty() && cluster.size() < MAX_CLUSTER_SIZE) {
            BlockPos current = queue.poll();
            cluster.add(current);
            unprocessed.remove(current);
            
            // Verificar todos os vizinhos (26 direções)
            for (int dx = -1; dx <= 1; dx++) {
                for (int dy = -1; dy <= 1; dy++) {
                    for (int dz = -1; dz <= 1; dz++) {
                        if (dx == 0 && dy == 0 && dz == 0) continue;
                        
                        BlockPos neighbor = current.add(dx, dy, dz);
                        if (unprocessed.contains(neighbor) && !visited.contains(neighbor)) {
                            queue.add(neighbor);
                            visited.add(neighbor);
                        }
                    }
                }
            }
        }
        
        return cluster;
    }
    
    /**
     * Fase 3: Análise geométrica completa do cluster
     */
    private TreeCluster analyzeTreeCluster(List<BlockPos> logBlocks, Block logType) {
        // Encontrar blocos base (acessíveis do chão)
        List<BlockPos> baseBlocks = findBaseBlocks(logBlocks);
        if (baseBlocks.isEmpty()) {
            return null; // Árvore inacessível
        }
        
        // Calcular centroide geométrico da base
        Vec3 centroid = calculateCentroid(baseBlocks);
        
        // Encontrar ponto de quebra ótimo
        BlockPos optimalBreakPoint = findOptimalBreakPoint(baseBlocks, centroid);
        if (optimalBreakPoint == null) {
            return null;
        }
        
        // Encontrar posição ideal para ficar
        BlockPos standingPoint = findOptimalStandingPoint(optimalBreakPoint);
        if (standingPoint == null) {
            return null;
        }
        
        // Calcular utilidade
        double utility = calculateClusterUtility(logBlocks, standingPoint);
        
        return new TreeCluster(
            logBlocks, baseBlocks, optimalBreakPoint, standingPoint,
            centroid, utility, true
        );
    }
    
    /**
     * Encontra blocos base (acessíveis do chão)
     */
    private List<BlockPos> findBaseBlocks(List<BlockPos> logBlocks) {
        List<BlockPos> baseBlocks = new ArrayList<>();
        
        for (BlockPos logPos : logBlocks) {
            // Verificar se é um bloco base válido
            if (isBaseBlock(logPos)) {
                baseBlocks.add(logPos);
            }
        }
        
        return baseBlocks;
    }
    
    /**
     * Verifica se um bloco é um bloco base válido
     */
    private boolean isBaseBlock(BlockPos logPos) {
        // Deve estar próximo ao nível do jogador
        if (Math.abs(logPos.getY() - mc.thePlayer.posY) > 2) {
            return false;
        }
        
        // Deve ter chão sólido embaixo
        Block blockBelow = mc.theWorld.getBlockState(logPos.down()).getBlock();
        if (blockBelow != Blocks.dirt && blockBelow != Blocks.grass && !blockBelow.isFullBlock()) {
            return false;
        }
        
        // Deve ser acessível (não cercado por outros blocos)
        return isAccessibleFromGround(logPos);
    }
    
    /**
     * Verifica se um bloco é acessível do chão
     */
    private boolean isAccessibleFromGround(BlockPos logPos) {
        // Verificar se há pelo menos uma direção livre
        int[][] directions = {{1,0}, {-1,0}, {0,1}, {0,-1}};
        
        for (int[] dir : directions) {
            BlockPos adjacentGround = logPos.add(dir[0], -1, dir[1]);
            BlockPos adjacentAir = logPos.add(dir[0], 0, dir[1]);
            
            Block groundBlock = mc.theWorld.getBlockState(adjacentGround).getBlock();
            Block airBlock = mc.theWorld.getBlockState(adjacentAir).getBlock();
            
            if (groundBlock.isFullBlock() && (airBlock == Blocks.air || !airBlock.isFullBlock())) {
                return true; // Há acesso por esta direção
            }
        }
        
        return false;
    }
    
    /**
     * Calcula centroide geométrico dos blocos base
     */
    private Vec3 calculateCentroid(List<BlockPos> baseBlocks) {
        double sumX = 0, sumY = 0, sumZ = 0;
        
        for (BlockPos pos : baseBlocks) {
            sumX += pos.getX();
            sumY += pos.getY();
            sumZ += pos.getZ();
        }
        
        int count = baseBlocks.size();
        return new Vec3(sumX / count, sumY / count, sumZ / count);
    }
    
    /**
     * Encontra ponto de quebra ótimo (mais próximo do centroide)
     */
    private BlockPos findOptimalBreakPoint(List<BlockPos> baseBlocks, Vec3 centroid) {
        BlockPos bestBlock = null;
        double bestDistance = Double.MAX_VALUE;
        
        for (BlockPos candidate : baseBlocks) {
            Vec3 candidateVec = new Vec3(candidate.getX(), candidate.getY(), candidate.getZ());
            double distance = candidateVec.distanceTo(centroid);
            
            if (distance < bestDistance) {
                bestDistance = distance;
                bestBlock = candidate;
            }
        }
        
        return bestBlock;
    }
    
    /**
     * Encontra posição ótima para ficar (com linha de visão para o bloco de quebra)
     */
    private BlockPos findOptimalStandingPoint(BlockPos breakPoint) {
        Vec3 breakCenter = new Vec3(breakPoint.getX() + 0.5, breakPoint.getY() + 0.5, breakPoint.getZ() + 0.5);
        
        // Testar posições em círculo ao redor do bloco de quebra
        for (double radius = 2.0; radius <= MAX_BREAK_DISTANCE; radius += 0.5) {
            for (double angle = 0; angle < 2 * Math.PI; angle += Math.PI / 8) {
                double x = breakPoint.getX() + radius * Math.cos(angle);
                double z = breakPoint.getZ() + radius * Math.sin(angle);
                
                BlockPos candidatePos = new BlockPos(x, breakPoint.getY(), z);
                
                // Verificar se é uma posição válida para ficar
                if (isValidStandingPosition(candidatePos)) {
                    // Verificar linha de visão
                    Vec3 standingCenter = new Vec3(candidatePos.getX() + 0.5, candidatePos.getY() + 1.6, candidatePos.getZ() + 0.5);
                    if (hasLineOfSight(standingCenter, breakCenter)) {
                        return candidatePos;
                    }
                }
            }
        }
        
        return null; // Nenhuma posição válida encontrada
    }
    
    /**
     * Verifica se é uma posição válida para o jogador ficar
     */
    private boolean isValidStandingPosition(BlockPos pos) {
        Block groundBlock = mc.theWorld.getBlockState(pos).getBlock();
        Block airBlock1 = mc.theWorld.getBlockState(pos.up()).getBlock();
        Block airBlock2 = mc.theWorld.getBlockState(pos.up(2)).getBlock();
        Block supportBlock = mc.theWorld.getBlockState(pos.down()).getBlock();
        
        return supportBlock.isFullBlock() && // Chão sólido
               (groundBlock == Blocks.air || !groundBlock.isFullBlock()) && // Espaço para pés
               (airBlock1 == Blocks.air || !airBlock1.isFullBlock()) && // Espaço para corpo
               (airBlock2 == Blocks.air || !airBlock2.isFullBlock()); // Espaço para cabeça
    }
    
    /**
     * Verifica linha de visão entre dois pontos
     */
    private boolean hasLineOfSight(Vec3 from, Vec3 to) {
        double distance = from.distanceTo(to);
        int steps = Math.max(1, (int) Math.ceil(distance * 2)); // Mais precisão
        
        for (int i = 0; i <= steps; i++) {
            double t = (double) i / steps;
            Vec3 direction = to.subtract(from);
            Vec3 scaledDirection = new Vec3(direction.xCoord * t, direction.yCoord * t, direction.zCoord * t);
            Vec3 point = from.add(scaledDirection);
            BlockPos blockPos = new BlockPos(point);
            
            Block block = mc.theWorld.getBlockState(blockPos).getBlock();
            if (block != Blocks.air && block.isFullBlock()) {
                return false; // Obstáculo encontrado
            }
        }
        
        return true;
    }
    
    /**
     * Calcula utilidade do cluster
     */
    private double calculateClusterUtility(List<BlockPos> logBlocks, BlockPos standingPoint) {
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        double distance = Math.sqrt(standingPoint.distanceSq(new BlockPos(playerPos)));
        
        // Componentes da utilidade
        double sizeValue = logBlocks.size() * 2.0; // Valor por tamanho
        double distancePenalty = distance * 0.1; // Penalidade por distância
        double accessibilityBonus = 10.0; // Bonus por ser acessível
        
        return sizeValue + accessibilityBonus - distancePenalty;
    }
}
