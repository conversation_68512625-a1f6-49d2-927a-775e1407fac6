package com.rato.addons.features;

import com.rato.addons.config.RatoAddonsConfigSimple;
import com.rato.addons.util.AdvancedESP;
import com.rato.addons.util.RenderUtils;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.FontRenderer;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.Tessellator;
import net.minecraft.client.renderer.WorldRenderer;
import net.minecraft.client.renderer.vertex.DefaultVertexFormats;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.monster.EntityMob;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.item.ItemStack;
import net.minecraft.util.AxisAlignedBB;
import net.minecraftforge.client.event.RenderWorldLastEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import org.lwjgl.opengl.GL11;

import java.awt.*;

public class ESP {

    private static final Minecraft mc = Minecraft.getMinecraft();

    @SubscribeEvent
    public void onRenderWorldLast(RenderWorldLastEvent event) {
        if (mc.theWorld == null || mc.thePlayer == null)
            return;

        // Atualizar matrizes para o ESP 2D/3D
        AdvancedESP.updateMatrices();

        for (Entity entity : mc.theWorld.loadedEntityList) {
            if (entity == mc.thePlayer || !(entity instanceof EntityLivingBase))
                continue;

            double distance = mc.thePlayer.getDistanceToEntity(entity);
            if (distance > RatoAddonsConfigSimple.espRange)
                continue;

            Color color = getEntityColor(entity);
            if (color != null) {
                EntityLivingBase livingEntity = (EntityLivingBase) entity;

                // Converter Color para int (ARGB)
                int colorInt = (255 << 24) | (color.getRed() << 16) | (color.getGreen() << 8) | color.getBlue();

                // Renderizar ESP 2D se ativado
                if (RatoAddonsConfigSimple.esp2D) {
                    AdvancedESP.render2DESP(livingEntity, colorInt, RatoAddonsConfigSimple.espShowHealth,
                            RatoAddonsConfigSimple.espShowNames, event.partialTicks);
                }

                // Renderizar ESP 3D se ativado
                if (RatoAddonsConfigSimple.esp3D) {
                    AdvancedESP.render3DESP(livingEntity, colorInt, RatoAddonsConfigSimple.espShowHealth,
                            RatoAddonsConfigSimple.espShowNames, event.partialTicks);
                }
            }
        }
    }

    private Color getEntityColor(Entity entity) {
        if (entity instanceof EntityPlayer) {
            if (RatoAddonsConfigSimple.playerESP) {
                return new Color(RatoAddonsConfigSimple.espPlayerColor.getRGB());
            }
        } else if (entity instanceof EntityLivingBase) {
            if (RatoAddonsConfigSimple.mobESP) {
                if (entity instanceof EntityMob) {
                    return new Color(RatoAddonsConfigSimple.espMobColor.getRGB());
                } else {
                    return new Color(255, 255, 0, 255); // Amarelo para mobs passivos
                }
            }
        }
        return null;
    }

    private void drawBoundingBox(AxisAlignedBB bb, float r, float g, float b, float a) {
        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer buffer = tessellator.getWorldRenderer();

        GlStateManager.color(r, g, b, a);

        // Linhas horizontais embaixo
        buffer.begin(GL11.GL_LINE_LOOP, DefaultVertexFormats.POSITION);
        buffer.pos(bb.minX, bb.minY, bb.minZ).endVertex();
        buffer.pos(bb.maxX, bb.minY, bb.minZ).endVertex();
        buffer.pos(bb.maxX, bb.minY, bb.maxZ).endVertex();
        buffer.pos(bb.minX, bb.minY, bb.maxZ).endVertex();
        tessellator.draw();

        // Linhas horizontais em cima
        buffer.begin(GL11.GL_LINE_LOOP, DefaultVertexFormats.POSITION);
        buffer.pos(bb.minX, bb.maxY, bb.minZ).endVertex();
        buffer.pos(bb.maxX, bb.maxY, bb.minZ).endVertex();
        buffer.pos(bb.maxX, bb.maxY, bb.maxZ).endVertex();
        buffer.pos(bb.minX, bb.maxY, bb.maxZ).endVertex();
        tessellator.draw();

        // Verticais
        buffer.begin(GL11.GL_LINES, DefaultVertexFormats.POSITION);
        buffer.pos(bb.minX, bb.minY, bb.minZ).endVertex();
        buffer.pos(bb.minX, bb.maxY, bb.minZ).endVertex();

        buffer.pos(bb.maxX, bb.minY, bb.minZ).endVertex();
        buffer.pos(bb.maxX, bb.maxY, bb.minZ).endVertex();

        buffer.pos(bb.maxX, bb.minY, bb.maxZ).endVertex();
        buffer.pos(bb.maxX, bb.maxY, bb.maxZ).endVertex();

        buffer.pos(bb.minX, bb.minY, bb.maxZ).endVertex();
        buffer.pos(bb.minX, bb.maxY, bb.maxZ).endVertex();
        tessellator.draw();
    }

    private void renderEntityName(Entity entity, double px, double py, double pz, float partialTicks) {
        String name = entity.getName();
        if (name == null || name.isEmpty())
            return;

        double x = entity.lastTickPosX + (entity.posX - entity.lastTickPosX) * partialTicks - px;
        double y = entity.lastTickPosY + (entity.posY - entity.lastTickPosY) * partialTicks - py + entity.height + 1.0;
        double z = entity.lastTickPosZ + (entity.posZ - entity.lastTickPosZ) * partialTicks - pz;

        double distance = Math.sqrt(x * x + y * y + z * z);

        // Escala dinâmica baseada na distância
        float baseScale = 0.01666667F * 1.5F;
        float dynamicScale = baseScale * Math.max(0.5F, Math.min(2.0F, (float) (distance / 10.0)));

        GlStateManager.pushMatrix();
        GlStateManager.translate(x, y, z);
        GlStateManager.rotate(-mc.getRenderManager().playerViewY, 0.0F, 1.0F, 0.0F);
        GlStateManager.rotate(mc.getRenderManager().playerViewX, 1.0F, 0.0F, 0.0F);
        GlStateManager.scale(-dynamicScale, -dynamicScale, dynamicScale);

        // Estados OpenGL otimizados
        GlStateManager.enableTexture2D();
        GlStateManager.enableBlend();
        GlStateManager.depthMask(false);
        GlStateManager.disableLighting();

        FontRenderer fontRenderer = mc.fontRendererObj;

        // === PREPARAR DADOS ===
        boolean isPlayer = entity instanceof EntityPlayer;
        EntityPlayer player = isPlayer ? (EntityPlayer) entity : null;
        ItemStack[] equipment = isPlayer ? getPlayerEquipment(player) : null;
        int level = isPlayer ? player.experienceLevel : 0;

        // === PREPARAR TEXTOS ===
        String nameColor = isPlayer ? "§b" : "§f"; // Ciano para players, branco para mobs
        String distanceColor = getDistanceColor(distance);

        // Textos limpos para medição (sem códigos de cor)
        String cleanName = name;
        String cleanLevel = level > 0 ? String.valueOf(level) : "";
        String cleanDistance = "[" + Math.round(distance) + "m]";

        // Calcular larguras individuais
        int nameWidth = fontRenderer.getStringWidth(cleanName);
        int levelWidth = level > 0 ? fontRenderer.getStringWidth(cleanLevel) : 0;
        int distanceWidth = fontRenderer.getStringWidth(cleanDistance);
        int separatorWidth = fontRenderer.getStringWidth(" | ");
        int spacing = 4; // Espaço entre nome e distância

        // Calcular largura total do texto corretamente
        int totalTextWidth;
        if (level > 0) {
            totalTextWidth = levelWidth + separatorWidth + nameWidth + spacing + distanceWidth;
        } else {
            totalTextWidth = nameWidth + spacing + distanceWidth;
        }

        // === CALCULAR DIMENSÕES ===
        int iconsWidth = (equipment != null && equipment.length > 0) ? equipment.length * 18 : 0;
        int contentWidth = Math.max(totalTextWidth, iconsWidth);
        int padding = 6; // Padding interno
        int totalWidth = contentWidth + (padding * 2);
        int totalHeight = (equipment != null && equipment.length > 0) ? 36 : 20; // Com/sem ícones

        // === DESENHAR FUNDO ARREDONDADO ===
        float bgX = -totalWidth / 2.0F;
        float bgY = -totalHeight / 2.0F;
        RenderUtils.renderRoundedQuad(bgX, bgY, totalWidth, totalHeight, 6.0F, 0x80000000); // Preto translúcido

        // === RENDERIZAR ÍCONES DOS EQUIPAMENTOS ===
        if (equipment != null && equipment.length > 0) {
            GlStateManager.enableTexture2D();
            GlStateManager.color(1.0F, 1.0F, 1.0F, 1.0F);

            int iconStartX = -iconsWidth / 2;
            int iconY = (int) (bgY + 4); // Margem superior

            for (int i = 0; i < equipment.length; i++) {
                ItemStack stack = equipment[i];
                if (stack != null) {
                    int iconX = iconStartX + (i * 18);
                    mc.getRenderItem().renderItemAndEffectIntoGUI(stack, iconX, iconY);
                }
            }
        }

        // === RENDERIZAR TEXTO PRINCIPAL ===
        GlStateManager.enableTexture2D();
        GlStateManager.color(1.0F, 1.0F, 1.0F, 1.0F);

        // Posição do texto
        int textY = (equipment != null && equipment.length > 0) ? 6 : -4; // Abaixo dos ícones ou centralizado
        int textStartX = -totalTextWidth / 2; // Centralizar baseado na largura total real

        // Renderizar cada parte do texto com posicionamento correto
        int currentX = textStartX;

        if (level > 0) {
            // Renderizar level
            fontRenderer.drawStringWithShadow("§a" + cleanLevel, currentX, textY, 0xFFFFFF);
            currentX += levelWidth;

            // Renderizar separador
            fontRenderer.drawStringWithShadow("§7 | ", currentX, textY, 0xFFFFFF);
            currentX += separatorWidth;
        }

        // Renderizar nome
        fontRenderer.drawStringWithShadow(nameColor + cleanName, currentX, textY, 0xFFFFFF);
        currentX += nameWidth + spacing;

        // Renderizar distância
        fontRenderer.drawStringWithShadow(distanceColor + cleanDistance, currentX, textY, 0xFFFFFF);

        // Restaurar estados OpenGL
        GlStateManager.enableTexture2D();
        GlStateManager.depthMask(true);
        GlStateManager.enableLighting();
        GlStateManager.disableBlend();
        GlStateManager.color(1.0F, 1.0F, 1.0F, 1.0F);
        GlStateManager.popMatrix();
    }

    // Métodos de renderização removidos - agora usando
    // RenderUtils.renderRoundedQuad

    private ItemStack[] getPlayerEquipment(EntityPlayer player) {
        java.util.List<ItemStack> equipment = new java.util.ArrayList<>();

        // Item na mão
        ItemStack heldItem = player.getHeldItem();
        if (heldItem != null) {
            equipment.add(heldItem);
        }

        // Armadura (capacete, peitoral, calça, bota)
        for (int i = 3; i >= 0; i--) {
            ItemStack armor = player.inventory.armorInventory[i];
            if (armor != null) {
                equipment.add(armor);
            }
        }

        return equipment.toArray(new ItemStack[0]);
    }

    private String getDistanceColor(double distance) {
        if (distance < 10)
            return "§a"; // Verde - muito próximo
        if (distance < 25)
            return "§e"; // Amarelo - próximo
        if (distance < 50)
            return "§6"; // Laranja - médio
        return "§c"; // Vermelho - longe
    }
}