package com.rato.addons.visuals.font

import net.minecraft.client.Minecraft
import net.minecraft.client.renderer.GlStateManager
import net.minecraft.client.renderer.Tessellator
import net.minecraft.client.renderer.vertex.DefaultVertexFormats
import org.lwjgl.BufferUtils
import org.lwjgl.opengl.GL11
import java.awt.*
import java.awt.geom.Rectangle2D
import java.awt.image.BufferedImage
import java.nio.ByteBuffer
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.ceil

/**
 * Sistema de renderização de fontes em Kotlin (estilo LiquidBounce)
 * Muito mais limpo e eficiente que Java
 */
object KotlinFontRenderer {
    
    private val mc = Minecraft.getMinecraft()
    
    // Cache de fontes thread-safe
    private val fontCache = ConcurrentHashMap<String, FontRenderer>()
    
    // Instâncias padrão (lazy initialization)
    val DEFAULT by lazy { getFont("Segoe UI", Font.PLAIN, 16) }
    val BOLD by lazy { getFont("Segoe UI", Font.BOLD, 16) }
    val LARGE by lazy { getFont("Segoe UI", Font.PLAIN, 20) }
    val SMALL by lazy { getFont("Segoe UI", Font.PLAIN, 12) }
    
    /**
     * Obtém uma instância de fonte com cache
     */
    fun getFont(name: String, style: Int, size: Int): FontRenderer {
        val key = "${name}_${style}_${size}"
        return fontCache.computeIfAbsent(key) {
            FontRenderer(Font(name, style, size), true)
        }
    }
    
    /**
     * Classe de renderização de fonte
     */
    class FontRenderer(private val font: Font, private val antiAlias: Boolean) {
        
        private val fontMetrics: FontMetrics
        private val fontHeight: Int
        
        // Cache de texturas para caracteres
        private val charCache = ConcurrentHashMap<Char, CharTexture>()
        
        init {
            // Inicializar métricas da fonte
            val tempImg = BufferedImage(1, 1, BufferedImage.TYPE_INT_ARGB)
            val tempGraphics = tempImg.createGraphics()
            
            if (antiAlias) {
                tempGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON)
                tempGraphics.setRenderingHint(RenderingHints.KEY_FRACTIONALMETRICS, RenderingHints.VALUE_FRACTIONALMETRICS_ON)
            }
            
            tempGraphics.font = font
            fontMetrics = tempGraphics.fontMetrics
            fontHeight = fontMetrics.height
            
            tempGraphics.dispose()
        }
        
        /**
         * Data class para textura de caractere
         */
        private data class CharTexture(
            val textureId: Int,
            val width: Int,
            val height: Int
        )
        
        /**
         * Gera textura para um caractere
         */
        private fun generateCharTexture(character: Char): CharTexture {
            // Calcular dimensões do caractere
            val bounds = fontMetrics.getStringBounds(character.toString(), null)
            val charWidth = ceil(bounds.width).toInt() + 8 // Padding
            val charHeight = ceil(bounds.height).toInt() + 8
            
            val finalWidth = if (charWidth <= 0) 8 else charWidth
            val finalHeight = if (charHeight <= 0) fontHeight else charHeight
            
            // Criar imagem para o caractere
            val charImage = BufferedImage(finalWidth, finalHeight, BufferedImage.TYPE_INT_ARGB)
            val graphics = charImage.createGraphics()
            
            // Configurar renderização
            if (antiAlias) {
                graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON)
                graphics.setRenderingHint(RenderingHints.KEY_FRACTIONALMETRICS, RenderingHints.VALUE_FRACTIONALMETRICS_ON)
            }
            
            graphics.font = font
            graphics.color = Color.WHITE
            
            // Desenhar caractere
            graphics.drawString(character.toString(), 4, fontMetrics.ascent + 4)
            graphics.dispose()
            
            // Converter para textura OpenGL
            val textureId = GL11.glGenTextures()
            GL11.glBindTexture(GL11.GL_TEXTURE_2D, textureId)
            
            // Configurar parâmetros da textura
            GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_MIN_FILTER, GL11.GL_LINEAR)
            GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_MAG_FILTER, GL11.GL_LINEAR)
            GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_WRAP_S, GL11.GL_CLAMP)
            GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_WRAP_T, GL11.GL_CLAMP)
            
            // Upload da imagem para GPU
            uploadImageToTexture(charImage)
            
            return CharTexture(textureId, finalWidth, finalHeight)
        }
        
        /**
         * Upload da imagem para textura OpenGL (compatível com Java 8)
         */
        private fun uploadImageToTexture(image: BufferedImage) {
            val width = image.width
            val height = image.height
            
            val pixels = IntArray(width * height)
            image.getRGB(0, 0, width, height, pixels, 0, width)
            
            // Converter para ByteBuffer (RGBA)
            val buffer = BufferUtils.createByteBuffer(width * height * 4)
            
            for (pixel in pixels) {
                buffer.put(((pixel shr 16) and 0xFF).toByte()) // Red
                buffer.put(((pixel shr 8) and 0xFF).toByte())  // Green
                buffer.put((pixel and 0xFF).toByte())          // Blue
                buffer.put(((pixel shr 24) and 0xFF).toByte()) // Alpha
            }
            
            (buffer as java.nio.Buffer).flip() // Cast para compatibilidade Java 8
            
            GL11.glTexImage2D(
                GL11.GL_TEXTURE_2D, 0, GL11.GL_RGBA, width, height, 0,
                GL11.GL_RGBA, GL11.GL_UNSIGNED_BYTE, buffer
            )
        }
        
        /**
         * Desenha uma string (método principal)
         */
        fun drawString(text: String, x: Float, y: Float, color: Int, shadow: Boolean = false): Float {
            if (text.isEmpty()) return x
            
            GlStateManager.pushMatrix()
            GlStateManager.enableBlend()
            GlStateManager.blendFunc(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA)
            GlStateManager.enableTexture2D()
            
            var currentX = x
            
            // Desenhar sombra primeiro
            if (shadow) {
                val shadowColor = if ((color and 0xFF000000.toInt()) != 0) {
                    ((color shr 24 and 0xFF) / 4 shl 24)
                } else {
                    0x40000000
                }
                drawStringInternal(text, x + 1f, y + 1f, shadowColor)
            }
            
            // Desenhar texto principal
            currentX = drawStringInternal(text, x, y, color)
            
            GlStateManager.disableBlend()
            GlStateManager.popMatrix()
            
            return currentX
        }
        
        /**
         * Renderização interna da string
         */
        private fun drawStringInternal(text: String, x: Float, y: Float, color: Int): Float {
            var currentX = x
            
            // Extrair componentes de cor
            val alpha = ((color shr 24) and 0xFF) / 255.0f
            val red = ((color shr 16) and 0xFF) / 255.0f
            val green = ((color shr 8) and 0xFF) / 255.0f
            val blue = (color and 0xFF) / 255.0f
            
            GlStateManager.color(red, green, blue, alpha)
            
            for (character in text) {
                if (character == ' ') {
                    currentX += getCharWidth(' ')
                    continue
                }
                
                val charTexture = charCache.computeIfAbsent(character) { generateCharTexture(it) }
                
                // Renderizar caractere
                GL11.glBindTexture(GL11.GL_TEXTURE_2D, charTexture.textureId)
                
                val tessellator = Tessellator.getInstance()
                val worldRenderer = tessellator.worldRenderer
                
                worldRenderer.begin(GL11.GL_QUADS, DefaultVertexFormats.POSITION_TEX)
                worldRenderer.pos(currentX.toDouble(), (y + charTexture.height).toDouble(), 0.0).tex(0.0, 1.0).endVertex()
                worldRenderer.pos((currentX + charTexture.width).toDouble(), (y + charTexture.height).toDouble(), 0.0).tex(1.0, 1.0).endVertex()
                worldRenderer.pos((currentX + charTexture.width).toDouble(), y.toDouble(), 0.0).tex(1.0, 0.0).endVertex()
                worldRenderer.pos(currentX.toDouble(), y.toDouble(), 0.0).tex(0.0, 0.0).endVertex()
                
                tessellator.draw()
                
                currentX += charTexture.width - 8 // Remover padding
            }
            
            return currentX
        }
        
        /**
         * Obtém a largura de um caractere
         */
        fun getCharWidth(character: Char): Int {
            if (character == ' ') {
                return fontMetrics.charWidth(' ')
            }
            
            val charTexture = charCache[character]
            return if (charTexture != null) {
                charTexture.width - 8 // Remover padding
            } else {
                fontMetrics.charWidth(character)
            }
        }
        
        /**
         * Obtém a largura de uma string
         */
        fun getStringWidth(text: String): Int {
            if (text.isEmpty()) return 0
            return text.sumOf { getCharWidth(it) }
        }
        
        /**
         * Obtém a altura da fonte
         */
        fun getHeight(): Int = fontHeight
        
        /**
         * Desenha string centralizada
         */
        fun drawCenteredString(text: String, x: Float, y: Float, color: Int, shadow: Boolean = false) {
            val width = getStringWidth(text)
            drawString(text, x - width / 2f, y, color, shadow)
        }
        
        /**
         * Limpa o cache de texturas
         */
        fun clearCache() {
            charCache.values.forEach { charTexture ->
                GL11.glDeleteTextures(charTexture.textureId)
            }
            charCache.clear()
        }
    }
}
