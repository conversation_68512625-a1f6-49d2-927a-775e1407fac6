package com.rato.addons.pathfinding.professional;

import net.minecraft.block.Block;
import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.Tessellator;
import net.minecraft.client.renderer.WorldRenderer;
import net.minecraft.client.renderer.vertex.DefaultVertexFormats;
import net.minecraft.entity.Entity;
import net.minecraft.util.AxisAlignedBB;
import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;
import net.minecraft.world.World;
import net.minecraftforge.client.event.RenderWorldLastEvent;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import org.lwjgl.opengl.GL11;

import java.awt.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Sistema de renderização visual baseado no Mucifex original
 * Renderiza ESP de blocos e linhas conectadas como no Mucifex
 */
public class MucifexPathRenderer {
    
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Estado da renderização
    private List<PathNode> currentPath = new ArrayList<>();
    private boolean enabled = false;
    private boolean registered = false;
    
    // Configurações do Mucifex
    private static final int MAX_RENDERED_NODES = 300;
    private static final double MAX_RENDER_DISTANCE = 150 * 150;
    
    // Cores melhoradas baseadas no estilo Polar/Mucifex
    private static final Color PATH_COLOR = new Color(100, 200, 255, 220); // Azul claro brilhante
    private static final Color JUMP_COLOR = new Color(255, 140, 0, 200); // Laranja para pulos
    private static final Color NODE_COLOR = new Color(150, 255, 150, 160); // Verde claro para nodes normais
    private static final Color FALL_COLOR = new Color(255, 100, 255, 180); // Magenta para quedas
    private static final Color TERRAIN_NODE_COLOR = new Color(100, 255, 200, 140); // Cyan para nodes que seguem terreno
    private static final Color SLAB_NODE_COLOR = new Color(255, 200, 100, 160); // Laranja claro para slabs
    private static final Color CURRENT_NODE_COLOR = new Color(0, 255, 100, 255); // Verde brilhante para node atual
    
    /**
     * Define o caminho a ser renderizado
     */
    public void setPath(List<PathNode> path) {
        this.currentPath = path != null ? new ArrayList<>(path) : new ArrayList<>();
    }
    
    /**
     * Ativa/desativa a renderização
     */
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        
        if (enabled && !registered) {
            MinecraftForge.EVENT_BUS.register(this);
            registered = true;
        } else if (!enabled && registered) {
            MinecraftForge.EVENT_BUS.unregister(this);
            registered = false;
        }
    }
    
    /**
     * Evento principal de renderização (baseado no Mucifex)
     */
    @SubscribeEvent
    public void onRenderWorldLast(RenderWorldLastEvent event) {
        try {
            // Skip rendering if player is null
            if (mc.thePlayer == null) {
                return;
            }
            
            // Get current player position
            Vec3 playerPos = mc.thePlayer.getPositionVector();
            
            // Skip path rendering if path is empty or disabled
            if (!enabled || currentPath.isEmpty()) {
                return;
            }

            int nodesToRender = Math.min(currentPath.size(), MAX_RENDERED_NODES);
            PathNode lastNode = null;
            
            // Only render nodes within a reasonable distance
            for (int i = 0; i < nodesToRender; i++) {
                PathNode node = currentPath.get(i);
                
                if (node == null) {
                    continue; // Skip null elements
                }

                // If the node is too far from the player, don't render it
                if (isTooFarToRender(playerPos, node.position)) {
                    continue;
                }
                
                // Render ESP based on node type (melhorado para mostrar terreno)
                Color nodeColor = getNodeColor(node, i == 0);

                try {
                    // Renderizar o node principal
                    drawFilledEsp(node.blockPos, nodeColor);

                    // Se é um node que segue terreno, renderizar indicador adicional
                    if (isTerrainFollowingNode(node, lastNode)) {
                        drawTerrainIndicator(node.blockPos);
                    }

                    // Se é um slab, renderizar indicador especial
                    if (isSlabNode(node.blockPos)) {
                        drawSlabIndicator(node.blockPos);
                    }

                } catch (Exception e) {
                    // Skip if can't render
                }

                // Draw lines between nodes (like Mucifex)
                if (lastNode != null) {
                    try {
                        List<Vec3> lines = new ArrayList<>();
                        lines.add(lastNode.position.addVector(0, -0.5, 0));
                        lines.add(node.position.addVector(0, -0.5, 0));
                        drawLines(lines, 3.0f, event.partialTicks, PATH_COLOR.getRGB());
                    } catch (Exception e) {
                        // Skip if can't render lines
                    }
                }

                lastNode = node;
            }
            
        } catch (Exception e) {
            // If rendering fails for any reason, just silently ignore
            // This prevents crashes during rendering
        }
    }
    
    /**
     * Verifica se o nó está muito longe para renderizar
     */
    private boolean isTooFarToRender(Vec3 playerPos, Vec3 nodePos) {
        double distanceSquared = playerPos.squareDistanceTo(nodePos);
        return distanceSquared > MAX_RENDER_DISTANCE;
    }
    
    /**
     * Desenha ESP preenchido para um bloco (baseado no Mucifex)
     */
    private void drawFilledEsp(BlockPos blockPos, Color color) {
        if (mc.theWorld == null) return;
        
        try {
            World world = mc.theWorld;
            Block block = world.getBlockState(blockPos).getBlock();
            
            AxisAlignedBB box = block.getSelectedBoundingBox(world, blockPos)
                .offset(-mc.getRenderManager().viewerPosX, 
                       -mc.getRenderManager().viewerPosY, 
                       -mc.getRenderManager().viewerPosZ);
            
            drawBox(box, color, true, true, 2);
            
        } catch (Exception e) {
            // Skip if can't render
        }
    }
    
    /**
     * Desenha caixa com outline e preenchimento (baseado no Mucifex)
     */
    private void drawBox(AxisAlignedBB boundingBox, Color color, boolean outline, boolean box, int outlineWidth) {
        GL11.glBlendFunc(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA);
        GL11.glEnable(GL11.GL_BLEND);
        GL11.glLineWidth(outlineWidth);
        GL11.glDisable(GL11.GL_TEXTURE_2D);
        GL11.glDisable(GL11.GL_DEPTH_TEST);
        GL11.glDepthMask(false);
        setColor(color.getRGB());

        if (outline) {
            GL11.glLineWidth(outlineWidth);
            GL11.glEnable(GL11.GL_LINE_SMOOTH);
            setColor(new Color(color.getRed(), color.getGreen(), color.getBlue(), 95).getRGB());
            drawSelectionBoundingBox(boundingBox);
        }

        if (box) {
            setColor(new Color(color.getRed(), color.getGreen(), color.getBlue(), outline ? 26 : 35).getRGB());
            drawFilledBox(boundingBox);
        }

        GlStateManager.resetColor();
        GL11.glEnable(GL11.GL_TEXTURE_2D);
        GL11.glEnable(GL11.GL_DEPTH_TEST);
        GL11.glDepthMask(true);
        GL11.glDisable(GL11.GL_BLEND);
    }
    
    /**
     * Define cor OpenGL
     */
    private void setColor(int color) {
        float alpha = (color >> 24 & 0xFF) / 255.0f;
        float red = (color >> 16 & 0xFF) / 255.0f;
        float green = (color >> 8 & 0xFF) / 255.0f;
        float blue = (color & 0xFF) / 255.0f;
        GlStateManager.color(red, green, blue, alpha);
    }
    
    /**
     * Desenha outline da caixa
     */
    private void drawSelectionBoundingBox(AxisAlignedBB boundingBox) {
        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer worldRenderer = tessellator.getWorldRenderer();
        worldRenderer.begin(GL11.GL_LINE_STRIP, DefaultVertexFormats.POSITION);
        
        // Bottom face
        worldRenderer.pos(boundingBox.minX, boundingBox.minY, boundingBox.minZ).endVertex();
        worldRenderer.pos(boundingBox.maxX, boundingBox.minY, boundingBox.minZ).endVertex();
        worldRenderer.pos(boundingBox.maxX, boundingBox.minY, boundingBox.maxZ).endVertex();
        worldRenderer.pos(boundingBox.minX, boundingBox.minY, boundingBox.maxZ).endVertex();
        worldRenderer.pos(boundingBox.minX, boundingBox.minY, boundingBox.minZ).endVertex();
        
        tessellator.draw();
        
        worldRenderer.begin(GL11.GL_LINE_STRIP, DefaultVertexFormats.POSITION);
        
        // Top face
        worldRenderer.pos(boundingBox.minX, boundingBox.maxY, boundingBox.minZ).endVertex();
        worldRenderer.pos(boundingBox.maxX, boundingBox.maxY, boundingBox.minZ).endVertex();
        worldRenderer.pos(boundingBox.maxX, boundingBox.maxY, boundingBox.maxZ).endVertex();
        worldRenderer.pos(boundingBox.minX, boundingBox.maxY, boundingBox.maxZ).endVertex();
        worldRenderer.pos(boundingBox.minX, boundingBox.maxY, boundingBox.minZ).endVertex();
        
        tessellator.draw();
        
        worldRenderer.begin(GL11.GL_LINES, DefaultVertexFormats.POSITION);
        
        // Vertical lines
        worldRenderer.pos(boundingBox.minX, boundingBox.minY, boundingBox.minZ).endVertex();
        worldRenderer.pos(boundingBox.minX, boundingBox.maxY, boundingBox.minZ).endVertex();
        worldRenderer.pos(boundingBox.maxX, boundingBox.minY, boundingBox.minZ).endVertex();
        worldRenderer.pos(boundingBox.maxX, boundingBox.maxY, boundingBox.minZ).endVertex();
        worldRenderer.pos(boundingBox.maxX, boundingBox.minY, boundingBox.maxZ).endVertex();
        worldRenderer.pos(boundingBox.maxX, boundingBox.maxY, boundingBox.maxZ).endVertex();
        worldRenderer.pos(boundingBox.minX, boundingBox.minY, boundingBox.maxZ).endVertex();
        worldRenderer.pos(boundingBox.minX, boundingBox.maxY, boundingBox.maxZ).endVertex();
        
        tessellator.draw();
    }
    
    /**
     * Desenha caixa preenchida
     */
    private void drawFilledBox(AxisAlignedBB axisAlignedBB) {
        final Tessellator tessellator = Tessellator.getInstance();
        final WorldRenderer worldRenderer = tessellator.getWorldRenderer();
        worldRenderer.begin(GL11.GL_QUADS, DefaultVertexFormats.POSITION);
        
        // Bottom
        worldRenderer.pos(axisAlignedBB.minX, axisAlignedBB.minY, axisAlignedBB.minZ).endVertex();
        worldRenderer.pos(axisAlignedBB.maxX, axisAlignedBB.minY, axisAlignedBB.minZ).endVertex();
        worldRenderer.pos(axisAlignedBB.maxX, axisAlignedBB.minY, axisAlignedBB.maxZ).endVertex();
        worldRenderer.pos(axisAlignedBB.minX, axisAlignedBB.minY, axisAlignedBB.maxZ).endVertex();
        
        // Top
        worldRenderer.pos(axisAlignedBB.minX, axisAlignedBB.maxY, axisAlignedBB.minZ).endVertex();
        worldRenderer.pos(axisAlignedBB.minX, axisAlignedBB.maxY, axisAlignedBB.maxZ).endVertex();
        worldRenderer.pos(axisAlignedBB.maxX, axisAlignedBB.maxY, axisAlignedBB.maxZ).endVertex();
        worldRenderer.pos(axisAlignedBB.maxX, axisAlignedBB.maxY, axisAlignedBB.minZ).endVertex();
        
        // North
        worldRenderer.pos(axisAlignedBB.minX, axisAlignedBB.minY, axisAlignedBB.minZ).endVertex();
        worldRenderer.pos(axisAlignedBB.minX, axisAlignedBB.maxY, axisAlignedBB.minZ).endVertex();
        worldRenderer.pos(axisAlignedBB.maxX, axisAlignedBB.maxY, axisAlignedBB.minZ).endVertex();
        worldRenderer.pos(axisAlignedBB.maxX, axisAlignedBB.minY, axisAlignedBB.minZ).endVertex();
        
        // South
        worldRenderer.pos(axisAlignedBB.maxX, axisAlignedBB.minY, axisAlignedBB.maxZ).endVertex();
        worldRenderer.pos(axisAlignedBB.maxX, axisAlignedBB.maxY, axisAlignedBB.maxZ).endVertex();
        worldRenderer.pos(axisAlignedBB.minX, axisAlignedBB.maxY, axisAlignedBB.maxZ).endVertex();
        worldRenderer.pos(axisAlignedBB.minX, axisAlignedBB.minY, axisAlignedBB.maxZ).endVertex();
        
        // West
        worldRenderer.pos(axisAlignedBB.minX, axisAlignedBB.minY, axisAlignedBB.minZ).endVertex();
        worldRenderer.pos(axisAlignedBB.minX, axisAlignedBB.minY, axisAlignedBB.maxZ).endVertex();
        worldRenderer.pos(axisAlignedBB.minX, axisAlignedBB.maxY, axisAlignedBB.maxZ).endVertex();
        worldRenderer.pos(axisAlignedBB.minX, axisAlignedBB.maxY, axisAlignedBB.minZ).endVertex();
        
        // East
        worldRenderer.pos(axisAlignedBB.maxX, axisAlignedBB.minY, axisAlignedBB.maxZ).endVertex();
        worldRenderer.pos(axisAlignedBB.maxX, axisAlignedBB.minY, axisAlignedBB.minZ).endVertex();
        worldRenderer.pos(axisAlignedBB.maxX, axisAlignedBB.maxY, axisAlignedBB.minZ).endVertex();
        worldRenderer.pos(axisAlignedBB.maxX, axisAlignedBB.maxY, axisAlignedBB.maxZ).endVertex();
        
        tessellator.draw();
    }
    
    /**
     * Desenha linhas conectadas (baseado no Mucifex)
     */
    private void drawLines(List<Vec3> poses, float thickness, float partialTicks, int color) {
        Entity render = mc.getRenderViewEntity();
        WorldRenderer worldRenderer = Tessellator.getInstance().getWorldRenderer();
        double realX = render.lastTickPosX + (render.posX - render.lastTickPosX) * partialTicks;
        double realY = render.lastTickPosY + (render.posY - render.lastTickPosY) * partialTicks;
        double realZ = render.lastTickPosZ + (render.posZ - render.lastTickPosZ) * partialTicks;
        
        GlStateManager.pushMatrix();
        GlStateManager.translate(-realX, -realY, -realZ);
        GlStateManager.disableTexture2D();
        GlStateManager.disableLighting();
        GL11.glDisable(3553);
        GlStateManager.enableBlend();
        GlStateManager.disableAlpha();
        GL11.glLineWidth(thickness);
        GlStateManager.disableDepth();
        GlStateManager.depthMask(false);
        GlStateManager.tryBlendFuncSeparate(770, 771, 1, 0);
        GlStateManager.color(1.0f, 1.0f, 1.0f, 1.0f);
        
        worldRenderer.begin(3, DefaultVertexFormats.POSITION_COLOR);
        for (final Vec3 pos : poses) {
            worldRenderer.pos(pos.xCoord + 0.5, pos.yCoord + 0.5, pos.zCoord + 0.5)
                .color((color >> 16 & 0xFF) / 255.0f, (color >> 8 & 0xFF) / 255.0f, 
                       (color & 0xFF) / 255.0f, (color >> 24 & 0xFF) / 255.0f).endVertex();
        }
        
        Tessellator.getInstance().draw();
        GlStateManager.enableAlpha();
        GlStateManager.enableTexture2D();
        GlStateManager.enableLighting();
        GlStateManager.disableBlend();
        GlStateManager.depthMask(true);
        GlStateManager.enableDepth();
        GlStateManager.popMatrix();
    }
    
    /**
     * Limpa o caminho atual
     */
    public void clearPath() {
        this.currentPath.clear();
    }
    
    /**
     * Verifica se a renderização está ativa
     */
    public boolean isEnabled() {
        return enabled;
    }

    /**
     * Determina a cor do node baseado no tipo e contexto
     */
    private Color getNodeColor(PathNode node, boolean isCurrent) {
        if (isCurrent) {
            return CURRENT_NODE_COLOR;
        }

        if (node.movementType == PathNode.MovementType.JUMP || node.isJump) {
            return JUMP_COLOR;
        } else if (node.movementType == PathNode.MovementType.FALL || node.isFall) {
            return FALL_COLOR;
        } else if (isSlabNode(node.blockPos)) {
            return SLAB_NODE_COLOR;
        } else {
            return NODE_COLOR;
        }
    }

    /**
     * Verifica se um node segue o terreno naturalmente
     */
    private boolean isTerrainFollowingNode(PathNode current, PathNode previous) {
        if (previous == null) return false;

        int heightDiff = Math.abs(current.blockPos.getY() - previous.blockPos.getY());

        // Se há mudança de altura gradual, é seguindo terreno
        return heightDiff > 0 && heightDiff <= 2;
    }

    /**
     * Verifica se é um node em slab
     */
    private boolean isSlabNode(BlockPos pos) {
        try {
            Block blockBelow = mc.theWorld.getBlockState(pos.down()).getBlock();
            return blockBelow instanceof net.minecraft.block.BlockSlab;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Renderiza indicador de terreno
     */
    private void drawTerrainIndicator(BlockPos pos) {
        try {
            // Renderizar um pequeno indicador acima do node
            BlockPos indicatorPos = pos.up();
            Color terrainColor = new Color(TERRAIN_NODE_COLOR.getRed(),
                                         TERRAIN_NODE_COLOR.getGreen(),
                                         TERRAIN_NODE_COLOR.getBlue(), 80);
            drawFilledEsp(indicatorPos, terrainColor);
        } catch (Exception e) {
            // Skip if can't render
        }
    }

    /**
     * Renderiza indicador de slab
     */
    private void drawSlabIndicator(BlockPos pos) {
        try {
            // Renderizar indicador especial para slabs (cubo menor)
            Vec3 center = new Vec3(pos.getX() + 0.5, pos.getY() + 0.25, pos.getZ() + 0.5);
            drawSmallCube(center, 0.3, SLAB_NODE_COLOR);
        } catch (Exception e) {
            // Skip if can't render
        }
    }

    /**
     * Desenha um cubo pequeno para indicadores especiais
     */
    private void drawSmallCube(Vec3 center, double size, Color color) {
        double half = size / 2.0;

        AxisAlignedBB box = new AxisAlignedBB(
            center.xCoord - half, center.yCoord - half, center.zCoord - half,
            center.xCoord + half, center.yCoord + half, center.zCoord + half
        ).offset(-mc.getRenderManager().viewerPosX,
                -mc.getRenderManager().viewerPosY,
                -mc.getRenderManager().viewerPosZ);

        // Definir cor antes de desenhar
        setColor(color.getRGB());
        drawFilledBox(box);
    }
}
