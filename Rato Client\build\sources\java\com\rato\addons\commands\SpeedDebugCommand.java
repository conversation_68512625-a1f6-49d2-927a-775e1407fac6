package com.rato.addons.commands;

import com.rato.addons.pathfinding.movement.SimpleCameraController;
import com.rato.addons.util.Logger;
import net.minecraft.command.CommandBase;
import net.minecraft.command.CommandException;
import net.minecraft.command.ICommandSender;
import net.minecraft.entity.player.EntityPlayer;

/**
 * Comando para debug do sistema de detecção de velocidade
 */
public class SpeedDebugCommand extends CommandBase {
    
    private SimpleCameraController cameraController;
    
    public SpeedDebugCommand(SimpleCameraController cameraController) {
        this.cameraController = cameraController;
    }

    @Override
    public String getCommandName() {
        return "speeddebug";
    }

    @Override
    public String getCommandUsage(ICommandSender sender) {
        return "/speeddebug - Mostra informações de debug sobre detecção de velocidade";
    }

    @Override
    public void processCommand(ICommandSender sender, String[] args) throws CommandException {
        if (!(sender instanceof EntityPlayer)) {
            Logger.sendMessage("§cEste comando só pode ser usado por jogadores!");
            return;
        }
        
        if (cameraController == null) {
            Logger.sendMessage("§cSistema de câmera não inicializado!");
            return;
        }
        
        String debugInfo = cameraController.getSpeedDebugInfo();
        Logger.sendMessage("§7[Speed Debug] " + debugInfo);
    }

    @Override
    public boolean canCommandSenderUseCommand(ICommandSender sender) {
        return true;
    }
}
