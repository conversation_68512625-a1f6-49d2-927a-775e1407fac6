package com.rato.addons.gui;

import com.rato.addons.config.CustomConfigManager;
import net.minecraft.client.gui.GuiScreen;
import net.minecraft.client.gui.Gui;
import net.minecraft.client.renderer.GlStateManager;
import org.lwjgl.input.Keyboard;
import org.lwjgl.opengl.GL11;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Interface customizada baseada em OpenGL para substituir OneConfig
 * Inspirada no OpenGL-Hk reference
 */
public class CustomGUI extends GuiScreen {
    
    // Cores do tema
    private static final int BACKGROUND_COLOR = 0xE0101010;
    private static final int SIDEBAR_COLOR = 0xF0151515;
    private static final int SELECTED_COLOR = 0xFF00FF88;
    private static final int HOVER_COLOR = 0x4000FF88;
    private static final int TEXT_COLOR = 0xFFFFFFFF;
    private static final int SECONDARY_TEXT_COLOR = 0xFFAAAAAA;
    
    // Layout
    private static final int SIDEBAR_WIDTH = 150;
    private static final int PADDING = 10;
    private static final int COMPONENT_HEIGHT = 25;
    private static final int COMPONENT_SPACING = 5;
    
    // Estado
    private Category selectedCategory = Category.COMBAT;
    private List<GuiComponent> components = new ArrayList<>();
    private int scrollOffset = 0;
    
    public enum Category {
        COMBAT("Combat", 0xFF00FF88),
        VISUALS("Visuals", 0xFF0088FF),
        MOVEMENT("Movement", 0xFFFF8800),
        PLAYER("Player", 0xFFFF0088),
        WORLD("World", 0xFF88FF00),
        MISC("Misc", 0xFF8800FF);
        
        public final String name;
        public final int color;
        
        Category(String name, int color) {
            this.name = name;
            this.color = color;
        }
    }
    
    @Override
    public void initGui() {
        super.initGui();
        loadComponents();
    }
    
    private void loadComponents() {
        components.clear();
        
        switch (selectedCategory) {
            case COMBAT:
                loadCombatComponents();
                break;
            case VISUALS:
                loadVisualsComponents();
                break;
            case MOVEMENT:
                loadMovementComponents();
                break;
            case PLAYER:
                loadPlayerComponents();
                break;
            case WORLD:
                loadWorldComponents();
                break;
            case MISC:
                loadMiscComponents();
                break;
        }
    }
    
    private void loadCombatComponents() {
        components.add(new GuiComponent.ToggleComponent("Aimbot", "combat", "aimbot_enabled"));
        components.add(new GuiComponent.SliderComponent("FOV", "combat", "aimbot_fov", 10.0f, 180.0f));
        components.add(new GuiComponent.SliderComponent("Range", "combat", "aimbot_range", 1.0f, 10.0f));
        components.add(new GuiComponent.SliderComponent("Smoothness", "combat", "aimbot_smoothness", 0.01f, 1.0f));
        components.add(new GuiComponent.SliderComponent("Speed", "combat", "aimbot_speed", 0.1f, 3.0f));
        components.add(new GuiComponent.ToggleComponent("Prediction", "combat", "aimbot_prediction"));
        components.add(new GuiComponent.ToggleComponent("Raytrace", "combat", "aimbot_raytrace"));
    }
    
    private void loadVisualsComponents() {
        components.add(new GuiComponent.ToggleComponent("Player ESP", "visuals", "player_esp"));
        components.add(new GuiComponent.ToggleComponent("Mob ESP", "visuals", "mob_esp"));
        components.add(new GuiComponent.SliderComponent("ESP Range", "visuals", "esp_range", 10.0f, 100.0f));
        components.add(new GuiComponent.ToggleComponent("Show Names", "visuals", "esp_show_names"));
        components.add(new GuiComponent.ToggleComponent("2D ESP", "visuals", "esp_2d"));
        components.add(new GuiComponent.ToggleComponent("3D ESP", "visuals", "esp_3d"));
        components.add(new GuiComponent.ToggleComponent("Show Health", "visuals", "esp_show_health"));
        components.add(new GuiComponent.ColorComponent("Player Color", "visuals", "player_esp_color"));
        components.add(new GuiComponent.ColorComponent("Mob Color", "visuals", "mob_esp_color"));
    }
    
    private void loadMovementComponents() {
        components.add(new GuiComponent.ToggleComponent("Pathfinding", "movement", "pathfinding_enabled"));
        components.add(new GuiComponent.SliderComponent("Risk Tolerance", "movement", "risk_tolerance", 0, 100));
        components.add(new GuiComponent.ToggleComponent("Human Movement", "movement", "human_movement"));
        components.add(new GuiComponent.ToggleComponent("Block Breaking", "movement", "allow_block_breaking"));
        components.add(new GuiComponent.ToggleComponent("Risky Jumps", "movement", "allow_risky_jumps"));
        components.add(new GuiComponent.ToggleComponent("Path Visualization", "movement", "path_visualization"));
        components.add(new GuiComponent.SliderComponent("Movement Speed", "movement", "movement_speed", 1, 10));
        components.add(new GuiComponent.SliderComponent("Camera Speed", "movement", "camera_rotation_speed", 0.2f, 2.0f));
        components.add(new GuiComponent.SliderComponent("Camera Smoothness", "movement", "camera_smoothness", 0.01f, 0.2f));
        components.add(new GuiComponent.ToggleComponent("Natural Movement", "movement", "natural_movement"));
        components.add(new GuiComponent.SliderComponent("Jump Penalty", "movement", "jump_penalty", 1.2f, 8.0f));
    }
    
    private void loadPlayerComponents() {
        components.add(new GuiComponent.ToggleComponent("Freecam", "player", "freecam_enabled"));
        components.add(new GuiComponent.SliderComponent("Freecam Speed", "player", "freecam_speed", 1, 10));
        components.add(new GuiComponent.ToggleComponent("Inventory PIP", "player", "inventory_pip"));
        components.add(new GuiComponent.SliderComponent("PIP Position", "player", "pip_position", 0, 3));
        components.add(new GuiComponent.ColorComponent("PIP Border Color", "player", "pip_border_color"));
    }
    
    private void loadWorldComponents() {
        components.add(new GuiComponent.ToggleComponent("Mining Helper", "world", "mining_helper"));
        components.add(new GuiComponent.SliderComponent("Mining Speed", "world", "mining_speed", 1, 100));
        components.add(new GuiComponent.ToggleComponent("Auto Farm", "world", "auto_farm"));
        components.add(new GuiComponent.SliderComponent("Crop Type", "world", "crop_type", 0, 5));
        components.add(new GuiComponent.ToggleComponent("Foraging", "world", "foraging_enabled"));
        components.add(new GuiComponent.SliderComponent("Foraging Area", "world", "foraging_area", 0, 3));
        components.add(new GuiComponent.SliderComponent("Tree Scan Radius", "world", "tree_scan_radius", 20, 80));
        components.add(new GuiComponent.SliderComponent("Tree Break Delay", "world", "tree_break_delay", 100, 2000));
    }
    
    private void loadMiscComponents() {
        components.add(new GuiComponent.ToggleComponent("Emergency Stop", "misc", "emergency_stop_all"));
        components.add(new GuiComponent.ToggleComponent("Staff Check Response", "misc", "staff_check_response"));
        components.add(new GuiComponent.ToggleComponent("Manual Pause", "misc", "manual_pause"));
        components.add(new GuiComponent.ToggleComponent("Rift Autofarm", "misc", "rift_autofarm"));
        components.add(new GuiComponent.ColorComponent("Rift Area Color", "misc", "rift_area_color"));
        components.add(new GuiComponent.ColorComponent("Rift Zombie ESP", "misc", "rift_zombie_esp_color"));
        components.add(new GuiComponent.ColorComponent("Rift Player ESP", "misc", "rift_player_esp_color"));
    }
    
    @Override
    public void drawScreen(int mouseX, int mouseY, float partialTicks) {
        // Background
        drawRect(0, 0, width, height, BACKGROUND_COLOR);
        
        // Sidebar
        drawRect(0, 0, SIDEBAR_WIDTH, height, SIDEBAR_COLOR);
        
        // Sidebar categories
        int categoryY = PADDING;
        for (Category category : Category.values()) {
            boolean isSelected = category == selectedCategory;
            boolean isHovered = mouseX >= 0 && mouseX <= SIDEBAR_WIDTH && 
                              mouseY >= categoryY && mouseY <= categoryY + COMPONENT_HEIGHT;
            
            int bgColor = isSelected ? SELECTED_COLOR : (isHovered ? HOVER_COLOR : 0);
            if (bgColor != 0) {
                drawRect(0, categoryY, SIDEBAR_WIDTH, categoryY + COMPONENT_HEIGHT, bgColor);
            }
            
            int textColor = isSelected ? 0xFF000000 : TEXT_COLOR;
            drawString(fontRendererObj, category.name, PADDING, categoryY + 8, textColor);
            
            categoryY += COMPONENT_HEIGHT + COMPONENT_SPACING;
        }
        
        // Main content area
        int contentX = SIDEBAR_WIDTH + PADDING;
        int contentY = PADDING - scrollOffset;
        int contentWidth = width - SIDEBAR_WIDTH - PADDING * 2;
        
        // Category title
        String title = selectedCategory.name + " Settings";
        drawString(fontRendererObj, title, contentX, contentY, selectedCategory.color);
        contentY += 30;
        
        // Components
        for (GuiComponent component : components) {
            if (contentY + component.getHeight() > 0 && contentY < height) {
                component.draw(contentX, contentY, contentWidth, mouseX, mouseY);
            }
            contentY += component.getHeight() + COMPONENT_SPACING;
        }
        
        super.drawScreen(mouseX, mouseY, partialTicks);
    }
    
    @Override
    protected void mouseClicked(int mouseX, int mouseY, int mouseButton) throws IOException {
        // Check sidebar clicks
        if (mouseX <= SIDEBAR_WIDTH) {
            int categoryY = PADDING;
            for (Category category : Category.values()) {
                if (mouseY >= categoryY && mouseY <= categoryY + COMPONENT_HEIGHT) {
                    if (selectedCategory != category) {
                        selectedCategory = category;
                        loadComponents();
                        scrollOffset = 0;
                    }
                    return;
                }
                categoryY += COMPONENT_HEIGHT + COMPONENT_SPACING;
            }
        }
        
        // Check component clicks
        int contentX = SIDEBAR_WIDTH + PADDING;
        int contentY = PADDING + 30 - scrollOffset; // Account for title
        int contentWidth = width - SIDEBAR_WIDTH - PADDING * 2;
        
        for (GuiComponent component : components) {
            if (contentY + component.getHeight() > 0 && contentY < height) {
                if (component.mouseClicked(contentX, contentY, mouseX, mouseY, mouseButton)) {
                    return;
                }
            }
            contentY += component.getHeight() + COMPONENT_SPACING;
        }
        
        super.mouseClicked(mouseX, mouseY, mouseButton);
    }
    
    @Override
    protected void keyTyped(char typedChar, int keyCode) throws IOException {
        if (keyCode == Keyboard.KEY_ESCAPE) {
            mc.displayGuiScreen(null);
            return;
        }
        super.keyTyped(typedChar, keyCode);
    }
    
    @Override
    public void onGuiClosed() {
        CustomConfigManager.saveConfig();
        super.onGuiClosed();
    }
    
    @Override
    public boolean doesGuiPauseGame() {
        return false;
    }
}
