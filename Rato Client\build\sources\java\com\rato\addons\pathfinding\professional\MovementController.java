package com.rato.addons.pathfinding.professional;

import net.minecraft.block.Block;
import net.minecraft.client.Minecraft;
import net.minecraft.client.settings.KeyBinding;
import net.minecraft.init.Blocks;
import net.minecraft.util.BlockPos;
import net.minecraft.util.MathHelper;
import net.minecraft.util.Vec3;
import org.lwjgl.input.Keyboard;

import java.util.List;

/**
 * Controlador de movimento suave para o pathfinding profissional
 */
public class MovementController {
    
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Estado do movimento
    private List<PathNode> currentPath;
    private int currentWaypointIndex = 0;
    private boolean isMoving = false;
    
    // Configurações
    private float movementSpeed = 1.0f;
    private float rotationSmoothness = 0.7f;
    private static final double WAYPOINT_REACH_DISTANCE = 0.8;
    private static final float MAX_ROTATION_SPEED = 15.0f;
    
    // Detecção de stuck
    private Vec3 lastPosition;
    private long lastMovementTime;
    private int stuckCounter = 0;
    private static final int STUCK_THRESHOLD = 400; // 20 segundos a 20 TPS (extremamente menos sensível)

    // Controle de pulo
    private boolean isJumping = false;
    private boolean wasOnGround = true;
    private long lastJumpTime = 0;
    private static final long JUMP_COOLDOWN = 500; // 500ms entre pulos
    
    // Callbacks
    private Runnable onDestinationReached;
    private Runnable onMovementFailed;
    
    // Teclas de movimento
    private final KeyBinding[] movementKeys = {
        mc.gameSettings.keyBindForward,
        mc.gameSettings.keyBindBack,
        mc.gameSettings.keyBindLeft,
        mc.gameSettings.keyBindRight,
        mc.gameSettings.keyBindJump,
        mc.gameSettings.keyBindSneak
    };
    
    /**
     * Define o caminho a ser seguido
     */
    public void setPath(List<PathNode> path) {
        this.currentPath = path;
        this.currentWaypointIndex = 0;
        this.isMoving = true;
        this.lastPosition = mc.thePlayer != null ? mc.thePlayer.getPositionVector() : null;
        this.lastMovementTime = System.currentTimeMillis();
        this.stuckCounter = 0;
    }
    
    /**
     * Atualiza o caminho atual (para recálculos)
     */
    public void updatePath(List<PathNode> newPath) {
        if (newPath == null || newPath.isEmpty()) return;
        
        // Encontrar o waypoint mais próximo no novo caminho
        if (mc.thePlayer != null && currentPath != null) {
            Vec3 playerPos = mc.thePlayer.getPositionVector();
            int bestIndex = 0;
            double bestDistance = Double.MAX_VALUE;
            
            for (int i = 0; i < newPath.size(); i++) {
                double distance = newPath.get(i).distanceTo(playerPos);
                if (distance < bestDistance) {
                    bestDistance = distance;
                    bestIndex = i;
                }
            }
            
            this.currentPath = newPath;
            this.currentWaypointIndex = Math.max(0, bestIndex - 1); // Começar um pouco antes para suavidade
        } else {
            setPath(newPath);
        }
    }
    
    /**
     * Atualização principal do movimento (chamada a cada tick)
     */
    public void update() {
        if (!isMoving || mc.thePlayer == null || currentPath == null || currentPath.isEmpty()) {
            return;
        }
        
        // Verificar se chegou ao final do caminho
        if (currentWaypointIndex >= currentPath.size()) {
            completeMovement();
            return;
        }
        
        // Detectar se está preso
        if (detectStuck()) {
            handleStuckDetection();
            return;
        }
        
        // Executar movimento para o waypoint atual
        executeMovementToWaypoint();
    }
    
    /**
     * Executa movimento em direção ao waypoint atual com detecção inteligente
     */
    private void executeMovementToWaypoint() {
        PathNode targetWaypoint = currentPath.get(currentWaypointIndex);
        Vec3 playerPos = mc.thePlayer.getPositionVector();

        // Verificar se chegou ao waypoint atual
        double distanceToWaypoint = playerPos.distanceTo(targetWaypoint.position);
        if (distanceToWaypoint <= WAYPOINT_REACH_DISTANCE) {
            currentWaypointIndex++;

            if (currentWaypointIndex >= currentPath.size()) {
                completeMovement();
                return;
            }

            targetWaypoint = currentPath.get(currentWaypointIndex);
            distanceToWaypoint = playerPos.distanceTo(targetWaypoint.position);
        }

        // Calcular direção e rotação necessária
        Vec3 direction = targetWaypoint.position.subtract(playerPos).normalize();
        float targetYaw = (float) Math.toDegrees(Math.atan2(-direction.xCoord, direction.zCoord));

        // Aplicar rotação suave apenas se necessário
        float currentYaw = mc.thePlayer.rotationYaw;
        float yawDiff = Math.abs(MathHelper.wrapAngleTo180_float(targetYaw - currentYaw));

        if (yawDiff > 10.0f) {
            applySmoothRotation(targetYaw);
            return; // Esperar rotação completar antes de mover
        }

        // Detectar tipo de movimento necessário baseado no ambiente
        PathNode.MovementType detectedMovement = detectRequiredMovement(playerPos, targetWaypoint.position);

        // Executar movimento baseado na detecção
        executeMovementType(detectedMovement, direction, distanceToWaypoint);
    }

    /**
     * Detecta o tipo de movimento necessário baseado no ambiente atual
     */
    private PathNode.MovementType detectRequiredMovement(Vec3 from, Vec3 to) {
        double heightDiff = to.yCoord - from.yCoord;
        double horizontalDistance = Math.sqrt(
            Math.pow(to.xCoord - from.xCoord, 2) +
            Math.pow(to.zCoord - from.zCoord, 2)
        );

        // Verificar se há obstáculo no caminho que requer pulo
        if (hasObstacleInPath(from, to)) {
            return PathNode.MovementType.JUMP;
        }

        // Movimento vertical
        if (heightDiff > 0.5) {
            return PathNode.MovementType.JUMP;
        } else if (heightDiff < -0.5) {
            return PathNode.MovementType.FALL;
        }

        // Movimento horizontal
        if (horizontalDistance > 1.5) {
            return PathNode.MovementType.DIAGONAL;
        } else {
            return PathNode.MovementType.WALK;
        }
    }

    /**
     * Verifica se há obstáculo no caminho que requer pulo
     */
    private boolean hasObstacleInPath(Vec3 from, Vec3 to) {
        if (mc.theWorld == null) return false;

        // Verificar bloco na direção do movimento
        Vec3 direction = to.subtract(from).normalize();
        Vec3 checkPos = from.addVector(direction.xCoord, 0, direction.zCoord);

        BlockPos blockPos = new BlockPos(checkPos);
        Block block = mc.theWorld.getBlockState(blockPos).getBlock();

        // Se há bloco sólido no caminho, precisa pular
        if (block.getMaterial().isSolid() && block != Blocks.air) {
            // Verificar se pode pular por cima
            Block aboveBlock = mc.theWorld.getBlockState(blockPos.up()).getBlock();
            return !aboveBlock.getMaterial().isSolid();
        }

        return false;
    }
    
    /**
     * Aplica rotação suave em direção ao alvo (corrigido para evitar giros excessivos)
     */
    private void applySmoothRotation(float targetYaw) {
        float currentYaw = mc.thePlayer.rotationYaw;
        float yawDifference = MathHelper.wrapAngleTo180_float(targetYaw - currentYaw);

        // Só rotacionar se a diferença for significativa
        if (Math.abs(yawDifference) > 5.0f) {
            // Calcular velocidade de rotação mais conservadora
            float rotationSpeed = Math.min(Math.abs(yawDifference) * 0.3f, MAX_ROTATION_SPEED * 0.7f);
            rotationSpeed *= rotationSmoothness;

            // Aplicar rotação gradual
            float newYaw = currentYaw + Math.signum(yawDifference) * rotationSpeed;
            mc.thePlayer.rotationYaw = newYaw;
        }

        // Ajustar pitch apenas se necessário (para pulos/quedas)
        if (currentPath != null && currentWaypointIndex < currentPath.size()) {
            PathNode target = currentPath.get(currentWaypointIndex);
            Vec3 playerPos = mc.thePlayer.getPositionVector();

            // Só ajustar pitch para movimentos verticais significativos
            double heightDiff = target.position.yCoord - playerPos.yCoord;
            if (Math.abs(heightDiff) > 0.5) {
                double horizontalDistance = Math.sqrt(
                    Math.pow(target.position.xCoord - playerPos.xCoord, 2) +
                    Math.pow(target.position.zCoord - playerPos.zCoord, 2)
                );

                if (horizontalDistance > 0.1) {
                    float targetPitch = (float) Math.toDegrees(Math.atan2(heightDiff, horizontalDistance));
                    targetPitch = MathHelper.clamp_float(targetPitch, -30, 30); // Limitar pitch

                    float currentPitch = mc.thePlayer.rotationPitch;
                    float pitchDiff = targetPitch - currentPitch;

                    if (Math.abs(pitchDiff) > 3.0f) {
                        float pitchStep = Math.signum(pitchDiff) * Math.min(Math.abs(pitchDiff) * 0.2f, 3.0f);
                        mc.thePlayer.rotationPitch = MathHelper.clamp_float(currentPitch + pitchStep, -30, 30);
                    }
                }
            }
        }
    }
    
    /**
     * Executa o tipo de movimento específico
     */
    private void executeMovementType(PathNode.MovementType movementType, Vec3 direction, double distance) {
        // Liberar todas as teclas primeiro
        releaseAllKeys();

        // VERIFICAÇÃO ESPECIAL: Se é movimento de queda, garantir que não pule
        if (movementType == PathNode.MovementType.FALL) {
            setKeyPressed(mc.gameSettings.keyBindJump, false);
        }

        switch (movementType) {
            case WALK:
            case DIAGONAL:
                executeWalkMovement(direction);
                break;

            case JUMP:
                executeJumpMovement(direction);
                break;

            case FALL:
                executeFallMovement(direction);
                // Garantir novamente que não pule
                setKeyPressed(mc.gameSettings.keyBindJump, false);
                break;
                
            case CLIMB:
                executeClimbMovement(direction);
                break;
                
            case SWIM:
                executeSwimMovement(direction);
                break;
                
            case SPRINT:
                executeSprintMovement(direction);
                break;
                
            case SNEAK:
                executeSneakMovement(direction);
                break;
                
            default:
                executeWalkMovement(direction);
                break;
        }
    }
    
    /**
     * Executa movimento de caminhada normal (corrigido)
     */
    private void executeWalkMovement(Vec3 direction) {
        // Verificar alinhamento antes de mover
        double directionAngle = Math.atan2(-direction.xCoord, direction.zCoord);
        double playerYaw = Math.toRadians(mc.thePlayer.rotationYaw);
        double angleDiff = Math.abs(MathHelper.wrapAngleTo180_double(Math.toDegrees(directionAngle - playerYaw)));

        // Só mover se estiver bem alinhado
        if (angleDiff < 15.0) {
            setKeyPressed(mc.gameSettings.keyBindForward, true);
        } else {
            // Não mover até estar alinhado, apenas rotacionar
            return;
        }
    }
    
    /**
     * Executa movimento com pulo (melhorado)
     */
    private void executeJumpMovement(Vec3 direction) {
        // Controlar pulo de forma inteligente
        handleJumpControl();

        // Mover para frente apenas se bem alinhado
        executeWalkMovement(direction);
    }
    
    /**
     * Executa movimento de queda
     */
    private void executeFallMovement(Vec3 direction) {
        executeWalkMovement(direction);
        // NUNCA pressionar pulo durante movimento de queda
        setKeyPressed(mc.gameSettings.keyBindJump, false);
    }
    
    /**
     * Executa movimento de subida
     */
    private void executeClimbMovement(Vec3 direction) {
        executeWalkMovement(direction);
        setKeyPressed(mc.gameSettings.keyBindJump, true);
    }
    
    /**
     * Executa movimento de natação
     */
    private void executeSwimMovement(Vec3 direction) {
        executeWalkMovement(direction);
        setKeyPressed(mc.gameSettings.keyBindJump, true); // Nadar para cima
    }
    
    /**
     * Executa movimento de corrida
     */
    private void executeSprintMovement(Vec3 direction) {
        executeWalkMovement(direction);
        mc.thePlayer.setSprinting(true);
    }
    
    /**
     * Executa movimento agachado
     */
    private void executeSneakMovement(Vec3 direction) {
        executeWalkMovement(direction);
        setKeyPressed(mc.gameSettings.keyBindSneak, true);
    }
    
    /**
     * Define estado de uma tecla
     */
    private void setKeyPressed(KeyBinding key, boolean pressed) {
        KeyBinding.setKeyBindState(key.getKeyCode(), pressed);
    }
    
    /**
     * Controla o pulo de forma inteligente
     */
    private void handleJumpControl() {
        boolean currentlyOnGround = mc.thePlayer.onGround;
        long currentTime = System.currentTimeMillis();

        // Detectar quando o player sai do chão (completou o pulo)
        if (isJumping && wasOnGround && !currentlyOnGround) {
            // Player saiu do chão, liberar tecla de pulo
            setKeyPressed(mc.gameSettings.keyBindJump, false);
            isJumping = false;
        }

        // Verificar se deve iniciar um novo pulo
        if (!isJumping && currentlyOnGround && (currentTime - lastJumpTime) > JUMP_COOLDOWN) {
            setKeyPressed(mc.gameSettings.keyBindJump, true);
            isJumping = true;
            lastJumpTime = currentTime;
        }

        // Atualizar estado anterior
        wasOnGround = currentlyOnGround;
    }

    /**
     * Libera todas as teclas de movimento
     */
    private void releaseAllKeys() {
        for (KeyBinding key : movementKeys) {
            setKeyPressed(key, false);
        }
        mc.thePlayer.setSprinting(false);

        // Resetar estado de pulo
        isJumping = false;
        wasOnGround = true;
    }
    
    /**
     * Detecta se o player está preso
     */
    private boolean detectStuck() {
        if (mc.thePlayer == null) return false;

        Vec3 currentPos = mc.thePlayer.getPositionVector();
        long currentTime = System.currentTimeMillis();

        if (lastPosition != null) {
            double distanceMoved = currentPos.distanceTo(lastPosition);

            // Movimento muito pequeno por muito tempo indica stuck
            if (distanceMoved < 0.005) { // Extremamente tolerante
                stuckCounter++;
            } else if (distanceMoved > 0.02) { // Extremamente tolerante
                // Movimento significativo - resetar contador
                stuckCounter = 0;
                lastMovementTime = currentTime;
            } else {
                // Movimento pequeno mas presente - reduzir contador gradualmente
                stuckCounter = Math.max(0, stuckCounter - 5); // Reduz muito rapidamente
            }

            // Verificar se está preso há muito tempo
            if (currentTime - lastMovementTime > 8000) { // 8 segundos sem movimento significativo
                return true;
            }
        }

        lastPosition = currentPos;

        return stuckCounter >= STUCK_THRESHOLD;
    }
    
    /**
     * Lida com detecção de stuck
     */
    private void handleStuckDetection() {
        releaseAllKeys();

        // Estratégias para sair do stuck
        Vec3 playerPos = mc.thePlayer.getPositionVector();

        // Verificar se está tentando descer (não pular se estiver descending)
        boolean isDescending = false;
        if (currentPath != null && currentWaypointIndex < currentPath.size()) {
            PathNode currentWaypoint = currentPath.get(currentWaypointIndex);
            double yDiff = currentWaypoint.position.yCoord - playerPos.yCoord;
            isDescending = yDiff < -0.5; // Se o waypoint está mais de 0.5 blocos abaixo
        }

        // Estratégia 1: Mover para trás brevemente
        if (stuckCounter > STUCK_THRESHOLD / 3) {
            setKeyPressed(mc.gameSettings.keyBindBack, true);
        }

        // Estratégia 2: Mover lateralmente
        if (stuckCounter > STUCK_THRESHOLD / 2) {
            setKeyPressed(mc.gameSettings.keyBindLeft, true);
        }

        // Estratégia 3: Pular apenas se muito preso E não estiver descendo
        if (stuckCounter > STUCK_THRESHOLD * 3 / 4 && !isDescending) {
            setKeyPressed(mc.gameSettings.keyBindJump, true);
        }

        // Estratégia 3: Movimento lateral aleatório
        if (stuckCounter > STUCK_THRESHOLD * 0.75) {
            if (System.currentTimeMillis() % 2 == 0) {
                setKeyPressed(mc.gameSettings.keyBindLeft, true);
            } else {
                setKeyPressed(mc.gameSettings.keyBindRight, true);
            }
        }

        // Estratégia 4: Rotação aleatória para encontrar saída
        if (stuckCounter > STUCK_THRESHOLD * 0.9) {
            float randomYaw = mc.thePlayer.rotationYaw + (float) (Math.random() * 90 - 45);
            mc.thePlayer.rotationYaw = randomYaw;
        }

        // Se ainda está preso após todas as tentativas, notificar para recálculo
        if (stuckCounter >= STUCK_THRESHOLD) {
            if (onMovementFailed != null) {
                onMovementFailed.run();
            }
            stuckCounter = 0;
        }
    }
    
    /**
     * Completa o movimento (chegou ao destino)
     */
    private void completeMovement() {
        stop();
        
        if (onDestinationReached != null) {
            onDestinationReached.run();
        }
    }
    
    /**
     * Para o movimento atual
     */
    public void stop() {
        isMoving = false;
        releaseAllKeys();
        currentPath = null;
        currentWaypointIndex = 0;
    }
    
    // Getters e Setters
    public boolean isMoving() { return isMoving; }
    public void setMovementSpeed(float speed) { this.movementSpeed = speed; }
    public void setRotationSmoothness(float smoothness) { this.rotationSmoothness = smoothness; }
    public void setOnDestinationReached(Runnable callback) { this.onDestinationReached = callback; }
    public void setOnMovementFailed(Runnable callback) { this.onMovementFailed = callback; }
    
    /**
     * Retorna progresso atual do movimento
     */
    public float getProgress() {
        if (currentPath == null || currentPath.isEmpty()) return 0.0f;
        return (float) currentWaypointIndex / currentPath.size();
    }
    
    /**
     * Retorna waypoint atual
     */
    public PathNode getCurrentWaypoint() {
        if (currentPath == null || currentWaypointIndex >= currentPath.size()) return null;
        return currentPath.get(currentWaypointIndex);
    }
}
