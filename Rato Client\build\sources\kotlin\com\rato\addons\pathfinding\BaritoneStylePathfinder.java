package com.rato.addons.pathfinding;

import com.rato.addons.pathfinding.movement.MovementType;
import com.rato.addons.util.Logger;
import com.rato.addons.config.RatoAddonsConfigSimple;
import net.minecraft.block.Block;
import net.minecraft.client.Minecraft;
import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;

import java.util.*;

/**
 * Sistema de pathfinding baseado no Baritone
 * Usa movimentos modulares e otimizações avançadas
 */
public class BaritoneStylePathfinder {

    private final Minecraft mc = Minecraft.getMinecraft();

    // Configurações otimizadas para velocidade e precisão
    private static final int MAX_SEARCH_NODES = 20000; // Aumentado para grandes distâncias
    private static final long PRIMARY_TIMEOUT_MS = 5000; // 5 segundos para grandes distâncias
    private static final long FAILURE_TIMEOUT_MS = 12000; // 12 segundos máximo
    private static final double MINIMUM_IMPROVEMENT = 0.01; // Balanceado
    private static final int TIME_CHECK_INTERVAL = 64; // Verificar tempo a cada 64 nodes
    private static final double DIRECT_PATH_THRESHOLD = 30.0; // Caminho direto para distâncias curtas
    private static final double LONG_DISTANCE_THRESHOLD = 100.0; // Considerar longa distância
    private static final double SEGMENTED_PATH_THRESHOLD = 200.0; // Usar pathfinding segmentado

    // Todos os tipos de movimento disponíveis (ORDEM PRIORIZADA)
    private static final MovementType[] ALL_MOVEMENTS = {
            MovementType.DIAGONAL, // PRIORIDADE MÁXIMA: Movimentos diagonais
            MovementType.ASCEND, // Pulos diagonais
            MovementType.DESCEND, // Descidas diagonais
            MovementType.TRAVERSE, // ÚLTIMA OPÇÃO: Movimentos cardinais
            MovementType.FALL // Emergência: Quedas
    };

    /**
     * Calcula caminho usando algoritmo A* otimizado com movimentos modulares
     */
    public PathfindingResult calculatePath(Vec3 start, Vec3 goal) {
        long startTime = System.currentTimeMillis();

        BlockPos startPos = new BlockPos(start);
        BlockPos goalPos = new BlockPos(goal);

        double distance = start.distanceTo(goal);

        // Estratégia baseada na distância
        if (distance <= DIRECT_PATH_THRESHOLD) {
            // Tentar caminho direto para distâncias curtas
            PathfindingResult directResult = tryDirectPath(start, goal, startTime);
            if (directResult.success) {
                return directResult;
            }
        } else if (distance >= SEGMENTED_PATH_THRESHOLD) {
            // Usar pathfinding segmentado para distâncias muito grandes
            return calculateSegmentedPath(start, goal, startTime);
        }

        // Estruturas de dados do A* otimizadas
        PriorityQueue<BaritoneNode> openSet = new PriorityQueue<>(Comparator.comparingDouble(n -> n.fCost));
        Set<BlockPos> closedSet = new HashSet<>(MAX_SEARCH_NODES / 4); // Pre-size para performance
        Map<BlockPos, BaritoneNode> allNodes = new HashMap<>(MAX_SEARCH_NODES / 2);

        // Nó inicial
        BaritoneNode startNode = new BaritoneNode(startPos, 0, calculateHeuristic(startPos, goalPos), null,
                MovementType.TRAVERSE);
        openSet.add(startNode);
        allNodes.put(startPos, startNode);

        int nodesExplored = 0;
        int movementsConsidered = 0;
        boolean failing = true;

        long primaryTimeoutTime = startTime + PRIMARY_TIMEOUT_MS;
        long failureTimeoutTime = startTime + FAILURE_TIMEOUT_MS;

        BaritoneNode bestNode = startNode;
        double bestHeuristic = startNode.hCost;

        while (!openSet.isEmpty() && nodesExplored < MAX_SEARCH_NODES) {
            // Verificar timeout periodicamente
            if ((nodesExplored & (TIME_CHECK_INTERVAL - 1)) == 0) {
                long now = System.currentTimeMillis();
                if (now >= failureTimeoutTime || (!failing && now >= primaryTimeoutTime)) {
                    break;
                }
            }

            BaritoneNode current = openSet.poll();
            closedSet.add(current.position);
            nodesExplored++;

            // Verificar se chegou ao objetivo
            if (current.position.equals(goalPos) || isCloseEnough(current.position, goalPos)) {
                return PathfindingResult.success(reconstructPath(current), nodesExplored,
                        System.currentTimeMillis() - startTime);
            }

            // Atualizar melhor nó
            double currentHeuristic = current.hCost + current.gCost / 10.0;
            if (currentHeuristic < bestHeuristic) {
                bestHeuristic = currentHeuristic;
                bestNode = current;
                if (failing && getDistanceFromStart(current, startNode) > 16) {
                    failing = false;
                }
            }

            // Usar destinos inteligentes priorizando direção do objetivo
            List<BlockPos> possibleDestinations = generateSmartDestinations(current.position, goalPos);

            for (BlockPos destination : possibleDestinations) {
                if (closedSet.contains(destination))
                    continue;

                movementsConsidered++;

                // Determinar tipo de movimento para este destino
                MovementType movementType = determineMovementType(current.position, destination);

                // Calcular custo do movimento
                MovementType.MovementResult movementResult = movementType.calculateCost(current.position, destination,
                        mc);
                if (!movementResult.isValid)
                    continue;

                // Aplicar modificadores de custo baseados no terreno
                double baseCost = movementResult.cost;
                double terrainModifier = calculateTerrainModifier(destination);

                // PENALIZAR mudanças de direção para favorecer linha reta
                double directionPenalty = calculateDirectionPenalty(current, destination);

                double finalCost = baseCost * terrainModifier * directionPenalty;

                double tentativeGCost = current.gCost + finalCost;

                BaritoneNode existingNode = allNodes.get(destination);
                if (existingNode != null && tentativeGCost >= existingNode.gCost + MINIMUM_IMPROVEMENT) {
                    continue; // Caminho pior ou melhoria insuficiente
                }

                // Criar ou atualizar nó
                double hCost = calculateHeuristic(destination, goalPos);
                BaritoneNode neighborNode = new BaritoneNode(destination, tentativeGCost, hCost, current, movementType);

                if (existingNode != null) {
                    openSet.remove(existingNode);
                }

                allNodes.put(destination, neighborNode);
                openSet.add(neighborNode);
            }
        }

        // Se não encontrou caminho completo, retornar melhor caminho parcial
        if (bestNode != startNode) {
            return PathfindingResult.partial(reconstructPath(bestNode), nodesExplored,
                    System.currentTimeMillis() - startTime);
        }

        return PathfindingResult.failure("No path found", nodesExplored,
                System.currentTimeMillis() - startTime);
    }

    /**
     * Pathfinding segmentado para grandes distâncias (baseado no Baritone)
     */
    private PathfindingResult calculateSegmentedPath(Vec3 start, Vec3 goal, long startTime) {
        double totalDistance = start.distanceTo(goal);
        double segmentLength = 80.0; // Segmentos de 80 blocos
        int numSegments = (int) Math.ceil(totalDistance / segmentLength);

        List<BaritoneNode> completePath = new ArrayList<>();
        Vec3 currentStart = start;
        int totalNodesExplored = 0;

        for (int i = 0; i < numSegments; i++) {
            // Calcular ponto intermediário
            double t = Math.min(1.0, (double) (i + 1) / numSegments);
            Vec3 segmentGoal = interpolateVec3(start, goal, t);

            // Ajustar para terreno válido
            BlockPos segmentGoalPos = findNearestValidPosition(new BlockPos(segmentGoal));
            if (segmentGoalPos == null) {
                segmentGoalPos = new BlockPos(segmentGoal);
            }
            segmentGoal = new Vec3(segmentGoalPos.getX() + 0.5, segmentGoalPos.getY(), segmentGoalPos.getZ() + 0.5);

            // Calcular segmento
            PathfindingResult segmentResult = calculatePathInternal(currentStart, segmentGoal, startTime,
                    MAX_SEARCH_NODES / 2);

            if (segmentResult.success && !segmentResult.path.isEmpty()) {
                // Remover primeiro node se não for o primeiro segmento
                List<BaritoneNode> segmentPath = segmentResult.path;
                if (!completePath.isEmpty() && !segmentPath.isEmpty()) {
                    segmentPath.remove(0);
                }
                completePath.addAll(segmentPath);
                totalNodesExplored += segmentResult.nodesExplored;

                // Atualizar ponto de início para próximo segmento
                currentStart = segmentGoal;
            } else {
                // Se um segmento falhar, tentar pathfinding direto como fallback
                return calculatePathInternal(start, goal, startTime, MAX_SEARCH_NODES);
            }
        }

        if (!completePath.isEmpty()) {
            // Otimizar caminho final
            List<BaritoneNode> optimizedPath = optimizePath(completePath);
            int originalSize = completePath.size();
            int optimizedSize = optimizedPath.size();

            if (optimizedSize < originalSize) {
                Logger.sendMessage("§a[Pathfinding] Optimized " + (originalSize - optimizedSize)
                        + " previously unloaded path nodes!");
            }

            return PathfindingResult.success(optimizedPath, totalNodesExplored,
                    System.currentTimeMillis() - startTime);
        }

        return PathfindingResult.failure("Segmented pathfinding failed", totalNodesExplored,
                System.currentTimeMillis() - startTime);
    }

    /**
     * Pathfinding interno com configurações específicas
     */
    private PathfindingResult calculatePathInternal(Vec3 start, Vec3 goal, long startTime, int maxNodes) {
        BlockPos startPos = new BlockPos(start);
        BlockPos goalPos = new BlockPos(goal);

        // Usar algoritmo A* padrão com limite de nodes específico
        PriorityQueue<BaritoneNode> openSet = new PriorityQueue<>(Comparator.comparingDouble(n -> n.fCost));
        Set<BlockPos> closedSet = new HashSet<>(maxNodes / 4);
        Map<BlockPos, BaritoneNode> allNodes = new HashMap<>(maxNodes / 2);

        BaritoneNode startNode = new BaritoneNode(startPos, 0, calculateHeuristic(startPos, goalPos), null,
                MovementType.TRAVERSE);
        openSet.add(startNode);
        allNodes.put(startPos, startNode);

        int nodesExplored = 0;
        BaritoneNode bestNode = startNode;
        double bestHeuristic = startNode.hCost;

        while (!openSet.isEmpty() && nodesExplored < maxNodes) {
            BaritoneNode current = openSet.poll();
            closedSet.add(current.position);
            nodesExplored++;

            // Verificar se chegou ao objetivo
            if (current.position.equals(goalPos) || isCloseEnough(current.position, goalPos)) {
                return PathfindingResult.success(reconstructPath(current), nodesExplored,
                        System.currentTimeMillis() - startTime);
            }

            // Atualizar melhor nó
            double currentHeuristic = current.hCost + current.gCost / 10.0;
            if (currentHeuristic < bestHeuristic) {
                bestHeuristic = currentHeuristic;
                bestNode = current;
            }

            // Explorar destinos
            List<BlockPos> possibleDestinations = generateSmartDestinations(current.position, goalPos);

            for (BlockPos destination : possibleDestinations) {
                if (closedSet.contains(destination))
                    continue;

                MovementType movementType = determineMovementType(current.position, destination);
                MovementType.MovementResult movementResult = movementType.calculateCost(current.position, destination,
                        mc);
                if (!movementResult.isValid)
                    continue;

                // Aplicar modificadores de custo baseados no terreno
                double baseCost = movementResult.cost;
                double terrainModifier = calculateTerrainModifier(destination);

                // PENALIZAR mudanças de direção para favorecer linha reta
                double directionPenalty = calculateDirectionPenalty(current, destination);

                double finalCost = baseCost * terrainModifier * directionPenalty;

                double tentativeGCost = current.gCost + finalCost;

                BaritoneNode existingNode = allNodes.get(destination);
                if (existingNode != null && tentativeGCost >= existingNode.gCost + MINIMUM_IMPROVEMENT) {
                    continue;
                }

                double hCost = calculateHeuristic(destination, goalPos);
                BaritoneNode neighborNode = new BaritoneNode(destination, tentativeGCost, hCost, current, movementType);

                if (existingNode != null) {
                    openSet.remove(existingNode);
                }

                allNodes.put(destination, neighborNode);
                openSet.add(neighborNode);
            }
        }

        // Retornar melhor caminho parcial se não encontrou completo
        if (bestNode != startNode) {
            return PathfindingResult.partial(reconstructPath(bestNode), nodesExplored,
                    System.currentTimeMillis() - startTime);
        }

        return PathfindingResult.failure("No path found in internal calculation", nodesExplored,
                System.currentTimeMillis() - startTime);
    }

    /**
     * Interpola entre dois Vec3
     */
    private Vec3 interpolateVec3(Vec3 start, Vec3 end, double t) {
        return new Vec3(
                start.xCoord + (end.xCoord - start.xCoord) * t,
                start.yCoord + (end.yCoord - start.yCoord) * t,
                start.zCoord + (end.zCoord - start.zCoord) * t);
    }

    /**
     * Encontra posição válida mais próxima
     */
    private BlockPos findNearestValidPosition(BlockPos pos) {
        // Verificar posição original
        if (MovementType.canWalkOn(pos.down(), mc) && canMoveThrough(pos, 2)) {
            return pos;
        }

        // Procurar em área 3x3x3
        for (int y = -1; y <= 1; y++) {
            for (int x = -1; x <= 1; x++) {
                for (int z = -1; z <= 1; z++) {
                    BlockPos candidate = pos.add(x, y, z);
                    if (MovementType.canWalkOn(candidate.down(), mc) && canMoveThrough(candidate, 2)) {
                        return candidate;
                    }
                }
            }
        }

        return null;
    }

    /**
     * Gera destinos possíveis para um tipo de movimento (otimizado para velocidade)
     */
    private List<BlockPos> generateDestinations(BlockPos from, MovementType movementType) {
        List<BlockPos> destinations = new ArrayList<>();

        switch (movementType) {
            case DIAGONAL:
                // PRIORIDADE MÁXIMA: Movimentos diagonais (EXTREMAMENTE preferidos)
                destinations.add(from.add(1, 0, 1)); // Nordeste
                destinations.add(from.add(1, 0, -1)); // Sudeste
                destinations.add(from.add(-1, 0, 1)); // Noroeste
                destinations.add(from.add(-1, 0, -1)); // Sudoeste
                break;

            case TRAVERSE:
                // PENALIDADE: Movimentos cardinais (evitar quando possível)
                destinations.add(from.add(1, 0, 0));
                destinations.add(from.add(-1, 0, 0));
                destinations.add(from.add(0, 0, 1));
                destinations.add(from.add(0, 0, -1));
                break;

            case ASCEND:
                // Pulos otimizados - priorizar movimento horizontal + vertical
                destinations.add(from.add(1, 1, 0));
                destinations.add(from.add(-1, 1, 0));
                destinations.add(from.add(0, 1, 1));
                destinations.add(from.add(0, 1, -1));
                // Pulo vertical apenas se necessário
                destinations.add(from.add(0, 1, 0));
                break;

            case DESCEND:
                // Descidas controladas
                destinations.add(from.add(1, -1, 0));
                destinations.add(from.add(-1, -1, 0));
                destinations.add(from.add(0, -1, 1));
                destinations.add(from.add(0, -1, -1));
                destinations.add(from.add(0, -1, 0));
                break;

            case FALL:
                // Quedas otimizadas - limitar distância para segurança
                for (int fallDistance = 2; fallDistance <= 3; fallDistance++) { // Reduzido para 3
                    destinations.add(from.add(0, -fallDistance, 0));
                    // Apenas quedas com movimento horizontal se seguro
                    if (fallDistance <= 2) {
                        destinations.add(from.add(1, -fallDistance, 0));
                        destinations.add(from.add(-1, -fallDistance, 0));
                        destinations.add(from.add(0, -fallDistance, 1));
                        destinations.add(from.add(0, -fallDistance, -1));
                    }
                }
                break;
        }

        return destinations;
    }

    /**
     * Gera destinos inteligentes priorizando direção do objetivo e caminhos
     * naturais
     */
    private List<BlockPos> generateSmartDestinations(BlockPos from, BlockPos goal) {
        List<BlockPos> destinations = new ArrayList<>();

        // Calcular direção para o objetivo
        int deltaX = goal.getX() - from.getX();
        int deltaY = goal.getY() - from.getY();
        int deltaZ = goal.getZ() - from.getZ();

        // Normalizar direções
        int dirX = Integer.compare(deltaX, 0);
        int dirY = Integer.compare(deltaY, 0);
        int dirZ = Integer.compare(deltaZ, 0);

        // PRIORIDADE 1: Movimento diagonal natural direto para o objetivo (PREFERIDO)
        if (dirX != 0 && dirZ != 0) {
            // Diagonal pura (movimento mais natural)
            destinations.add(from.add(dirX, 0, dirZ));

            // Diagonal com movimento vertical gradual (movimento 3D natural)
            if (dirY != 0) {
                destinations.add(from.add(dirX, dirY, dirZ));
            }
        }

        // PRIORIDADE 2: Movimentos em linha reta para o objetivo
        if (dirX != 0) {
            destinations.add(from.add(dirX, 0, 0));
            // Combinar com movimento vertical gradual
            if (dirY != 0) {
                destinations.add(from.add(dirX, dirY, 0));
            }
        }
        if (dirZ != 0) {
            destinations.add(from.add(0, 0, dirZ));
            // Combinar com movimento vertical gradual
            if (dirY != 0) {
                destinations.add(from.add(0, dirY, dirZ));
            }
        }

        // PRIORIDADE 3: Movimento vertical puro (menos natural)
        if (dirY != 0) {
            destinations.add(from.add(0, dirY, 0));
        }

        // PRIORIDADE 4: Movimentos diagonais alternativos (para contornar obstáculos)
        // Só adicionar se não foram adicionados como movimento direto
        if (dirX == 0 || dirZ == 0) {
            // Adicionar diagonais para contornar obstáculos
            destinations.add(from.add(1, 0, 1));
            destinations.add(from.add(-1, 0, 1));
            destinations.add(from.add(1, 0, -1));
            destinations.add(from.add(-1, 0, -1));
        }

        // PRIORIDADE 5: Movimentos laterais para contornar obstáculos (menos
        // preferidos)
        if (dirX != 0) {
            destinations.add(from.add(dirX, 0, 1));
            destinations.add(from.add(dirX, 0, -1));
        }
        if (dirZ != 0) {
            destinations.add(from.add(1, 0, dirZ));
            destinations.add(from.add(-1, 0, dirZ));
        }

        // BACKUP: Adicionar movimentos padrão como último recurso
        for (MovementType type : ALL_MOVEMENTS) {
            List<BlockPos> backupDestinations = generateDestinations(from, type);
            // Só adicionar se não foram adicionados antes
            for (BlockPos backup : backupDestinations) {
                if (!destinations.contains(backup)) {
                    destinations.add(backup);
                }
            }
        }

        // Filtrar e priorizar destinos baseado no terreno
        return filterAndPrioritizeDestinations(destinations, from, goal);
    }

    /**
     * Filtra e prioriza destinos baseado no tipo de terreno
     */
    private List<BlockPos> filterAndPrioritizeDestinations(List<BlockPos> destinations, BlockPos from, BlockPos goal) {
        List<BlockPos> prioritized = new ArrayList<>();
        List<BlockPos> normal = new ArrayList<>();
        List<BlockPos> backup = new ArrayList<>();

        for (BlockPos dest : destinations) {
            if (isPreferredTerrain(dest)) {
                prioritized.add(dest); // Terreno preferido (estradas, caminhos)
            } else if (isNormalTerrain(dest)) {
                normal.add(dest); // Terreno normal
            } else {
                backup.add(dest); // Terreno difícil
            }
        }

        // Retornar em ordem de prioridade
        List<BlockPos> result = new ArrayList<>();
        result.addAll(prioritized);
        result.addAll(normal);
        result.addAll(backup);

        return result;
    }

    /**
     * Verifica se é terreno preferido (estradas, caminhos)
     */
    private boolean isPreferredTerrain(BlockPos pos) {
        try {
            Block blockBelow = mc.theWorld.getBlockState(pos.down()).getBlock();

            // Blocos de estrada/caminho preferidos
            return blockBelow == net.minecraft.init.Blocks.stone ||
                    blockBelow == net.minecraft.init.Blocks.cobblestone ||
                    blockBelow == net.minecraft.init.Blocks.stone_brick_stairs ||
                    blockBelow == net.minecraft.init.Blocks.brick_block ||
                    blockBelow == net.minecraft.init.Blocks.sandstone ||
                    blockBelow == net.minecraft.init.Blocks.planks ||
                    blockBelow == net.minecraft.init.Blocks.log ||
                    blockBelow == net.minecraft.init.Blocks.log2;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Verifica se é terreno normal
     */
    private boolean isNormalTerrain(BlockPos pos) {
        try {
            Block blockBelow = mc.theWorld.getBlockState(pos.down()).getBlock();

            // Terreno normal
            return blockBelow == net.minecraft.init.Blocks.grass ||
                    blockBelow == net.minecraft.init.Blocks.dirt ||
                    blockBelow == net.minecraft.init.Blocks.sand ||
                    blockBelow == net.minecraft.init.Blocks.gravel;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Verifica se pode mover através de uma posição com altura específica
     */
    private boolean canMoveThrough(BlockPos pos, int height) {
        for (int i = 0; i < height; i++) {
            BlockPos checkPos = pos.up(i);
            if (!MovementType.canWalkThrough(checkPos, mc)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Tenta caminho direto para distâncias curtas (otimização)
     */
    private PathfindingResult tryDirectPath(Vec3 start, Vec3 goal, long startTime) {
        BlockPos startPos = new BlockPos(start);
        BlockPos goalPos = new BlockPos(goal);

        // Verificar se há linha de visão direta
        if (hasDirectLineOfSight(startPos, goalPos)) {
            // Criar caminho direto simples
            List<BaritoneNode> directPath = createDirectPath(startPos, goalPos);
            if (directPath != null && !directPath.isEmpty()) {
                return PathfindingResult.success(directPath, 1, System.currentTimeMillis() - startTime);
            }
        }

        return PathfindingResult.failure("Direct path not possible", 0, System.currentTimeMillis() - startTime);
    }

    /**
     * Verifica linha de visão direta
     */
    private boolean hasDirectLineOfSight(BlockPos start, BlockPos goal) {
        Vec3 startVec = new Vec3(start.getX() + 0.5, start.getY() + 0.5, start.getZ() + 0.5);
        Vec3 goalVec = new Vec3(goal.getX() + 0.5, goal.getY() + 0.5, goal.getZ() + 0.5);

        double distance = startVec.distanceTo(goalVec);
        int steps = (int) Math.ceil(distance * 3); // Mais passos para melhor precisão

        for (int i = 1; i < steps; i++) {
            double t = (double) i / steps;
            Vec3 direction = goalVec.subtract(startVec);
            Vec3 scaledDirection = new Vec3(direction.xCoord * t, direction.yCoord * t, direction.zCoord * t);
            Vec3 point = startVec.add(scaledDirection);

            BlockPos checkPos = new BlockPos(point);

            // Verificar se pode passar por esta posição
            if (!canMoveThrough(checkPos, 2)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Cria caminho direto simples
     */
    private List<BaritoneNode> createDirectPath(BlockPos start, BlockPos goal) {
        List<BaritoneNode> path = new ArrayList<>();

        // Adicionar ponto inicial
        path.add(new BaritoneNode(start, 0, 0, null, MovementType.TRAVERSE));

        // Calcular passos intermediários se necessário
        int dx = goal.getX() - start.getX();
        int dy = goal.getY() - start.getY();
        int dz = goal.getZ() - start.getZ();

        int steps = Math.max(Math.max(Math.abs(dx), Math.abs(dy)), Math.abs(dz));

        if (steps > 1) {
            for (int i = 1; i < steps; i++) {
                double t = (double) i / steps;
                int x = start.getX() + (int) Math.round(dx * t);
                int y = start.getY() + (int) Math.round(dy * t);
                int z = start.getZ() + (int) Math.round(dz * t);

                BlockPos intermediatePos = new BlockPos(x, y, z);
                MovementType movementType = determineMovementType(path.get(path.size() - 1).position, intermediatePos);

                path.add(new BaritoneNode(intermediatePos, i, 0, path.get(path.size() - 1), movementType));
            }
        }

        // Adicionar ponto final
        MovementType finalMovementType = determineMovementType(path.get(path.size() - 1).position, goal);
        path.add(new BaritoneNode(goal, steps, 0, path.get(path.size() - 1), finalMovementType));

        return path;
    }

    /**
     * Determina tipo de movimento entre duas posições - INTELIGENTE PARA ROTAS
     * NATURAIS
     */
    private MovementType determineMovementType(BlockPos from, BlockPos to) {
        int dx = Math.abs(to.getX() - from.getX());
        int dy = to.getY() - from.getY();
        int dz = Math.abs(to.getZ() - from.getZ());

        if (dy > 0) {
            // NOVA LÓGICA: Verificar se realmente precisa pular
            boolean needsJump = determineIfJumpNeeded(from, to, dy);

            if (needsJump) {
                Logger.sendMessage("§a[DEBUG] Node ASCEND criado: " + from + " -> " + to);
                return MovementType.ASCEND; // Pulo necessário
            } else {
                return MovementType.TRAVERSE; // Movimento natural
            }
        } else if (dy < -1) {
            return MovementType.FALL;
        } else if (dy == -1) {
            return MovementType.DESCEND;
        } else if (dx == 1 && dz == 1) {
            return MovementType.DIAGONAL;
        } else {
            // Verificar se há obstáculo horizontal que requer pulo
            if (hasHorizontalObstacle(from, to)) {
                Logger.sendMessage("§a[DEBUG] Node ASCEND criado para obstáculo: " + from + " -> " + to);
                return MovementType.ASCEND; // Pulo para superar obstáculo
            }
            return MovementType.TRAVERSE;
        }
    }

    /**
     * Determina se movimento vertical requer pulo
     */
    private boolean determineIfJumpNeeded(BlockPos from, BlockPos to, int dy) {
        if (mc.theWorld == null)
            return false;

        // 1. Verificar altura real dos blocos
        double fromHeight = MovementType.getActualBlockHeight(from.down(), mc);
        double toHeight = MovementType.getActualBlockHeight(to.down(), mc);
        double realHeightDiff = (to.getY() + toHeight) - (from.getY() + fromHeight);

        // DETECÇÃO MELHORADA DE SLABS E MOVIMENTO NATURAL
        Block fromBlock = mc.theWorld.getBlockState(from.down()).getBlock();
        Block toBlock = mc.theWorld.getBlockState(to.down()).getBlock();

        // Verificar se são slabs primeiro (prioridade máxima)
        boolean fromIsSlab = isSlabBlock(fromBlock);
        boolean toIsSlab = isSlabBlock(toBlock);

        // CORREÇÃO PRINCIPAL: Movimento com slabs - ajustar Y ao invés de pular
        if (fromIsSlab || toIsSlab) {
            // Para slabs, permitir movimento natural até 0.75 blocos de diferença
            if (realHeightDiff <= 0.75) {
                Logger.sendMessage("§7[DEBUG] Movimento com slab detectado - SEM PULO (diff: "
                        + String.format("%.2f", realHeightDiff) + ")");
                return false;
            }
            // Se diferença é maior que 0.75 mas menor que 1.2, ainda é movimento natural
            if (realHeightDiff <= 1.2) {
                Logger.sendMessage("§7[DEBUG] Movimento slab natural - ajuste Y (diff: "
                        + String.format("%.2f", realHeightDiff) + ")");
                return false;
            }
        }

        // Verificar outros blocos que permitem movimento natural
        if (isNaturalMovementBlock(fromBlock) || isNaturalMovementBlock(toBlock)) {
            if (realHeightDiff <= 1.0) {
                Logger.sendMessage("§7[DEBUG] Movimento natural detectado - sem pulo (diff: "
                        + String.format("%.2f", realHeightDiff) + ")");
                return false;
            }
        }

        // Só pular se a diferença for realmente grande (mais de 1.2 blocos)
        if (realHeightDiff > 1.2) {
            Logger.sendMessage("§7[DEBUG] Pulo necessário - altura real: " + String.format("%.2f", realHeightDiff));
            return true;
        }

        // 2. Verificar se há obstáculo no caminho horizontal (mas não para slabs)
        if (!fromIsSlab && !toIsSlab) {
            boolean hasObstacle = hasHorizontalObstacle(from, to);
            if (hasObstacle) {
                Logger.sendMessage("§7[DEBUG] Pulo necessário - obstáculo horizontal");
                return true;
            }
        }

        return false;
    }

    /**
     * Verifica especificamente se é um bloco slab
     */
    private boolean isSlabBlock(Block block) {
        if (block == null)
            return false;

        // Verificação direta por instância (mais confiável)
        if (block instanceof net.minecraft.block.BlockSlab) {
            return true;
        }

        String blockName = block.getUnlocalizedName().toLowerCase();
        String className = block.getClass().getSimpleName().toLowerCase();

        // Detecção específica de slabs por nome
        return blockName.contains("slab") ||
                blockName.contains("step") ||
                className.contains("slab") ||
                className.contains("step");
    }

    /**
     * Verifica se o bloco permite movimento natural (sem pulo)
     */
    private boolean isNaturalMovementBlock(Block block) {
        if (block == null)
            return false;

        String blockName = block.getUnlocalizedName().toLowerCase();

        // Stairs e blocos que permitem movimento natural (excluindo slabs que já são
        // tratados separadamente)
        return blockName.contains("stair") ||
                blockName.contains("path") ||
                blockName.contains("farmland") ||
                blockName.contains("grass") ||
                blockName.contains("dirt") ||
                blockName.contains("stone") ||
                blockName.contains("cobblestone");
    }

    /**
     * Modificador de terreno SIMPLES
     * Prioriza slabs mas mantém simplicidade
     */
    private double calculateTerrainModifier(BlockPos pos) {
        if (mc.theWorld == null)
            return 1.0;

        Block blockBelow = mc.theWorld.getBlockState(pos.down()).getBlock();
        String blockName = blockBelow.getUnlocalizedName().toLowerCase();

        // VERIFICAÇÃO BÁSICA: Clearance do jogador
        if (!hasPlayerClearance(pos)) {
            return 5.0; // Penalidade para segurança
        }

        // SLABS - Prioridade máxima mas simples
        if (blockBelow instanceof net.minecraft.block.BlockSlab) {
            return 0.2; // Muito preferível
        }

        // Caminhos e estradas - muito bom
        if (blockName.contains("path") || blockName.contains("road")) {
            return 0.4; // Bom
        }

        // Escadas - bom para movimento vertical
        if (blockName.contains("stair")) {
            return 0.5; // Bom
        }

        // Blocos sólidos - padrão bom
        if (blockName.contains("stone") || blockName.contains("brick") ||
                blockName.contains("cobblestone") || blockName.contains("concrete")) {
            return 0.7; // Aceitável
        }

        // Grama e dirt - padrão
        if (blockName.contains("grass") || blockName.contains("dirt")) {
            return 0.9; // Padrão
        }

        // Terreno difícil - penalidade leve
        if (blockName.contains("gravel") || blockName.contains("sand") ||
                blockName.contains("soul")) {
            return 1.3; // Penalidade leve
        }

        return 1.0; // Custo normal
    }

    /**
     * Verifica se a posição está próxima a paredes/estruturas
     */
    private boolean isNearWalls(BlockPos pos) {
        if (mc.theWorld == null)
            return false;

        int wallCount = 0;

        // Verificar blocos ao redor (horizontal)
        for (int dx = -1; dx <= 1; dx++) {
            for (int dz = -1; dz <= 1; dz++) {
                if (dx == 0 && dz == 0)
                    continue; // Pular centro

                BlockPos checkPos = pos.add(dx, 0, dz);
                Block block = mc.theWorld.getBlockState(checkPos).getBlock();

                // Se há bloco sólido ao redor
                if (!MovementType.canWalkThrough(checkPos, mc)) {
                    wallCount++;
                }
            }
        }

        // Se mais de 3 blocos sólidos ao redor, considera próximo a parede
        return wallCount >= 3;
    }

    /**
     * Verifica se há obstáculo horizontal que requer pulo
     */
    private boolean hasHorizontalObstacle(BlockPos from, BlockPos to) {
        if (mc.theWorld == null)
            return false;

        // Verificar se estamos lidando com slabs primeiro
        Block fromBlock = mc.theWorld.getBlockState(from.down()).getBlock();
        Block toBlock = mc.theWorld.getBlockState(to.down()).getBlock();
        boolean hasSlabs = isSlabBlock(fromBlock) || isSlabBlock(toBlock);

        // Se há slabs envolvidos, NÃO considerar obstáculos horizontais para pulo
        if (hasSlabs) {
            Logger.sendMessage("§7[DEBUG] Movimento com slabs - ignorando obstáculos horizontais");
            return false;
        }

        // Calcular direção do movimento
        int dx = to.getX() - from.getX();
        int dz = to.getZ() - from.getZ();

        // Se movimento é puramente vertical, não há obstáculo horizontal
        if (dx == 0 && dz == 0)
            return false;

        // Normalizar direção
        int stepX = dx == 0 ? 0 : (dx > 0 ? 1 : -1);
        int stepZ = dz == 0 ? 0 : (dz > 0 ? 1 : -1);

        // Verificar obstáculo no caminho
        BlockPos obstaclePos = from.add(stepX, 0, stepZ);
        Block obstacleBlock = mc.theWorld.getBlockState(obstaclePos).getBlock();

        if (isBlockSolid(obstacleBlock)) {
            // IMPORTANTE: Se é slab, NÃO é obstáculo que requer pulo
            if (isSlabBlock(obstacleBlock)) {
                Logger.sendMessage("§7[DEBUG] Slab detectada - NÃO é obstáculo");
                return false;
            }

            // Há obstáculo real - verificar se pode pular por cima
            Block aboveObstacle = mc.theWorld.getBlockState(obstaclePos.up()).getBlock();
            Block aboveAbove = mc.theWorld.getBlockState(obstaclePos.up(2)).getBlock();

            // Precisa pular se há espaço acima do obstáculo
            boolean canJumpOver = MovementType.canWalkThrough(obstaclePos.up(), mc) &&
                    MovementType.canWalkThrough(obstaclePos.up(2), mc);

            if (canJumpOver) {
                Logger.sendMessage("§7[DEBUG] Obstáculo real detectado - pulo necessário");
            }

            return canJumpOver;
        }

        return false; // Não há obstáculo
    }

    /**
     * Verifica se há clearance adequado para o jogador na posição
     */
    private boolean hasPlayerClearance(BlockPos pos) {
        if (mc.theWorld == null)
            return false;

        // Verificar espaço vertical (2 blocos de altura para o jogador)
        if (!MovementType.canWalkThrough(pos, mc) || !MovementType.canWalkThrough(pos.up(), mc)) {
            return false;
        }

        // Verificar espaço horizontal ao redor (raio de 1 bloco)
        int solidBlocksAround = 0;
        for (int x = -1; x <= 1; x++) {
            for (int z = -1; z <= 1; z++) {
                if (x == 0 && z == 0)
                    continue; // Pular centro

                BlockPos checkPos = pos.add(x, 0, z);
                Block block = mc.theWorld.getBlockState(checkPos).getBlock();

                // Contar blocos sólidos que podem bloquear movimento
                if (block.isFullBlock() && !MovementType.canWalkThrough(checkPos, mc)) {
                    solidBlocksAround++;
                }
            }
        }

        // Se há muitos blocos sólidos ao redor, não há clearance adequado
        return solidBlocksAround <= 4; // Máximo 4 dos 8 blocos adjacentes podem ser sólidos
    }

    /**
     * Verifica se há árvores ou obstáculos verticais próximos
     */
    private boolean isNearTreesOrObstacles(BlockPos pos) {
        if (mc.theWorld == null)
            return false;

        // Verificar em um raio de 2 blocos por árvores e obstáculos verticais
        for (int x = -2; x <= 2; x++) {
            for (int z = -2; z <= 2; z++) {
                BlockPos checkPos = pos.add(x, 0, z);

                // Verificar se há troncos de árvore
                Block groundBlock = mc.theWorld.getBlockState(checkPos).getBlock();
                if (isLogBlock(groundBlock)) {
                    return true;
                }

                // Verificar obstáculos verticais (pilares de blocos)
                int verticalBlocks = 0;
                for (int y = 0; y <= 3; y++) {
                    Block blockAtHeight = mc.theWorld.getBlockState(checkPos.add(0, y, 0)).getBlock();
                    if (blockAtHeight.isFullBlock() && blockAtHeight != net.minecraft.init.Blocks.air) {
                        verticalBlocks++;
                    }
                }

                // Se há 3+ blocos verticais, é um obstáculo
                if (verticalBlocks >= 3) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Verifica se é um bloco de tronco de árvore
     */
    private boolean isLogBlock(Block block) {
        if (block == null)
            return false;
        String blockName = block.getUnlocalizedName().toLowerCase();
        return blockName.contains("log") || blockName.contains("wood");
    }

    /**
     * Verifica se a posição é uma ponte de slabs (slabs sobre água/ar)
     */
    private boolean isSlabBridge(BlockPos pos) {
        if (mc.theWorld == null)
            return false;

        // Verificar se há água ou ar embaixo da slab (indicando ponte)
        Block blockBelow = mc.theWorld.getBlockState(pos.down(2)).getBlock();
        Block blockDirectlyBelow = mc.theWorld.getBlockState(pos.down()).getBlock();

        // Se é slab e há água/ar embaixo, é uma ponte
        if (blockDirectlyBelow instanceof net.minecraft.block.BlockSlab) {
            return blockBelow == net.minecraft.init.Blocks.water ||
                    blockBelow == net.minecraft.init.Blocks.flowing_water ||
                    blockBelow == net.minecraft.init.Blocks.air ||
                    blockBelow == net.minecraft.init.Blocks.lava ||
                    blockBelow == net.minecraft.init.Blocks.flowing_lava;
        }

        return false;
    }

    /**
     * Verifica se bloco é sólido
     */
    private boolean isBlockSolid(Block block) {
        return block != null &&
                block != net.minecraft.init.Blocks.air &&
                block.getMaterial().isSolid() &&
                block.isFullBlock();
    }

    /**
     * Heurística LINHA RETA - Prioriza trajetos diretos e retos
     * Penaliza curvas e mudanças de direção
     */
    private double calculateHeuristic(BlockPos from, BlockPos to) {
        // Calcular diferenças de coordenadas
        double dx = to.getX() - from.getX();
        double dy = to.getY() - from.getY();
        double dz = to.getZ() - from.getZ();

        // Distâncias absolutas
        double absDx = Math.abs(dx);
        double absDy = Math.abs(dy);
        double absDz = Math.abs(dz);

        // Usar distância euclidiana como base (favorece diagonal naturalmente)
        double euclideanDistance = Math.sqrt(dx * dx + dy * dy + dz * dz);

        // Bonus moderado para movimento diagonal
        double diagonalBonus = 1.0;

        // Verificar se é movimento diagonal horizontal
        if (absDx > 0.1 && absDz > 0.1) {
            // Calcular quão próximo está de uma diagonal perfeita
            double ratio = Math.min(absDx, absDz) / Math.max(absDx, absDz);

            if (ratio > 0.7) { // Diagonal boa (próximo de 45°)
                diagonalBonus = 0.7; // 30% de desconto para diagonal
            } else if (ratio > 0.4) { // Diagonal razoável
                diagonalBonus = 0.85; // 15% de desconto
            } else {
                diagonalBonus = 0.95; // 5% de desconto para diagonal imperfeita
            }

            // Bonus para movimento diagonal 3D gradual
            if (absDy > 0) {
                double horizontalDist = Math.sqrt(absDx * absDx + absDz * absDz);
                double slope3D = absDy / horizontalDist;

                if (slope3D <= 0.5) { // Subida gradual
                    diagonalBonus *= 0.8; // Bonus para movimento 3D suave
                } else if (slope3D <= 1.0) { // Subida moderada
                    diagonalBonus *= 0.9; // Bonus menor
                } else {
                    diagonalBonus *= 1.3; // Pequena penalidade para subida íngreme
                }
            }
        }
        // Penalidade moderada para movimento em linha reta pura
        else if ((absDx > 0.1 && absDz < 0.1) || (absDx < 0.1 && absDz > 0.1)) {
            diagonalBonus = 1.5; // Penalidade moderada para linha reta
        }
        // Penalidade para movimento só vertical
        else if (absDx < 0.1 && absDz < 0.1 && absDy > 0.1) {
            diagonalBonus = 2.0; // Penalidade para movimento só vertical
        }

        // RESULTADO: Favorece movimento diagonal de forma equilibrada
        return euclideanDistance * diagonalBonus;
    }

    /**
     * Calcula penalidade por mudança de direção para favorecer linha reta
     * Retorna multiplicador de custo (1.0 = sem penalidade, >1.0 = penalidade)
     */
    private double calculateDirectionPenalty(BaritoneNode currentNode, BlockPos destination) {
        // Se não há nó pai, não há direção anterior
        if (currentNode.parent == null) {
            return 1.0; // Sem penalidade para primeiro movimento
        }

        // Calcular direção anterior (do avô para o pai)
        BlockPos grandParent = currentNode.parent.position;
        BlockPos parent = currentNode.position;

        int prevDx = parent.getX() - grandParent.getX();
        int prevDz = parent.getZ() - grandParent.getZ();

        // Calcular direção atual (do pai para o destino)
        int currDx = destination.getX() - parent.getX();
        int currDz = destination.getZ() - parent.getZ();

        // Se continua na MESMA direção exata = BONUS BOM
        if (prevDx == currDx && prevDz == currDz) {
            return 0.5; // 50% de desconto para continuar na mesma direção
        }

        // Verificar se ambos são movimentos diagonais
        boolean prevDiagonal = (Math.abs(prevDx) > 0 && Math.abs(prevDz) > 0);
        boolean currDiagonal = (Math.abs(currDx) > 0 && Math.abs(currDz) > 0);

        // Bonus para manter movimento diagonal
        if (prevDiagonal && currDiagonal) {
            // Verificar se mantém direção diagonal similar
            double prevRatio = Math.abs((double) prevDx / prevDz);
            double currRatio = Math.abs((double) currDx / currDz);

            // Verificar se as direções são similares (mesmo quadrante)
            boolean sameQuadrant = (prevDx * currDx > 0) && (prevDz * currDz > 0);

            if (sameQuadrant && Math.abs(prevRatio - currRatio) < 0.4) {
                return 0.6; // 40% de desconto para manter diagonal similar
            } else if (sameQuadrant) {
                return 0.8; // 20% de desconto para diagonal no mesmo quadrante
            } else {
                return 1.8; // Penalidade moderada para mudar quadrante diagonal
            }
        }

        // Penalidade para movimento em linha reta
        boolean prevStraight = (prevDx == 0 || prevDz == 0);
        boolean currStraight = (currDx == 0 || currDz == 0);

        if (prevStraight && currStraight) {
            // Ambos são movimentos retos
            if ((prevDx == 0 && currDx == 0) || (prevDz == 0 && currDz == 0)) {
                return 1.2; // Pequena penalidade para continuar no mesmo eixo
            } else {
                return 3.0; // Penalidade por mudar de eixo (curva de 90°)
            }
        }

        // Penalidade para mudança de diagonal para linha reta ou vice-versa
        if ((prevDiagonal && currStraight) || (prevStraight && currDiagonal)) {
            return 2.0; // Penalidade moderada para mudança de tipo de movimento
        }

        // Calcular ângulo entre direções usando produto escalar
        double dotProduct = prevDx * currDx + prevDz * currDz;
        double prevMagnitude = Math.sqrt(prevDx * prevDx + prevDz * prevDz);
        double currMagnitude = Math.sqrt(currDx * currDx + currDz * currDz);

        if (prevMagnitude == 0 || currMagnitude == 0) {
            return 1.0; // Sem penalidade se não há movimento
        }

        double cosAngle = dotProduct / (prevMagnitude * currMagnitude);
        cosAngle = Math.max(-1.0, Math.min(1.0, cosAngle)); // Clamp para evitar erros

        // Converter para penalidade ULTRA AGRESSIVA:
        // cosAngle = 1.0 (0°, mesma direção) -> bonus massivo
        // cosAngle = 0.0 (90°, curva) -> penalidade extrema
        // cosAngle = -1.0 (180°, volta) -> penalidade máxima

        if (cosAngle > 0.98) {
            return 0.01; // Desvio mínimo, bonus massivo
        } else if (cosAngle > 0.9) {
            return 0.1; // Desvio pequeno, ainda com bonus grande
        } else if (cosAngle > 0.5) {
            return 20.0; // Curva moderada - PENALIDADE EXTREMA
        } else if (cosAngle > 0.0) {
            return 100.0; // Curva acentuada - PENALIDADE MÁXIMA
        } else {
            return 500.0; // Volta ou curva muito acentuada - PENALIDADE ABSURDA
        }
    }

    /**
     * Verifica se está próximo o suficiente do objetivo
     */
    private boolean isCloseEnough(BlockPos current, BlockPos goal) {
        return current.distanceSq(goal) <= 4; // Dentro de 2 blocos
    }

    /**
     * Calcula distância do início
     */
    private double getDistanceFromStart(BaritoneNode node, BaritoneNode start) {
        return Math.sqrt(node.position.distanceSq(start.position));
    }

    /**
     * Reconstrói o caminho a partir do nó final
     */
    private List<BaritoneNode> reconstructPath(BaritoneNode endNode) {
        List<BaritoneNode> path = new ArrayList<>();
        BaritoneNode current = endNode;

        while (current != null) {
            path.add(0, current);
            current = current.parent;
        }

        return path;
    }

    /**
     * Otimiza caminho removendo nodes desnecessários
     */
    private List<BaritoneNode> optimizePath(List<BaritoneNode> originalPath) {
        if (originalPath.size() <= 2)
            return originalPath;

        List<BaritoneNode> optimized = new ArrayList<>();
        optimized.add(originalPath.get(0)); // Sempre manter primeiro node

        for (int i = 1; i < originalPath.size() - 1; i++) {
            BaritoneNode current = originalPath.get(i);
            BaritoneNode previous = optimized.get(optimized.size() - 1);
            BaritoneNode next = originalPath.get(i + 1);

            // Verificar se pode pular este node (linha reta)
            if (!canSkipNode(previous.position, current.position, next.position)) {
                optimized.add(current);
            }
        }

        optimized.add(originalPath.get(originalPath.size() - 1)); // Sempre manter último node
        return optimized;
    }

    /**
     * Verifica se pode pular um node (se está em linha reta)
     */
    private boolean canSkipNode(BlockPos from, BlockPos middle, BlockPos to) {
        // Verificar se os três pontos estão aproximadamente em linha reta
        double dx1 = middle.getX() - from.getX();
        double dz1 = middle.getZ() - from.getZ();
        double dx2 = to.getX() - middle.getX();
        double dz2 = to.getZ() - middle.getZ();

        // Calcular produto cruzado para verificar colinearidade
        double crossProduct = Math.abs(dx1 * dz2 - dx2 * dz1);

        // Se produto cruzado é pequeno, os pontos estão em linha reta
        return crossProduct < 0.1 && Math.abs(from.getY() - middle.getY()) <= 1
                && Math.abs(middle.getY() - to.getY()) <= 1;
    }

    /**
     * Nó do pathfinding baseado no Baritone
     */
    public static class BaritoneNode {
        public final BlockPos position;
        public final double gCost; // Custo do início até este nó
        public final double hCost; // Heurística (estimativa até o objetivo)
        public final double fCost; // gCost + hCost
        public final BaritoneNode parent;
        public final MovementType movementType;

        public BaritoneNode(BlockPos position, double gCost, double hCost, BaritoneNode parent,
                MovementType movementType) {
            this.position = position;
            this.gCost = gCost;
            this.hCost = hCost;
            this.fCost = gCost + hCost;
            this.parent = parent;
            this.movementType = movementType;
        }
    }

    /**
     * Resultado do pathfinding
     */
    public static class PathfindingResult {
        public final boolean success;
        public final boolean isPartial;
        public final List<BaritoneNode> path;
        public final int nodesExplored;
        public final long timeMs;
        public final String errorMessage;

        private PathfindingResult(boolean success, boolean isPartial, List<BaritoneNode> path,
                int nodesExplored, long timeMs, String errorMessage) {
            this.success = success;
            this.isPartial = isPartial;
            this.path = path;
            this.nodesExplored = nodesExplored;
            this.timeMs = timeMs;
            this.errorMessage = errorMessage;
        }

        public static PathfindingResult success(List<BaritoneNode> path, int nodesExplored, long timeMs) {
            return new PathfindingResult(true, false, path, nodesExplored, timeMs, null);
        }

        public static PathfindingResult partial(List<BaritoneNode> path, int nodesExplored, long timeMs) {
            return new PathfindingResult(true, true, path, nodesExplored, timeMs, null);
        }

        public static PathfindingResult failure(String errorMessage, int nodesExplored, long timeMs) {
            return new PathfindingResult(false, false, new ArrayList<>(), nodesExplored, timeMs, errorMessage);
        }
    }
}
