package com.rato.addons.failsafe;

import com.rato.addons.config.RatoAddonsConfigSimple;
import com.rato.addons.util.Logger;

public class BlockCheckFailsafe extends Failsafe {

    private BlockCheckState state = BlockCheckState.NONE;

    @Override
    public int getPriority() {
        return 2;
    }

    @Override
    public FailsafeManager.EmergencyType getType() {
        return FailsafeManager.EmergencyType.BLOCK_CHECK;
    }

    @Override
    public boolean shouldSendNotification() {
        return RatoAddonsConfigSimple.discordWebhook;
    }

    @Override
    public boolean shouldPlaySound() {
        return RatoAddonsConfigSimple.soundAlert;
    }

    @Override
    public void duringFailsafeTrigger() {
        switch (state) {
            case NONE:
                FailsafeManager.getInstance().scheduleRandomDelay(200, 500);
                state = BlockCheckState.WAIT_BEFORE_START;
                break;

            case WAIT_BEFORE_START:
                stopMovement();
                state = BlockCheckState.LOOK_AROUND;
                FailsafeManager.getInstance().scheduleRandomDelay(300, 600);
                break;

            case LOOK_AROUND:
                performHumanLikeMovement();
                state = BlockCheckState.END;
                FailsafeManager.getInstance().scheduleRandomDelay(400, 800);
                break;

            case END:
                endOfFailsafeTrigger();
                break;
        }
    }

    @Override
    public void endOfFailsafeTrigger() {
        Logger.sendMessage("§a✓ Block check handled");
        FailsafeManager.getInstance().stopFailsafes();
        FailsafeManager.getInstance().scheduleDelay(1000);
    }

    @Override
    public void resetStates() {
        state = BlockCheckState.NONE;
    }

    private enum BlockCheckState {
        NONE,
        WAIT_BEFORE_START,
        LOOK_AROUND,
        END
    }
}
