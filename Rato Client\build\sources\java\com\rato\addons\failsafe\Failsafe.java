package com.rato.addons.failsafe;

import com.rato.addons.config.RatoAddonsConfigSimple;
import net.minecraft.client.Minecraft;
import net.minecraft.network.Packet;

public abstract class Failsafe {

    protected static final Minecraft mc = Minecraft.getMinecraft();

    /**
     * Prioridade do failsafe (maior = mais importante)
     */
    public abstract int getPriority();

    /**
     * Tipo de emergência
     */
    public abstract FailsafeManager.EmergencyType getType();

    /**
     * Se deve enviar notificação Discord
     */
    public abstract boolean shouldSendNotification();

    /**
     * Se deve tocar som de alerta
     */
    public abstract boolean shouldPlaySound();

    /**
     * Se deve pausar automações
     */
    public boolean shouldPauseAutomations() {
        return true;
    }

    /**
     * Detecção baseada em pacotes de rede
     */
    public void onReceivedPacketDetection(Packet packet) {
        // Override em subclasses se necessário
    }

    /**
     * Detecção baseada em mudanças no mundo/player
     */
    public void onWorldChangeDetection() {
        // Override em subclasses se necessário
    }

    /**
     * Executado durante o failsafe (máquina de estados)
     */
    public abstract void duringFailsafeTrigger();

    /**
     * Executado no final do failsafe
     */
    public abstract void endOfFailsafeTrigger();

    /**
     * Resetar estados internos
     */
    public abstract void resetStates();

    /**
     * Verificar se o failsafe está habilitado
     */
    public boolean isEnabled() {
        switch (getType()) {
            case ROTATION_CHECK:
                return RatoAddonsConfigSimple.rotationDetection;
            case TELEPORT_CHECK:
                return RatoAddonsConfigSimple.tpDetection;
            case ITEM_CHANGE_CHECK:
                return RatoAddonsConfigSimple.itemSwapDetection;
            case BLOCK_CHECK:
                return RatoAddonsConfigSimple.blockCheckDetection;
            case VELOCITY_CHECK:
                return RatoAddonsConfigSimple.velocityCheckDetection;
            case PLAYER_CHECK:
                return RatoAddonsConfigSimple.playerCheckDetection;
            default:
                return false;
        }
    }

    /**
     * Simular movimento humano aleatório
     */
    protected void performHumanLikeMovement() {
        if (mc.thePlayer == null)
            return;

        // Movimento de mouse sutil e aleatório
        float yawChange = (float) ((Math.random() - 0.5) * 20); // -10 a +10 graus
        float pitchChange = (float) ((Math.random() - 0.5) * 10); // -5 a +5 graus

        mc.thePlayer.rotationYaw += yawChange;
        mc.thePlayer.rotationPitch += pitchChange;

        // Limitar pitch
        if (mc.thePlayer.rotationPitch > 90.0f) {
            mc.thePlayer.rotationPitch = 90.0f;
        }
        if (mc.thePlayer.rotationPitch < -90.0f) {
            mc.thePlayer.rotationPitch = -90.0f;
        }
    }

    /**
     * Parar todos os movimentos
     */
    protected void stopMovement() {
        if (mc.thePlayer != null) {
            mc.thePlayer.motionX = 0;
            mc.thePlayer.motionZ = 0;
        }
    }

    /**
     * Verificar se o player está em movimento
     */
    protected boolean isPlayerMoving() {
        if (mc.thePlayer == null)
            return false;
        return mc.thePlayer.motionX != 0 || mc.thePlayer.motionZ != 0 || mc.thePlayer.motionY != 0;
    }

    /**
     * Verificar se há input do usuário
     */
    protected boolean hasUserInput() {
        if (mc.gameSettings == null)
            return false;
        try {
            return mc.gameSettings.keyBindForward.isKeyDown() ||
                    mc.gameSettings.keyBindBack.isKeyDown() ||
                    mc.gameSettings.keyBindLeft.isKeyDown() ||
                    mc.gameSettings.keyBindRight.isKeyDown() ||
                    mc.gameSettings.keyBindJump.isKeyDown() ||
                    mc.gameSettings.keyBindSneak.isKeyDown();
        } catch (Exception e) {
            return false;
        }
    }
}
