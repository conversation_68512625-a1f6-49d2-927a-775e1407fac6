package com.rato.addons.gui;

import com.rato.addons.config.CustomConfigManager;
import net.minecraft.client.Minecraft;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.InputEvent;
import org.lwjgl.input.Keyboard;

/**
 * Gerenciador de eventos para a GUI customizada
 */
public class GuiEventHandler {
    
    private static CustomGUI guiInstance = null;
    
    @SubscribeEvent
    public void onKeyInput(InputEvent.KeyInputEvent event) {
        if (!Keyboard.getEventKeyState()) return; // Only on key press, not release
        
        int key = Keyboard.getEventKey();
        
        // Check GUI toggle key
        if (key == CustomConfigManager.guiKey) {
            toggleGUI();
            return;
        }
        
        // Handle module keybinds
        handleModuleKeybinds(key);
    }
    
    private void toggleGUI() {
        Minecraft mc = Minecraft.getMinecraft();
        
        if (mc.currentScreen instanceof CustomGUI) {
            mc.displayGuiScreen(null);
        } else {
            if (guiInstance == null) {
                guiInstance = new CustomGUI();
            }
            mc.displayGuiScreen(guiInstance);
        }
    }
    
    private void handleModuleKeybinds(int key) {
        // Handle pathfinding toggle
        if (key == Keyboard.KEY_P) {
            boolean current = CustomConfigManager.getBoolean("movement", "pathfinding_enabled");
            CustomConfigManager.setBoolean("movement", "pathfinding_enabled", !current);
            CustomConfigManager.saveConfig();
            
            Minecraft mc = Minecraft.getMinecraft();
            if (mc.thePlayer != null) {
                String status = current ? "disabled" : "enabled";
                mc.thePlayer.addChatMessage(new net.minecraft.util.ChatComponentText(
                    "§7[§bRato§7] §fPathfinding " + status));
            }
        }
        
        // Handle freecam toggle
        if (key == Keyboard.KEY_G) {
            boolean current = CustomConfigManager.getBoolean("player", "freecam_enabled");
            CustomConfigManager.setBoolean("player", "freecam_enabled", !current);
            CustomConfigManager.saveConfig();
            
            Minecraft mc = Minecraft.getMinecraft();
            if (mc.thePlayer != null) {
                String status = current ? "disabled" : "enabled";
                mc.thePlayer.addChatMessage(new net.minecraft.util.ChatComponentText(
                    "§7[§bRato§7] §fFreecam " + status));
            }
        }
        
        // Handle mining toggle
        if (key == Keyboard.KEY_M) {
            boolean current = CustomConfigManager.getBoolean("world", "mining_helper");
            CustomConfigManager.setBoolean("world", "mining_helper", !current);
            CustomConfigManager.saveConfig();
            
            Minecraft mc = Minecraft.getMinecraft();
            if (mc.thePlayer != null) {
                String status = current ? "disabled" : "enabled";
                mc.thePlayer.addChatMessage(new net.minecraft.util.ChatComponentText(
                    "§7[§bRato§7] §fMining Helper " + status));
            }
        }
        
        // Handle farming toggle
        if (key == Keyboard.KEY_F) {
            boolean current = CustomConfigManager.getBoolean("world", "auto_farm");
            CustomConfigManager.setBoolean("world", "auto_farm", !current);
            CustomConfigManager.saveConfig();
            
            Minecraft mc = Minecraft.getMinecraft();
            if (mc.thePlayer != null) {
                String status = current ? "disabled" : "enabled";
                mc.thePlayer.addChatMessage(new net.minecraft.util.ChatComponentText(
                    "§7[§bRato§7] §fAuto Farm " + status));
            }
        }
        
        // Handle staff check response
        if (key == Keyboard.KEY_H) {
            boolean current = CustomConfigManager.getBoolean("misc", "staff_check_response");
            CustomConfigManager.setBoolean("misc", "staff_check_response", !current);
            CustomConfigManager.saveConfig();
            
            Minecraft mc = Minecraft.getMinecraft();
            if (mc.thePlayer != null) {
                String status = current ? "disabled" : "enabled";
                mc.thePlayer.addChatMessage(new net.minecraft.util.ChatComponentText(
                    "§7[§bRato§7] §fStaff Check Response " + status));
            }
        }
        
        // Handle manual pause
        if (key == Keyboard.KEY_PAUSE) {
            boolean current = CustomConfigManager.getBoolean("misc", "manual_pause");
            CustomConfigManager.setBoolean("misc", "manual_pause", !current);
            CustomConfigManager.saveConfig();
            
            Minecraft mc = Minecraft.getMinecraft();
            if (mc.thePlayer != null) {
                String status = current ? "resumed" : "paused";
                mc.thePlayer.addChatMessage(new net.minecraft.util.ChatComponentText(
                    "§7[§bRato§7] §fAll scripts " + status));
            }
        }
        
        // Handle emergency stop
        if (key == Keyboard.KEY_END) {
            // Emergency stop all scripts
            CustomConfigManager.setBoolean("combat", "aimbot_enabled", false);
            CustomConfigManager.setBoolean("movement", "pathfinding_enabled", false);
            CustomConfigManager.setBoolean("player", "freecam_enabled", false);
            CustomConfigManager.setBoolean("world", "mining_helper", false);
            CustomConfigManager.setBoolean("world", "auto_farm", false);
            CustomConfigManager.setBoolean("world", "foraging_enabled", false);
            CustomConfigManager.setBoolean("misc", "rift_autofarm", false);
            CustomConfigManager.setBoolean("misc", "emergency_stop_all", true);
            CustomConfigManager.saveConfig();
            
            Minecraft mc = Minecraft.getMinecraft();
            if (mc.thePlayer != null) {
                mc.thePlayer.addChatMessage(new net.minecraft.util.ChatComponentText(
                    "§7[§bRato§7] §c§lEMERGENCY STOP - All scripts disabled!"));
            }
        }
    }
    
    /**
     * Get the current GUI instance
     */
    public static CustomGUI getGuiInstance() {
        if (guiInstance == null) {
            guiInstance = new CustomGUI();
        }
        return guiInstance;
    }
    
    /**
     * Check if GUI is currently open
     */
    public static boolean isGuiOpen() {
        return Minecraft.getMinecraft().currentScreen instanceof CustomGUI;
    }
}
