package com.rato.addons.features.combat

import net.minecraft.client.Minecraft
import net.minecraft.entity.EntityLivingBase
import net.minecraft.entity.monster.EntityZombie
import net.minecraft.util.MathHelper
import net.minecraft.util.Vec3
import net.minecraft.util.MovingObjectPosition
import net.minecraft.util.AxisAlignedBB
import org.lwjgl.input.Mouse
import kotlin.math.abs
import kotlin.math.atan2
import kotlin.math.sqrt
import kotlin.math.PI
import kotlin.math.max
import kotlin.math.min
import kotlin.math.sin
import kotlin.math.cos

/**
 * Sistema de aimbot ultra-suave em Kotlin (estilo LiquidBounce)
 * Movimento de câmera extremamente fluido e natural
 */
object UltraSmoothAimbot {
    
    private val mc = Minecraft.getMinecraft()

    // Variáveis de controle
    private var currentTarget: EntityLivingBase? = null
    private var lastAttackTime: Long = 0
    private var aimStartTime: Long = 0
    private var isAiming: Boolean = false
    private var lastTargetSeenTime: Long = 0

    // Configurações ultra-suaves
    data class AimbotConfig(
        var enabled: Boolean = true,
        var range: Float = 4.0f,
        var fov: Float = 90.0f,
        var autoAttack: Boolean = true,
        var attackDelay: Int = 500,
        
        // Configurações otimizadas para input real do mouse
        var mouseSensitivity: Float = 0.5f,     // Sensibilidade do mouse
        var maxMouseMovement: Float = 50.0f,    // Movimento máximo por frame
        var aimPrecision: Float = 5.0f,         // Precisão necessária para atacar (graus) - mais permissivo
        var snapSpeed: Float = 8.0f,            // Velocidade de snap para alvos
        var smoothingFactor: Float = 0.3f,      // Suavização do movimento
        var attackRange: Float = 4.0f,          // Alcance para atacar
        var minAimTime: Int = 50,               // Tempo mínimo mirando antes de atacar (ms) - mais rápido
        var realMouseInput: Boolean = true,     // Usar input real do mouse

        // Configurações avançadas
        var predictMovement: Boolean = true,
        var prioritizeClosest: Boolean = true,
        var throughWalls: Boolean = false,
        var interpolateRotation: Boolean = true,
        var precisionMode: Boolean = true,      // Modo de precisão ativado

        // Configurações dinâmicas baseadas no LiquidBounce
        var dynamicSpeed: Boolean = true,       // Velocidade dinâmica baseada na distância
        var fovBasedSpeed: Boolean = true,      // Velocidade baseada no FOV
        var snapToTarget: Boolean = true        // Snap rápido para alvos distantes
    )
    
    val config = AimbotConfig()

    // Estado interno
    private var targetYaw: Float = 0f
    private var targetPitch: Float = 0f
    private var isRotating: Boolean = false
    
    // Sistema de interpolação ultra-suave
    private var yawVelocity: Float = 0f
    private var pitchVelocity: Float = 0f
    private var lastUpdateTime: Long = 0
    
    /**
     * Atualiza o aimbot (chamado a cada tick)
     */
    fun update() {
        if (!config.enabled || mc.thePlayer == null || mc.theWorld == null) {
            currentTarget = null
            isRotating = false
            return
        }

        // Encontrar novo alvo
        val newTarget = findBestTarget()
        val currentTime = System.currentTimeMillis()

        if (newTarget != null) {
            // Atualizar tempo da última vez que viu o alvo
            lastTargetSeenTime = currentTime
            currentTarget = newTarget
            val distance = mc.thePlayer.getDistanceToEntity(newTarget)

            // Calcular rotações necessárias
            val rotations = calculateRotations(newTarget)
            targetYaw = rotations.first
            targetPitch = rotations.second

            // Verificar se está no alcance de ataque (3.5 blocos para hit)
            val attackRange = 3.5f
            val isInAttackRange = distance <= attackRange

            // Verificar se está mirando bem o suficiente
            val currentYaw = mc.thePlayer.rotationYaw
            val currentPitch = mc.thePlayer.rotationPitch
            val yawDiff = abs(normalizeAngle(targetYaw - currentYaw))
            val pitchDiff = abs(targetPitch - currentPitch)
            val isAimingWell = yawDiff <= 5.0f && pitchDiff <= 5.0f

            // Só rotacionar se NÃO estiver no alcance OU se não estiver mirando bem
            if (!isInAttackRange || !isAimingWell) {
                // Aplicar rotação ultra-suave
                if (config.interpolateRotation) {
                    applyUltraSmoothRotation()
                } else {
                    // Rotação instantânea (fallback)
                    mc.thePlayer.rotationYaw = targetYaw
                    mc.thePlayer.rotationPitch = targetPitch
                }
                isRotating = true
            } else {
                // Parar de rotacionar quando estiver no alcance e mirando bem
                isRotating = false
            }

            // Auto attack com verificação de precisão
            if (config.autoAttack && canAttack()) {
                attack(newTarget)
            }

        } else {
            // FAIL-SAFE: Se perdeu o alvo por mais de 1 segundo, resetar completamente
            if (currentTarget != null && currentTime - lastTargetSeenTime > 1000) {
                println("[FAILSAFE DEBUG] Target lost for >1s, resetting rotation")
                currentTarget = null
                isAiming = false
                isRotating = false
                aimStartTime = 0
                // Reset da rotação para posição neutra
                val player = mc.thePlayer
                if (player != null) {
                    player.rotationPitch = 0f // Reset pitch para horizontal
                }
            } else if (currentTarget == null) {
                // Sem alvo, aplicar damping normal
                isRotating = false
                applyDamping()
            }
        }
    }
    
    /**
     * Encontra o melhor alvo disponível - SEMPRE O MAIS PRÓXIMO
     */
    private fun findBestTarget(): EntityLivingBase? {
        val player = mc.thePlayer ?: return null
        val world = mc.theWorld ?: return null

        // ✅ SEMPRE PRIORIZAR O MOB MAIS PRÓXIMO - Ignorar outras configurações
        val closestTarget = world.loadedEntityList
            .filterIsInstance<EntityLivingBase>()
            .filter { isValidTarget(it) }
            .filter { player.getDistanceToEntity(it) <= config.range }
            .filter { config.throughWalls || hasLineOfSight(it) }
            .minByOrNull { player.getDistanceToEntity(it) } // SEMPRE por distância

        if (closestTarget != null) {
            val distance = player.getDistanceToEntity(closestTarget)
            println("[TARGET DEBUG] ✅ Alvo mais próximo encontrado: ${closestTarget.javaClass.simpleName} a ${distance} blocos")
        } else {
            println("[TARGET DEBUG] ❌ Nenhum alvo válido encontrado")
        }

        return closestTarget
    }
    
    /**
     * Verifica se a entidade é um alvo válido
     */
    private fun isValidTarget(entity: EntityLivingBase): Boolean {
        val player = mc.thePlayer ?: return false
        
        if (entity == player) return false
        if (!entity.isEntityAlive) return false
        
        // Verificar tipos de entidade
        return when (entity) {
            is EntityZombie -> true
            else -> false
        }
    }
    
    /**
     * Verifica se a entidade está no campo de visão
     */
    private fun isInFOV(entity: EntityLivingBase): Boolean {
        val player = mc.thePlayer ?: return false
        val rotations = calculateRotations(entity)
        
        val yawDiff = abs(normalizeAngle(rotations.first - player.rotationYaw))
        val pitchDiff = abs(rotations.second - player.rotationPitch)
        
        return yawDiff <= config.fov / 2f && pitchDiff <= config.fov / 2f
    }
    
    /**
     * Calcula a diferença angular para uma entidade
     */
    private fun getAngleDifference(entity: EntityLivingBase): Float {
        val player = mc.thePlayer ?: return Float.MAX_VALUE
        val rotations = calculateRotations(entity)
        
        val yawDiff = abs(normalizeAngle(rotations.first - player.rotationYaw))
        val pitchDiff = abs(rotations.second - player.rotationPitch)
        
        return sqrt(yawDiff * yawDiff + pitchDiff * pitchDiff)
    }
    
    /**
     * Verifica linha de visão para a entidade
     */
    private fun hasLineOfSight(entity: EntityLivingBase): Boolean {
        val player = mc.thePlayer ?: return false
        val world = mc.theWorld ?: return false
        
        val playerPos = player.getPositionEyes(1.0f)
        val targetPos = getTargetPosition(entity)
        
        return world.rayTraceBlocks(playerPos, targetPos, false, true, false) == null
    }
    
    /**
     * Calcula as rotações necessárias para mirar na entidade
     */
    private fun calculateRotations(entity: EntityLivingBase): Pair<Float, Float> {
        val player = mc.thePlayer ?: return Pair(0f, 0f)

        val playerPos = player.getPositionEyes(1.0f)
        var targetPos = getTargetPosition(entity)

        // PREDIÇÃO DE MOVIMENTO para alvos em movimento
        val targetVelocity = sqrt(entity.motionX * entity.motionX + entity.motionZ * entity.motionZ).toFloat()
        if (targetVelocity > 0.1f) {
            // Calcular tempo estimado para alcançar o alvo
            val distance = playerPos.distanceTo(targetPos)
            val timeToReach = (distance / 20.0).coerceIn(0.1, 0.5) // Entre 0.1 e 0.5 segundos

            // Aplicar predição baseada na velocidade do alvo
            val predictedX = entity.posX + (entity.motionX * timeToReach)
            val predictedZ = entity.posZ + (entity.motionZ * timeToReach)
            val predictedY = entity.posY + (entity.motionY * timeToReach)

            targetPos = Vec3(predictedX, predictedY + entity.height * 0.75f, predictedZ)

            println("[PREDICTION DEBUG] Target velocity: $targetVelocity")
            println("[PREDICTION DEBUG] Time to reach: $timeToReach")
            println("[PREDICTION DEBUG] Original pos: ${entity.posX}, ${entity.posY}, ${entity.posZ}")
            println("[PREDICTION DEBUG] Predicted pos: $predictedX, $predictedY, $predictedZ")
        }

        val diff = targetPos.subtract(playerPos)
        val horizontalDistance = sqrt(diff.xCoord * diff.xCoord + diff.zCoord * diff.zCoord)

        // Cálculo simples e direto como LiquidBounce
        val yaw = (atan2(diff.zCoord, diff.xCoord) * 180.0 / PI).toFloat() - 90.0f
        val pitch = (-(atan2(diff.yCoord, horizontalDistance) * 180.0 / PI)).toFloat()

        return Pair(yaw, pitch)
    }
    
    /**
     * Obtém a posição ideal para mirar na entidade (peito/torso)
     */
    private fun getTargetPosition(entity: EntityLivingBase): Vec3 {
        // Mirar no peito do mob (mais alto que o centro, mais baixo que a cabeça)
        val chestHeight = entity.height * 0.75f // 75% da altura = região do peito
        return Vec3(
            entity.posX,
            entity.posY + chestHeight,
            entity.posZ
        )
    }
    
    /**
     * SISTEMA ULTRA-ORGÂNICO: Movimento Humano Natural com Micro-Variações
     */
    private fun applyUltraSmoothRotation() {
        val player = mc.thePlayer ?: return
        val target = currentTarget ?: return

        // ✅ RAYTRACE ULTRA-PRECISO - Só mira com visão direta
        val raytraceRotation = newRaytracedRotationEntity(target, config.attackRange, 0f)
        if (raytraceRotation == null) {
            println("[ULTRA-ORGANIC DEBUG] ❌ Sem visão direta - cancelando mira")
            return
        }

        // ✅ SISTEMA DE IMPERFEIÇÃO HUMANA AVANÇADO
        val currentTime = System.currentTimeMillis()
        val timeSinceAimStart = if (aimStartTime > 0) currentTime - aimStartTime else 0

        // Imperfeição que diminui com o tempo (humanos melhoram a mira gradualmente)
        val imperfectionFactor = (1000f - timeSinceAimStart.coerceAtMost(1000)).toFloat() / 1000f
        val maxOffset = 0.8f * imperfectionFactor + 0.1f // De 0.9° para 0.1° ao longo de 1 segundo

        val yawOffset = (Math.random() * maxOffset * 2f - maxOffset).toFloat()
        val pitchOffset = (Math.random() * maxOffset * 1.5f - maxOffset * 0.75f).toFloat()

        // ✅ MICRO-TREMORES HUMANOS (muito sutis)
        val microTremorYaw = (sin(currentTime * 0.003) * 0.05).toFloat()
        val microTremorPitch = (cos(currentTime * 0.0025) * 0.03).toFloat()

        val targetYaw = raytraceRotation.first + yawOffset + microTremorYaw
        val targetPitch = raytraceRotation.second + pitchOffset + microTremorPitch

        // ✅ VELOCIDADE ORGÂNICA COM VARIAÇÃO TEMPORAL
        val baseSpeed = 0.96f + (sin(currentTime * 0.001) * 0.06).toFloat() // 0.90x - 1.02x
        val speedMultiplier = (Math.random() * 0.08f + baseSpeed).toFloat()

        println("[ULTRA-ORGANIC DEBUG] ✅ Mira orgânica: Offset=±${maxOffset.format(2)}° Tremor=Y${microTremorYaw.format(3)} P${microTremorPitch.format(3)} Speed=${speedMultiplier.format(3)}")

        // ✅ APLICAR MOVIMENTO ULTRA-ORGÂNICO
        aimAtRotationUltraOrganic(targetYaw, targetPitch, speedMultiplier, imperfectionFactor)

        // Marcar como mirando
        if (!isAiming) {
            aimStartTime = System.currentTimeMillis()
            isAiming = true
        }

        // APLICAR STRAFE AUTOMÁTICO para melhor posicionamento
        applyStrafeMovement(target)
    }

    // Extensão para formatação de Float
    private fun Float.format(digits: Int) = "%.${digits}f".format(this)

    /**
     * Normaliza ângulo para o caminho mais curto (-180 a +180)
     */
    private fun wrapAngle(angle: Float): Float {
        var result = angle
        while (result > 180.0f) result -= 360.0f
        while (result < -180.0f) result += 360.0f
        return result
    }

    /**
     * LIQUIDBOUNCE-STYLE SMOOTHING MODES
     */
    private enum class SmoothMode {
        RELATIVE,    // Para grandes diferenças - movimento fluido
        LINEAR,      // Para médias diferenças - transição suave
        SINUSOIDAL,  // Para pequenas diferenças - movimento orgânico
        CUBIC_EASE   // Para micro-ajustes - precisão final
    }

    /**
     * Escolhe o modo de suavização baseado na diferença angular
     */
    private fun chooseSmoothMode(totalDiff: Float): SmoothMode {
        return when {
            totalDiff > 25f -> SmoothMode.RELATIVE
            totalDiff > 10f -> SmoothMode.LINEAR
            totalDiff > 3f -> SmoothMode.SINUSOIDAL
            else -> SmoothMode.CUBIC_EASE
        }
    }

    /**
     * Aplica o modo de suavização selecionado
     */
    private fun applySmoothMode(
        currentYaw: Float, currentPitch: Float,
        targetYaw: Float, targetPitch: Float,
        deltaYaw: Float, deltaPitch: Float,
        totalDiff: Float, mode: SmoothMode
    ): Pair<Float, Float> {

        // ✅ RANDOMIZAÇÃO POR FRAME - Variação de velocidade humana
        val speedMultiplier = (Math.random() * 0.3f + 0.85f).toFloat()

        return when (mode) {
            SmoothMode.RELATIVE -> {
                val factor = ((totalDiff / 40.0f) * speedMultiplier).coerceIn(0.1f, 0.4f)
                Pair(
                    relativeSmoother(currentYaw, targetYaw, factor),
                    relativeSmoother(currentPitch, targetPitch, factor)
                )
            }
            SmoothMode.LINEAR -> {
                val factor = ((totalDiff / 20.0f) * speedMultiplier).coerceIn(0.08f, 0.25f)
                Pair(
                    linearSmoother(currentYaw, targetYaw, factor),
                    linearSmoother(currentPitch, targetPitch, factor)
                )
            }
            SmoothMode.SINUSOIDAL -> {
                val factor = ((totalDiff / 10.0f) * speedMultiplier).coerceIn(0.05f, 0.18f)
                Pair(
                    sinusoidalSmoother(currentYaw, targetYaw, factor),
                    sinusoidalSmoother(currentPitch, targetPitch, factor)
                )
            }
            SmoothMode.CUBIC_EASE -> {
                val factor = ((totalDiff / 5.0f) * speedMultiplier).coerceIn(0.03f, 0.12f)
                Pair(
                    cubicEaseSmoother(currentYaw, targetYaw, factor),
                    cubicEaseSmoother(currentPitch, targetPitch, factor)
                )
            }
        }
    }

    /**
     * RELATIVE SMOOTHER - Baseado no LiquidBounce RotationUtils
     */
    private fun relativeSmoother(current: Float, target: Float, factor: Float): Float {
        val diff = wrapAngle(target - current)
        return current + diff * factor
    }

    /**
     * LINEAR SMOOTHER - Interpolação linear suave
     */
    private fun linearSmoother(current: Float, target: Float, factor: Float): Float {
        val diff = wrapAngle(target - current)
        return current + diff * factor
    }

    /**
     * SINUSOIDAL SMOOTHER - Movimento orgânico baseado em seno
     */
    private fun sinusoidalSmoother(current: Float, target: Float, factor: Float): Float {
        val diff = wrapAngle(target - current)
        val sinFactor = sin(factor * PI.toFloat() / 2f) // Curva senoidal suave
        return current + diff * sinFactor
    }

    /**
     * CUBIC EASE SMOOTHER - Easing cúbico para micro-ajustes
     */
    private fun cubicEaseSmoother(current: Float, target: Float, factor: Float): Float {
        val diff = wrapAngle(target - current)
        val cubicFactor = factor * factor * (3 - 2 * factor) // ease in-out cúbico
        return current + diff * cubicFactor
    }

    /**
     * RAYTRACE ULTRA-PRECISO - Hitbox exata com visão direta (LiquidBounce-style)
     */
    private fun newRaytracedRotationEntity(target: EntityLivingBase, range: Float, throughWallsRange: Float): Pair<Float, Float>? {
        val player = mc.thePlayer ?: return null
        val world = mc.theWorld ?: return null

        // ✅ MÚLTIPLOS PONTOS DA HITBOX - Encontrar o melhor ponto visível
        val targetPoints = listOf(
            // Centro da hitbox (principal)
            Vec3(target.posX, target.posY + target.eyeHeight * 0.85, target.posZ),
            // Peito (backup)
            Vec3(target.posX, target.posY + target.eyeHeight * 0.6, target.posZ),
            // Centro de massa (backup)
            Vec3(target.posX, target.posY + target.height * 0.5, target.posZ)
        )

        val playerEyePos = Vec3(
            player.posX,
            player.posY + player.eyeHeight,
            player.posZ
        )

        // ✅ TESTAR CADA PONTO DA HITBOX
        for (targetPos in targetPoints) {
            // Verificar distância
            val distance = playerEyePos.distanceTo(targetPos)
            if (distance > range) continue

            // ✅ RAYTRACE ULTRA-PRECISO - Verificar visão direta
            val raytraceResult = world.rayTraceBlocks(playerEyePos, targetPos, false, true, false)

            if (raytraceResult != null && raytraceResult.typeOfHit == MovingObjectPosition.MovingObjectType.BLOCK) {
                // Se throughWallsRange > 0, permitir através de paredes em distância menor
                if (throughWallsRange > 0f && distance <= throughWallsRange) {
                    println("[ULTRA-RAYTRACE DEBUG] ⚠️ Parede detectada, mas throughWalls permitido: ${distance} <= ${throughWallsRange}")
                } else {
                    // Tentar próximo ponto da hitbox
                    continue
                }
            }

            // ✅ PONTO VISÍVEL ENCONTRADO - Calcular rotação precisa
            val deltaX = targetPos.xCoord - playerEyePos.xCoord
            val deltaY = targetPos.yCoord - playerEyePos.yCoord
            val deltaZ = targetPos.zCoord - playerEyePos.zCoord

            val horizontalDistance = sqrt(deltaX * deltaX + deltaZ * deltaZ)

            val yaw = (atan2(deltaZ, deltaX) * 180.0 / PI - 90.0).toFloat()
            val pitch = (-(atan2(deltaY, horizontalDistance) * 180.0 / PI)).toFloat()

            println("[ULTRA-RAYTRACE DEBUG] ✅ Ponto visível encontrado: Y=$yaw P=$pitch (dist=$distance)")
            return Pair(yaw, pitch)
        }

        // ❌ Nenhum ponto da hitbox está visível
        println("[ULTRA-RAYTRACE DEBUG] ❌ Nenhum ponto da hitbox visível - alvo completamente bloqueado")
        return null
    }

    /**
     * AIMBOT ULTRA-ORGÂNICO - Movimento Humano Natural com Hesitação e Correções
     */
    private fun aimAtRotationUltraOrganic(targetYaw: Float, targetPitch: Float, speedMultiplier: Float, imperfectionFactor: Float) {
        val player = mc.thePlayer ?: return

        val currentYaw = player.rotationYaw
        val currentPitch = player.rotationPitch

        // Calcular diferenças angulares
        val deltaYaw = wrapAngle(targetYaw - currentYaw)
        val deltaPitch = wrapAngle(targetPitch - currentPitch)
        val totalDiff = sqrt(deltaYaw * deltaYaw + deltaPitch * deltaPitch)

        // ✅ SISTEMA DE HESITAÇÃO HUMANA - Humanos não se movem constantemente
        val currentTime = System.currentTimeMillis()
        val shouldHesitate = (currentTime % 200) < (30 + (Math.random() * 20).toInt()) // 15-25% do tempo hesita

        if (shouldHesitate && totalDiff < 8f) {
            // Micro-pausa humana - não fazer nada por alguns frames
            println("[ULTRA-ORGANIC DEBUG] 🤔 Hesitação humana - pausando movimento")
            return
        }

        // ✅ MOVIMENTO ORGÂNICO COM ACELERAÇÃO/DESACELERAÇÃO NATURAL
        val (newYaw, newPitch) = when {
            totalDiff > 20f -> {
                // Grandes diferenças - Movimento inicial rápido com desaceleração
                val baseFactor = ((totalDiff / 30.0f) * speedMultiplier).coerceIn(0.08f, 0.25f)
                val organicFactor = baseFactor * (1f + imperfectionFactor * 0.3f) // Mais impreciso no início
                Pair(
                    organicSmoother(currentYaw, targetYaw, organicFactor, "FAST"),
                    organicSmoother(currentPitch, targetPitch, organicFactor * 0.8f, "FAST") // Pitch mais lento
                )
            }
            totalDiff > 8f -> {
                // Médias diferenças - Movimento com micro-correções
                val baseFactor = ((totalDiff / 15.0f) * speedMultiplier).coerceIn(0.04f, 0.18f)
                val organicFactor = baseFactor * (0.9f + Math.random().toFloat() * 0.2f) // ±10% variação
                Pair(
                    organicSmoother(currentYaw, targetYaw, organicFactor, "MEDIUM"),
                    organicSmoother(currentPitch, targetPitch, organicFactor * 0.9f, "MEDIUM")
                )
            }
            totalDiff > 2f -> {
                // Pequenas diferenças - Movimento de ajuste fino com tremores
                val baseFactor = ((totalDiff / 8.0f) * speedMultiplier).coerceIn(0.02f, 0.12f)
                val organicFactor = baseFactor * (0.8f + Math.random().toFloat() * 0.4f) // ±20% variação
                Pair(
                    organicSmoother(currentYaw, targetYaw, organicFactor, "FINE"),
                    organicSmoother(currentPitch, targetPitch, organicFactor * 0.7f, "FINE")
                )
            }
            else -> {
                // Micro-diferenças - Movimento ultra-preciso com micro-tremores
                val baseFactor = ((totalDiff / 4.0f) * speedMultiplier).coerceIn(0.01f, 0.06f)
                val organicFactor = baseFactor * (0.7f + Math.random().toFloat() * 0.6f) // ±30% variação
                Pair(
                    organicSmoother(currentYaw, targetYaw, organicFactor, "MICRO"),
                    organicSmoother(currentPitch, targetPitch, organicFactor * 0.6f, "MICRO")
                )
            }
        }

        // ✅ SINCRONIZAÇÃO ORGÂNICA - Com micro-atraso natural
        player.rotationYaw = newYaw
        player.prevRotationYaw = newYaw

        player.rotationPitch = newPitch
        player.prevRotationPitch = newPitch

        println("[ULTRA-ORGANIC DEBUG] ✅ Movimento orgânico: Y=${newYaw.format(2)} P=${newPitch.format(2)} (diff=${totalDiff.format(2)}, imperfeição=${(imperfectionFactor*100).format(0)}%)")
    }

    /**
     * SUAVIZAÇÃO ORGÂNICA - Simula movimento humano real com imperfeições
     */
    private fun organicSmoother(current: Float, target: Float, factor: Float, mode: String): Float {
        val diff = wrapAngle(target - current)
        val currentTime = System.currentTimeMillis()

        // ✅ DIFERENTES TIPOS DE MOVIMENTO HUMANO
        val organicFactor = when (mode) {
            "FAST" -> {
                // Movimento rápido inicial com desaceleração natural
                val baseFactor = factor * factor * (3 - 2 * factor) // Cubic ease
                val deceleration = 1f - (abs(diff) / 45f).coerceIn(0f, 0.3f) // Desacelera perto do alvo
                baseFactor * deceleration
            }
            "MEDIUM" -> {
                // Movimento com micro-correções e hesitações
                val baseFactor = sin(factor * PI.toFloat() / 2f) // Sinusoidal
                val microCorrection = 1f + sin(currentTime * 0.01f) * 0.1f // ±10% variação
                baseFactor * microCorrection
            }
            "FINE" -> {
                // Movimento de ajuste fino com tremores naturais
                val baseFactor = factor * factor * factor * (factor * (factor * 6 - 15) + 10) // Quintic
                val naturalTremor = 1f + sin(currentTime * 0.02f) * 0.05f // ±5% tremor
                baseFactor * naturalTremor
            }
            "MICRO" -> {
                // Movimento ultra-preciso com micro-variações
                val baseFactor = factor * (2f - factor) // Ease-out
                val microVariation = 1f + (Math.random().toFloat() - 0.5f) * 0.1f // ±5% aleatório
                baseFactor * microVariation
            }
            else -> factor
        }

        // ✅ APLICAR LIMITAÇÃO HUMANA - Humanos não fazem movimentos perfeitos
        val humanLimitation = when {
            abs(diff) > 30f -> 0.95f // Movimento grande - 95% eficiência
            abs(diff) > 10f -> 0.98f // Movimento médio - 98% eficiência
            abs(diff) > 3f -> 0.99f  // Movimento pequeno - 99% eficiência
            else -> 1.0f             // Micro-movimento - 100% eficiência
        }

        val finalFactor = (organicFactor * humanLimitation).coerceIn(0.001f, 0.4f)
        return current + diff * finalFactor
    }

    /**
     * ULTRA-SMOOTH INTERPOLATE - Interpolação ultra-fluida para grandes movimentos
     */
    private fun ultraSmoothInterpolate(current: Float, target: Float, factor: Float): Float {
        val diff = wrapAngle(target - current)
        // Combina easing cúbico com suavização senoidal
        val cubicFactor = factor * factor * (3 - 2 * factor)
        val sinFactor = sin(cubicFactor * PI.toFloat() / 2f)
        return current + diff * sinFactor
    }

    /**
     * ULTRA-PRECISION SMOOTHER - Suavização ultra-precisa para micro-ajustes
     */
    private fun ultraPrecisionSmoother(current: Float, target: Float, factor: Float): Float {
        val diff = wrapAngle(target - current)
        // Easing quíntico para movimento ultra-suave
        val quinticFactor = factor * factor * factor * (factor * (factor * 6 - 15) + 10)
        return current + diff * quinticFactor
    }

    /**
     * Aplica movimento de strafe automático para melhor posicionamento
     */
    private fun applyStrafeMovement(target: EntityLivingBase) {
        val player = mc.thePlayer ?: return

        // Calcular distância para o alvo
        val distance = player.getDistanceToEntity(target)

        // Se muito próximo (< 2 blocos), aplicar strafe para manter distância ideal
        if (distance < 2.0f) {
            val strafeDirection = if (System.currentTimeMillis() % 2000 < 1000) 1 else -1

            // Aplicar movimento de strafe baseado na direção do player
            val yaw = Math.toRadians(player.rotationYaw.toDouble())
            val strafeYaw = yaw + (Math.PI / 2.0) * strafeDirection

            val strafeSpeed = 0.3f
            val motionX = -kotlin.math.sin(strafeYaw) * strafeSpeed
            val motionZ = kotlin.math.cos(strafeYaw) * strafeSpeed

            player.motionX = motionX
            player.motionZ = motionZ

            println("[STRAFE DEBUG] Aplicando strafe: direção=$strafeDirection, distância=$distance")
        }
    }

    /**
     * Aplica movimento real do mouse (MUITO MAIS RÁPIDO)
     */
    private fun applyRealMouseMovement(yawDiff: Float, pitchDiff: Float, distance: Float) {
        // Sensibilidade MUITO mais alta para movimento efetivo
        val baseSensitivity = 1.2f // Sensibilidade base muito alta
        val distanceMultiplier = when {
            distance > 4.0f -> 3.5f  // Alvos distantes - movimento muito rápido
            distance > 2.0f -> 2.8f  // Alvos médios - rápido
            else -> 2.0f             // Alvos próximos - ainda rápido
        }

        // Calcular movimento do mouse com sensibilidade muito alta
        val mouseX = yawDiff * baseSensitivity * distanceMultiplier
        val mouseY = pitchDiff * baseSensitivity * distanceMultiplier

        // Limitar movimento máximo por frame (muito maior)
        val maxMove = 80.0f
        val clampedX = mouseX.coerceIn(-maxMove, maxMove)
        val clampedY = mouseY.coerceIn(-maxMove, maxMove)

        println("[AIMBOT DEBUG] FAST Real mouse: X=$clampedX Y=$clampedY (dist=$distance)")

        // Aplicar movimento do mouse sempre (sem threshold mínimo)
        if (abs(clampedX) > 0.1f || abs(clampedY) > 0.1f) {
            mc.thePlayer.setAngles(clampedX, clampedY)
        }
    }

    /**
     * Aplica rotação direta (mais rápida que a versão anterior)
     */
    private fun applyDirectRotation(yawDiff: Float, pitchDiff: Float, distance: Float) {
        val player = mc.thePlayer

        // Velocidade MUITO mais agressiva baseada na urgência
        val speedMultiplier = when {
            distance > 4.0f -> 1.8f  // Alvos distantes - movimento muito rápido
            abs(yawDiff) > 45.0f || abs(pitchDiff) > 30.0f -> 1.5f  // Muito desalinhado - rápido
            abs(yawDiff) > 15.0f || abs(pitchDiff) > 15.0f -> 1.2f  // Moderadamente desalinhado
            else -> 0.8f  // Ajuste fino - ainda rápido
        }

        // Calcular movimento final com velocidade muito alta
        val yawMove = yawDiff * speedMultiplier
        val pitchMove = pitchDiff * speedMultiplier

        // Limitar movimento máximo por frame (muito maior)
        val maxMove = 90.0f  // Movimento máximo muito aumentado
        val clampedYawMove = yawMove.coerceIn(-maxMove, maxMove)
        val clampedPitchMove = pitchMove.coerceIn(-maxMove, maxMove)

        println("[AIMBOT DEBUG] Direct rotation: Y=$clampedYawMove P=$clampedPitchMove (speed=$speedMultiplier)")

        // Aplicar rotação
        player.rotationYaw = normalizeAngle(player.rotationYaw + clampedYawMove)
        player.rotationPitch = MathHelper.clamp_float(player.rotationPitch + clampedPitchMove, -90.0f, 90.0f)
    }
    
    /**
     * Aplica damping quando não há alvo
     */
    private fun applyDamping() {
        val currentTime = System.currentTimeMillis()
        val deltaTime = if (lastUpdateTime == 0L || currentTime - lastUpdateTime > 100) {
            0.016f
        } else {
            (currentTime - lastUpdateTime) / 1000.0f
        }
        
        // Aplicar damping simples às velocidades
        yawVelocity = yawVelocity * 0.95f
        pitchVelocity = pitchVelocity * 0.95f
        
        lastUpdateTime = currentTime
    }
    
    /**
     * Normaliza um ângulo para o intervalo [-180, 180]
     */
    private fun normalizeAngle(angle: Float): Float {
        var normalized = angle
        while (normalized > 180.0f) normalized -= 360.0f
        while (normalized < -180.0f) normalized += 360.0f
        return normalized
    }
    
    /**
     * Verifica se pode atacar - ULTRA-PRECISO com raytrace
     */
    private fun canAttack(): Boolean {
        val currentTime = System.currentTimeMillis()
        val timeSinceLastAttack = currentTime - lastAttackTime

        println("[ULTRA-ATTACK DEBUG] === VERIFICAÇÃO ULTRA-PRECISA ===")

        if (timeSinceLastAttack < config.attackDelay) {
            println("[ULTRA-ATTACK DEBUG] ❌ Cooldown ativo: ${timeSinceLastAttack}ms < ${config.attackDelay}ms")
            return false
        }

        val target = currentTarget
        if (target == null) {
            println("[ULTRA-ATTACK DEBUG] ❌ Sem alvo")
            return false
        }

        val player = mc.thePlayer
        if (player == null) {
            println("[ULTRA-ATTACK DEBUG] ❌ Sem player")
            return false
        }

        // Verificar distância primeiro
        val distance = player.getDistanceToEntity(target)
        if (distance > config.attackRange) {
            println("[ULTRA-ATTACK DEBUG] ❌ Muito longe: $distance > ${config.attackRange}")
            return false
        }

        // ✅ VERIFICAÇÃO ULTRA-PRECISA - Usar raytrace para confirmar visão direta
        val raytraceRotation = newRaytracedRotationEntity(target, config.attackRange, 0f)
        if (raytraceRotation == null) {
            println("[ULTRA-ATTACK DEBUG] ❌ Sem visão direta - parede bloqueando")
            return false
        }

        // ✅ VERIFICAR SE A CÂMERA ESTÁ REALMENTE APONTANDO PARA O MOB
        val currentYaw = normalizeAngle(player.rotationYaw)
        val currentPitch = player.rotationPitch
        val targetYaw = normalizeAngle(raytraceRotation.first)
        val targetPitch = MathHelper.clamp_float(raytraceRotation.second, -90.0f, 90.0f)

        val yawDiff = abs(normalizeAngle(targetYaw - currentYaw))
        val pitchDiff = abs(targetPitch - currentPitch)
        val totalDiff = sqrt(yawDiff * yawDiff + pitchDiff * pitchDiff)

        // ✅ FOV ULTRA-RESTRITIVO - Só ataca se estiver REALMENTE mirando
        val maxAngleDiff = (config.fov * 0.5f) // 50% do FOV configurado - MUITO mais restritivo
        val isAimedProperly = totalDiff <= maxAngleDiff

        println("[ULTRA-ATTACK DEBUG] Verificação de mira:")
        println("[ULTRA-ATTACK DEBUG]   Atual: Y=$currentYaw P=$currentPitch")
        println("[ULTRA-ATTACK DEBUG]   Alvo:  Y=$targetYaw P=$targetPitch")
        println("[ULTRA-ATTACK DEBUG]   Diff:  Y=$yawDiff P=$pitchDiff Total=$totalDiff")
        println("[ULTRA-ATTACK DEBUG]   Max:   $maxAngleDiff (50% do FOV)")
        println("[ULTRA-ATTACK DEBUG]   Mirando: $isAimedProperly")

        if (!isAimedProperly) {
            println("[ULTRA-ATTACK DEBUG] ❌ Mira imprecisa - ângulo muito grande")
            return false
        }

        println("[ULTRA-ATTACK DEBUG] ✅ PODE ATACAR - Todas condições atendidas")
        println("[ULTRA-ATTACK DEBUG] =====================================")

        return true
    }
    
    /**
     * Ataca a entidade - ULTRA-PRECISO com raytrace
     */
    private fun attack(target: EntityLivingBase) {
        val player = mc.thePlayer ?: return

        // Verificação dupla de distância
        val distance = player.getDistanceToEntity(target)
        if (distance > config.attackRange) {
            println("[ULTRA-ATTACK DEBUG] ❌ Ataque cancelado - muito longe: $distance")
            return
        }

        // ✅ VERIFICAÇÃO ULTRA-PRECISA - Usar raytrace para confirmar visão direta
        val raytraceRotation = newRaytracedRotationEntity(target, config.attackRange, 0f)
        if (raytraceRotation == null) {
            println("[ULTRA-ATTACK DEBUG] ❌ Ataque cancelado - sem visão direta")
            return
        }

        // ✅ VERIFICAR SE A CÂMERA ESTÁ REALMENTE APONTANDO PARA O MOB
        val currentYaw = normalizeAngle(player.rotationYaw)
        val currentPitch = player.rotationPitch
        val targetYaw = normalizeAngle(raytraceRotation.first)
        val targetPitch = MathHelper.clamp_float(raytraceRotation.second, -90.0f, 90.0f)

        val yawDiff = abs(normalizeAngle(targetYaw - currentYaw))
        val pitchDiff = abs(targetPitch - currentPitch)
        val totalDiff = sqrt(yawDiff * yawDiff + pitchDiff * pitchDiff)

        // ✅ FOV ULTRA-RESTRITIVO - Mesma verificação do canAttack()
        val maxAngleDiff = (config.fov * 0.5f) // 50% do FOV configurado
        if (totalDiff > maxAngleDiff) {
            println("[ULTRA-ATTACK DEBUG] ❌ Ataque cancelado - mira imprecisa: $totalDiff > $maxAngleDiff")
            return
        }

        println("[ULTRA-ATTACK DEBUG] ✅ ATAQUE AUTORIZADO - Precisão perfeita: $totalDiff <= $maxAngleDiff")

        // Atacar apenas se tudo estiver correto
        mc.playerController.attackEntity(player, target)
        player.swingItem()
        lastAttackTime = System.currentTimeMillis()

        println("[ULTRA-ATTACK DEBUG] ✅ Ataque executado! Distância: $distance, Precisão: $totalDiff")
    }
    
    /**
     * Obtém o alvo atual
     */
    fun getCurrentTarget(): EntityLivingBase? = currentTarget
    
    /**
     * Verifica se está rotacionando
     */
    fun isRotating(): Boolean = isRotating

    /**
     * Sistema de suavização DINÂMICA baseado no LiquidBounce com velocidade adaptativa
     */
    private fun applyLiquidBounceSmoothing(yawDiff: Float, pitchDiff: Float) {
        val player = mc.thePlayer ?: return
        val target = currentTarget ?: return

        // DETECTAR SE O ALVO ESTÁ EM MOVIMENTO
        val targetVelocity = sqrt(target.motionX * target.motionX + target.motionZ * target.motionZ).toFloat()
        val isTargetMoving = targetVelocity > 0.1f

        // CALCULAR VELOCIDADE DINÂMICA BASEADA NO MOVIMENTO E DIFERENÇA ANGULAR
        val totalAngleDiff = sqrt(yawDiff * yawDiff + pitchDiff * pitchDiff)

        // VELOCIDADE ULTRA ALTA - o bot estava muito lento
        val baseHorizontalSpeed = if (isTargetMoving) 1.0f else 0.95f // MÁXIMA velocidade
        val baseVerticalSpeed = if (isTargetMoving) 1.0f else 0.9f    // MÁXIMA velocidade

        // MULTIPLICADOR ULTRA ALTO PARA GRANDES DIFERENÇAS ANGULARES
        val speedMultiplier = when {
            totalAngleDiff > 15.0f -> 4.0f  // Muito desalinhado - velocidade 4.0x (era 2.5x)
            totalAngleDiff > 8.0f -> 3.0f   // Moderadamente desalinhado - velocidade 3.0x (era 1.8x)
            totalAngleDiff > 3.0f -> 2.0f   // Ligeiramente desalinhado - velocidade 2.0x (era 1.3x)
            else -> 1.5f                    // Bem alinhado - velocidade 1.5x (era 1.0x)
        }

        // APLICAR VELOCIDADE DINÂMICA
        val dynamicHorizontalSpeed = (baseHorizontalSpeed * speedMultiplier).coerceAtMost(1.0f)
        val dynamicVerticalSpeed = (baseVerticalSpeed * speedMultiplier).coerceAtMost(1.0f)

        // DEBUG: Log da velocidade dinâmica
        println("[SMOOTH DEBUG] Target moving: $isTargetMoving (vel: $targetVelocity)")
        println("[SMOOTH DEBUG] Angle diff: $totalAngleDiff, Speed mult: $speedMultiplier")
        println("[SMOOTH DEBUG] Dynamic speeds: H=$dynamicHorizontalSpeed V=$dynamicVerticalSpeed")

        // Calcular fatores de suavização baseados na diferença angular
        val yawFactor = calculateSmoothingFactor(abs(yawDiff), dynamicHorizontalSpeed)
        val pitchFactor = calculateSmoothingFactor(abs(pitchDiff), dynamicVerticalSpeed)

        // Aplicar movimento suave
        val smoothYaw = yawDiff * yawFactor
        val smoothPitch = pitchDiff * pitchFactor

        // Aplicar rotação
        player.rotationYaw += smoothYaw
        player.rotationPitch += smoothPitch

        // Limitar pitch
        player.rotationPitch = player.rotationPitch.coerceIn(-90.0f, 90.0f)

        println("[SMOOTH DEBUG] Final movement: Y=$smoothYaw P=$smoothPitch")
    }

    /**
     * Calcula fator de suavização ULTRA AGRESSIVO - movimento rápido
     */
    private fun calculateSmoothingFactor(angleDiff: Float, speed: Float): Float {
        // Normalizar diferença angular (0-1)
        val t = (angleDiff / 180f).coerceIn(0f, 1f)

        // Função sigmoid MUITO MAIS AGRESSIVA para movimento rápido
        val steepness = 15f // Aumentado de 6f para 15f - curva muito mais agressiva
        val midpoint = 0.2f // Reduzido de 0.4f para 0.2f - transição mais rápida
        val sigmoid = 1f / (1f + kotlin.math.exp(-steepness * (t - midpoint)))

        return sigmoid * speed
    }
}
