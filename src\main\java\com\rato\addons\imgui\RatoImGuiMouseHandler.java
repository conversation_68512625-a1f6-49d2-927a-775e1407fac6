package com.rato.addons.imgui;

import com.rato.addons.util.Logger;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.ScaledResolution;
import net.minecraftforge.client.event.MouseEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import org.lwjgl.input.Mouse;

/**
 * Handler para eventos de mouse do menu overlay
 */
public class RatoImGuiMouseHandler {
    
    private static final Minecraft mc = Minecraft.getMinecraft();
    private static RatoImGuiMouseHandler instance;
    
    public static RatoImGuiMouseHandler getInstance() {
        if (instance == null) {
            instance = new RatoImGuiMouseHandler();
        }
        return instance;
    }
    
    @SubscribeEvent
    public void onMouseEvent(MouseEvent event) {
        // Só processar se o menu estiver visível
        if (!RatoImGuiRenderer.isVisible()) {
            return;
        }
        
        // Só processar cliques do botão esquerdo
        if (event.button != 0 || !event.buttonstate) {
            return;
        }
        
        try {
            // Cancelar o evento para que não interfira com o jogo
            event.setCanceled(true);
            
            // Processar o clique no menu
            processMenuClick();
            
        } catch (Exception e) {
            Logger.sendLog("❌ Erro no mouse handler: " + e.getMessage());
        }
    }
    
    private void processMenuClick() {
        try {
            ScaledResolution sr = new ScaledResolution(mc);
            int mouseX = Mouse.getX() * sr.getScaledWidth() / mc.displayWidth;
            int mouseY = sr.getScaledHeight() - Mouse.getY() * sr.getScaledHeight() / mc.displayHeight - 1;
            
            Logger.sendLog("🖱️ Menu click at: " + mouseX + ", " + mouseY);
            
            // Calcular posição da janela
            int totalWidth = RatoImGuiRenderer.SIDEBAR_WIDTH + RatoImGuiRenderer.MAIN_PANEL_WIDTH;
            int windowX = (sr.getScaledWidth() - totalWidth) / 2;
            int windowY = (sr.getScaledHeight() - RatoImGuiRenderer.WINDOW_HEIGHT) / 2;
            
            // Verificar se o clique está dentro da janela
            if (mouseX >= windowX && mouseX <= windowX + totalWidth && 
                mouseY >= windowY && mouseY <= windowY + RatoImGuiRenderer.WINDOW_HEIGHT) {
                
                Logger.sendLog("✅ Click inside menu window");
                
                // Processar clique na sidebar
                if (mouseX >= windowX && mouseX <= windowX + RatoImGuiRenderer.SIDEBAR_WIDTH) {
                    handleSidebarClick(mouseX - windowX, mouseY - windowY);
                }
                
                // Processar clique no main panel
                if (mouseX >= windowX + RatoImGuiRenderer.SIDEBAR_WIDTH && mouseX <= windowX + totalWidth) {
                    handleMainPanelClick(mouseX - windowX - RatoImGuiRenderer.SIDEBAR_WIDTH, mouseY - windowY);
                }
            }
            
        } catch (Exception e) {
            Logger.sendLog("❌ Erro ao processar clique do menu: " + e.getMessage());
        }
    }
    
    private void handleSidebarClick(int relativeX, int relativeY) {
        Logger.sendLog("🎯 Sidebar click at: " + relativeX + ", " + relativeY);
        
        // Verificar cliques nas categorias
        int currentY = 50; // Posição inicial das categorias
        String[] categories = {"Combat", "Movement", "Visual", "Player", "World", "Misc"};
        
        for (String category : categories) {
            if (relativeY >= currentY && relativeY <= currentY + 25) {
                Logger.sendLog("📂 Category selected: " + category);
                RatoImGuiRenderer.getInstance().setSelectedCategory(category);
                break;
            }
            currentY += 30;
        }
    }
    
    private void handleMainPanelClick(int relativeX, int relativeY) {
        Logger.sendLog("🎯 Main panel click at: " + relativeX + ", " + relativeY);
        
        // Verificar cliques nos botões de features
        int currentY = 55; // Posição inicial dos botões
        String selectedCategory = RatoImGuiRenderer.getInstance().getSelectedCategory();
        
        if ("Combat".equals(selectedCategory)) {
            // Verificar cliques nos botões de combat
            if (relativeY >= currentY && relativeY <= currentY + 25) {
                Logger.sendLog("⚔️ Aimbot toggled");
                RatoImGuiRenderer.getInstance().toggleAimbot();
            }
            currentY += 37;
            
            if (relativeY >= currentY && relativeY <= currentY + 25) {
                Logger.sendLog("⚔️ KillAura toggled");
                RatoImGuiRenderer.getInstance().toggleKillAura();
            }
        }
        // Adicionar mais categorias conforme necessário
    }
}
