package com.rato.addons.failsafe;

import com.rato.addons.config.RatoAddonsConfigSimple;
import com.rato.addons.util.Logger;

public class PlayerCheckFailsafe extends Failsafe {
    
    private PlayerCheckState state = PlayerCheckState.NONE;
    
    @Override
    public int getPriority() {
        return 1;
    }
    
    @Override
    public FailsafeManager.EmergencyType getType() {
        return FailsafeManager.EmergencyType.PLAYER_CHECK;
    }
    
    @Override
    public boolean shouldSendNotification() {
        return RatoAddonsConfigSimple.discordWebhook;
    }
    
    @Override
    public boolean shouldPlaySound() {
        return RatoAddonsConfigSimple.soundAlert;
    }
    
    @Override
    public void duringFailsafeTrigger() {
        switch (state) {
            case NONE:
                FailsafeManager.getInstance().scheduleRandomDelay(200, 400);
                state = PlayerCheckState.WAIT_BEFORE_START;
                break;
                
            case WAIT_BEFORE_START:
                stopMovement();
                state = PlayerCheckState.LOOK_AROUND;
                FailsafeManager.getInstance().scheduleRandomDelay(300, 500);
                break;
                
            case LOOK_AROUND:
                performHumanLikeMovement();
                state = PlayerCheckState.END;
                FailsafeManager.getInstance().scheduleRandomDelay(400, 700);
                break;
                
            case END:
                endOfFailsafeTrigger();
                break;
        }
    }
    
    @Override
    public void endOfFailsafeTrigger() {
        Logger.sendMessage("§a✓ Player check handled");
        FailsafeManager.getInstance().stopFailsafes();
        FailsafeManager.getInstance().scheduleDelay(1000);
    }
    
    @Override
    public void resetStates() {
        state = PlayerCheckState.NONE;
    }
    
    private enum PlayerCheckState {
        NONE,
        WAIT_BEFORE_START,
        LOOK_AROUND,
        END
    }
}
