package com.rato.addons.pathfinding;

import net.minecraft.client.Minecraft;
import net.minecraft.util.Vec3;
import net.minecraft.util.BlockPos;
import net.minecraft.block.Block;
import net.minecraft.init.Blocks;

import java.util.*;

/**
 * Sistema avançado de pathfinding baseado em A* com NavMesh otimizado
 * Implementa seleção inteligente de blocos e pathfinding profissional
 */
public class AdvancedPathfinder {
    
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Configurações de custo otimizadas
    private static final double COST_WALK = 1.0;
    private static final double COST_JUMP = 1.5; // Penalidade por pulo (mais tempo)
    private static final double COST_FALL = 1.2; // Penalidade por queda
    private static final double COST_DIAGONAL = 1.414; // sqrt(2)
    private static final double COST_SOUL_SAND = 2.0; // Penalidade por terreno lento
    private static final double COST_WATER = 15.0; // PENALIDADE MASSIVA por água
    private static final double COST_WATER_EDGE = 8.0; // Penalidade por borda da água
    
    // Limites de performance
    private static final int MAX_SEARCH_NODES = 3000;
    private static final double WAYPOINT_THRESHOLD = 1.5;
    
    /**
     * Nó do grafo de navegação otimizado
     */
    private static class NavNode implements Comparable<NavNode> {
        public final BlockPos position;
        public final double gCost; // Custo real do início
        public final double hCost; // Heurística até o destino
        public final double fCost; // gCost + hCost
        public final NavNode parent;
        public final MovementType movementType;
        
        public NavNode(BlockPos position, double gCost, double hCost, NavNode parent, MovementType movementType) {
            this.position = position;
            this.gCost = gCost;
            this.hCost = hCost;
            this.fCost = gCost + hCost;
            this.parent = parent;
            this.movementType = movementType;
        }
        
        @Override
        public int compareTo(NavNode other) {
            int result = Double.compare(this.fCost, other.fCost);
            if (result == 0) {
                result = Double.compare(this.hCost, other.hCost); // Tie-breaker
            }
            return result;
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (!(obj instanceof NavNode)) return false;
            NavNode other = (NavNode) obj;
            return position.equals(other.position);
        }
        
        @Override
        public int hashCode() {
            return position.hashCode();
        }
    }
    
    /**
     * Tipos de movimento com custos específicos
     */
    private enum MovementType {
        WALK(COST_WALK),
        JUMP(COST_JUMP),
        FALL(COST_FALL),
        DIAGONAL(COST_DIAGONAL),
        SLOW_TERRAIN(COST_SOUL_SAND),
        WATER(COST_WATER);
        
        private final double baseCost;
        
        MovementType(double baseCost) {
            this.baseCost = baseCost;
        }
        
        public double getBaseCost() { return baseCost; }
    }
    
    /**
     * Encontra o melhor caminho usando A* otimizado
     */
    public List<Vec3> findOptimalPath(Vec3 start, Vec3 goal) {
        if (mc.theWorld == null) return new ArrayList<>();
        
        BlockPos startPos = findNearestWalkablePosition(new BlockPos(start));
        BlockPos goalPos = findNearestWalkablePosition(new BlockPos(goal));
        
        if (startPos == null || goalPos == null) {
            return Arrays.asList(start, goal); // Fallback para linha reta
        }
        
        // Estruturas de dados otimizadas do A*
        PriorityQueue<NavNode> openSet = new PriorityQueue<>();
        Set<BlockPos> closedSet = new HashSet<>();
        Map<BlockPos, NavNode> allNodes = new HashMap<>();
        
        // Nó inicial
        NavNode startNode = new NavNode(startPos, 0, manhattanDistance(startPos, goalPos), null, MovementType.WALK);
        openSet.add(startNode);
        allNodes.put(startPos, startNode);
        
        int nodesExplored = 0;
        
        while (!openSet.isEmpty() && nodesExplored < MAX_SEARCH_NODES) {
            NavNode current = openSet.poll();
            nodesExplored++;
            
            // Chegou ao destino
            if (current.position.equals(goalPos)) {
                List<Vec3> rawPath = reconstructPath(current);
                return optimizePathWithStringPulling(rawPath);
            }
            
            closedSet.add(current.position);
            
            // Explorar todos os tipos de movimento
            for (MovementCandidate candidate : generateMovementCandidates(current.position)) {
                if (closedSet.contains(candidate.targetPos)) continue;
                
                double movementCost = calculateMovementCost(current.position, candidate.targetPos, candidate.movementType);
                if (movementCost == Double.POSITIVE_INFINITY) continue; // Movimento inválido
                
                double tentativeGCost = current.gCost + movementCost;
                
                NavNode existingNode = allNodes.get(candidate.targetPos);
                if (existingNode != null && tentativeGCost >= existingNode.gCost) {
                    continue; // Caminho pior
                }
                
                // Criar novo nó
                double hCost = manhattanDistance(candidate.targetPos, goalPos);
                NavNode neighborNode = new NavNode(candidate.targetPos, tentativeGCost, hCost, current, candidate.movementType);
                
                allNodes.put(candidate.targetPos, neighborNode);
                openSet.add(neighborNode);
            }
        }
        
        // Caminho não encontrado - retornar linha reta
        return Arrays.asList(start, goal);
    }
    
    /**
     * Gera candidatos de movimento para todas as direções
     */
    private List<MovementCandidate> generateMovementCandidates(BlockPos from) {
        List<MovementCandidate> candidates = new ArrayList<>();
        
        // Movimentos cardinais (N, S, E, W)
        int[][] cardinalDirections = {{1,0,0}, {-1,0,0}, {0,0,1}, {0,0,-1}};
        for (int[] dir : cardinalDirections) {
            BlockPos target = from.add(dir[0], dir[1], dir[2]);
            candidates.add(new MovementCandidate(target, MovementType.WALK));
            
            // Também considerar pulos
            candidates.add(new MovementCandidate(target.up(), MovementType.JUMP));
            
            // E quedas
            candidates.add(new MovementCandidate(target.down(), MovementType.FALL));
        }
        
        // Movimentos diagonais
        int[][] diagonalDirections = {{1,0,1}, {1,0,-1}, {-1,0,1}, {-1,0,-1}};
        for (int[] dir : diagonalDirections) {
            BlockPos target = from.add(dir[0], dir[1], dir[2]);
            candidates.add(new MovementCandidate(target, MovementType.DIAGONAL));
        }
        
        return candidates;
    }
    
    /**
     * Calcula custo de movimento considerando terreno e física
     */
    private double calculateMovementCost(BlockPos from, BlockPos to, MovementType movementType) {
        if (!isValidMovement(from, to, movementType)) {
            return Double.POSITIVE_INFINITY;
        }

        double baseCost = movementType.getBaseCost();
        double distance = Math.sqrt(from.distanceSq(to));

        // PENALIDADES MASSIVAS PARA ÁGUA
        Block blockAtTo = mc.theWorld.getBlockState(to).getBlock();
        Block groundBlock = mc.theWorld.getBlockState(to.down()).getBlock();

        if (blockAtTo == Blocks.water || blockAtTo == Blocks.flowing_water) {
            baseCost *= COST_WATER; // Penalidade massiva por estar na água
        } else if (groundBlock == Blocks.water || groundBlock == Blocks.flowing_water) {
            baseCost *= COST_WATER_EDGE; // Penalidade por borda da água
        } else if (groundBlock == Blocks.soul_sand) {
            baseCost *= 2.0; // Penalidade por soul sand
        }

        // Verificar água no caminho (não apenas destino)
        if (hasWaterInPath(from, to)) {
            baseCost *= 5.0; // Penalidade adicional por atravessar água
        }

        // Penalidade adicional para quedas altas
        if (movementType == MovementType.FALL) {
            int fallHeight = from.getY() - to.getY();
            if (fallHeight > 3) {
                baseCost *= (1.0 + fallHeight * 0.5); // Penalidade crescente
            }
        }

        return baseCost * distance;
    }

    /**
     * Verifica se há água no caminho entre dois pontos
     */
    private boolean hasWaterInPath(BlockPos from, BlockPos to) {
        Vec3 fromVec = new Vec3(from.getX() + 0.5, from.getY(), from.getZ() + 0.5);
        Vec3 toVec = new Vec3(to.getX() + 0.5, to.getY(), to.getZ() + 0.5);
        Vec3 direction = toVec.subtract(fromVec);
        double distance = direction.lengthVector();
        int steps = Math.max(1, (int)(distance * 2));

        for (int i = 0; i <= steps; i++) {
            double t = (double) i / steps;
            Vec3 scaledDirection = new Vec3(direction.xCoord * t, direction.yCoord * t, direction.zCoord * t);
            Vec3 currentPos = fromVec.add(scaledDirection);
            BlockPos checkPos = new BlockPos(currentPos);

            Block block = mc.theWorld.getBlockState(checkPos).getBlock();
            if (block == Blocks.water || block == Blocks.flowing_water) {
                return true;
            }
        }

        return false;
    }
    
    /**
     * Valida se um movimento é fisicamente possível com simulação AABB
     */
    private boolean isValidMovement(BlockPos from, BlockPos to, MovementType movementType) {
        // Verificar se a posição de destino é caminhável
        if (!isWalkablePosition(to)) {
            return false;
        }

        // SIMULAÇÃO AABB - verificar se o player pode realmente fazer este movimento
        if (!simulateAABBMovement(from, to, movementType)) {
            return false;
        }

        // Validações específicas por tipo de movimento
        switch (movementType) {
            case JUMP:
                // Verificar se há espaço para pular
                return hasJumpClearance(from) && hasLandingSpace(to) && canPhysicallyJump(from, to);

            case FALL:
                // Verificar se a queda não é muito alta
                int fallHeight = from.getY() - to.getY();
                return fallHeight <= 10; // Máximo 10 blocos de queda

            case DIAGONAL:
                // Verificar se não há obstáculos nos cantos
                return !hasCornerObstacle(from, to);

            case WATER:
                // Água só é válida se for absolutamente necessária
                return isWaterMovementNecessary(from, to);

            default:
                return true;
        }
    }
    
    /**
     * Verifica se uma posição é caminhável (espaço livre + chão sólido)
     */
    private boolean isWalkablePosition(BlockPos pos) {
        if (mc.theWorld == null) return false;

        Block blockAtPos = mc.theWorld.getBlockState(pos).getBlock();
        Block blockAbove = mc.theWorld.getBlockState(pos.up()).getBlock();
        Block blockBelow = mc.theWorld.getBlockState(pos.down()).getBlock();

        // Verificar se há espaço para o player (2 blocos de altura)
        boolean hasSpace = (blockAtPos == Blocks.air || blockAtPos == Blocks.leaves || !blockAtPos.isFullBlock()) &&
                          (blockAbove == Blocks.air || blockAbove == Blocks.leaves || !blockAbove.isFullBlock());

        // Verificar se há chão sólido embaixo (excluir líquidos perigosos)
        boolean hasSolidGround = blockBelow.isFullBlock() &&
                               blockBelow != Blocks.water &&
                               blockBelow != Blocks.flowing_water &&
                               blockBelow != Blocks.lava &&
                               blockBelow != Blocks.flowing_lava &&
                               blockBelow.getMaterial().blocksMovement();

        return hasSpace && hasSolidGround;
    }
    
    /**
     * Encontra posição caminhável mais próxima
     */
    private BlockPos findNearestWalkablePosition(BlockPos center) {
        if (isWalkablePosition(center)) return center;
        
        for (int radius = 1; radius <= 5; radius++) {
            for (int x = -radius; x <= radius; x++) {
                for (int z = -radius; z <= radius; z++) {
                    for (int y = -2; y <= 2; y++) {
                        BlockPos candidate = center.add(x, y, z);
                        if (isWalkablePosition(candidate)) {
                            return candidate;
                        }
                    }
                }
            }
        }
        return null;
    }
    
    /**
     * Verifica se há espaço para pular
     */
    private boolean hasJumpClearance(BlockPos from) {
        Block blockAbove = mc.theWorld.getBlockState(from.up(2)).getBlock();
        return blockAbove == Blocks.air || !blockAbove.isFullBlock();
    }
    
    /**
     * Verifica se há espaço para aterrissar
     */
    private boolean hasLandingSpace(BlockPos to) {
        return isWalkablePosition(to);
    }
    
    /**
     * Verifica obstáculos em movimento diagonal
     */
    private boolean hasCornerObstacle(BlockPos from, BlockPos to) {
        int dx = to.getX() - from.getX();
        int dz = to.getZ() - from.getZ();
        
        // Verificar os dois blocos adjacentes no movimento diagonal
        BlockPos corner1 = from.add(dx, 0, 0);
        BlockPos corner2 = from.add(0, 0, dz);
        
        return !isWalkablePosition(corner1) || !isWalkablePosition(corner2);
    }
    
    /**
     * Calcula distância de Manhattan (heurística admissível)
     */
    private double manhattanDistance(BlockPos a, BlockPos b) {
        return Math.abs(a.getX() - b.getX()) + 
               Math.abs(a.getY() - b.getY()) + 
               Math.abs(a.getZ() - b.getZ());
    }
    
    /**
     * Reconstrói o caminho a partir do nó final
     */
    private List<Vec3> reconstructPath(NavNode finalNode) {
        List<Vec3> path = new ArrayList<>();
        NavNode current = finalNode;
        
        while (current != null) {
            path.add(new Vec3(current.position.getX() + 0.5, 
                            current.position.getY(), 
                            current.position.getZ() + 0.5));
            current = current.parent;
        }
        
        Collections.reverse(path);
        return path;
    }
    
    /**
     * Otimiza caminho usando String Pulling (Raycast Simplification)
     */
    private List<Vec3> optimizePathWithStringPulling(List<Vec3> originalPath) {
        if (originalPath.size() <= 2) return originalPath;
        
        List<Vec3> optimizedPath = new ArrayList<>();
        optimizedPath.add(originalPath.get(0));
        
        int currentIndex = 0;
        while (currentIndex < originalPath.size() - 1) {
            int farthestVisibleIndex = currentIndex + 1;
            
            // Encontrar o ponto mais distante com linha de visão clara
            for (int i = currentIndex + 2; i < originalPath.size(); i++) {
                if (hasLineOfSight(originalPath.get(currentIndex), originalPath.get(i))) {
                    farthestVisibleIndex = i;
                } else {
                    break;
                }
            }
            
            optimizedPath.add(originalPath.get(farthestVisibleIndex));
            currentIndex = farthestVisibleIndex;
        }
        
        return optimizedPath;
    }
    
    /**
     * Verifica linha de visão entre dois pontos
     */
    private boolean hasLineOfSight(Vec3 from, Vec3 to) {
        double distance = from.distanceTo(to);
        int steps = Math.max(1, (int) Math.ceil(distance));
        
        for (int i = 0; i <= steps; i++) {
            double t = (double) i / steps;
            Vec3 direction = to.subtract(from);
            Vec3 scaledDirection = new Vec3(direction.xCoord * t, direction.yCoord * t, direction.zCoord * t);
            Vec3 point = from.add(scaledDirection);
            BlockPos blockPos = new BlockPos(point);
            
            if (!isWalkablePosition(blockPos)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Simula movimento da hitbox (AABB) do player para validar movimento
     */
    private boolean simulateAABBMovement(BlockPos from, BlockPos to, MovementType movementType) {
        // Dimensões da hitbox do player (0.6 x 1.8 x 0.6)
        double playerWidth = 0.6;
        double playerHeight = 1.8;

        Vec3 fromVec = new Vec3(from.getX() + 0.5, from.getY(), from.getZ() + 0.5);
        Vec3 toVec = new Vec3(to.getX() + 0.5, to.getY(), to.getZ() + 0.5);

        if (movementType == MovementType.JUMP) {
            return simulateJumpTrajectory(fromVec, toVec, playerWidth, playerHeight);
        } else {
            return simulateWalkMovement(fromVec, toVec, playerWidth, playerHeight);
        }
    }

    /**
     * Simula movimento de caminhada verificando colisões
     */
    private boolean simulateWalkMovement(Vec3 from, Vec3 to, double width, double height) {
        Vec3 direction = to.subtract(from);
        double distance = direction.lengthVector();
        int steps = Math.max(1, (int)(distance * 4)); // 4 verificações por bloco

        for (int i = 0; i <= steps; i++) {
            double t = (double) i / steps;
            Vec3 scaledDirection = new Vec3(direction.xCoord * t, direction.yCoord * t, direction.zCoord * t);
            Vec3 currentPos = from.add(scaledDirection);

            if (hasAABBCollision(currentPos, width, height)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Simula trajetória de pulo com física parabólica
     */
    private boolean simulateJumpTrajectory(Vec3 from, Vec3 to, double width, double height) {
        double horizontalDistance = Math.sqrt(
            Math.pow(to.xCoord - from.xCoord, 2) +
            Math.pow(to.zCoord - from.zCoord, 2)
        );

        // Física do pulo no Minecraft
        double initialVelocityY = 0.42; // Velocidade inicial do pulo
        double gravity = 0.08; // Gravidade por tick
        double horizontalVelocity = horizontalDistance / 20.0; // Assumindo 20 ticks para o pulo

        // Simular trajetória em pequenos passos
        for (int tick = 0; tick <= 25; tick++) { // Máximo 25 ticks
            double t = tick / 20.0; // Tempo em segundos

            // Posição Y com física parabólica
            double currentY = from.yCoord + (initialVelocityY * tick) - (0.5 * gravity * tick * tick);

            // Posição horizontal linear
            double progress = Math.min(1.0, t);
            double currentX = from.xCoord + (to.xCoord - from.xCoord) * progress;
            double currentZ = from.zCoord + (to.zCoord - from.zCoord) * progress;

            Vec3 currentPos = new Vec3(currentX, currentY, currentZ);

            if (hasAABBCollision(currentPos, width, height)) {
                return false; // Colidiu durante o pulo
            }

            // Se chegou no destino
            if (currentY <= to.yCoord && progress >= 1.0) {
                break;
            }
        }

        return true;
    }

    /**
     * Verifica colisão da AABB do player
     */
    private boolean hasAABBCollision(Vec3 position, double width, double height) {
        double halfWidth = width / 2.0;

        // Verificar todos os blocos que a hitbox pode tocar
        int minX = (int) Math.floor(position.xCoord - halfWidth);
        int maxX = (int) Math.ceil(position.xCoord + halfWidth);
        int minY = (int) Math.floor(position.yCoord);
        int maxY = (int) Math.ceil(position.yCoord + height);
        int minZ = (int) Math.floor(position.zCoord - halfWidth);
        int maxZ = (int) Math.ceil(position.zCoord + halfWidth);

        for (int x = minX; x <= maxX; x++) {
            for (int y = minY; y <= maxY; y++) {
                for (int z = minZ; z <= maxZ; z++) {
                    BlockPos blockPos = new BlockPos(x, y, z);
                    Block block = mc.theWorld.getBlockState(blockPos).getBlock();

                    if (block != Blocks.air && block.isFullBlock()) {
                        return true; // Colisão detectada
                    }
                }
            }
        }

        return false;
    }

    /**
     * Verifica se o player pode fisicamente pular de um bloco para outro
     */
    private boolean canPhysicallyJump(BlockPos from, BlockPos to) {
        double horizontalDistance = Math.sqrt(from.distanceSq(to.getX(), from.getY(), to.getZ()));
        int verticalDistance = to.getY() - from.getY();

        // Limites físicos do Minecraft
        if (horizontalDistance > 4.0) return false; // Máximo 4 blocos horizontais
        if (verticalDistance > 1) return false; // Máximo 1 bloco para cima
        if (verticalDistance < -3) return false; // Máximo 3 blocos para baixo

        return true;
    }

    /**
     * Verifica se movimento na água é realmente necessário
     */
    private boolean isWaterMovementNecessary(BlockPos from, BlockPos to) {
        // Só permitir água se não houver alternativa em um raio de 10 blocos
        for (int x = -10; x <= 10; x++) {
            for (int z = -10; z <= 10; z++) {
                for (int y = -3; y <= 3; y++) {
                    BlockPos alternative = to.add(x, y, z);
                    if (isWalkablePosition(alternative) && !isWaterBlock(alternative)) {
                        return false; // Há alternativa seca
                    }
                }
            }
        }
        return true; // Água é necessária
    }

    /**
     * Verifica se um bloco é água
     */
    private boolean isWaterBlock(BlockPos pos) {
        Block block = mc.theWorld.getBlockState(pos).getBlock();
        return block == Blocks.water || block == Blocks.flowing_water;
    }

    /**
     * Candidato de movimento
     */
    private static class MovementCandidate {
        public final BlockPos targetPos;
        public final MovementType movementType;

        public MovementCandidate(BlockPos targetPos, MovementType movementType) {
            this.targetPos = targetPos;
            this.movementType = movementType;
        }
    }
}
