package com.rato.addons.features;

import com.rato.addons.config.RatoAddonsConfigSimple;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.ScaledResolution;
import net.minecraft.client.gui.inventory.GuiInventory;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.RenderHelper;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.item.ItemStack;
import net.minecraftforge.client.event.RenderGameOverlayEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import org.lwjgl.opengl.GL11;

public class InventoryPIP {

    private static final Minecraft mc = Minecraft.getMinecraft();

    // Dimensões do PIP - reduzidas horizontalmente
    private static final int PIP_WIDTH = 180;
    private static final int PIP_HEIGHT = 65;

    // Cores - background mais claro
    private static final int NEON_GREEN = 0xFF39FF14;  // Verde neon puro
    private static final int DARK_BG = 0x50000000;     // Fundo mais claro (31% opacidade)

    // Configurações do modelo do jogador - menor
    private static final int PLAYER_MODEL_SIZE = 28;   // Tamanho reduzido
    private static final int PADDING = 4;              // Padding reduzido

    // Animação
    private static boolean wasEnabled = false;
    private static long enableTime = 0;
    private static final long ANIMATION_DURATION = 300; // 300ms para fade-in
    
    @SubscribeEvent
    public void onRenderGameOverlay(RenderGameOverlayEvent.Post event) {
        if (event.type != RenderGameOverlayEvent.ElementType.ALL) return;
        if (mc.thePlayer == null || mc.currentScreen != null) return;

        // Controle de animação
        if (RatoAddonsConfigSimple.inventoryPIP && !wasEnabled) {
            enableTime = System.currentTimeMillis();
            wasEnabled = true;
        } else if (!RatoAddonsConfigSimple.inventoryPIP && wasEnabled) {
            wasEnabled = false;
        }

        if (!RatoAddonsConfigSimple.inventoryPIP) return;

        renderInventoryPIP();
    }
    
    private void renderInventoryPIP() {
        ScaledResolution sr = new ScaledResolution(mc);

        // Dimensões fixas do PIP
        int pipWidth = PIP_WIDTH;
        int pipHeight = PIP_HEIGHT;

        // Posição baseada na configuração
        int pipX, pipY;
        switch (RatoAddonsConfigSimple.pipPosition) {
            case 0: // Top Right
                pipX = sr.getScaledWidth() - pipWidth - 10;
                pipY = 10;
                break;
            case 1: // Top Left
                pipX = 10;
                pipY = 10;
                break;
            case 2: // Bottom Right
                pipX = sr.getScaledWidth() - pipWidth - 10;
                pipY = sr.getScaledHeight() - pipHeight - 10;
                break;
            case 3: // Bottom Left
                pipX = 10;
                pipY = sr.getScaledHeight() - pipHeight - 10;
                break;
            default:
                pipX = sr.getScaledWidth() - pipWidth - 10;
                pipY = 10;
                break;
        }

        // Calcular alpha da animação
        float animationAlpha = 1.0f;
        if (wasEnabled) {
            long elapsed = System.currentTimeMillis() - enableTime;
            if (elapsed < ANIMATION_DURATION) {
                animationAlpha = (float) elapsed / ANIMATION_DURATION;
                // Easing suave
                animationAlpha = animationAlpha * animationAlpha * (3.0f - 2.0f * animationAlpha);
            }
        }

        GlStateManager.pushMatrix();
        GlStateManager.enableBlend();
        GlStateManager.blendFunc(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA);

        // Aplicar alpha da animação
        GlStateManager.color(1.0f, 1.0f, 1.0f, animationAlpha);

        // Desenhar fundo translúcido (vidro escuro) com animação
        int animatedBg = applyAlpha(DARK_BG, animationAlpha);
        drawRect(pipX, pipY, pipX + pipWidth, pipY + pipHeight, animatedBg);

        // Desenhar bordas horizontais mais finas e fortes (2px)
        int borderColor = RatoAddonsConfigSimple.pipBorderColor.getRGB() | 0xFF000000; // Verde neon sem alpha
        int animatedBorderColor = applyAlpha(borderColor, animationAlpha);
        // Borda superior (2px)
        drawRect(pipX, pipY, pipX + pipWidth, pipY + 2, animatedBorderColor);
        // Borda inferior (2px)
        drawRect(pipX, pipY + pipHeight - 2, pipX + pipWidth, pipY + pipHeight, animatedBorderColor);

        // Renderizar itens do inventário
        renderInventoryItems(pipX, pipY, pipWidth, pipHeight, animationAlpha);

        // Renderizar modelo 3D do jogador na extrema direita
        renderPlayerModel(pipX, pipY, pipWidth, pipHeight, animationAlpha);

        GlStateManager.color(1.0f, 1.0f, 1.0f, 1.0f);
        GlStateManager.disableBlend();
        GlStateManager.popMatrix();
    }

    private void renderInventoryItems(int pipX, int pipY, int pipWidth, int pipHeight, float alpha) {
        if (mc.thePlayer == null || mc.thePlayer.inventory == null) return;

        // Área disponível para itens (layout horizontal, deixando espaço para player)
        int itemAreaWidth = pipWidth - PLAYER_MODEL_SIZE - (PADDING * 2);
        int itemAreaHeight = pipHeight - (PADDING * 2);

        // Tamanho menor dos itens para caber mais
        int itemSize = 14;
        int itemSpacing = 1;

        // Calcular quantos itens cabem horizontalmente
        int itemsPerRow = Math.max(1, itemAreaWidth / (itemSize + itemSpacing));

        GlStateManager.pushMatrix();
        GlStateManager.enableBlend();
        GlStateManager.color(1.0f, 1.0f, 1.0f, alpha);

        RenderHelper.enableGUIStandardItemLighting();

        // Layout horizontal - renderizar mais itens do inventário
        int currentX = pipX + PADDING;
        int currentY = pipY + PADDING;

        // Primeira linha - hotbar (slots 0-8)
        int itemsRendered = 0;
        for (int i = 0; i < 9 && itemsRendered < itemsPerRow; i++) {
            ItemStack stack = mc.thePlayer.inventory.mainInventory[i];
            if (stack != null) {
                mc.getRenderItem().renderItemAndEffectIntoGUI(stack, currentX, currentY);
                mc.getRenderItem().renderItemOverlayIntoGUI(mc.fontRendererObj, stack, currentX, currentY, null);
            }
            currentX += itemSize + itemSpacing;
            itemsRendered++;
        }

        // Segunda linha - sempre renderizar
        currentX = pipX + PADDING;
        currentY = pipY + PADDING + itemSize + itemSpacing + 1;
        itemsRendered = 0;

        // Renderizar próximos itens (slots 9-17)
        for (int i = 9; i < 18 && itemsRendered < itemsPerRow; i++) {
            if (i < mc.thePlayer.inventory.mainInventory.length) {
                ItemStack stack = mc.thePlayer.inventory.mainInventory[i];
                if (stack != null) {
                    mc.getRenderItem().renderItemAndEffectIntoGUI(stack, currentX, currentY);
                    mc.getRenderItem().renderItemOverlayIntoGUI(mc.fontRendererObj, stack, currentX, currentY, null);
                }
                currentX += itemSize + itemSpacing;
                itemsRendered++;
            }
        }

        // Terceira linha - sempre renderizar
        currentX = pipX + PADDING;
        currentY = pipY + PADDING + (itemSize + itemSpacing + 1) * 2;
        itemsRendered = 0;

        // Renderizar mais itens (slots 18-26)
        for (int i = 18; i < 27 && itemsRendered < itemsPerRow; i++) {
            if (i < mc.thePlayer.inventory.mainInventory.length) {
                ItemStack stack = mc.thePlayer.inventory.mainInventory[i];
                if (stack != null) {
                    mc.getRenderItem().renderItemAndEffectIntoGUI(stack, currentX, currentY);
                    mc.getRenderItem().renderItemOverlayIntoGUI(mc.fontRendererObj, stack, currentX, currentY, null);
                }
                currentX += itemSize + itemSpacing;
                itemsRendered++;
            }
        }

        RenderHelper.disableStandardItemLighting();
        GlStateManager.disableBlend();
        GlStateManager.popMatrix();
    }

    private void renderPlayerModel(int pipX, int pipY, int pipWidth, int pipHeight, float alpha) {
        if (mc.thePlayer == null) return;

        // Posição do modelo na extrema direita, menor e melhor posicionado
        int modelSize = PLAYER_MODEL_SIZE;
        int modelX = pipX + pipWidth - modelSize - PADDING;
        int modelY = pipY + pipHeight - 2; // Ligeiramente acima da borda inferior

        GlStateManager.pushMatrix();

        // Configurar renderização limpa
        GlStateManager.enableColorMaterial();
        GlStateManager.enableDepth();
        GlStateManager.enableAlpha();
        GlStateManager.color(1.0f, 1.0f, 1.0f, alpha);

        // Configurar iluminação adequada
        RenderHelper.enableStandardItemLighting();

        try {
            // Renderizar o modelo 3D do jogador menor e bem posicionado
            GuiInventory.drawEntityOnScreen(
                modelX + modelSize/2,
                modelY,
                modelSize,
                25.0f, // Rotação ligeiramente ajustada
                0.0f,  // Sem inclinação
                mc.thePlayer
            );
        } catch (Exception e) {
            // Fallback silencioso se houver erro no render
        }

        RenderHelper.disableStandardItemLighting();
        GlStateManager.disableAlpha();
        GlStateManager.disableDepth();
        GlStateManager.disableColorMaterial();
        GlStateManager.popMatrix();
    }
    
    private int applyAlpha(int color, float alpha) {
        int originalAlpha = (color >> 24) & 0xFF;
        int newAlpha = (int) (originalAlpha * alpha);
        return (color & 0x00FFFFFF) | (newAlpha << 24);
    }

    private void drawRect(int left, int top, int right, int bottom, int color) {
        if (left < right) {
            int temp = left;
            left = right;
            right = temp;
        }

        if (top < bottom) {
            int temp = top;
            top = bottom;
            bottom = temp;
        }

        float alpha = (float) (color >> 24 & 255) / 255.0F;
        float red = (float) (color >> 16 & 255) / 255.0F;
        float green = (float) (color >> 8 & 255) / 255.0F;
        float blue = (float) (color & 255) / 255.0F;

        GlStateManager.disableTexture2D();
        GlStateManager.color(red, green, blue, alpha);

        GL11.glBegin(GL11.GL_QUADS);
        GL11.glVertex2f(left, bottom);
        GL11.glVertex2f(right, bottom);
        GL11.glVertex2f(right, top);
        GL11.glVertex2f(left, top);
        GL11.glEnd();

        GlStateManager.enableTexture2D();
    }
}
