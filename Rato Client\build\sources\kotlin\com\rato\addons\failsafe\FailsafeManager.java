package com.rato.addons.failsafe;

import com.rato.addons.config.RatoAddonsConfigSimple;
import com.rato.addons.util.DiscordWebhook;
import com.rato.addons.util.Logger;
import net.minecraft.client.Minecraft;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.ConcurrentLinkedQueue;

public class FailsafeManager {

    private static FailsafeManager instance;
    private static final Minecraft mc = Minecraft.getMinecraft();

    private final List<Failsafe> failsafes = new ArrayList<>();
    private final ConcurrentLinkedQueue<ScheduledTask> scheduledTasks = new ConcurrentLinkedQueue<>();

    private boolean isFailsafeRunning = false;
    private Failsafe currentFailsafe = null;
    private long gameStartTime = 0;
    private static final long INITIALIZATION_DELAY = 3000; // 3 segundos

    public static FailsafeManager getInstance() {
        if (instance == null) {
            instance = new FailsafeManager();
        }
        return instance;
    }

    private FailsafeManager() {
        registerFailsafes();
    }

    private void registerFailsafes() {
        failsafes.add(RotationFailsafe.getInstance());
        failsafes.add(TeleportFailsafe.getInstance());
        failsafes.add(new ItemSwapFailsafe());
        failsafes.add(new BlockCheckFailsafe());
        failsafes.add(new VelocityFailsafe());
        failsafes.add(new PlayerCheckFailsafe());
    }

    @SubscribeEvent
    public void onClientTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.START)
            return;
        if (mc.thePlayer == null || mc.theWorld == null)
            return;

        // Inicializar tempo do jogo
        if (gameStartTime == 0) {
            gameStartTime = System.currentTimeMillis();
            return;
        }

        // Aguardar período de inicialização
        if (System.currentTimeMillis() - gameStartTime < INITIALIZATION_DELAY) {
            return;
        }

        // Processar tarefas agendadas
        processScheduledTasks();

        // Executar failsafe ativo
        if (isFailsafeRunning && currentFailsafe != null) {
            currentFailsafe.duringFailsafeTrigger();
        }
    }

    private void processScheduledTasks() {
        long currentTime = System.currentTimeMillis();
        while (!scheduledTasks.isEmpty()) {
            ScheduledTask task = scheduledTasks.peek();
            if (task.executionTime <= currentTime) {
                scheduledTasks.poll();
                task.run();
            } else {
                break;
            }
        }
    }

    public void possibleDetection(Failsafe failsafe) {
        if (isFailsafeRunning)
            return;

        // Verificar prioridade
        if (currentFailsafe != null && failsafe.getPriority() <= currentFailsafe.getPriority()) {
            return;
        }

        triggerFailsafe(failsafe);
    }

    private void triggerFailsafe(Failsafe failsafe) {
        // Pausar todas as automações
        pauseAllAutomations();

        // Configurar failsafe
        currentFailsafe = failsafe;
        isFailsafeRunning = true;
        failsafe.resetStates();

        // Enviar notificações
        if (failsafe.shouldSendNotification()) {
            sendDiscordNotification(failsafe);
        }

        if (failsafe.shouldPlaySound()) {
            mc.thePlayer.playSound("note.pling", 1.0f, 2.0f);
        }

        // Iniciar execução do failsafe
        failsafe.duringFailsafeTrigger();
    }

    private void sendDiscordNotification(Failsafe failsafe) {
        String details = failsafe.getType().getDisplayName() + " detected and handled automatically";
        String extraInfo = "Failsafe priority: " + failsafe.getPriority() + ", Response initiated";
        DiscordWebhook.sendStaffCheckAlert(failsafe.getType().getDisplayName(), details, extraInfo);
    }

    private void pauseAllAutomations() {
        // Pausar todas as automações do mod
        // Adicionar aqui as automações específicas do mod quando implementadas
        // Pausar automações silenciosamente
    }

    public void stopFailsafes() {
        isFailsafeRunning = false;
        currentFailsafe = null;
        scheduledTasks.clear();
    }

    public void scheduleRandomDelay(int minMs, int maxMs) {
        Random random = new Random();
        // Garantir que maxMs >= minMs
        if (maxMs < minMs) {
            int temp = maxMs;
            maxMs = minMs;
            minMs = temp;
        }

        int range = maxMs - minMs;
        int delay = minMs + (range > 0 ? random.nextInt(range + 1) : 0);
        scheduleDelay(delay);
    }

    public void scheduleDelay(int delayMs) {
        long executionTime = System.currentTimeMillis() + delayMs;
        scheduledTasks.offer(new ScheduledTask(executionTime, () -> {
        }));
    }

    public boolean isFailsafeRunning() {
        return isFailsafeRunning;
    }

    public List<Failsafe> getFailsafes() {
        return failsafes;
    }

    public enum EmergencyType {
        ROTATION_CHECK("Rotation Check"),
        TELEPORT_CHECK("Teleport Check"),
        ITEM_CHANGE_CHECK("Item Swap Check"),
        BLOCK_CHECK("Block Check"),
        VELOCITY_CHECK("Velocity Check"),
        PLAYER_CHECK("Player Check");

        private final String displayName;

        EmergencyType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    private static class ScheduledTask {
        final long executionTime;
        final Runnable task;

        ScheduledTask(long executionTime, Runnable task) {
            this.executionTime = executionTime;
            this.task = task;
        }

        void run() {
            task.run();
        }
    }
}
