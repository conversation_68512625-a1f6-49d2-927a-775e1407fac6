package com.rato.addons.visuals.esp;

import com.rato.addons.config.RatoAddonsConfigSimple;
import com.rato.addons.visuals.util.RenderUtils;
import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.entity.Entity;
import net.minecraft.entity.monster.EntityZombie;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.AxisAlignedBB;
import org.lwjgl.opengl.GL11;

import java.awt.*;
import java.util.List;

/**
 * Renderizador ESP baseado no LiquidBounce
 * Suporte para Box, Outline, Glow e Chams
 */
public class ESPRenderer {

    private static final Minecraft mc = Minecraft.getMinecraft();

    /**
     * Renderiza ESP para todas as entidades
     */
    public void render(float partialTicks) {
        if (mc.theWorld == null || mc.thePlayer == null)
            return;

        // Configurar OpenGL
        setupRenderState();

        try {
            List<Entity> entities = mc.theWorld.loadedEntityList;

            for (Entity entity : entities) {
                if (!shouldRenderEntity(entity))
                    continue;

                // Calcular posição interpolada
                double x = entity.lastTickPosX + (entity.posX - entity.lastTickPosX) * partialTicks;
                double y = entity.lastTickPosY + (entity.posY - entity.lastTickPosY) * partialTicks;
                double z = entity.lastTickPosZ + (entity.posZ - entity.lastTickPosZ) * partialTicks;

                // Ajustar para posição relativa da câmera
                double renderX = x - mc.getRenderManager().viewerPosX;
                double renderY = y - mc.getRenderManager().viewerPosY;
                double renderZ = z - mc.getRenderManager().viewerPosZ;

                // Obter cor da entidade
                Color color = getEntityColor(entity);

                // Renderizar baseado no modo
                String mode = getESPMode();
                switch (mode.toLowerCase()) {
                    case "box":
                        RenderUtils.drawEntityBox(entity, renderX, renderY, renderZ, color);
                        break;
                    case "outline":
                        RenderUtils.drawEntityOutline(entity, renderX, renderY, renderZ, color);
                        break;
                    case "glow":
                        RenderUtils.drawEntityGlow(entity, renderX, renderY, renderZ, color);
                        break;
                    case "chams":
                        RenderUtils.drawEntityChams(entity, renderX, renderY, renderZ, color);
                        break;
                    case "2d":
                        RenderUtils.drawEntity2D(entity, renderX, renderY, renderZ, color);
                        break;
                }

                // Efeitos adicionais
                if (RatoAddonsConfigSimple.visualsESPGlow && !mode.equalsIgnoreCase("glow")) {
                    RenderUtils.drawEntityGlowEffect(entity, renderX, renderY, renderZ, color,
                            RatoAddonsConfigSimple.visualsESPGlowRadius);
                }
            }
        } finally {
            restoreRenderState();
        }
    }

    /**
     * Verifica se a entidade deve ser renderizada
     */
    private boolean shouldRenderEntity(Entity entity) {
        if (entity == mc.thePlayer)
            return false;
        if (entity.isDead)
            return false;

        // Verificar tipos habilitados
        if (entity instanceof EntityZombie && RatoAddonsConfigSimple.visualsESPZombies) {
            return true;
        }

        if (entity instanceof EntityPlayer && RatoAddonsConfigSimple.visualsESPPlayers) {
            return true;
        }

        return false;
    }

    /**
     * Obtém a cor da entidade
     */
    private Color getEntityColor(Entity entity) {
        if (entity instanceof EntityZombie) {
            return new Color(
                    RatoAddonsConfigSimple.visualsZombieESPColor.getRed(),
                    RatoAddonsConfigSimple.visualsZombieESPColor.getGreen(),
                    RatoAddonsConfigSimple.visualsZombieESPColor.getBlue(),
                    RatoAddonsConfigSimple.visualsZombieESPColor.getAlpha());
        } else if (entity instanceof EntityPlayer) {
            return new Color(
                    RatoAddonsConfigSimple.visualsPlayerESPColor.getRed(),
                    RatoAddonsConfigSimple.visualsPlayerESPColor.getGreen(),
                    RatoAddonsConfigSimple.visualsPlayerESPColor.getBlue(),
                    RatoAddonsConfigSimple.visualsPlayerESPColor.getAlpha());
        }

        return new Color(255, 255, 255, 150);
    }

    /**
     * Obtém o modo ESP atual
     */
    private String getESPMode() {
        switch (RatoAddonsConfigSimple.visualsESPMode) {
            case 0:
                return "Box";
            case 1:
                return "Outline";
            case 2:
                return "Glow";
            case 3:
                return "Chams";
            case 4:
                return "2D";
            default:
                return "Box";
        }
    }

    /**
     * Configura o estado OpenGL
     */
    private void setupRenderState() {
        GlStateManager.pushMatrix();
        GlStateManager.enableBlend();
        GlStateManager.blendFunc(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA);
        GlStateManager.disableTexture2D();
        GlStateManager.disableLighting();
        GlStateManager.disableDepth();
        GlStateManager.depthMask(false);
        GL11.glEnable(GL11.GL_LINE_SMOOTH);
        GL11.glHint(GL11.GL_LINE_SMOOTH_HINT, GL11.GL_NICEST);
        GL11.glLineWidth(1.5f);
    }

    /**
     * Restaura o estado OpenGL
     */
    private void restoreRenderState() {
        GL11.glDisable(GL11.GL_LINE_SMOOTH);
        GlStateManager.depthMask(true);
        GlStateManager.enableDepth();
        GlStateManager.enableLighting();
        GlStateManager.enableTexture2D();
        GlStateManager.disableBlend();
        GlStateManager.popMatrix();
    }
}
