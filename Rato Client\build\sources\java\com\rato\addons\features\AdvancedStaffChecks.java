package com.rato.addons.features;

import com.rato.addons.config.RatoAddonsConfigSimple;
import com.rato.addons.util.DiscordWebhook;
import com.rato.addons.util.Logger;
import net.minecraft.block.Block;
import net.minecraft.block.state.IBlockState;
import net.minecraft.client.Minecraft;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.init.Blocks;
import net.minecraft.util.BlockPos;
import net.minecraft.util.MathHelper;
import net.minecraft.util.Vec3;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;

import java.util.*;

public class AdvancedStaffChecks {
    
    private static final Minecraft mc = Minecraft.getMinecraft();
    
    // Block Check Detection - Detecta blocos individuais que aparecem e somem
    private static Map<BlockPos, BlockCheckData> trackedBlocks = new HashMap<>();
    private static long lastBlockScanTime = 0;
    private static final long BLOCK_SCAN_INTERVAL = 50; // Verificar a cada 50ms (mais frequente)
    private static final long BLOCK_CHECK_COOLDOWN = 3000; // 3 segundos
    private static final long SUSPICIOUS_DURATION = 1000; // 1 segundo para considerar suspeito
    
    // Velocity Check Detection
    private static double lastMotionY = 0;
    private static double[] recentMotionY = new double[5]; // Últimas 5 velocidades
    private static int motionIndex = 0;
    private static long lastVelocityTime = 0;
    private static final long VELOCITY_CHECK_COOLDOWN = 3000; // 3 segundos
    private static boolean wasOnGround = true;
    
    // Player Check Detection
    private static Map<String, PlayerWatchData> watchingPlayers = new HashMap<>();
    private static long lastPlayerCheckTime = 0;
    private static final long PLAYER_CHECK_COOLDOWN = 3000; // 3 segundos
    private static long lastBlockCheckTime = 0;

    // Controle de inicialização
    private static long gameStartTime = 0;
    private static final long INITIALIZATION_DELAY = 5000; // 5 segundos
    
    @SubscribeEvent
    public void onClientTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.START) return;
        if (mc.thePlayer == null || mc.theWorld == null) return;

        // Inicializar tempo do jogo na primeira execução
        if (gameStartTime == 0) {
            gameStartTime = System.currentTimeMillis();
            return;
        }

        // Aguardar período de inicialização para evitar falsos positivos
        if (System.currentTimeMillis() - gameStartTime < INITIALIZATION_DELAY) {
            return;
        }

        // Block Check Detection
        if (RatoAddonsConfigSimple.blockCheckDetection) {
            checkForSuspiciousBlocks();
        }

        // Velocity Check Detection
        if (RatoAddonsConfigSimple.velocityCheckDetection) {
            checkForVelocityManipulation();
        }

        // Player Check Detection
        if (RatoAddonsConfigSimple.playerCheckDetection) {
            checkForSuspiciousPlayers();
        }
    }
    
    private void checkForSuspiciousBlocks() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastBlockScanTime < BLOCK_SCAN_INTERVAL) return;
        lastBlockScanTime = currentTime;

        BlockPos playerPos = mc.thePlayer.getPosition();

        // Verificar blocos em um raio menor e mais focado
        for (int x = -4; x <= 4; x++) {
            for (int y = -1; y <= 2; y++) {
                for (int z = -4; z <= 4; z++) {
                    BlockPos pos = playerPos.add(x, y, z);
                    Block currentBlock = mc.theWorld.getBlockState(pos).getBlock();

                    BlockCheckData data = trackedBlocks.get(pos);

                    if (currentBlock != Blocks.air) {
                        // Bloco existe
                        if (data == null) {
                            // Novo bloco apareceu
                            if (isSuspiciousBlock(currentBlock)) {
                                trackedBlocks.put(pos, new BlockCheckData(currentBlock, currentTime));
                            }
                        }
                    } else {
                        // Posição vazia
                        if (data != null) {
                            // Bloco desapareceu
                            long duration = currentTime - data.appearTime;
                            if (duration >= SUSPICIOUS_DURATION && duration <= 10000) { // Entre 1-10 segundos
                                detectSingleBlockCheck(pos, data.block, duration);
                            }
                            trackedBlocks.remove(pos);
                        }
                    }
                }
            }
        }

        // Limpar blocos antigos (mais de 15 segundos)
        trackedBlocks.entrySet().removeIf(entry ->
            currentTime - entry.getValue().appearTime > 15000);
    }

    private boolean isSuspiciousBlock(Block block) {
        // Blocos típicos de staff check
        return block == Blocks.glass ||
               block == Blocks.stained_glass ||
               block == Blocks.barrier ||
               block == Blocks.bedrock ||
               block == Blocks.stone ||
               block == Blocks.wool ||
               block == Blocks.clay;
    }

    private void detectSingleBlockCheck(BlockPos pos, Block block, long duration) {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastBlockCheckTime < BLOCK_CHECK_COOLDOWN) return;
        lastBlockCheckTime = currentTime;

        String blockName = block.getLocalizedName();
        String details = "Suspicious block appeared and disappeared near player";
        String extraInfo = String.format("Block: %s, Duration: %.1fs, Distance: %.1f blocks",
            blockName, duration / 1000.0,
            Math.sqrt(pos.distanceSq(mc.thePlayer.getPosition())));

        Logger.sendMessage("§c⚠ Block Check §7- " + blockName + " (" + String.format("%.1fs", duration / 1000.0) + ")");

        // Enviar para Discord
        DiscordWebhook.sendStaffCheckAlert("Block Check", details, extraInfo);

        // Som de alerta
        if (RatoAddonsConfigSimple.soundAlert) {
            mc.thePlayer.playSound("note.pling", 1.0f, 2.0f);
        }
    }
    

    
    private void checkForVelocityManipulation() {
        if (mc.thePlayer == null) return;

        double currentMotionY = mc.thePlayer.motionY;
        boolean currentlyOnGround = mc.thePlayer.onGround;

        // Atualizar histórico de velocidades
        recentMotionY[motionIndex] = currentMotionY;
        motionIndex = (motionIndex + 1) % recentMotionY.length;

        // Detectar lançamento artificial
        if (isArtificialVelocity(currentMotionY, currentlyOnGround)) {
            if (System.currentTimeMillis() - lastVelocityTime > VELOCITY_CHECK_COOLDOWN) {
                detectVelocityCheck(currentMotionY, Math.abs(currentMotionY - lastMotionY));
            }
        }

        lastMotionY = currentMotionY;
        wasOnGround = currentlyOnGround;
    }

    private boolean isArtificialVelocity(double motionY, boolean onGround) {
        // 1. Lançamento muito forte (> 1.0) sem estar pulando
        if (motionY > 1.0 && wasOnGround) {
            return true;
        }

        // 2. Mudança súbita muito grande
        double motionDiff = Math.abs(motionY - lastMotionY);
        if (motionDiff > 1.2 && motionY > 0.3) {
            return true;
        }

        // 3. Velocidade "perfeita" (valores exatos são suspeitos)
        if (motionY > 0.5 && motionY == Math.floor(motionY * 10) / 10) {
            return true; // Velocidades como 1.0, 1.5, 2.0 são suspeitas
        }

        // 4. Padrão não natural (velocidade constante por muito tempo)
        boolean allSimilar = true;
        for (double vel : recentMotionY) {
            if (Math.abs(vel - motionY) > 0.1) {
                allSimilar = false;
                break;
            }
        }
        if (allSimilar && motionY > 0.5) {
            return true;
        }

        return false;
    }
    
    private void detectVelocityCheck(double motionY, double motionDiff) {
        lastVelocityTime = System.currentTimeMillis();
        
        String details = "Player launched upward with unusual velocity";
        String extraInfo = String.format("Velocity Y: %.2f, Change: %.2f", motionY, motionDiff);

        Logger.sendMessage("§c⚠ Velocity Check §7- Y:" + String.format("%.1f", motionY));
        
        // Enviar para Discord
        DiscordWebhook.sendStaffCheckAlert("Velocity Check", details, extraInfo);
        
        // Som de alerta
        if (RatoAddonsConfigSimple.soundAlert) {
            mc.thePlayer.playSound("note.pling", 1.0f, 2.0f);
        }
    }
    
    private void checkForSuspiciousPlayers() {
        if (System.currentTimeMillis() - lastPlayerCheckTime < 1000) return; // Check every second
        
        for (EntityPlayer player : mc.theWorld.playerEntities) {
            if (player == mc.thePlayer) continue;
            
            double distance = mc.thePlayer.getDistanceToEntity(player);
            if (distance > RatoAddonsConfigSimple.playerCheckRange) continue;
            
            // Verificar se o player está olhando diretamente para nós
            if (isPlayerWatchingMe(player, distance)) {
                updatePlayerWatchData(player.getName(), distance);
            } else {
                // Remover da lista se não está mais olhando
                watchingPlayers.remove(player.getName());
            }
        }
        
        // Verificar se algum player está sendo suspeito
        checkForSuspiciousWatching();
        lastPlayerCheckTime = System.currentTimeMillis();
    }
    
    private boolean isPlayerWatchingMe(EntityPlayer player, double distance) {
        // Calcular vetor de direção do player para mim
        Vec3 playerPos = new Vec3(player.posX, player.posY + player.getEyeHeight(), player.posZ);
        Vec3 myPos = new Vec3(mc.thePlayer.posX, mc.thePlayer.posY + mc.thePlayer.getEyeHeight(), mc.thePlayer.posZ);
        Vec3 directionToMe = myPos.subtract(playerPos).normalize();
        
        // Calcular vetor de direção do olhar do player
        float yaw = player.rotationYaw;
        float pitch = player.rotationPitch;
        
        Vec3 lookDirection = new Vec3(
            -MathHelper.sin(yaw * 0.017453292F) * MathHelper.cos(pitch * 0.017453292F),
            -MathHelper.sin(pitch * 0.017453292F),
            MathHelper.cos(yaw * 0.017453292F) * MathHelper.cos(pitch * 0.017453292F)
        );
        
        // Calcular ângulo entre os vetores
        double dotProduct = lookDirection.dotProduct(directionToMe);
        double angle = Math.acos(Math.max(-1.0, Math.min(1.0, dotProduct))) * 180.0 / Math.PI;
        
        // Se o ângulo é pequeno (< 20°), o player está olhando diretamente para mim
        return angle < 20.0 && distance < RatoAddonsConfigSimple.playerCheckRange;
    }
    
    private void updatePlayerWatchData(String playerName, double distance) {
        PlayerWatchData data = watchingPlayers.getOrDefault(playerName, new PlayerWatchData());
        data.lastWatchTime = System.currentTimeMillis();
        data.watchCount++;
        data.averageDistance = (data.averageDistance + distance) / 2.0;
        watchingPlayers.put(playerName, data);
    }
    
    private void checkForSuspiciousWatching() {
        long currentTime = System.currentTimeMillis();
        
        for (Map.Entry<String, PlayerWatchData> entry : watchingPlayers.entrySet()) {
            String playerName = entry.getKey();
            PlayerWatchData data = entry.getValue();
            
            // Se o player está olhando por mais de 2 segundos consecutivos
            if (currentTime - data.lastWatchTime < 500 && data.watchCount > 2) {
                detectSuspiciousPlayer(playerName, data);
                data.watchCount = 0; // Reset para evitar spam
            }
        }
    }
    
    private void detectSuspiciousPlayer(String playerName, PlayerWatchData data) {
        String details = "Suspicious player detected watching you closely";
        String extraInfo = String.format("Player: %s, Distance: %.1f blocks, Duration: %d seconds",
            playerName, data.averageDistance, (System.currentTimeMillis() - data.lastWatchTime) / 1000);

        Logger.sendMessage("§c⚠ Player Check §7- " + playerName);
        
        // Enviar para Discord
        DiscordWebhook.sendStaffCheckAlert("Player Check", details, extraInfo);
        
        // Som de alerta
        if (RatoAddonsConfigSimple.soundAlert) {
            mc.thePlayer.playSound("note.pling", 1.0f, 1.5f);
        }
    }
    
    private static class PlayerWatchData {
        long lastWatchTime = 0;
        int watchCount = 0;
        double averageDistance = 0;
    }

    private static class BlockCheckData {
        Block block;
        long appearTime;

        BlockCheckData(Block block, long appearTime) {
            this.block = block;
            this.appearTime = appearTime;
        }
    }
}
