package com.rato.addons.commands;

import com.rato.addons.pathfinding.examples.ForagingPathfindingExample;
import com.rato.addons.pathfinding.ForagingPathfindingSystem;
import com.rato.addons.util.Logger;
import net.minecraft.command.CommandBase;
import net.minecraft.command.CommandException;
import net.minecraft.command.ICommandSender;
import net.minecraft.util.ChatComponentText;

/**
 * Comando para testar o sistema de pathfinding 3D
 * Uso: /pathtest [exemplo|sistema|stats|help]
 */
public class PathfindingTestCommand extends CommandBase {
    
    private static ForagingPathfindingSystem testSystem = null;
    
    @Override
    public String getCommandName() {
        return "pathtest";
    }
    
    @Override
    public String getCommandUsage(ICommandSender sender) {
        return "/pathtest [exemplo|sistema|stats|help]";
    }
    
    @Override
    public int getRequiredPermissionLevel() {
        return 0; // Qualquer jogador pode usar
    }
    
    @Override
    public void processCommand(ICommandSender sender, String[] args) throws CommandException {
        if (args.length == 0) {
            showHelp(sender);
            return;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "exemplo":
            case "examples":
                runExamples(sender, args);
                break;
                
            case "sistema":
            case "system":
                testSystem(sender, args);
                break;
                
            case "stats":
            case "status":
                showStats(sender);
                break;
                
            case "help":
            case "ajuda":
                showHelp(sender);
                break;
                
            default:
                sender.addChatMessage(new ChatComponentText("§cComando inválido! Use /pathtest help"));
                break;
        }
    }
    
    /**
     * Executa exemplos do sistema
     */
    private void runExamples(ICommandSender sender, String[] args) {
        ForagingPathfindingExample examples = new ForagingPathfindingExample();
        
        if (args.length == 1) {
            // Executar todos os exemplos
            Logger.sendMessage("§aExecutando todos os exemplos...");
            new Thread(() -> examples.executarTodosExemplos()).start();
            return;
        }
        
        String exampleType = args[1].toLowerCase();
        
        switch (exampleType) {
            case "mapa":
            case "mapping":
                Logger.sendMessage("§aExecutando exemplo de mapeamento 3D...");
                examples.exemploMapeamento3D();
                break;
                
            case "arvores":
            case "trees":
                Logger.sendMessage("§aExecutando exemplo de detecção de árvores...");
                examples.exemploDeteccaoArvores();
                break;
                
            case "pathfinding":
            case "path":
                Logger.sendMessage("§aExecutando exemplo de pathfinding...");
                examples.exemploPathfinding();
                break;
                
            case "multiplas":
            case "multiple":
                Logger.sendMessage("§aExecutando exemplo de múltiplas árvores...");
                examples.exemploMultiplasArvores();
                break;
                
            case "completo":
            case "complete":
                Logger.sendMessage("§aExecutando exemplo do sistema completo...");
                examples.exemploSistemaCompleto();
                break;
                
            case "todos":
            case "all":
                Logger.sendMessage("§aExecutando todos os exemplos...");
                new Thread(() -> examples.executarTodosExemplos()).start();
                break;
                
            default:
                sender.addChatMessage(new ChatComponentText("§cExemplo inválido! Tipos: mapa, arvores, pathfinding, multiplas, completo, todos"));
                break;
        }
    }
    
    /**
     * Testa o sistema principal
     */
    private void testSystem(ICommandSender sender, String[] args) {
        if (args.length == 1) {
            sender.addChatMessage(new ChatComponentText("§cUse: /pathtest sistema [start|stop|status]"));
            return;
        }
        
        String action = args[1].toLowerCase();
        
        switch (action) {
            case "start":
            case "iniciar":
                if (testSystem != null && testSystem.isActive()) {
                    Logger.sendMessage("§cSistema de teste já está ativo!");
                    return;
                }
                
                testSystem = new ForagingPathfindingSystem();
                
                // Configurar callbacks de teste
                testSystem.setOnTreeReached(() -> {
                    Logger.sendMessage("§a[TESTE] Árvore alcançada!");
                });
                
                testSystem.setOnNoTreesFound(() -> {
                    Logger.sendMessage("§e[TESTE] Nenhuma árvore encontrada");
                });
                
                testSystem.setOnPathfindingFailed(() -> {
                    Logger.sendMessage("§c[TESTE] Falha no pathfinding");
                });
                
                boolean success = testSystem.startForagingPathfinding();
                
                if (success) {
                    Logger.sendMessage("§a[TESTE] Sistema de pathfinding iniciado!");
                } else {
                    Logger.sendMessage("§c[TESTE] Falha ao iniciar sistema!");
                    testSystem = null;
                }
                break;
                
            case "stop":
            case "parar":
                if (testSystem == null || !testSystem.isActive()) {
                    Logger.sendMessage("§cSistema de teste não está ativo!");
                    return;
                }
                
                testSystem.stopForagingPathfinding();
                testSystem = null;
                Logger.sendMessage("§a[TESTE] Sistema parado!");
                break;
                
            case "status":
            case "estado":
                if (testSystem == null) {
                    Logger.sendMessage("§7[TESTE] Sistema não inicializado");
                } else {
                    Logger.sendMessage("§6[TESTE] Status do Sistema:");
                    Logger.sendMessage(testSystem.getSystemStats());
                }
                break;
                
            default:
                sender.addChatMessage(new ChatComponentText("§cAção inválida! Use: start, stop, status"));
                break;
        }
    }
    
    /**
     * Mostra estatísticas do sistema
     */
    private void showStats(ICommandSender sender) {
        Logger.sendMessage("§6=== ESTATÍSTICAS DO PATHFINDING 3D ===");
        
        if (testSystem != null && testSystem.isActive()) {
            Logger.sendMessage("§aStatus: Sistema de teste ativo");
            Logger.sendMessage(testSystem.getSystemStats());
            
            if (testSystem.getCurrentTarget() != null) {
                Logger.sendMessage("§7Alvo atual: " + testSystem.getCurrentTarget().treeType.displayName);
                Logger.sendMessage("§7Posição: " + testSystem.getCurrentTarget().basePosition);
            }
            
            Logger.sendMessage("§7Árvores detectadas: " + testSystem.getDetectedTrees().size());
            
            if (testSystem.getPathExecutor().isExecuting()) {
                Logger.sendMessage("§7Progresso: " + String.format("%.1f", testSystem.getPathExecutor().getProgressPercentage()) + "%");
                Logger.sendMessage("§7Estado do executor: " + testSystem.getPathExecutor().getCurrentState().displayName);
            }
        } else {
            Logger.sendMessage("§7Status: Sistema inativo");
        }
        
        // Estatísticas de memória
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory() / 1024 / 1024;
        long freeMemory = runtime.freeMemory() / 1024 / 1024;
        long usedMemory = totalMemory - freeMemory;
        
        Logger.sendMessage("§7Memória: " + usedMemory + "MB / " + totalMemory + "MB");
    }
    
    /**
     * Mostra ajuda do comando
     */
    private void showHelp(ICommandSender sender) {
        Logger.sendMessage("§6=== COMANDO PATHFINDING TEST ===");
        Logger.sendMessage("§7/pathtest exemplo [tipo] - Executar exemplos");
        Logger.sendMessage("§7  Tipos: mapa, arvores, pathfinding, multiplas, completo, todos");
        Logger.sendMessage("§7/pathtest sistema [acao] - Controlar sistema de teste");
        Logger.sendMessage("§7  Ações: start, stop, status");
        Logger.sendMessage("§7/pathtest stats - Mostrar estatísticas");
        Logger.sendMessage("§7/pathtest help - Mostrar esta ajuda");
        Logger.sendMessage("");
        Logger.sendMessage("§eExemplos de uso:");
        Logger.sendMessage("§7/pathtest exemplo todos - Executar todos os exemplos");
        Logger.sendMessage("§7/pathtest exemplo mapa - Testar mapeamento 3D");
        Logger.sendMessage("§7/pathtest sistema start - Iniciar sistema de teste");
        Logger.sendMessage("§7/pathtest stats - Ver estatísticas");
    }
    
    /**
     * Atualiza o sistema de teste (chamado externamente)
     */
    public static void updateTestSystem() {
        if (testSystem != null && testSystem.isActive()) {
            testSystem.update();
        }
    }
    
    /**
     * Para o sistema de teste (chamado externamente)
     */
    public static void stopTestSystem() {
        if (testSystem != null) {
            testSystem.stopForagingPathfinding();
            testSystem = null;
        }
    }
    
    /**
     * Verifica se o sistema de teste está ativo
     */
    public static boolean isTestSystemActive() {
        return testSystem != null && testSystem.isActive();
    }
}
