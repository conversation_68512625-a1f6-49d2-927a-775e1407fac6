package com.rato.addons.pathfinding.professional;

import net.minecraft.block.Block;
import net.minecraft.block.material.Material;
import net.minecraft.client.Minecraft;
import net.minecraft.init.Blocks;
import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;
import net.minecraft.world.World;

import java.util.*;

/**
 * Analisador avançado de terreno para pathfinding inteligente
 */
public class TerrainAnalyzer {
    
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Cache de análise de terreno
    private final Map<BlockPos, TerrainInfo> terrainCache = new HashMap<>();
    private long lastCacheClean = System.currentTimeMillis();
    private static final long CACHE_LIFETIME = 30000; // 30 segundos
    
    // Configurações de análise
    private static final int ANALYSIS_RADIUS = 5;
    private static final int VERTICAL_SCAN_RANGE = 10;
    
    /**
     * Informações detalhadas sobre um bloco de terreno
     */
    public static class TerrainInfo {
        public TerrainType type;
        public double movementCost;
        public boolean isWalkable;
        public boolean requiresJump;
        public boolean isDangerous;
        public boolean hasWater;
        public boolean hasLava;
        public int groundLevel;
        public List<PathOption> pathOptions;
        public long timestamp;
        
        public TerrainInfo() {
            this.pathOptions = new ArrayList<>();
            this.timestamp = System.currentTimeMillis();
        }
    }
    
    /**
     * Tipos de terreno identificados
     */
    public enum TerrainType {
        FLAT_GROUND(1.0),           // Terreno plano
        ROUGH_TERRAIN(1.5),         // Terreno irregular
        STEEP_HILL(2.0),            // Colina íngreme
        CLIFF(3.0),                 // Penhasco
        WATER_SHALLOW(1.8),         // Água rasa
        WATER_DEEP(2.5),            // Água profunda
        LAVA(10.0),                 // Lava (muito perigoso)
        CAVE_ENTRANCE(1.2),         // Entrada de caverna
        BRIDGE_AREA(1.1),           // Área de ponte
        FOREST(1.3),                // Floresta
        DESERT(1.0),                // Deserto
        MOUNTAIN(2.5),              // Montanha
        UNDERGROUND(1.4),           // Subterrâneo
        STRUCTURE(1.6),             // Estrutura construída
        VOID_AREA(100.0);           // Área vazia (evitar)
        
        public final double baseCost;
        
        TerrainType(double baseCost) {
            this.baseCost = baseCost;
        }
    }
    
    /**
     * Opções de caminho disponíveis
     */
    public static class PathOption {
        public Vec3 position;
        public PathNode.MovementType movementType;
        public double cost;
        public String description;
        
        public PathOption(Vec3 pos, PathNode.MovementType type, double cost, String desc) {
            this.position = pos;
            this.movementType = type;
            this.cost = cost;
            this.description = desc;
        }
    }
    
    /**
     * Analisa o terreno em uma posição específica
     */
    public TerrainInfo analyzePosition(BlockPos pos) {
        // Verificar cache primeiro
        TerrainInfo cached = terrainCache.get(pos);
        if (cached != null && (System.currentTimeMillis() - cached.timestamp) < CACHE_LIFETIME) {
            return cached;
        }
        
        // Limpar cache antigo periodicamente
        cleanOldCache();
        
        World world = mc.theWorld;
        if (world == null) return createDefaultTerrain();
        
        TerrainInfo info = new TerrainInfo();
        
        // Análise básica do bloco
        Block currentBlock = world.getBlockState(pos).getBlock();
        Block groundBlock = world.getBlockState(pos.down()).getBlock();
        Block aboveBlock = world.getBlockState(pos.up()).getBlock();
        
        // Determinar nível do chão
        info.groundLevel = findGroundLevel(pos);
        
        // Análise de líquidos
        info.hasWater = isWater(currentBlock) || isWater(groundBlock);
        info.hasLava = isLava(currentBlock) || isLava(groundBlock);
        info.isDangerous = info.hasLava || isDangerousBlock(currentBlock);
        
        // Análise de caminhabilidade
        info.isWalkable = isBlockPassable(currentBlock) && isBlockPassable(aboveBlock);
        
        // Análise do tipo de terreno
        info.type = determineTerrainType(pos, info);
        info.movementCost = calculateMovementCost(pos, info);
        
        // Análise de opções de movimento
        info.pathOptions = analyzeMovementOptions(pos, info);
        
        // Verificar se requer pulo
        info.requiresJump = requiresJumpMovement(pos, info);
        
        // Armazenar no cache
        terrainCache.put(pos, info);
        
        return info;
    }
    
    /**
     * Determina o tipo de terreno baseado na análise do ambiente
     */
    private TerrainType determineTerrainType(BlockPos pos, TerrainInfo info) {
        World world = mc.theWorld;
        
        // Verificar líquidos primeiro
        if (info.hasLava) return TerrainType.LAVA;
        if (info.hasWater) {
            int waterDepth = calculateWaterDepth(pos);
            return waterDepth > 2 ? TerrainType.WATER_DEEP : TerrainType.WATER_SHALLOW;
        }
        
        // Análise de elevação
        int[] elevationProfile = getElevationProfile(pos);
        double elevationVariance = calculateVariance(elevationProfile);
        
        if (elevationVariance > 25) return TerrainType.CLIFF;
        if (elevationVariance > 10) return TerrainType.STEEP_HILL;
        if (elevationVariance > 4) return TerrainType.ROUGH_TERRAIN;
        
        // Análise de densidade de blocos
        double blockDensity = calculateBlockDensity(pos);
        
        // Verificar se está underground
        if (pos.getY() < 50 && blockDensity > 0.7) return TerrainType.UNDERGROUND;
        
        // Análise de bioma/estrutura
        if (blockDensity > 0.8) return TerrainType.STRUCTURE;
        if (hasTreesNearby(pos)) return TerrainType.FOREST;
        if (isMountainous(pos)) return TerrainType.MOUNTAIN;
        if (isBridgeArea(pos)) return TerrainType.BRIDGE_AREA;
        if (isCaveEntrance(pos)) return TerrainType.CAVE_ENTRANCE;
        if (isDesertLike(pos)) return TerrainType.DESERT;
        if (isVoidArea(pos)) return TerrainType.VOID_AREA;
        
        return TerrainType.FLAT_GROUND;
    }
    
    /**
     * Calcula o custo de movimento considerando o terreno
     */
    private double calculateMovementCost(BlockPos pos, TerrainInfo info) {
        double baseCost = info.type.baseCost;
        
        // Modificadores baseados no ambiente
        if (info.isDangerous) baseCost *= 3.0;
        if (info.requiresJump) baseCost *= 1.3;
        if (info.hasWater && !info.hasLava) baseCost *= 1.5;
        
        // Análise de inclinação
        double slope = calculateSlope(pos);
        if (slope > 0.5) baseCost *= (1.0 + slope);
        
        // Análise de obstáculos próximos
        int obstacleCount = countNearbyObstacles(pos);
        baseCost *= (1.0 + obstacleCount * 0.1);
        
        return Math.max(0.5, baseCost);
    }
    
    /**
     * Analisa opções de movimento disponíveis
     */
    private List<PathOption> analyzeMovementOptions(BlockPos pos, TerrainInfo info) {
        List<PathOption> options = new ArrayList<>();
        World world = mc.theWorld;
        
        // Verificar movimento horizontal
        for (int dx = -1; dx <= 1; dx++) {
            for (int dz = -1; dz <= 1; dz++) {
                if (dx == 0 && dz == 0) continue;
                
                BlockPos targetPos = pos.add(dx, 0, dz);
                if (canWalkTo(pos, targetPos)) {
                    PathNode.MovementType moveType = (Math.abs(dx) + Math.abs(dz) == 1) ? 
                        PathNode.MovementType.WALK : PathNode.MovementType.DIAGONAL;
                    double cost = moveType.baseCost * info.movementCost;
                    options.add(new PathOption(
                        new Vec3(targetPos.getX() + 0.5, targetPos.getY(), targetPos.getZ() + 0.5),
                        moveType, cost, "Movimento horizontal"
                    ));
                }
            }
        }
        
        // Verificar movimento vertical
        if (canJumpUp(pos)) {
            BlockPos upPos = pos.up();
            double cost = PathNode.MovementType.JUMP.baseCost * info.movementCost;
            options.add(new PathOption(
                new Vec3(upPos.getX() + 0.5, upPos.getY(), upPos.getZ() + 0.5),
                PathNode.MovementType.JUMP, cost, "Pulo para cima"
            ));
        }
        
        if (canFallDown(pos)) {
            BlockPos downPos = pos.down();
            double cost = PathNode.MovementType.FALL.baseCost * info.movementCost;
            options.add(new PathOption(
                new Vec3(downPos.getX() + 0.5, downPos.getY(), downPos.getZ() + 0.5),
                PathNode.MovementType.FALL, cost, "Queda controlada"
            ));
        }
        
        // Verificar movimento em água
        if (info.hasWater) {
            for (int dy = -1; dy <= 1; dy++) {
                BlockPos swimPos = pos.add(0, dy, 0);
                if (isWater(world.getBlockState(swimPos).getBlock())) {
                    double cost = PathNode.MovementType.SWIM.baseCost * info.movementCost;
                    options.add(new PathOption(
                        new Vec3(swimPos.getX() + 0.5, swimPos.getY(), swimPos.getZ() + 0.5),
                        PathNode.MovementType.SWIM, cost, "Natação"
                    ));
                }
            }
        }
        
        return options;
    }
    
    /**
     * Encontra o nível do chão real com precisão
     */
    private int findGroundLevel(BlockPos pos) {
        World world = mc.theWorld;

        // Primeiro verificar se já está no chão
        Block currentBlock = world.getBlockState(pos).getBlock();
        Block belowBlock = world.getBlockState(pos.down()).getBlock();

        if (isBlockSolid(belowBlock) && isBlockPassable(currentBlock)) {
            return pos.getY();
        }

        // Procurar para baixo até encontrar chão sólido
        for (int i = 1; i <= VERTICAL_SCAN_RANGE; i++) {
            BlockPos checkPos = pos.down(i);
            Block block = world.getBlockState(checkPos).getBlock();
            Block above = world.getBlockState(checkPos.up()).getBlock();

            if (isBlockSolid(block) && isBlockPassable(above)) {
                return checkPos.getY() + 1; // Retorna a posição onde o player ficaria
            }
        }

        // Se não encontrou para baixo, procurar para cima
        for (int i = 1; i <= 3; i++) { // Reduzir busca para cima
            BlockPos checkPos = pos.up(i);
            Block block = world.getBlockState(checkPos.down()).getBlock();
            Block current = world.getBlockState(checkPos).getBlock();

            if (isBlockSolid(block) && isBlockPassable(current)) {
                return checkPos.getY();
            }
        }

        return pos.getY(); // Fallback para posição atual
    }
    
    /**
     * Calcula perfil de elevação ao redor da posição
     */
    private int[] getElevationProfile(BlockPos center) {
        List<Integer> elevations = new ArrayList<>();
        
        for (int dx = -ANALYSIS_RADIUS; dx <= ANALYSIS_RADIUS; dx++) {
            for (int dz = -ANALYSIS_RADIUS; dz <= ANALYSIS_RADIUS; dz++) {
                BlockPos pos = center.add(dx, 0, dz);
                elevations.add(findGroundLevel(pos));
            }
        }
        
        return elevations.stream().mapToInt(Integer::intValue).toArray();
    }
    
    /**
     * Calcula variância de elevação
     */
    private double calculateVariance(int[] elevations) {
        if (elevations.length == 0) return 0;
        
        double mean = Arrays.stream(elevations).average().orElse(0);
        double variance = Arrays.stream(elevations)
            .mapToDouble(e -> Math.pow(e - mean, 2))
            .average().orElse(0);
        
        return variance;
    }
    
    /**
     * Calcula densidade de blocos na área
     */
    private double calculateBlockDensity(BlockPos center) {
        World world = mc.theWorld;
        int totalBlocks = 0;
        int solidBlocks = 0;
        
        for (int dx = -2; dx <= 2; dx++) {
            for (int dy = -2; dy <= 2; dy++) {
                for (int dz = -2; dz <= 2; dz++) {
                    BlockPos pos = center.add(dx, dy, dz);
                    Block block = world.getBlockState(pos).getBlock();
                    totalBlocks++;
                    if (isBlockSolid(block)) solidBlocks++;
                }
            }
        }
        
        return totalBlocks > 0 ? (double) solidBlocks / totalBlocks : 0;
    }
    
    /**
     * Calcula inclinação do terreno
     */
    private double calculateSlope(BlockPos pos) {
        int currentLevel = findGroundLevel(pos);
        int maxDiff = 0;
        
        for (int dx = -1; dx <= 1; dx++) {
            for (int dz = -1; dz <= 1; dz++) {
                if (dx == 0 && dz == 0) continue;
                int neighborLevel = findGroundLevel(pos.add(dx, 0, dz));
                maxDiff = Math.max(maxDiff, Math.abs(neighborLevel - currentLevel));
            }
        }
        
        return maxDiff / 3.0; // Normalizar
    }
    
    /**
     * Conta obstáculos próximos
     */
    private int countNearbyObstacles(BlockPos center) {
        World world = mc.theWorld;
        int obstacles = 0;
        
        for (int dx = -2; dx <= 2; dx++) {
            for (int dz = -2; dz <= 2; dz++) {
                BlockPos pos = center.add(dx, 0, dz);
                Block block = world.getBlockState(pos).getBlock();
                if (!isBlockPassable(block)) obstacles++;
            }
        }
        
        return obstacles;
    }
    
    // Métodos auxiliares de verificação de terreno
    private boolean hasTreesNearby(BlockPos pos) {
        World world = mc.theWorld;
        for (int dx = -3; dx <= 3; dx++) {
            for (int dz = -3; dz <= 3; dz++) {
                for (int dy = 0; dy <= 5; dy++) {
                    Block block = world.getBlockState(pos.add(dx, dy, dz)).getBlock();
                    if (block == Blocks.log || block == Blocks.log2 || block == Blocks.leaves || block == Blocks.leaves2) {
                        return true;
                    }
                }
            }
        }
        return false;
    }
    
    private boolean isMountainous(BlockPos pos) {
        int[] elevations = getElevationProfile(pos);
        double variance = calculateVariance(elevations);
        return variance > 15 && pos.getY() > 80;
    }
    
    private boolean isBridgeArea(BlockPos pos) {
        World world = mc.theWorld;
        Block groundBlock = world.getBlockState(pos.down()).getBlock();
        
        // Verificar se há espaço vazio embaixo (indicativo de ponte)
        for (int i = 2; i <= 5; i++) {
            Block belowBlock = world.getBlockState(pos.down(i)).getBlock();
            if (belowBlock == Blocks.air) return true;
        }
        
        return false;
    }
    
    private boolean isCaveEntrance(BlockPos pos) {
        World world = mc.theWorld;
        int airBlocks = 0;
        int solidBlocks = 0;
        
        // Verificar padrão de caverna (muito ar com alguns blocos sólidos)
        for (int dx = -2; dx <= 2; dx++) {
            for (int dy = -2; dy <= 2; dy++) {
                for (int dz = -2; dz <= 2; dz++) {
                    Block block = world.getBlockState(pos.add(dx, dy, dz)).getBlock();
                    if (block == Blocks.air) airBlocks++;
                    else if (isBlockSolid(block)) solidBlocks++;
                }
            }
        }
        
        return airBlocks > solidBlocks && solidBlocks > 5;
    }
    
    private boolean isDesertLike(BlockPos pos) {
        World world = mc.theWorld;
        Block groundBlock = world.getBlockState(pos.down()).getBlock();
        return groundBlock == Blocks.sand || groundBlock == Blocks.sandstone;
    }
    
    private boolean isVoidArea(BlockPos pos) {
        return pos.getY() < 5 || findGroundLevel(pos) < 0;
    }
    
    private int calculateWaterDepth(BlockPos pos) {
        World world = mc.theWorld;
        int depth = 0;
        
        for (int i = 0; i < 10; i++) {
            Block block = world.getBlockState(pos.down(i)).getBlock();
            if (isWater(block)) depth++;
            else break;
        }
        
        return depth;
    }
    
    private boolean requiresJumpMovement(BlockPos pos, TerrainInfo info) {
        World world = mc.theWorld;

        // Verificar se há obstáculos que requerem pulo
        for (int dx = -1; dx <= 1; dx++) {
            for (int dz = -1; dz <= 1; dz++) {
                if (dx == 0 && dz == 0) continue;

                BlockPos neighborPos = pos.add(dx, 0, dz);

                // Verificar se há bloco sólido na altura dos pés que bloqueia movimento
                Block blockAtFeet = world.getBlockState(neighborPos).getBlock();
                if (isBlockSolid(blockAtFeet)) {
                    // Verificar se pode pular por cima
                    Block blockAbove = world.getBlockState(neighborPos.up()).getBlock();
                    if (isBlockPassable(blockAbove)) {
                        return true; // Precisa pular para passar
                    }
                }

                // Verificar diferença de altura significativa
                int currentGround = findGroundLevel(pos);
                int neighborGround = findGroundLevel(neighborPos);
                if (neighborGround > currentGround) {
                    return true; // Precisa pular para subir
                }
            }
        }

        return false;
    }
    
    // Métodos de verificação de movimento
    private boolean canWalkTo(BlockPos from, BlockPos to) {
        World world = mc.theWorld;

        // Verificar se a posição de destino é válida
        Block targetBlock = world.getBlockState(to).getBlock();
        Block targetAbove = world.getBlockState(to.up()).getBlock();
        Block targetGround = world.getBlockState(to.down()).getBlock();

        // Deve ter espaço para o player (2 blocos de altura)
        if (!isBlockPassable(targetBlock) || !isBlockPassable(targetAbove)) {
            return false;
        }

        // Deve ter chão sólido ou ser água
        if (!isBlockSolid(targetGround) && !isWater(targetBlock)) {
            return false;
        }

        // Verificar diferença de altura (não pode ser muito alta)
        int heightDiff = to.getY() - from.getY();
        if (Math.abs(heightDiff) > 1) {
            return false; // Movimento muito vertical para caminhada simples
        }

        return true;
    }
    
    private boolean canJumpUp(BlockPos pos) {
        World world = mc.theWorld;
        BlockPos upPos = pos.up();

        // Verificar se há espaço para pular (2 blocos de altura)
        Block upBlock = world.getBlockState(upPos).getBlock();
        Block upAbove = world.getBlockState(upPos.up()).getBlock();

        if (!isBlockPassable(upBlock) || !isBlockPassable(upAbove)) {
            return false;
        }

        // Verificar se há chão sólido para pousar
        Block upGround = world.getBlockState(upPos.down()).getBlock();
        if (!isBlockSolid(upGround) && !isWater(upBlock)) {
            return false;
        }

        // Verificar se não há obstáculo acima da posição atual
        Block currentAbove = world.getBlockState(pos.up(2)).getBlock();
        if (!isBlockPassable(currentAbove)) {
            return false; // Não há espaço para pular
        }

        return true;
    }
    
    private boolean canFallDown(BlockPos pos) {
        World world = mc.theWorld;
        BlockPos downPos = pos.down();
        Block downBlock = world.getBlockState(downPos).getBlock();
        
        return isBlockPassable(downBlock) || isWater(downBlock);
    }
    
    // Métodos auxiliares de verificação de blocos
    private boolean isBlockPassable(Block block) {
        return block == Blocks.air || isWater(block) || isLava(block) || 
               block.getMaterial() == Material.plants || !block.getMaterial().isSolid();
    }
    
    private boolean isBlockSolid(Block block) {
        return block != Blocks.air && !isWater(block) && !isLava(block) && 
               block.getMaterial().isSolid();
    }
    
    private boolean isWater(Block block) {
        return block == Blocks.water || block == Blocks.flowing_water;
    }
    
    private boolean isLava(Block block) {
        return block == Blocks.lava || block == Blocks.flowing_lava;
    }
    
    private boolean isDangerousBlock(Block block) {
        return isLava(block) || block == Blocks.cactus || block == Blocks.fire;
    }
    
    private TerrainInfo createDefaultTerrain() {
        TerrainInfo info = new TerrainInfo();
        info.type = TerrainType.FLAT_GROUND;
        info.movementCost = 1.0;
        info.isWalkable = true;
        return info;
    }
    
    private void cleanOldCache() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastCacheClean > 60000) { // Limpar a cada minuto
            terrainCache.entrySet().removeIf(entry -> 
                currentTime - entry.getValue().timestamp > CACHE_LIFETIME);
            lastCacheClean = currentTime;
        }
    }
    
    /**
     * Obtém informações de terreno com cache
     */
    public TerrainInfo getTerrainInfo(BlockPos pos) {
        return analyzePosition(pos);
    }
    
    /**
     * Limpa todo o cache
     */
    public void clearCache() {
        terrainCache.clear();
    }
}
