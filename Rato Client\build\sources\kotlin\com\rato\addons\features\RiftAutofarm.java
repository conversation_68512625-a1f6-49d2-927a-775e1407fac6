package com.rato.addons.features;

import com.rato.addons.config.RatoAddonsConfigSimple;
import com.rato.addons.pathfinding.BaritoneStylePathfinder;
import com.rato.addons.pathfinding.movement.BaritoneMovementExecutor;
import com.rato.addons.pathfinding.movement.SimpleCameraController;
import com.rato.addons.features.combat.UltraSmoothAimbot;
import com.rato.addons.util.Logger;
import com.rato.addons.util.AdvancedESP;
import com.rato.addons.render.ModernESP;
import net.minecraft.client.Minecraft;
import net.minecraft.entity.Entity;
import net.minecraft.entity.monster.EntityZombie;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.item.ItemStack;
import net.minecraft.item.ItemSword;
import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;
import net.minecraftforge.client.event.RenderWorldLastEvent;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;

import java.awt.*;
import java.lang.reflect.Field;
import java.util.List;
import java.util.ArrayList;
import java.util.Comparator;

/**
 * Sistema de Autofarm para Rift
 * Mata zumbis automaticamente em área definida por waypoints
 */
public class RiftAutofarm {

    private static RiftAutofarm instance;
    private final Minecraft mc = Minecraft.getMinecraft();

    // Estado do sistema
    private boolean isActive = false;
    private boolean isPaused = false;

    // Waypoints da área
    private BlockPos waypoint1 = null;
    private BlockPos waypoint2 = null;
    private FarmArea farmArea = null;

    // Sistema de combate
    private EntityZombie currentTarget = null;
    private long lastAttackTime = 0;
    private StrafeDirection strafeDirection = StrafeDirection.LEFT;
    private int strafeTimer = 0;

    // Sistema de pathfinding nativo do RatoClient
    private BaritoneStylePathfinder pathfinder;
    private BaritoneMovementExecutor movementExecutor;
    private SimpleCameraController cameraController;
    private List<BaritoneStylePathfinder.BaritoneNode> currentPath = null;
    private boolean isMovingToTarget = false;

    // Estatísticas
    private int zombiesKilled = 0;
    private long sessionStartTime = 0;

    public enum StrafeDirection {
        LEFT, RIGHT, FORWARD, BACKWARD
    }

    public enum AutofarmState {
        IDLE("Idle"),
        SCANNING("Scanning for zombies"),
        MOVING("Moving to target"),
        FIGHTING("Fighting zombie"),
        PAUSED("Paused");

        private final String displayName;

        AutofarmState(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    private AutofarmState currentState = AutofarmState.IDLE;

    private RiftAutofarm() {
        this.pathfinder = new BaritoneStylePathfinder();
        this.movementExecutor = new BaritoneMovementExecutor();
        this.cameraController = new SimpleCameraController();

        MinecraftForge.EVENT_BUS.register(this);
    }

    public static RiftAutofarm getInstance() {
        if (instance == null) {
            instance = new RiftAutofarm();
        }
        return instance;
    }

    // === CONTROLES PRINCIPAIS ===

    public void startAutofarm() {
        if (farmArea == null) {
            Logger.sendMessage("§c[Rift] Defina a área de farm primeiro! Use os botões Set WP1 e Set WP2.");
            return;
        }

        if (!hasIronSword()) {
            Logger.sendMessage("§c[Rift] Espada de ferro não encontrada no inventário!");
            return;
        }

        isActive = true;
        isPaused = false;
        sessionStartTime = System.currentTimeMillis();
        zombiesKilled = 0;
        currentState = AutofarmState.SCANNING;

        // DESABILITADO: SimpleCameraController conflita com aimbot
        // cameraController.enable();

        Logger.sendMessage("§a[Rift] Autofarm iniciado! Área: " + farmArea.getSize() + " blocos²");
        Logger.sendMessage("§7[Rift] Use 'Stop Autofarm' para parar o sistema.");
    }

    public void stopAutofarm() {
        isActive = false;
        isPaused = false;
        currentTarget = null;
        currentPath = null;
        isMovingToTarget = false;
        currentState = AutofarmState.IDLE;

        // Desativar sistemas de movimento
        if (movementExecutor != null && movementExecutor.isExecuting()) {
            movementExecutor.stopExecution();
        }
        // DESABILITADO: SimpleCameraController não é mais usado
        // if (cameraController != null) {
        // cameraController.disable();
        // }

        // Parar movimento
        stopMovement();

        long sessionTime = (System.currentTimeMillis() - sessionStartTime) / 1000;
        Logger.sendMessage("§c[Rift] Autofarm parado!");
        Logger.sendMessage("§7[Rift] Sessão: " + sessionTime + "s, Zumbis mortos: " + zombiesKilled);
    }

    public void pauseAutofarm() {
        if (isActive) {
            isPaused = true;
            currentState = AutofarmState.PAUSED;
            Logger.sendMessage("§e[Rift] Autofarm pausado.");
        }
    }

    public void resumeAutofarm() {
        if (isActive && isPaused) {
            isPaused = false;
            currentState = AutofarmState.SCANNING;
            Logger.sendMessage("§a[Rift] Autofarm retomado.");
        }
    }

    // === CONFIGURAÇÃO DE ÁREA ===

    public void setWaypoint1() {
        if (mc.thePlayer == null)
            return;

        waypoint1 = new BlockPos(mc.thePlayer.posX, mc.thePlayer.posY, mc.thePlayer.posZ);
        Logger.sendMessage("§a[Rift] Waypoint 1 definido: " + formatBlockPos(waypoint1));

        updateFarmArea();
    }

    public void setWaypoint2() {
        if (mc.thePlayer == null)
            return;

        waypoint2 = new BlockPos(mc.thePlayer.posX, mc.thePlayer.posY, mc.thePlayer.posZ);
        Logger.sendMessage("§a[Rift] Waypoint 2 definido: " + formatBlockPos(waypoint2));

        updateFarmArea();
    }

    public void clearArea() {
        waypoint1 = null;
        waypoint2 = null;
        farmArea = null;

        if (isActive) {
            stopAutofarm();
        }

        Logger.sendMessage("§c[Rift] Área de farm limpa.");
    }

    private void updateFarmArea() {
        if (waypoint1 != null && waypoint2 != null) {
            farmArea = new FarmArea(waypoint1, waypoint2);
            Logger.sendMessage("§e[Rift] Área de farm definida: " + farmArea.getSize() + " blocos²");
        }
    }

    // === SISTEMA PRINCIPAL ===

    @SubscribeEvent
    public void onTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.START || mc.thePlayer == null || !isActive || isPaused) {
            return;
        }

        // Verificar segurança
        if (!performSafetyChecks()) {
            return;
        }

        // Camera controller é atualizado automaticamente via eventos

        // Máquina de estados principal
        switch (currentState) {
            case IDLE:
                // Estado inativo - não fazer nada
                break;
            case SCANNING:
                handleScanning();
                break;
            case MOVING:
                handleMovement();
                break;
            case FIGHTING:
                handleCombat();
                break;
            case PAUSED:
                // Estado pausado - não fazer nada
                break;
        }
    }

    private boolean performSafetyChecks() {
        // Verificar vida
        if (mc.thePlayer.getHealth() <= RatoAddonsConfigSimple.riftHealthThreshold) {
            pauseAutofarm();
            Logger.sendMessage("§c[Rift] Vida baixa! Autofarm pausado.");
            return false;
        }

        // Verificar jogadores próximos
        if (RatoAddonsConfigSimple.riftPauseOnPlayer && hasNearbyPlayers()) {
            pauseAutofarm();
            Logger.sendMessage("§e[Rift] Jogador detectado! Autofarm pausado.");
            return false;
        }

        // Verificar espada
        if (!hasIronSword()) {
            pauseAutofarm();
            Logger.sendMessage("§c[Rift] Espada de ferro não encontrada! Autofarm pausado.");
            return false;
        }

        return true;
    }

    private void handleScanning() {
        currentTarget = findNearestZombie();

        if (currentTarget != null) {
            // Iniciar pathfinding para o alvo
            Vec3 start = new Vec3(mc.thePlayer.posX, mc.thePlayer.posY, mc.thePlayer.posZ);
            Vec3 goal = new Vec3(currentTarget.posX, currentTarget.posY, currentTarget.posZ);

            BaritoneStylePathfinder.PathfindingResult result = pathfinder.calculatePath(start, goal);

            if (result.success) {
                currentPath = result.path;
                movementExecutor.startExecution(result.path);
                isMovingToTarget = true;
                currentState = AutofarmState.MOVING;

                if (RatoAddonsConfigSimple.riftDebugMode) {
                    Logger.sendMessage("§7[Rift] Alvo encontrado: " + currentTarget.getName() +
                            " (" + String.format("%.1f", mc.thePlayer.getDistanceToEntity(currentTarget)) + "m)");
                    Logger.sendMessage("§7[Rift] Caminho calculado: " + result.path.size() + " nós");
                }
            } else {
                // Fallback para movimento direto
                currentState = AutofarmState.MOVING;
                if (RatoAddonsConfigSimple.riftDebugMode) {
                    Logger.sendMessage("§c[Rift] Pathfinding falhou, usando movimento direto");
                }
            }
        } else {
            // Debug: informar que não encontrou zumbis
            if (RatoAddonsConfigSimple.riftDebugMode) {
                Logger.sendMessage("§7[Rift] Nenhum zumbi encontrado na área. Continuando busca...");
            }
        }
    }

    private void handleMovement() {
        if (currentTarget == null || currentTarget.isDead) {
            movementExecutor.stopExecution();
            currentPath = null;
            currentState = AutofarmState.SCANNING;
            return;
        }

        double distanceToTarget = mc.thePlayer.getDistanceToEntity(currentTarget);

        // Verificar se chegou perto o suficiente para atacar (3.5 blocos para dar
        // margem)
        if (distanceToTarget <= 3.5) {
            // Parar movimento e iniciar combate
            if (movementExecutor.isExecuting()) {
                movementExecutor.stopExecution();
            }
            isMovingToTarget = false;
            currentState = AutofarmState.FIGHTING;
            strafeTimer = 0;

            if (RatoAddonsConfigSimple.riftDebugMode) {
                Logger.sendMessage("§a[Rift] Chegou ao alvo! Iniciando combate (distância: " +
                        String.format("%.1f", distanceToTarget) + "m)");
            }
        } else {
            // Verificar se o MovementExecutor ainda está executando
            if (!movementExecutor.isExecuting()) {
                // Aguardar um pouco antes de recalcular (evitar spam)
                if (System.currentTimeMillis() % 20 == 0) { // A cada 20 ticks
                    if (RatoAddonsConfigSimple.riftDebugMode) {
                        Logger.sendMessage("§7[Rift] Movimento parou, recalculando caminho... (distância: " +
                                String.format("%.1f", distanceToTarget) + "m)");
                    }

                    Vec3 start = new Vec3(mc.thePlayer.posX, mc.thePlayer.posY, mc.thePlayer.posZ);
                    Vec3 goal = new Vec3(currentTarget.posX, currentTarget.posY, currentTarget.posZ);

                    BaritoneStylePathfinder.PathfindingResult result = pathfinder.calculatePath(start, goal);

                    if (result.success && result.path.size() > 1) {
                        currentPath = result.path;
                        movementExecutor.startExecution(result.path);

                        if (RatoAddonsConfigSimple.riftDebugMode) {
                            Logger.sendMessage("§7[Rift] Novo caminho calculado: " + result.path.size() + " nodes");
                        }
                    } else {
                        // Fallback para movimento direto
                        if (RatoAddonsConfigSimple.riftDebugMode) {
                            Logger.sendMessage("§c[Rift] Pathfinding falhou, usando movimento direto");
                        }
                        moveDirectly();
                    }
                }
            }
        }
    }

    // === UTILITÁRIOS ===

    private String formatBlockPos(BlockPos pos) {
        return pos.getX() + ", " + pos.getY() + ", " + pos.getZ();
    }

    private boolean hasIronSword() {
        if (mc.thePlayer == null)
            return false;

        for (int i = 0; i < mc.thePlayer.inventory.getSizeInventory(); i++) {
            ItemStack stack = mc.thePlayer.inventory.getStackInSlot(i);
            if (stack != null && stack.getItem() instanceof ItemSword) {
                String itemName = stack.getUnlocalizedName().toLowerCase();
                if (itemName.contains("iron")) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean hasNearbyPlayers() {
        if (mc.theWorld == null)
            return false;

        List<EntityPlayer> players = mc.theWorld.getEntitiesWithinAABB(EntityPlayer.class,
                mc.thePlayer.getEntityBoundingBox().expand(
                        RatoAddonsConfigSimple.riftPlayerDetectionRange,
                        RatoAddonsConfigSimple.riftPlayerDetectionRange,
                        RatoAddonsConfigSimple.riftPlayerDetectionRange));

        // Remover o próprio jogador da lista
        players.removeIf(player -> player == mc.thePlayer);

        return !players.isEmpty();
    }

    public void showStatus() {
        Logger.sendMessage("§6=== RIFT AUTOFARM STATUS ===");
        Logger.sendMessage("§7Status: " + (isActive ? (isPaused ? "§ePausado" : "§aAtivo") : "§cInativo"));
        Logger.sendMessage("§7Estado: §e" + currentState.getDisplayName());
        Logger.sendMessage("§7Área: " + (farmArea != null ? "§a" + farmArea.getSize() + " blocos²" : "§cNão definida"));
        Logger.sendMessage("§7Alvo atual: " + (currentTarget != null ? "§e" + currentTarget.getName() : "§7Nenhum"));
        Logger.sendMessage("§7Zumbis mortos: §e" + zombiesKilled);

        if (isActive) {
            long sessionTime = (System.currentTimeMillis() - sessionStartTime) / 1000;
            Logger.sendMessage("§7Tempo de sessão: §e" + sessionTime + "s");
        }
    }

    private EntityZombie findNearestZombie() {
        if (mc.theWorld == null || farmArea == null)
            return null;

        List<EntityZombie> zombies = mc.theWorld.getEntitiesWithinAABB(EntityZombie.class,
                farmArea.getBoundingBox());

        if (RatoAddonsConfigSimple.riftDebugMode) {
            Logger.sendMessage("§7[Rift] Zumbis encontrados na área: " + zombies.size());
        }

        if (zombies.isEmpty())
            return null;

        // Filtrar zumbis válidos (vivos e dentro da área)
        zombies.removeIf(
                zombie -> zombie.isDead || !farmArea.contains(new BlockPos(zombie.posX, zombie.posY, zombie.posZ)));

        if (RatoAddonsConfigSimple.riftDebugMode) {
            Logger.sendMessage("§7[Rift] Zumbis válidos após filtro: " + zombies.size());
        }

        if (zombies.isEmpty())
            return null;

        // Ordenar por distância (mais próximo primeiro)
        zombies.sort(Comparator.comparingDouble(zombie -> mc.thePlayer.getDistanceToEntity(zombie)));

        return zombies.get(0);
    }

    private void moveDirectly() {
        if (currentTarget == null)
            return;

        // Calcular yaw para o alvo
        double dx = currentTarget.posX - mc.thePlayer.posX;
        double dz = currentTarget.posZ - mc.thePlayer.posZ;
        float targetYaw = (float) (Math.atan2(dz, dx) * 180.0 / Math.PI - 90.0);

        // DESABILITADO: Não usar SimpleCameraController quando aimbot está ativo
        // para evitar conflito entre sistemas de rotação
        // cameraController.setTargetYaw(targetYaw);

        // Calcular se a rotação está próxima manualmente
        float currentYaw = mc.thePlayer.rotationYaw;
        float yawDiff = Math.abs(normalizeAngleDifference(targetYaw - currentYaw));
        boolean canMove = yawDiff < 15.0f; // Tolerância de 15 graus

        if (canMove) {
            setKeyPressed(mc.gameSettings.keyBindForward, true);
            setKeyPressed(mc.gameSettings.keyBindLeft, false);
            setKeyPressed(mc.gameSettings.keyBindRight, false);
            setKeyPressed(mc.gameSettings.keyBindBack, false);
        } else {
            // Parar movimento enquanto rotaciona
            stopMovement();
        }
    }

    /**
     * Versão otimizada do handleCombat
     */
    private void handleCombat() {
        // Atualizar estado de combate
        updateCombatState();

        if (currentTarget == null || currentTarget.isDead) {
            if (currentTarget != null && currentTarget.isDead) {
                zombiesKilled++;
                if (RatoAddonsConfigSimple.riftDebugMode) {
                    Logger.sendMessage("§a[Rift] Zumbi morto! Total: " + zombiesKilled);
                }
            }
            currentTarget = null;
            currentPath = null;
            currentState = AutofarmState.SCANNING;
            stopMovement();
            return;
        }

        double distanceToTarget = mc.thePlayer.getDistanceToEntity(currentTarget);

        // Verificar se está muito longe
        if (distanceToTarget > 6.0) {
            if (RatoAddonsConfigSimple.riftDebugMode) {
                Logger.sendMessage(
                        "§7[Rift] Alvo muito longe (" + String.format("%.1f", distanceToTarget) + "m), perseguindo");
            }
            currentState = AutofarmState.MOVING;
            return;
        }

        // Sistema de aimbot LiquidBounce style
        updateLiquidAimbot();

        // Movimento sutil para evitar hits
        if (distanceToTarget > 2.5) {
            // Se está longe, aproximar
            setKeyPressed(mc.gameSettings.keyBindForward, true);
            setKeyPressed(mc.gameSettings.keyBindLeft, false);
            setKeyPressed(mc.gameSettings.keyBindRight, false);
            setKeyPressed(mc.gameSettings.keyBindBack, false);
        } else {
            // Se está próximo, fazer strafe leve
            performLightStrafe();
        }
    }

    // === SISTEMA DE CÂMERA FLUIDA ALTO TPS ===
    private float smoothYaw = 0f;
    private float smoothPitch = 0f;
    private long lastSmoothUpdate = System.currentTimeMillis();
    private static final long HIGH_TPS_INTERVAL = 16; // ~60 FPS para movimento ultra-fluido

    // === CONTROLE DE CÂMERA DURANTE COMBATE ===
    private boolean isInCombat = false;
    private static final long COMBAT_TIMEOUT = 2000; // 2 segundos sem atacar = sair do combate

    // Configurações de suavização
    private static final float SMOOTH_FACTOR = 0.85f; // Mais responsivo para seguir mobs
    private static final float MIN_MOVEMENT = 0.2f; // Dead zone menor para máxima sensibilidade
    private static final float MAX_DELTA_PER_TICK = 8.0f; // Movimento maior para seguir mobs rápidos

    /**
     * Atualiza o sistema UltraSmoothAimbot para Rift (Kotlin)
     */
    private void updateLiquidAimbot() {
        // Configurar UltraSmoothAimbot baseado nas configurações do Rift (ultra-suave)
        UltraSmoothAimbot.INSTANCE.getConfig().setEnabled(true);
        UltraSmoothAimbot.INSTANCE.getConfig().setRange(4.0f);
        UltraSmoothAimbot.INSTANCE.getConfig().setFov(90.0f);
        UltraSmoothAimbot.INSTANCE.getConfig().setAutoAttack(true);
        UltraSmoothAimbot.INSTANCE.getConfig().setAttackDelay(500);
        // Configurações para input real do mouse
        UltraSmoothAimbot.INSTANCE.getConfig().setMouseSensitivity(0.5f); // Sensibilidade do mouse
        UltraSmoothAimbot.INSTANCE.getConfig().setMaxMouseMovement(50.0f); // Movimento máximo por frame
        UltraSmoothAimbot.INSTANCE.getConfig().setAimPrecision(2.0f); // Precisão necessária para atacar
        UltraSmoothAimbot.INSTANCE.getConfig().setSnapSpeed(8.0f); // Velocidade de snap
        UltraSmoothAimbot.INSTANCE.getConfig().setSmoothingFactor(0.3f); // Suavização
        UltraSmoothAimbot.INSTANCE.getConfig().setAttackRange(4.0f); // Alcance para atacar
        UltraSmoothAimbot.INSTANCE.getConfig().setMinAimTime(100); // Tempo mínimo mirando
        UltraSmoothAimbot.INSTANCE.getConfig().setRealMouseInput(true); // Usar input real do mouse
        UltraSmoothAimbot.INSTANCE.getConfig().setPredictMovement(true);
        UltraSmoothAimbot.INSTANCE.getConfig().setPrioritizeClosest(true);
        UltraSmoothAimbot.INSTANCE.getConfig().setThroughWalls(true);
        UltraSmoothAimbot.INSTANCE.getConfig().setInterpolateRotation(true);
        UltraSmoothAimbot.INSTANCE.getConfig().setPrecisionMode(true);

        // Atualizar UltraSmoothAimbot
        UltraSmoothAimbot.INSTANCE.update();

        // Sincronizar alvo atual (verificar se é um zombie)
        if (UltraSmoothAimbot.INSTANCE.getCurrentTarget() != null
                && UltraSmoothAimbot.INSTANCE.getCurrentTarget() instanceof EntityZombie) {
            currentTarget = (EntityZombie) UltraSmoothAimbot.INSTANCE.getCurrentTarget();
        }
    }

    /**
     * Sistema de aimbot suave anti-travamento com unit vectors (LEGACY)
     */
    private void performSmoothAimbot(double distanceToTarget) {
        if (currentTarget == null || currentTarget.isDead) {
            return;
        }

        long currentTime = System.currentTimeMillis();

        // Controle de tick rate para evitar movimento muito rápido (60 FPS máximo)
        if (currentTime - lastSmoothUpdate < 16) {
            return; // Skip se muito rápido
        }
        lastSmoothUpdate = currentTime;

        // Calcular ângulos alvo
        double deltaX = currentTarget.posX - mc.thePlayer.posX;
        double deltaY = currentTarget.posY + currentTarget.getEyeHeight()
                - (mc.thePlayer.posY + mc.thePlayer.getEyeHeight());
        double deltaZ = currentTarget.posZ - mc.thePlayer.posZ;

        float targetYaw = (float) Math.toDegrees(Math.atan2(-deltaX, deltaZ));
        float targetPitch = (float) -Math.toDegrees(Math.atan2(deltaY, Math.sqrt(deltaX * deltaX + deltaZ * deltaZ)));

        // Normalizar ângulos
        targetYaw = normalizeAngle(targetYaw);
        targetPitch = Math.max(-90f, Math.min(90f, targetPitch));

        // Calcular diferenças angulares
        float yawDiff = normalizeAngleDifference(targetYaw - mc.thePlayer.rotationYaw);
        float pitchDiff = targetPitch - mc.thePlayer.rotationPitch;

        // THRESHOLD MAIS RIGOROSO - parar completamente quando próximo
        float alignmentThreshold = 1.5f; // Muito mais rigoroso

        // Verificar se está alinhado
        if (Math.abs(yawDiff) < alignmentThreshold && Math.abs(pitchDiff) < alignmentThreshold) {
            // PARAR COMPLETAMENTE - não aplicar nenhuma rotação
            if (distanceToTarget <= 3.5) {
                performAttack();
            }
            return; // IMPORTANTE: Sair sem aplicar rotação
        }

        // === MÉTODO UNIT VECTOR PROFISSIONAL ===
        // Calcular o comprimento do vetor (magnitude)
        float vectorLength = (float) Math.sqrt(yawDiff * yawDiff + pitchDiff * pitchDiff);
        if (vectorLength == 0)
            return;

        // Converter em unit vector (length = 1, direção mantida)
        float unitYaw = yawDiff / vectorLength;
        float unitPitch = pitchDiff / vectorLength;

        // Velocidade REDUZIDA para movimento mais suave
        float speed = calculateUnitVectorSpeed(distanceToTarget, vectorLength) * 0.4f; // 60% mais lento

        // Aplicar movimento usando unit vector * speed
        float finalYawMove = unitYaw * speed;
        float finalPitchMove = unitPitch * speed;

        // APLICAR APENAS SE O MOVIMENTO FOR SIGNIFICATIVO
        if (Math.abs(finalYawMove) > 0.05f || Math.abs(finalPitchMove) > 0.05f) {
            mc.thePlayer.rotationYaw += finalYawMove;
            mc.thePlayer.rotationPitch += finalPitchMove;
        }

        // Limitar pitch
        mc.thePlayer.rotationPitch = Math.max(-90f, Math.min(90f, mc.thePlayer.rotationPitch));

        // === SISTEMA DE AUTO-ATTACK MELHORADO ===
        // Verificar se está mirando no alvo e dentro do alcance
        boolean isAiming = vectorLength <= 8.0f; // FOV mais generoso
        boolean inRange = distanceToTarget <= 3.5;

        if (isAiming && inRange) {
            // Atacar automaticamente com cooldown
            if (currentTime - lastAttackTime >= 500) { // 500ms cooldown
                performAttack();
                lastAttackTime = currentTime;
            }
        }

        // Debug otimizado
        if (RatoAddonsConfigSimple.riftDebugMode && System.currentTimeMillis() % 1000 < 50) {
            Logger.sendMessage("§7[UnitVector] FOV: " + String.format("%.1f", vectorLength) +
                    "° | Dist: " + String.format("%.1f", distanceToTarget) + "m | Speed: "
                    + String.format("%.2f", speed));
        }
    }

    /**
     * Aplica rotação suave usando UNIT VECTORS (método profissional anti-tremor)
     */
    private void applySmoothRotation(float targetYaw, float targetPitch, float deltaTime, double distance) {
        // Controle de tick rate para evitar movimento muito rápido (60 FPS máximo)
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastSmoothUpdate < 16) {
            return; // Skip se muito rápido
        }
        lastSmoothUpdate = currentTime;

        float currentYaw = mc.thePlayer.rotationYaw;
        float currentPitch = mc.thePlayer.rotationPitch;

        // Calcular diferenças angulares
        float yawDiff = normalizeAngleDifference(targetYaw - currentYaw);
        float pitchDiff = targetPitch - currentPitch;

        // Verificar se o movimento é significativo
        if (Math.abs(yawDiff) < MIN_MOVEMENT && Math.abs(pitchDiff) < MIN_MOVEMENT) {
            return;
        }

        // === MÉTODO UNIT VECTOR PROFISSIONAL ===
        // Calcular o comprimento do vetor (magnitude)
        float vectorLength = (float) Math.sqrt(yawDiff * yawDiff + pitchDiff * pitchDiff);
        if (vectorLength == 0)
            return;

        // Converter em unit vector (length = 1, direção mantida)
        float unitYaw = yawDiff / vectorLength;
        float unitPitch = pitchDiff / vectorLength;

        // Velocidade baseada na distância e FOV (mais suave)
        float speed = calculateUnitVectorSpeed(distance, vectorLength);

        // Aplicar movimento usando unit vector * speed
        float finalYawMove = unitYaw * speed;
        float finalPitchMove = unitPitch * speed;

        // Aplicar rotação diretamente (unit vector já é naturalmente suave)
        mc.thePlayer.rotationYaw += finalYawMove;
        mc.thePlayer.rotationPitch += finalPitchMove;

        // Limitar pitch
        mc.thePlayer.rotationPitch = Math.max(-90f, Math.min(90f, mc.thePlayer.rotationPitch));
    }

    /**
     * Calcula velocidade para unit vector (método profissional anti-tremor)
     */
    private float calculateUnitVectorSpeed(double distance, float vectorLength) {
        // Velocidade base mais baixa para movimento suave
        float baseSpeed = 1.2f;

        // Ajustar baseado no FOV (vectorLength = magnitude do ângulo)
        if (vectorLength > 30.0f) {
            baseSpeed = 3.0f; // Snap moderado para ângulos grandes
        } else if (vectorLength > 15.0f) {
            baseSpeed = 2.2f; // Movimento controlado
        } else if (vectorLength > 8.0f) {
            baseSpeed = 1.8f; // Movimento suave
        } else if (vectorLength > 3.0f) {
            baseSpeed = 1.4f; // Movimento preciso
        } else {
            baseSpeed = 1.0f; // Movimento muito preciso para micro-ajustes
        }

        // Ajustar baseado na distância
        if (distance < 2.0) {
            baseSpeed *= 0.7f; // Mais devagar quando próximo
        } else if (distance > 4.0) {
            baseSpeed *= 1.2f; // Boost para alvos distantes
        }

        return baseSpeed;
    }

    /**
     * Atualiza o estado de combate baseado na atividade de ataque
     */
    private void updateCombatState() {
        long currentTime = System.currentTimeMillis();

        // Se atacou recentemente, está em combate
        if (currentTime - lastAttackTime < COMBAT_TIMEOUT) {
            if (!isInCombat) {
                isInCombat = true;
                // Desabilitar câmera do pathfinding durante combate
                if (movementExecutor != null) {
                    movementExecutor.setCombatMode(true);
                }
                Logger.sendLog("§a[COMBATE] §7Entrando em modo combate - câmera do pathfinding DESABILITADA");
            }
        } else {
            // Se não atacou há muito tempo, sair do combate
            if (isInCombat) {
                isInCombat = false;
                // Reabilitar câmera do pathfinding após combate
                if (movementExecutor != null) {
                    movementExecutor.setCombatMode(false);
                }
                Logger.sendLog("§c[COMBATE] §7Saindo do modo combate - câmera do pathfinding HABILITADA");
            }
        }
    }

    /**
     * Reset da suavização quando não há alvo
     */
    private void resetSmoothRotation() {
        smoothYaw *= 0.9f; // Reduzir gradualmente
        smoothPitch *= 0.9f;

        if (Math.abs(smoothYaw) < 0.01f)
            smoothYaw = 0;
        if (Math.abs(smoothPitch) < 0.01f)
            smoothPitch = 0;
    }

    /**
     * Interpolação linear
     */
    private float lerp(float start, float end, float factor) {
        return start + factor * (end - start);
    }

    /**
     * Calcula ângulos para o alvo com predição otimizada
     */
    private float[] calculateTargetAngles(EntityZombie target) {
        // Posição do jogador (olhos)
        double playerX = mc.thePlayer.posX;
        double playerY = mc.thePlayer.posY + mc.thePlayer.getEyeHeight();
        double playerZ = mc.thePlayer.posZ;

        // Predição simples e eficiente
        double motionX = target.posX - target.prevPosX;
        double motionZ = target.posZ - target.prevPosZ;
        double predictionFactor = 0.15; // Predição leve

        // Posição do alvo com predição
        double targetX = target.posX + (motionX * predictionFactor);
        double targetY = target.posY + target.getEyeHeight();
        double targetZ = target.posZ + (motionZ * predictionFactor);

        // Calcular diferença
        double deltaX = targetX - playerX;
        double deltaY = targetY - playerY;
        double deltaZ = targetZ - playerZ;

        // Calcular ângulos
        double horizontalDistance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);

        float yaw = (float) (Math.atan2(deltaZ, deltaX) * 180.0 / Math.PI) - 90.0f;
        float pitch = (float) -(Math.atan2(deltaY, horizontalDistance) * 180.0 / Math.PI);

        // Normalizar yaw
        while (yaw > 180.0f)
            yaw -= 360.0f;
        while (yaw < -180.0f)
            yaw += 360.0f;

        // Limitar pitch
        pitch = Math.max(-90f, Math.min(90f, pitch));

        return new float[] { yaw, pitch };
    }

    /**
     * Calcula multiplicador de velocidade baseado na situação
     */
    private float calculateSpeedMultiplier(double distance, float deltaYaw, float deltaPitch) {
        // FOV atual (distância angular)
        double fov = Math.sqrt(deltaYaw * deltaYaw + deltaPitch * deltaPitch);

        // Velocidade base
        float baseSpeed = 0.8f;

        // Ajustar baseado no FOV
        if (fov > 45.0) {
            // Longe do alvo - movimento rápido
            baseSpeed = 1.2f;
        } else if (fov < 5.0) {
            // Próximo do alvo - movimento mais preciso
            baseSpeed = 0.4f;
        }

        // Ajustar baseado na distância
        if (distance < 2.0) {
            // Muito próximo - mais precisão
            baseSpeed *= 0.7f;
        } else if (distance > 4.0) {
            // Distante - mais velocidade
            baseSpeed *= 1.3f;
        }

        // Boost para movimentos grandes (snap rápido)
        if (fov > 60.0) {
            baseSpeed *= 1.5f;
        }

        return baseSpeed;
    }

    /**
     * Strafe leve e eficiente
     */
    private void performLightStrafe() {
        strafeTimer++;

        // Alternar direção mais frequentemente
        if (strafeTimer >= 10) {
            strafeTimer = 0;
            strafeDirection = (strafeDirection == StrafeDirection.LEFT) ? StrafeDirection.RIGHT : StrafeDirection.LEFT;
        }

        // Movimento mais sutil
        switch (strafeDirection) {
            case LEFT:
                setKeyPressed(mc.gameSettings.keyBindLeft, true);
                setKeyPressed(mc.gameSettings.keyBindRight, false);
                setKeyPressed(mc.gameSettings.keyBindForward, false);
                setKeyPressed(mc.gameSettings.keyBindBack, false);
                break;
            case RIGHT:
                setKeyPressed(mc.gameSettings.keyBindLeft, false);
                setKeyPressed(mc.gameSettings.keyBindRight, true);
                setKeyPressed(mc.gameSettings.keyBindForward, false);
                setKeyPressed(mc.gameSettings.keyBindBack, false);
                break;
            default:
                stopMovement();
                break;
        }
    }

    /**
     * Normaliza ângulo para o range [0, 360]
     */
    private float normalizeAngle(float angle) {
        while (angle >= 360f)
            angle -= 360f;
        while (angle < 0f)
            angle += 360f;
        return angle;
    }

    /**
     * Versão otimizada de normalização de ângulos
     */
    private float normalizeAngleDifference(float angleDiff) {
        // Método mais eficiente
        if (angleDiff > 180.0f) {
            angleDiff -= 360.0f;
        } else if (angleDiff < -180.0f) {
            angleDiff += 360.0f;
        }
        return angleDiff;
    }

    /**
     * Função de easing para movimento mais natural (cubic ease-in-out)
     */
    private float easeInOutCubic(float t) {
        if (t < 0.5f) {
            return 4 * t * t * t;
        } else {
            float f = 2 * t - 2;
            return 1 + f * f * f / 2;
        }
    }

    /**
     * Prediz a posição futura do alvo baseada em sua velocidade
     */
    private Vec3 predictTargetPosition(EntityZombie target, float deltaTime) {
        double predictionTime = Math.min(0.2, deltaTime * 3); // Máximo 200ms de predição

        double motionX = target.posX - target.prevPosX;
        double motionY = target.posY - target.prevPosY;
        double motionZ = target.posZ - target.prevPosZ;

        // Aplicar predição apenas se o alvo estiver se movendo significativamente
        if (Math.abs(motionX) > 0.01 || Math.abs(motionZ) > 0.01) {
            return new Vec3(
                    target.posX + motionX * predictionTime,
                    target.posY + target.getEyeHeight() + motionY * predictionTime,
                    target.posZ + motionZ * predictionTime);
        }

        return new Vec3(target.posX, target.posY + target.getEyeHeight(), target.posZ);
    }

    /**
     * Calcula ângulos yaw e pitch para uma direção
     */
    private float[] calculateAngles(Vec3 direction) {
        double horizontalDistance = Math
                .sqrt(direction.xCoord * direction.xCoord + direction.zCoord * direction.zCoord);

        float yaw = (float) (Math.atan2(direction.zCoord, direction.xCoord) * 180.0 / Math.PI) - 90.0f;
        float pitch = (float) -(Math.atan2(direction.yCoord, horizontalDistance) * 180.0 / Math.PI);

        // Normalizar yaw
        while (yaw > 180.0f)
            yaw -= 360.0f;
        while (yaw < -180.0f)
            yaw += 360.0f;

        // Limitar pitch
        pitch = Math.max(-90f, Math.min(90f, pitch));

        return new float[] { yaw, pitch };
    }

    /**
     * Classe para configurações dinâmicas do aimbot
     */
    private static class AimbotSettings {
        public double maxFov;
        public double smoothness;
        public double speed;
        public double attackFov;
        public boolean useAcceleration;

        public AimbotSettings(double maxFov, double smoothness, double speed, double attackFov,
                boolean useAcceleration) {
            this.maxFov = maxFov;
            this.smoothness = smoothness;
            this.speed = speed;
            this.attackFov = attackFov;
            this.useAcceleration = useAcceleration;
        }
    }

    /**
     * Calcula configurações dinâmicas baseadas na situação
     */
    private AimbotSettings calculateDynamicSettings(double distance, double fov, float deltaTime) {
        // Configurações base
        double maxFov = 120.0;
        double smoothness = 0.15; // Menor = mais suave
        double speed = 1.0;
        double attackFov = 20.0;
        boolean useAcceleration = true;

        // Ajustar baseado na distância
        if (distance < 2.0) {
            // Alvo muito próximo - mais suave e preciso
            smoothness = 0.08;
            speed = 0.6;
            attackFov = 25.0;
        } else if (distance > 4.0) {
            // Alvo distante - mais rápido
            smoothness = 0.25;
            speed = 1.5;
            attackFov = 15.0;
        }

        // Ajustar baseado no FOV atual
        if (fov < 10.0) {
            // Já está próximo do alvo - movimentos muito suaves
            smoothness = 0.05;
            speed = 0.4;
        } else if (fov > 45.0) {
            // Longe do alvo - movimentos mais rápidos
            smoothness = 0.3;
            speed = 2.0;
        }

        // Sistema simples - sem bonus especiais

        return new AimbotSettings(maxFov, smoothness, speed, attackFov, useAcceleration);
    }

    /**
     * Verifica se deve aplicar aimbot
     */
    private boolean shouldApplyAimbot(double fov, double distance, AimbotSettings settings) {
        // Verificar FOV máximo
        if (fov > settings.maxFov)
            return false;

        // Verificar distância máxima
        if (distance > 4.5)
            return false;

        // Verificar se o alvo ainda existe e está vivo
        if (currentTarget == null || currentTarget.isDead)
            return false;

        return true;
    }

    /**
     * Verifica se pode atacar o alvo
     */
    private boolean canAttack(double fov, double distance) {
        AimbotSettings settings = calculateDynamicSettings(distance, fov, 0.016f);

        // Verificar FOV de ataque
        if (fov > settings.attackFov)
            return false;

        // Verificar distância de ataque
        if (distance > 3.5)
            return false;

        // Verificar se está olhando aproximadamente para o alvo
        return fov <= settings.attackFov;
    }

    /**
     * Aplica rotação suave usando interpolação avançada
     */
    private void applySmoothRotation(float deltaYaw, float deltaPitch, AimbotSettings settings, float deltaTime) {
        // Calcular fatores de suavização baseados na velocidade angular
        float yawSpeed = Math.abs(deltaYaw) / deltaTime;
        float pitchSpeed = Math.abs(deltaPitch) / deltaTime;

        // Aplicar curva de suavização (ease-out)
        float yawSmoothFactor = (float) (settings.smoothness * (1.0 + Math.exp(-yawSpeed / 100.0)));
        float pitchSmoothFactor = (float) (settings.smoothness * (1.0 + Math.exp(-pitchSpeed / 100.0)));

        // Calcular movimento suave usando LERP
        float yawMove = deltaYaw * yawSmoothFactor * (float) settings.speed;
        float pitchMove = deltaPitch * pitchSmoothFactor * (float) settings.speed;

        // Limitar velocidade máxima para evitar movimentos bruscos
        float maxYawMove = 15.0f * deltaTime; // Máximo 15 graus por segundo
        float maxPitchMove = 10.0f * deltaTime; // Máximo 10 graus por segundo

        yawMove = Math.max(-maxYawMove, Math.min(maxYawMove, yawMove));
        pitchMove = Math.max(-maxPitchMove, Math.min(maxPitchMove, pitchMove));

        // Aplicar aceleração se habilitada
        if (settings.useAcceleration) {
            // Acelerar quando longe do alvo, desacelerar quando próximo
            double fov = Math.sqrt(deltaYaw * deltaYaw + deltaPitch * deltaPitch);
            float accelerationFactor = (float) Math.max(0.3, Math.min(2.0, fov / 20.0));

            yawMove *= accelerationFactor;
            pitchMove *= accelerationFactor;
        }

        // Aplicar rotação final
        mc.thePlayer.rotationYaw += yawMove;
        mc.thePlayer.rotationPitch += pitchMove;

        // Limitar pitch
        mc.thePlayer.rotationPitch = Math.max(-90f, Math.min(90f, mc.thePlayer.rotationPitch));

        // Sistema de lock estável - não precisa atualizar valores
    }

    private void performAttack() {
        long currentTime = System.currentTimeMillis();
        long attackDelay = 1000 / RatoAddonsConfigSimple.riftAttackSpeed; // Converter CPS para delay

        if (currentTime - lastAttackTime >= attackDelay) {
            // Equipar espada de ferro
            equipIronSword();

            // Atacar
            if (mc.playerController != null && currentTarget != null) {
                mc.playerController.attackEntity(mc.thePlayer, currentTarget);
                mc.thePlayer.swingItem();
                lastAttackTime = currentTime;

                if (RatoAddonsConfigSimple.riftDebugMode) {
                    Logger.sendMessage("§7[Rift] Atacando " + currentTarget.getName() +
                            " (Vida: " + String.format("%.1f", currentTarget.getHealth()) + ")");
                }
            }
        }
    }

    private void performStrafe() {
        strafeTimer++;

        // Mudar direção do strafe a cada 15 ticks (mais dinâmico)
        if (strafeTimer >= 15) {
            strafeTimer = 0;
            strafeDirection = StrafeDirection.values()[(strafeDirection.ordinal() + 1)
                    % StrafeDirection.values().length];
        }

        // Aplicar movimento de strafe mais suave (não parar completamente)
        switch (strafeDirection) {
            case LEFT:
                setKeyPressed(mc.gameSettings.keyBindLeft, true);
                setKeyPressed(mc.gameSettings.keyBindRight, false);
                setKeyPressed(mc.gameSettings.keyBindForward, false);
                setKeyPressed(mc.gameSettings.keyBindBack, false);
                break;
            case RIGHT:
                setKeyPressed(mc.gameSettings.keyBindLeft, false);
                setKeyPressed(mc.gameSettings.keyBindRight, true);
                setKeyPressed(mc.gameSettings.keyBindForward, false);
                setKeyPressed(mc.gameSettings.keyBindBack, false);
                break;
            case FORWARD:
                setKeyPressed(mc.gameSettings.keyBindLeft, false);
                setKeyPressed(mc.gameSettings.keyBindRight, false);
                setKeyPressed(mc.gameSettings.keyBindForward, true);
                setKeyPressed(mc.gameSettings.keyBindBack, false);
                break;
            case BACKWARD:
                setKeyPressed(mc.gameSettings.keyBindLeft, false);
                setKeyPressed(mc.gameSettings.keyBindRight, false);
                setKeyPressed(mc.gameSettings.keyBindForward, false);
                setKeyPressed(mc.gameSettings.keyBindBack, true);
                break;
        }
    }

    private void equipIronSword() {
        for (int i = 0; i < 9; i++) { // Hotbar slots
            ItemStack stack = mc.thePlayer.inventory.getStackInSlot(i);
            if (stack != null && stack.getItem() instanceof ItemSword) {
                String itemName = stack.getUnlocalizedName().toLowerCase();
                if (itemName.contains("iron")) {
                    mc.thePlayer.inventory.currentItem = i;
                    return;
                }
            }
        }
    }

    private void stopMovement() {
        setKeyPressed(mc.gameSettings.keyBindForward, false);
        setKeyPressed(mc.gameSettings.keyBindLeft, false);
        setKeyPressed(mc.gameSettings.keyBindRight, false);
        setKeyPressed(mc.gameSettings.keyBindBack, false);
    }

    /**
     * Método auxiliar para definir o estado de uma tecla usando reflexão
     */
    private void setKeyPressed(net.minecraft.client.settings.KeyBinding keyBinding, boolean pressed) {
        try {
            Field pressedField = net.minecraft.client.settings.KeyBinding.class.getDeclaredField("pressed");
            pressedField.setAccessible(true);
            pressedField.setBoolean(keyBinding, pressed);
        } catch (Exception e) {
            Logger.sendMessage("§c[Rift] Erro ao controlar tecla: " + e.getMessage());
        }
    }

    // === RENDERIZAÇÃO ===

    @SubscribeEvent
    public void onRenderWorld(RenderWorldLastEvent event) {
        if (mc.thePlayer == null || !RatoAddonsConfigSimple.riftShowArea) {
            return;
        }

        // Atualizar matrizes para ESP avançado
        AdvancedESP.updateMatrices();

        // Renderizar waypoints individuais
        if (waypoint1 != null) {
            renderWaypoint(waypoint1, "WP1", 0xFF00FF00, event.partialTicks); // Verde
        }

        if (waypoint2 != null) {
            renderWaypoint(waypoint2, "WP2", 0xFF0000FF, event.partialTicks); // Azul
        }

        // Renderizar área de farm
        if (farmArea != null) {
            renderFarmArea(event.partialTicks);
        }

        // Renderizar alvo atual com ESP avançado
        if (currentTarget != null && isActive) {
            renderAdvancedTarget(event.partialTicks);
        }

        // Renderizar caminho até o mob
        if (currentPath != null && isActive && RatoAddonsConfigSimple.riftShowArea) {
            renderPath(event.partialTicks);
        }

        // Renderizar todos os zumbis na área com ESP avançado
        if (isActive && farmArea != null) {
            renderAllZombiesESP();
        }
    }

    private void renderWaypoint(BlockPos pos, String label, int color, float partialTicks) {
        double playerX = mc.thePlayer.lastTickPosX + (mc.thePlayer.posX - mc.thePlayer.lastTickPosX) * partialTicks;
        double playerY = mc.thePlayer.lastTickPosY + (mc.thePlayer.posY - mc.thePlayer.lastTickPosY) * partialTicks;
        double playerZ = mc.thePlayer.lastTickPosZ + (mc.thePlayer.posZ - mc.thePlayer.lastTickPosZ) * partialTicks;

        double x = pos.getX() + 0.5 - playerX;
        double y = pos.getY() + 1.0 - playerY;
        double z = pos.getZ() + 0.5 - playerZ;

        // Renderizar cubo do waypoint
        com.rato.addons.util.RenderUtils.drawOutlinedBoundingBox(
                new net.minecraft.util.AxisAlignedBB(x - 0.5, y - 1.0, z - 0.5, x + 0.5, y, z + 0.5),
                color, 2.0f);

        // Renderizar texto do waypoint
        com.rato.addons.util.RenderUtils.drawNameTag(label, x, y + 0.5, z, color);
    }

    private void renderFarmArea(float partialTicks) {
        double playerX = mc.thePlayer.lastTickPosX + (mc.thePlayer.posX - mc.thePlayer.lastTickPosX) * partialTicks;
        double playerY = mc.thePlayer.lastTickPosY + (mc.thePlayer.posY - mc.thePlayer.lastTickPosY) * partialTicks;
        double playerZ = mc.thePlayer.lastTickPosZ + (mc.thePlayer.posZ - mc.thePlayer.lastTickPosZ) * partialTicks;

        // Calcular posições relativas
        double minX = farmArea.getMinX() - playerX;
        double minY = farmArea.getMinY() - playerY;
        double minZ = farmArea.getMinZ() - playerZ;
        double maxX = farmArea.getMaxX() + 1 - playerX;
        double maxY = farmArea.getMaxY() + 1 - playerY;
        double maxZ = farmArea.getMaxZ() + 1 - playerZ;

        // Cor da área (usar configuração do usuário)
        int color = RatoAddonsConfigSimple.riftAreaColor.getRGB();

        // Renderizar bordas da área
        com.rato.addons.util.RenderUtils.drawOutlinedBoundingBox(
                new net.minecraft.util.AxisAlignedBB(minX, minY, minZ, maxX, maxY, maxZ),
                color, 1.5f);

        // Renderizar área preenchida (transparente)
        com.rato.addons.util.RenderUtils.drawFilledBoundingBox(
                new net.minecraft.util.AxisAlignedBB(minX, minY, minZ, maxX, minY + 0.1, maxZ),
                color & 0x40FFFFFF // Tornar mais transparente
        );

        // Renderizar informações da área
        double centerX = (minX + maxX) / 2;
        double centerY = maxY + 1;
        double centerZ = (minZ + maxZ) / 2;

        String areaInfo = "Farm Area (" + farmArea.getSize() + " blocks²)";
        com.rato.addons.util.RenderUtils.drawNameTag(areaInfo, centerX, centerY, centerZ, 0xFFFFFFFF);
    }

    private void renderTarget(float partialTicks) {
        double playerX = mc.thePlayer.lastTickPosX + (mc.thePlayer.posX - mc.thePlayer.lastTickPosX) * partialTicks;
        double playerY = mc.thePlayer.lastTickPosY + (mc.thePlayer.posY - mc.thePlayer.lastTickPosY) * partialTicks;
        double playerZ = mc.thePlayer.lastTickPosZ + (mc.thePlayer.posZ - mc.thePlayer.lastTickPosZ) * partialTicks;

        double targetX = currentTarget.lastTickPosX + (currentTarget.posX - currentTarget.lastTickPosX) * partialTicks;
        double targetY = currentTarget.lastTickPosY + (currentTarget.posY - currentTarget.lastTickPosY) * partialTicks;
        double targetZ = currentTarget.lastTickPosZ + (currentTarget.posZ - currentTarget.lastTickPosZ) * partialTicks;

        double x = targetX - playerX;
        double y = targetY - playerY;
        double z = targetZ - playerZ;

        // Renderizar outline vermelho ao redor do alvo
        com.rato.addons.util.RenderUtils.drawOutlinedBoundingBox(
                currentTarget.getEntityBoundingBox().offset(-playerX, -playerY, -playerZ),
                0xFFFF0000, 3.0f);

        // Renderizar informações do alvo
        String targetInfo = "Target: " + currentTarget.getName() +
                " (HP: " + String.format("%.1f", currentTarget.getHealth()) +
                ", Dist: " + String.format("%.1f", mc.thePlayer.getDistanceToEntity(currentTarget)) + "m)";

        com.rato.addons.util.RenderUtils.drawNameTag(targetInfo, x, y + currentTarget.height + 0.5, z, 0xFFFF0000);
    }

    private void renderPath(float partialTicks) {
        if (currentPath == null || currentPath.size() < 2)
            return;

        double playerX = mc.thePlayer.lastTickPosX + (mc.thePlayer.posX - mc.thePlayer.lastTickPosX) * partialTicks;
        double playerY = mc.thePlayer.lastTickPosY + (mc.thePlayer.posY - mc.thePlayer.lastTickPosY) * partialTicks;
        double playerZ = mc.thePlayer.lastTickPosZ + (mc.thePlayer.posZ - mc.thePlayer.lastTickPosZ) * partialTicks;

        // Renderizar linha conectando os pontos do caminho
        for (int i = 0; i < currentPath.size() - 1; i++) {
            BaritoneStylePathfinder.BaritoneNode currentNode = currentPath.get(i);
            BaritoneStylePathfinder.BaritoneNode nextNode = currentPath.get(i + 1);

            BlockPos currentPos = currentNode.position;
            BlockPos nextPos = nextNode.position;

            // Calcular posições relativas
            double x1 = currentPos.getX() + 0.5 - playerX;
            double y1 = currentPos.getY() + 0.5 - playerY;
            double z1 = currentPos.getZ() + 0.5 - playerZ;

            double x2 = nextPos.getX() + 0.5 - playerX;
            double y2 = nextPos.getY() + 0.5 - playerY;
            double z2 = nextPos.getZ() + 0.5 - playerZ;

            // Cor do caminho (amarelo brilhante)
            int pathColor = 0xFFFFFF00;

            // Renderizar linha entre os pontos
            com.rato.addons.util.RenderUtils.drawLine(x1, y1, z1, x2, y2, z2, pathColor, 3.0f);

            // Renderizar pequenos cubos nos pontos do caminho
            com.rato.addons.util.RenderUtils.drawOutlinedBoundingBox(
                    new net.minecraft.util.AxisAlignedBB(x1 - 0.1, y1 - 0.1, z1 - 0.1, x1 + 0.1, y1 + 0.1, z1 + 0.1),
                    pathColor, 2.0f);
        }

        // Renderizar informação do caminho
        if (currentPath.size() > 0) {
            BaritoneStylePathfinder.BaritoneNode lastNode = currentPath.get(currentPath.size() - 1);
            BlockPos lastPos = lastNode.position;

            double x = lastPos.getX() + 0.5 - playerX;
            double y = lastPos.getY() + 1.5 - playerY;
            double z = lastPos.getZ() + 0.5 - playerZ;

            String pathInfo = "Path: " + currentPath.size() + " nodes";
            com.rato.addons.util.RenderUtils.drawNameTag(pathInfo, x, y, z, 0xFFFFFF00);
        }
    }

    /**
     * Renderizar alvo atual com ESP avançado
     */
    private void renderAdvancedTarget(float partialTicks) {
        if (currentTarget == null)
            return;

        // Usar ESP avançado para renderizar o alvo principal
        AdvancedESP.render2DESP(currentTarget, 0xFFFF0000, true, true);

        // Renderizar outline 3D tradicional também
        renderTarget(partialTicks);
    }

    /**
     * Renderizar todos os zumbis na área com ESP avançado
     */
    private void renderAllZombiesESP() {
        if (mc.theWorld == null || farmArea == null)
            return;

        List<EntityZombie> zombies = mc.theWorld.getEntitiesWithinAABB(EntityZombie.class,
                farmArea.getBoundingBox());

        for (EntityZombie zombie : zombies) {
            if (zombie.isDead || !farmArea.contains(new BlockPos(zombie.posX, zombie.posY, zombie.posZ))) {
                continue;
            }

            // Cor diferente para alvo atual vs outros zumbis
            int color = (zombie == currentTarget) ? 0xFFFF0000 : 0xFFFFFF00;
            boolean showHealth = (zombie == currentTarget);
            boolean showName = (zombie == currentTarget);

            // Renderizar ESP 2D avançado
            AdvancedESP.render2DESP(zombie, color, showHealth, showName);
        }
    }

    // Getters
    public boolean isActive() {
        return isActive;
    }

    public boolean isPaused() {
        return isPaused;
    }

    public AutofarmState getCurrentState() {
        return currentState;
    }

    public FarmArea getFarmArea() {
        return farmArea;
    }

    public int getZombiesKilled() {
        return zombiesKilled;
    }

    /**
     * Classe que representa a área de farm definida por dois waypoints
     */
    public static class FarmArea {
        private final BlockPos corner1;
        private final BlockPos corner2;
        private final int minX, maxX, minY, maxY, minZ, maxZ;

        public FarmArea(BlockPos wp1, BlockPos wp2) {
            this.corner1 = wp1;
            this.corner2 = wp2;

            // Calcular limites da área
            this.minX = Math.min(wp1.getX(), wp2.getX());
            this.maxX = Math.max(wp1.getX(), wp2.getX());
            this.minY = Math.min(wp1.getY(), wp2.getY()) - 2; // Permitir 2 blocos abaixo
            this.maxY = Math.max(wp1.getY(), wp2.getY()) + 3; // Permitir 3 blocos acima
            this.minZ = Math.min(wp1.getZ(), wp2.getZ());
            this.maxZ = Math.max(wp1.getZ(), wp2.getZ());
        }

        public boolean contains(BlockPos pos) {
            return pos.getX() >= minX && pos.getX() <= maxX &&
                    pos.getY() >= minY && pos.getY() <= maxY &&
                    pos.getZ() >= minZ && pos.getZ() <= maxZ;
        }

        public net.minecraft.util.AxisAlignedBB getBoundingBox() {
            return new net.minecraft.util.AxisAlignedBB(minX, minY, minZ, maxX + 1, maxY + 1, maxZ + 1);
        }

        public int getSize() {
            return (maxX - minX + 1) * (maxZ - minZ + 1);
        }

        public BlockPos getCorner1() {
            return corner1;
        }

        public BlockPos getCorner2() {
            return corner2;
        }

        public int getMinX() {
            return minX;
        }

        public int getMaxX() {
            return maxX;
        }

        public int getMinY() {
            return minY;
        }

        public int getMaxY() {
            return maxY;
        }

        public int getMinZ() {
            return minZ;
        }

        public int getMaxZ() {
            return maxZ;
        }
    }
}
