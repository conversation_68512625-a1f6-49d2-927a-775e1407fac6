package com.rato.addons.pathfinding.professional;

import net.minecraft.client.Minecraft;
import net.minecraft.util.Vec3;

import java.util.ArrayList;
import java.util.List;

/**
 * Otimizador de caminhos para o sistema de pathfinding profissional
 */
public class PathOptimizer {
    
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Configurações de otimização
    private static final double LINE_OF_SIGHT_TOLERANCE = 0.5;
    private static final double SMOOTHING_FACTOR = 0.3;
    private static final int MAX_LOOK_AHEAD = 5;
    
    /**
     * Otimiza um caminho aplicando várias técnicas
     */
    public List<PathNode> optimizePath(List<PathNode> originalPath) {
        if (originalPath == null || originalPath.size() <= 2) {
            return originalPath;
        }
        
        List<PathNode> optimizedPath = new ArrayList<>(originalPath);
        
        // Aplicar otimizações em sequência
        optimizedPath = removeRedundantNodes(optimizedPath);
        optimizedPath = smoothPath(optimizedPath);
        optimizedPath = optimizeJumps(optimizedPath);
        optimizedPath = stringPulling(optimizedPath);
        
        return optimizedPath;
    }
    
    /**
     * Remove nós redundantes usando line-of-sight
     */
    private List<PathNode> removeRedundantNodes(List<PathNode> path) {
        if (path.size() <= 2) return path;
        
        List<PathNode> optimized = new ArrayList<>();
        optimized.add(path.get(0)); // Sempre manter o primeiro nó
        
        int currentIndex = 0;
        
        while (currentIndex < path.size() - 1) {
            int farthestReachable = currentIndex;
            
            // Encontrar o nó mais distante que ainda tem line-of-sight
            for (int i = currentIndex + 1; i < Math.min(currentIndex + MAX_LOOK_AHEAD + 1, path.size()); i++) {
                if (hasLineOfSight(path.get(currentIndex), path.get(i))) {
                    farthestReachable = i;
                }
            }
            
            // Se encontrou um nó mais distante, pular para ele
            if (farthestReachable > currentIndex + 1) {
                currentIndex = farthestReachable;
                optimized.add(path.get(currentIndex));
            } else {
                // Caso contrário, ir para o próximo nó
                currentIndex++;
                optimized.add(path.get(currentIndex));
            }
        }
        
        return optimized;
    }
    
    /**
     * Suaviza o caminho usando interpolação
     */
    private List<PathNode> smoothPath(List<PathNode> path) {
        if (path.size() <= 2) return path;
        
        List<PathNode> smoothed = new ArrayList<>();
        smoothed.add(path.get(0)); // Manter primeiro nó
        
        for (int i = 1; i < path.size() - 1; i++) {
            PathNode prev = path.get(i - 1);
            PathNode current = path.get(i);
            PathNode next = path.get(i + 1);
            
            // Calcular posição suavizada
            Vec3 smoothedPos = calculateSmoothedPosition(prev.position, current.position, next.position);
            
            // Criar novo nó com posição suavizada
            PathNode smoothedNode = new PathNode(smoothedPos, current.gCost, current.hCost, current.parent);
            smoothedNode.movementType = current.movementType;
            
            // Verificar se a posição suavizada é válida
            if (isPositionSafe(smoothedPos)) {
                smoothed.add(smoothedNode);
            } else {
                smoothed.add(current); // Manter original se suavização não for segura
            }
        }
        
        smoothed.add(path.get(path.size() - 1)); // Manter último nó
        return smoothed;
    }
    
    /**
     * Otimiza sequências de pulos
     */
    private List<PathNode> optimizeJumps(List<PathNode> path) {
        List<PathNode> optimized = new ArrayList<>();
        
        for (int i = 0; i < path.size(); i++) {
            PathNode current = path.get(i);
            
            // Se é um pulo, verificar se pode ser otimizado
            if (current.movementType == PathNode.MovementType.JUMP) {
                PathNode optimizedJump = optimizeJumpNode(path, i);
                optimized.add(optimizedJump);
            } else {
                optimized.add(current);
            }
        }
        
        return optimized;
    }
    
    /**
     * Aplica algoritmo de string pulling para encurtar o caminho
     */
    private List<PathNode> stringPulling(List<PathNode> path) {
        if (path.size() <= 2) return path;
        
        List<PathNode> pulled = new ArrayList<>();
        pulled.add(path.get(0));
        
        int current = 0;
        
        while (current < path.size() - 1) {
            int next = findFarthestVisibleNode(path, current);
            
            if (next > current + 1) {
                // Criar nó intermediário se necessário
                PathNode intermediateNode = createIntermediateNode(path.get(current), path.get(next));
                if (intermediateNode != null) {
                    pulled.add(intermediateNode);
                }
            }
            
            pulled.add(path.get(next));
            current = next;
        }
        
        return pulled;
    }
    
    /**
     * Verifica se há line-of-sight entre dois nós
     */
    private boolean hasLineOfSight(PathNode from, PathNode to) {
        // Verificação simplificada - pode ser melhorada com raycasting
        double distance = from.distanceTo(to);
        
        // Se muito distante, provavelmente não tem line-of-sight
        if (distance > 10.0) return false;
        
        // Verificar diferença de altura
        double heightDiff = Math.abs(to.position.yCoord - from.position.yCoord);
        if (heightDiff > 3.0) return false;
        
        // Verificar se o caminho direto é viável
        return isDirectPathViable(from.position, to.position);
    }
    
    /**
     * Verifica se um caminho direto entre duas posições é viável
     */
    private boolean isDirectPathViable(Vec3 from, Vec3 to) {
        // Implementação simplificada - verificar alguns pontos no caminho
        int steps = (int) Math.ceil(from.distanceTo(to));
        steps = Math.min(steps, 10); // Limitar verificações
        
        for (int i = 1; i < steps; i++) {
            double t = (double) i / steps;
            Vec3 direction = to.subtract(from);
            Vec3 checkPos = from.addVector(direction.xCoord * t, direction.yCoord * t, direction.zCoord * t);
            
            if (!isPositionSafe(checkPos)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Verifica se uma posição é segura para movimento
     */
    private boolean isPositionSafe(Vec3 pos) {
        if (mc.theWorld == null) return false;
        
        // Verificação básica - pode ser expandida
        return pos.yCoord >= 0 && pos.yCoord <= 255;
    }
    
    /**
     * Calcula posição suavizada usando interpolação
     */
    private Vec3 calculateSmoothedPosition(Vec3 prev, Vec3 current, Vec3 next) {
        // Interpolação quadrática simples
        Vec3 direction1 = current.subtract(prev);
        Vec3 direction2 = next.subtract(current);
        
        // Média ponderada das direções
        Vec3 avgDirection = direction1.add(direction2);
        avgDirection = new Vec3(avgDirection.xCoord * 0.5, avgDirection.yCoord * 0.5, avgDirection.zCoord * 0.5);

        // Aplicar suavização
        Vec3 smoothed = current.addVector(
            avgDirection.xCoord * SMOOTHING_FACTOR,
            avgDirection.yCoord * SMOOTHING_FACTOR,
            avgDirection.zCoord * SMOOTHING_FACTOR
        );
        
        return smoothed;
    }
    
    /**
     * Otimiza um nó de pulo específico
     */
    private PathNode optimizeJumpNode(List<PathNode> path, int index) {
        PathNode jumpNode = path.get(index);
        
        // Verificar se pode combinar com pulos adjacentes
        if (index > 0 && index < path.size() - 1) {
            PathNode prev = path.get(index - 1);
            PathNode next = path.get(index + 1);
            
            // Se ambos os nós adjacentes também são pulos, otimizar
            if (prev.movementType == PathNode.MovementType.JUMP && 
                next.movementType == PathNode.MovementType.JUMP) {
                
                // Criar pulo otimizado
                Vec3 optimizedPos = calculateOptimalJumpPosition(prev.position, jumpNode.position, next.position);
                PathNode optimized = new PathNode(optimizedPos, jumpNode.gCost, jumpNode.hCost, jumpNode.parent);
                optimized.movementType = PathNode.MovementType.JUMP;
                
                return optimized;
            }
        }
        
        return jumpNode; // Retornar original se não pode otimizar
    }
    
    /**
     * Calcula posição ótima para um pulo
     */
    private Vec3 calculateOptimalJumpPosition(Vec3 prev, Vec3 current, Vec3 next) {
        // Calcular trajetória ótima considerando física do pulo
        Vec3 direction = next.subtract(prev).normalize();
        double distance = prev.distanceTo(next);
        
        // Posição ótima é geralmente no meio da trajetória
        double scaleFactor = distance * 0.5;
        return prev.addVector(direction.xCoord * scaleFactor, direction.yCoord * scaleFactor, direction.zCoord * scaleFactor);
    }
    
    /**
     * Encontra o nó mais distante que ainda é visível
     */
    private int findFarthestVisibleNode(List<PathNode> path, int startIndex) {
        int farthest = startIndex + 1;
        
        for (int i = startIndex + 2; i < Math.min(startIndex + MAX_LOOK_AHEAD, path.size()); i++) {
            if (hasLineOfSight(path.get(startIndex), path.get(i))) {
                farthest = i;
            } else {
                break; // Parar na primeira obstrução
            }
        }
        
        return Math.min(farthest, path.size() - 1);
    }
    
    /**
     * Cria nó intermediário se necessário
     */
    private PathNode createIntermediateNode(PathNode from, PathNode to) {
        double distance = from.distanceTo(to);
        
        // Só criar nó intermediário se a distância for significativa
        if (distance > 5.0) {
            Vec3 sum = from.position.add(to.position);
            Vec3 midPoint = new Vec3(sum.xCoord * 0.5, sum.yCoord * 0.5, sum.zCoord * 0.5);
            return new PathNode(midPoint, from.gCost + distance * 0.5, to.hCost, from);
        }
        
        return null;
    }
    
    /**
     * Calcula métricas de qualidade do caminho
     */
    public PathQuality analyzePath(List<PathNode> path) {
        if (path == null || path.isEmpty()) {
            return new PathQuality(0, 0, 0, 0);
        }
        
        double totalDistance = 0;
        double totalCost = 0;
        int jumpCount = 0;
        int dangerousNodes = 0;
        
        for (int i = 1; i < path.size(); i++) {
            PathNode current = path.get(i);
            PathNode prev = path.get(i - 1);
            
            totalDistance += current.distanceTo(prev);
            totalCost += current.getTotalMovementCost();
            
            if (current.movementType == PathNode.MovementType.JUMP) {
                jumpCount++;
            }
            
            if (current.isDangerous) {
                dangerousNodes++;
            }
        }
        
        return new PathQuality(totalDistance, totalCost, jumpCount, dangerousNodes);
    }
    
    /**
     * Classe para métricas de qualidade do caminho
     */
    public static class PathQuality {
        public final double totalDistance;
        public final double totalCost;
        public final int jumpCount;
        public final int dangerousNodes;
        
        public PathQuality(double totalDistance, double totalCost, int jumpCount, int dangerousNodes) {
            this.totalDistance = totalDistance;
            this.totalCost = totalCost;
            this.jumpCount = jumpCount;
            this.dangerousNodes = dangerousNodes;
        }
        
        public double getQualityScore() {
            // Calcular score de qualidade (menor é melhor)
            return totalCost + (jumpCount * 0.5) + (dangerousNodes * 2.0);
        }
        
        @Override
        public String toString() {
            return String.format("Distance: %.1f, Cost: %.1f, Jumps: %d, Dangerous: %d, Score: %.1f",
                totalDistance, totalCost, jumpCount, dangerousNodes, getQualityScore());
        }
    }
}
