package com.rato.addons.features;

import com.rato.addons.config.RatoAddonsConfigSimple;
import com.rato.addons.failsafe.FailsafeManager;
import com.rato.addons.failsafe.RotationFailsafe;
import com.rato.addons.failsafe.TeleportFailsafe;
import com.rato.addons.util.DiscordWebhook;
import com.rato.addons.util.Logger;
import net.minecraft.block.Block;
import net.minecraft.client.Minecraft;
import net.minecraft.client.entity.EntityOtherPlayerMP;
import net.minecraft.entity.item.EntityFallingBlock;
import net.minecraft.init.Blocks;
import net.minecraft.item.ItemStack;
import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.InputEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;
import org.lwjgl.input.Keyboard;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public class StaffCheckDetection {
    
    private static final Minecraft mc = Minecraft.getMinecraft();
    
    // Variáveis para detectar mudanças
    private static float lastYaw = 0;
    private static float lastPitch = 0;
    private static Vec3 lastPosition = null;
    private static ItemStack[] lastInventory = null;

    // Variáveis para detecção inteligente
    private static boolean playerIsMovingMouse = false;
    private static long lastMouseMovement = 0;
    private static float[] recentYawChanges = new float[10]; // Últimas 10 mudanças
    private static float[] recentPitchChanges = new float[10];
    private static int changeIndex = 0;
    private static boolean playerIsWalking = false;
    private static long lastWalkTime = 0;
    
    // Controle de detecção
    private static boolean staffCheckDetected = false;
    private static long lastDetectionTime = 0;
    private static final long DETECTION_COOLDOWN = 5000; // 5 segundos

    // Controle de simulação
    private static boolean isSimulating = false;
    private static int simulationStep = 0;
    private static long simulationStartTime = 0;
    private static final long SIMULATION_DELAY = 3000; // 3 segundos entre cada teste

    // Entidades de simulação
    private static List<BlockPos> simulatedBlocks = new ArrayList<>();
    private static EntityOtherPlayerMP simulatedPlayer = null;
    private static Vec3 originalPosition = null;

    // Controle de câmera para evitar bugs
    private static float originalYaw = 0;
    private static float originalPitch = 0;
    private static int totalChecks = 4;
    private static int detectedChecks = 0;

    // Controle de inicialização para evitar falsos positivos
    private static long gameStartTime = 0;
    private static final long INITIALIZATION_DELAY = 5000; // 5 segundos após entrar no jogo
    
    @SubscribeEvent
    public void onKeyInput(InputEvent.KeyInputEvent event) {
        if (RatoAddonsConfigSimple.staffCheckResponse.isActive() && staffCheckDetected) {
            respondToStaffCheck();
        }
    }
    
    @SubscribeEvent
    public void onClientTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.START) return;
        if (mc.thePlayer == null || mc.theWorld == null) return;

        // Inicializar tempo do jogo na primeira execução
        if (gameStartTime == 0) {
            gameStartTime = System.currentTimeMillis();
            return;
        }

        // Aguardar período de inicialização para evitar falsos positivos
        if (System.currentTimeMillis() - gameStartTime < INITIALIZATION_DELAY) {
            // Apenas inicializar valores durante este período
            if (lastPosition == null) {
                initializeValues();
            } else {
                updateValues(); // Continuar atualizando para ter dados válidos
            }
            return;
        }

        // Verificar se as detecções estão ativadas
        if (!RatoAddonsConfigSimple.rotationDetection && !RatoAddonsConfigSimple.tpDetection && !RatoAddonsConfigSimple.itemSwapDetection) {
            return;
        }

        // Cooldown para evitar spam de detecções
        if (System.currentTimeMillis() - lastDetectionTime < DETECTION_COOLDOWN) {
            return;
        }

        // Primeira execução - inicializar valores
        if (lastPosition == null) {
            initializeValues();
            return;
        }

        // Processar simulação se ativa
        if (isSimulating) {
            processSimulation();
            return;
        }

        // Chamar detecções dos novos failsafes
        RotationFailsafe.getInstance().onWorldChangeDetection();
        TeleportFailsafe.getInstance().onWorldChangeDetection();

        // Atualizar valores para próxima verificação
        updateValues();
    }
    
    private void initializeValues() {
        lastYaw = mc.thePlayer.rotationYaw;
        lastPitch = mc.thePlayer.rotationPitch;
        lastPosition = new Vec3(mc.thePlayer.posX, mc.thePlayer.posY, mc.thePlayer.posZ);
        lastInventory = copyInventory();
    }
    
    private void checkRotationChange() {
        if (!RatoAddonsConfigSimple.rotationDetection) return;

        float yawDiff = Math.abs(mc.thePlayer.rotationYaw - lastYaw);
        float pitchDiff = Math.abs(mc.thePlayer.rotationPitch - lastPitch);

        // Normalizar diferença de yaw (0-180)
        if (yawDiff > 180) {
            yawDiff = 360 - yawDiff;
        }

        // Atualizar histórico de mudanças
        updateRotationHistory(yawDiff, pitchDiff);

        // Detectar se o player está movendo o mouse ativamente
        updateMouseMovementDetection(yawDiff, pitchDiff);

        // Detectar se o player está andando
        updateWalkingDetection();

        // Debug removido para sistema limpo

        // Só detectar staff check se não for movimento natural do player
        if (isUnnaturalRotationChange(yawDiff, pitchDiff)) {
            triggerStaffCheckDetection("Rotation", "Unnatural rotation change detected! Yaw: " + String.format("%.1f", yawDiff) + "°, Pitch: " + String.format("%.1f", pitchDiff) + "°");
        }
    }

    private void updateRotationHistory(float yawDiff, float pitchDiff) {
        recentYawChanges[changeIndex] = yawDiff;
        recentPitchChanges[changeIndex] = pitchDiff;
        changeIndex = (changeIndex + 1) % recentYawChanges.length;
    }

    private void updateMouseMovementDetection(float yawDiff, float pitchDiff) {
        // Se há mudança significativa de rotação, considerar como movimento do mouse
        if (yawDiff > 2.0f || pitchDiff > 2.0f) {
            playerIsMovingMouse = true;
            lastMouseMovement = System.currentTimeMillis();
        } else if (System.currentTimeMillis() - lastMouseMovement > 1000) {
            // Se não há movimento por 1 segundo, considerar que parou de mover o mouse
            playerIsMovingMouse = false;
        }
    }

    private void updateWalkingDetection() {
        // Detectar se o player está se movendo
        boolean isMoving = mc.thePlayer.motionX != 0 || mc.thePlayer.motionZ != 0 ||
                          mc.gameSettings.keyBindForward.isKeyDown() ||
                          mc.gameSettings.keyBindBack.isKeyDown() ||
                          mc.gameSettings.keyBindLeft.isKeyDown() ||
                          mc.gameSettings.keyBindRight.isKeyDown();

        if (isMoving) {
            playerIsWalking = true;
            lastWalkTime = System.currentTimeMillis();
        } else if (System.currentTimeMillis() - lastWalkTime > 1000) {
            // Se não está andando por 1 segundo, considerar parado
            playerIsWalking = false;
        }
    }

    private boolean isUnnaturalRotationChange(float yawDiff, float pitchDiff) {
        // 1. Movimento instantâneo grande sem mouse (principal detecção)
        if ((yawDiff > 30.0f || pitchDiff > 30.0f) && !playerIsMovingMouse) {
            return true; // Qualquer movimento >30° sem mouse é suspeito
        }

        // 2. Verificar padrão de movimento - staff checks são mais "robóticos"
        if (isRoboticMovement(yawDiff, pitchDiff)) {
            return true;
        }

        // 3. Verificar se é um movimento "perfeito" (valores muito redondos)
        if (isPerfectRotation(yawDiff, pitchDiff)) {
            return true;
        }

        // 4. Movimento muito grande (> 45°) sem input recente do mouse
        if ((yawDiff > 45.0f || pitchDiff > 45.0f) && System.currentTimeMillis() - lastMouseMovement > 300) {
            return true;
        }

        return false;
    }

    private boolean isRoboticMovement(float yawDiff, float pitchDiff) {
        // Staff checks tendem a ter valores mais "perfeitos" ou padrões específicos

        // Verificar se é exatamente 90° (muito comum em staff checks)
        if (Math.abs(yawDiff - 90) < 15.0f && yawDiff > 75) {
            return true;
        }

        // Verificar se é exatamente 180° (comum em staff checks)
        if (Math.abs(yawDiff - 180) < 15.0f && yawDiff > 165) {
            return true;
        }

        // Verificar múltiplos de 45° (comum em staff checks)
        if (yawDiff > 40 && Math.abs(yawDiff % 45) < 5.0f) {
            return true;
        }

        // Verificar mudanças de pitch suspeitas (staff raramente muda pitch)
        if (yawDiff > 60 && Math.abs(pitchDiff) < 10.0f) {
            return true; // Grande mudança de yaw sem mudança significativa de pitch
        }

        return false;
    }

    private boolean isPerfectRotation(float yawDiff, float pitchDiff) {
        // Verificar se os valores são "muito perfeitos" para serem humanos

        // Valores exatos (sem decimais) são suspeitos
        if (yawDiff == Math.floor(yawDiff) && yawDiff > 30) {
            return true;
        }

        if (pitchDiff == Math.floor(pitchDiff) && pitchDiff > 30) {
            return true;
        }

        return false;
    }
    
    private void checkTeleportation() {
        if (!RatoAddonsConfigSimple.tpDetection) return;

        Vec3 currentPos = new Vec3(mc.thePlayer.posX, mc.thePlayer.posY, mc.thePlayer.posZ);
        double distance = lastPosition.distanceTo(currentPos);

        // Debug: Log movimentos grandes
        if (distance > 5.0) {
            Logger.sendMessage("§7[DEBUG] Movement: Distance=" + String.format("%.1f", distance) + " blocks Walking=" + playerIsWalking);
        }

        // Detectar teleporte com verificações mais inteligentes
        if (isUnnaturalTeleport(distance, currentPos)) {
            triggerStaffCheckDetection("Teleport", "Unnatural teleportation detected! Distance: " + String.format("%.1f", distance) + " blocks");
        }
    }

    private boolean isUnnaturalTeleport(double distance, Vec3 currentPos) {
        // 1. Distância muito pequena não é teleporte
        if (distance < 3.0) {
            return false;
        }

        // 2. Se o player está voando (creative/spectator), movimentos grandes são normais
        if (mc.thePlayer.capabilities.isFlying || mc.thePlayer.capabilities.allowFlying) {
            return distance > 30.0; // Threshold menor para detectar melhor
        }

        // 3. Verificar se o player está em modo criativo (pode voar livremente)
        if (mc.thePlayer.capabilities.isCreativeMode) {
            return distance > 50.0; // Threshold menor para criativo
        }

        // 4. Verificar se o player está em um veículo (cavalo, barco, etc.)
        if (mc.thePlayer.isRiding()) {
            return distance > 20.0; // Threshold menor para veículos
        }

        // 5. Movimento muito grande instantâneo (> 8 blocos)
        if (distance > 8.0) {
            return true; // Qualquer movimento > 8 blocos é suspeito
        }

        // 6. Verificar se a posição é "muito perfeita" (coordenadas redondas)
        if (isPerfectCoordinates(currentPos) && distance > 5.0) {
            return true; // Threshold menor para coordenadas suspeitas
        }

        // 7. Movimento horizontal muito grande sem explicação
        double horizontalDistance = Math.sqrt(
            Math.pow(currentPos.xCoord - lastPosition.xCoord, 2) +
            Math.pow(currentPos.zCoord - lastPosition.zCoord, 2)
        );

        if (horizontalDistance > 8.0 && !playerIsWalking) {
            return true; // Grande movimento horizontal sem estar andando
        }

        // 8. Verificar se é um movimento vertical suspeito (Y muito diferente)
        double yDiff = Math.abs(currentPos.yCoord - lastPosition.yCoord);
        if (yDiff > 10.0 && distance > 5.0) {
            return true; // Movimento vertical + horizontal = suspeito
        }

        return false;
    }

    private boolean isPerfectCoordinates(Vec3 pos) {
        // Staff teleports frequentemente usam coordenadas "redondas"
        double x = pos.xCoord;
        double y = pos.yCoord;
        double z = pos.zCoord;

        // Verificar se as coordenadas são números inteiros ou .5
        boolean xPerfect = (x == Math.floor(x)) || (Math.abs(x - Math.floor(x) - 0.5) < 0.1);
        boolean yPerfect = (y == Math.floor(y)) || (Math.abs(y - Math.floor(y) - 0.5) < 0.1);
        boolean zPerfect = (z == Math.floor(z)) || (Math.abs(z - Math.floor(z) - 0.5) < 0.1);

        return xPerfect && yPerfect && zPerfect;
    }
    
    private void checkItemSwap() {
        if (!RatoAddonsConfigSimple.itemSwapDetection) return;

        ItemStack[] currentInventory = copyInventory();

        // Comparar inventários com verificações mais inteligentes
        if (lastInventory != null && isUnnaturalInventoryChange(lastInventory, currentInventory)) {
            triggerStaffCheckDetection("Item Swap", "Unnatural inventory change detected! Items may have been swapped by staff");
        }
    }

    private boolean isUnnaturalInventoryChange(ItemStack[] oldInventory, ItemStack[] newInventory) {
        if (oldInventory.length != newInventory.length) return true;

        // 1. Verificar se o player está com inventário aberto
        if (mc.currentScreen != null && mc.currentScreen.getClass().getSimpleName().contains("Gui")) {
            return false; // Player está mexendo no inventário, mudanças são normais
        }

        // 2. Verificar se o player está clicando (pode estar dropando itens)
        if (mc.gameSettings.keyBindDrop.isKeyDown() || mc.gameSettings.keyBindUseItem.isKeyDown()) {
            return false; // Player pode estar dropando/usando itens
        }

        // 3. Contar mudanças significativas
        int significantChanges = 0;
        int swappedSlots = 0;

        for (int i = 0; i < oldInventory.length; i++) {
            ItemStack oldItem = oldInventory[i];
            ItemStack newItem = newInventory[i];

            // Verificar se houve mudança significativa
            if (hasSignificantChange(oldItem, newItem)) {
                significantChanges++;

                // Verificar se é um swap (item foi para outro slot)
                if (oldItem != null && newItem == null) {
                    // Item removido, verificar se apareceu em outro lugar
                    for (int j = 0; j < newInventory.length; j++) {
                        if (j != i && ItemStack.areItemsEqual(oldItem, newInventory[j]) &&
                            (oldInventory[j] == null || !ItemStack.areItemsEqual(oldInventory[j], newInventory[j]))) {
                            swappedSlots++;
                            break;
                        }
                    }
                }
            }
        }

        // 4. Staff swaps geralmente envolvem múltiplos slots mudando simultaneamente
        if (significantChanges >= 2 && swappedSlots >= 1) {
            return true;
        }

        // 5. Mudança muito súbita de muitos itens sem explicação
        if (significantChanges >= 4) {
            return true;
        }

        // 6. Verificar padrões suspeitos (hotbar sendo reorganizada perfeitamente)
        if (isHotbarPerfectlyOrganized(newInventory) && !isHotbarPerfectlyOrganized(oldInventory)) {
            return true;
        }

        return false;
    }

    private boolean hasSignificantChange(ItemStack oldItem, ItemStack newItem) {
        // Mudança significativa = item completamente diferente ou quantidade muito diferente

        if (oldItem == null && newItem != null) return true;
        if (oldItem != null && newItem == null) return true;
        if (oldItem == null && newItem == null) return false;

        // Itens diferentes
        if (!ItemStack.areItemsEqual(oldItem, newItem)) return true;

        // Quantidade muito diferente (mais que 10 itens de diferença)
        if (Math.abs(oldItem.stackSize - newItem.stackSize) > 10) return true;

        // NBT diferente
        if (!ItemStack.areItemStackTagsEqual(oldItem, newItem)) return true;

        return false;
    }

    private boolean isHotbarPerfectlyOrganized(ItemStack[] inventory) {
        // Verificar se o hotbar (primeiros 9 slots) está "perfeitamente" organizado
        // Staff às vezes organiza o inventário de forma suspeita

        if (inventory.length < 9) return false;

        int emptySlots = 0;
        int filledSlots = 0;

        for (int i = 0; i < 9; i++) {
            if (inventory[i] == null) {
                emptySlots++;
            } else {
                filledSlots++;
            }
        }

        // Se todos os itens estão no início e todos os vazios no final, é suspeito
        boolean perfectOrder = true;
        boolean foundEmpty = false;

        for (int i = 0; i < 9; i++) {
            if (inventory[i] == null) {
                foundEmpty = true;
            } else if (foundEmpty) {
                perfectOrder = false;
                break;
            }
        }

        return perfectOrder && filledSlots > 0 && emptySlots > 0;
    }
    

    
    private ItemStack[] copyInventory() {
        if (mc.thePlayer == null || mc.thePlayer.inventory == null) return new ItemStack[0];
        
        ItemStack[] copy = new ItemStack[mc.thePlayer.inventory.mainInventory.length];
        for (int i = 0; i < copy.length; i++) {
            ItemStack original = mc.thePlayer.inventory.mainInventory[i];
            copy[i] = original != null ? original.copy() : null;
        }
        return copy;
    }
    
    private void triggerStaffCheckDetection(String type, String message) {
        staffCheckDetected = true;
        lastDetectionTime = System.currentTimeMillis();

        // Log minimalista
        Logger.sendMessage("§c⚠ " + type + " §7- Press " + RatoAddonsConfigSimple.staffCheckResponse.getDisplay());

        // Enviar para Discord webhook
        String extraInfo = "Response keybind: " + RatoAddonsConfigSimple.staffCheckResponse.getDisplay();
        DiscordWebhook.sendStaffCheckAlert(type, message, extraInfo);

        // Som de alerta se ativado
        if (RatoAddonsConfigSimple.soundAlert) {
            mc.thePlayer.playSound("note.pling", 1.0f, 2.0f);
        }

        // Auto-pause se ativado
        if (RatoAddonsConfigSimple.autoPause) {
            pauseAllAutomations();
        }
    }
    
    private void respondToStaffCheck() {
        if (!staffCheckDetected) return;

        Logger.sendMessage("§a✓ Responded");

        // Simular resposta humana
        mc.thePlayer.rotationYaw += (Math.random() - 0.5) * 10;
        mc.thePlayer.rotationPitch += (Math.random() - 0.5) * 5;

        // Resetar detecção
        staffCheckDetected = false;
    }
    
    private void pauseAllAutomations() {
        // Pausar todas as automações do mod
        RatoAddonsConfigSimple.autoFarm = false;
        RatoAddonsConfigSimple.miningHelper = false;
        RatoAddonsConfigSimple.foragingEnabled = false;
        RatoAddonsConfigSimple.autoClicker = false;
        
        Logger.sendMessage("§c[AUTO PAUSE] §fAll automations paused due to staff check detection!");
    }
    
    private void updateValues() {
        lastYaw = mc.thePlayer.rotationYaw;
        lastPitch = mc.thePlayer.rotationPitch;
        lastPosition = new Vec3(mc.thePlayer.posX, mc.thePlayer.posY, mc.thePlayer.posZ);
        lastInventory = copyInventory();
    }
    
    public static boolean isStaffCheckDetected() {
        return staffCheckDetected;
    }
    
    public static void resetDetection() {
        staffCheckDetected = false;
        lastDetectionTime = 0;
    }

    // === SISTEMA DE SIMULAÇÃO ===

    public static void simulateStaffCheck() {
        if (mc.thePlayer == null || mc.theWorld == null) {
            Logger.sendMessage("§c[SIMULATION] §fCannot simulate - player not in world!");
            return;
        }

        if (isSimulating) {
            Logger.sendMessage("§e[SIMULATION] §fSimulation already running! Please wait...");
            return;
        }

        isSimulating = true;
        simulationStep = 0;
        simulationStartTime = System.currentTimeMillis();
        detectedChecks = 0;

        // Salvar posição original da câmera
        if (mc.thePlayer != null) {
            originalYaw = mc.thePlayer.rotationYaw;
            originalPitch = mc.thePlayer.rotationPitch;
        }

        Logger.sendMessage("§6Check Simulator §7» §fStarting standard failsafe test suite...");
    }

    private void processSimulation() {
        long elapsed = System.currentTimeMillis() - simulationStartTime;

        switch (simulationStep) {
            case 0: // Rotation 1 - Leve
                if (elapsed >= 1000) {
                    Logger.sendMessage("§6Iniciando rotation leve....");
                    simulateRotationCheck(30.0f); // 30 graus
                    simulationStep++;
                    simulationStartTime = System.currentTimeMillis();
                }
                break;

            case 1: // Rotation 2 - Leve
                if (elapsed >= 3000) {
                    simulateRotationCheck(45.0f); // 45 graus
                    simulationStep++;
                    simulationStartTime = System.currentTimeMillis();
                }
                break;

            case 2: // Rotation 3 - Forte
                if (elapsed >= 3000) {
                    Logger.sendMessage("§6Iniciando rotation forte....");
                    simulateRotationCheck(90.0f); // 90 graus
                    simulationStep++;
                    simulationStartTime = System.currentTimeMillis();
                }
                break;

            case 3: // Rotation 4 - Forte
                if (elapsed >= 3000) {
                    simulateRotationCheck(135.0f); // 135 graus
                    simulationStep++;
                    simulationStartTime = System.currentTimeMillis();
                }
                break;

            case 4: // Finalizar
                if (elapsed >= 3000) {
                    finishSimulation();
                }
                break;
        }
    }

    private void simulateRotationCheck() {
        simulateRotationCheck(90.0f); // Padrão 90 graus
    }

    private void simulateRotationCheck(float yawDifference) {
        if (!RatoAddonsConfigSimple.rotationDetection) return;

        // Simular pacote de rotação do servidor
        float originalYaw = mc.thePlayer.rotationYaw;
        float originalPitch = mc.thePlayer.rotationPitch;

        // Criar rotação ALEATÓRIA para qualquer direção
        Random random = new Random();

        // Direção aleatória: -1 ou +1
        float yawDirection = random.nextBoolean() ? 1.0f : -1.0f;
        float pitchDirection = random.nextBoolean() ? 1.0f : -1.0f;

        // Aplicar rotação aleatória com intensidade variável
        float newYaw = originalYaw + (yawDifference * yawDirection);
        float newPitch = originalPitch + (random.nextFloat() * 30.0f * pitchDirection); // Pitch aleatório até 30°

        // Simular recebimento do pacote
        net.minecraft.network.play.server.S08PacketPlayerPosLook fakePacket =
            new net.minecraft.network.play.server.S08PacketPlayerPosLook(
                mc.thePlayer.posX, mc.thePlayer.posY, mc.thePlayer.posZ,
                newYaw, newPitch, java.util.EnumSet.noneOf(net.minecraft.network.play.server.S08PacketPlayerPosLook.EnumFlags.class)
            );

        // Enviar para o sistema de failsafe
        RotationFailsafe.getInstance().onReceivedPacketDetection(fakePacket);

        // Aplicar rotação real para simular o efeito
        mc.thePlayer.rotationYaw = newYaw;
        mc.thePlayer.rotationPitch = newPitch;
    }

    private void simulateTeleportCheck() {
        if (!RatoAddonsConfigSimple.tpDetection) return;

        // Salvar posição original
        originalPosition = new Vec3(mc.thePlayer.posX, mc.thePlayer.posY, mc.thePlayer.posZ);

        // Calcular nova posição "perfeita" (padrão de staff)
        double newX = Math.floor(originalPosition.xCoord) + 25.0;
        double newY = originalPosition.yCoord;
        double newZ = Math.floor(originalPosition.zCoord) + 25.0;

        // Criar pacote simulado de teleporte
        net.minecraft.network.play.server.S08PacketPlayerPosLook fakePacket =
            new net.minecraft.network.play.server.S08PacketPlayerPosLook(
                newX, newY, newZ,
                mc.thePlayer.rotationYaw, mc.thePlayer.rotationPitch, java.util.EnumSet.noneOf(net.minecraft.network.play.server.S08PacketPlayerPosLook.EnumFlags.class)
            );

        // Enviar para o sistema de failsafe
        TeleportFailsafe.getInstance().onReceivedPacketDetection(fakePacket);

        // Aplicar teleporte real para simular o efeito
        mc.thePlayer.setPosition(newX, newY, newZ);

        // Restaurar posição após um tempo MAIOR para permitir detecção
        new Thread(() -> {
            try {
                Thread.sleep(20000); // 20 segundos - tempo suficiente para o failsafe completo
                if (mc.thePlayer != null && originalPosition != null) {
                    mc.thePlayer.setPosition(originalPosition.xCoord, originalPosition.yCoord, originalPosition.zCoord);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
    }

    private void simulateItemSwapCheck() {
        if (!RatoAddonsConfigSimple.itemSwapDetection) return;

        // Swap REAL de itens no inventário
        if (mc.thePlayer.inventory != null && mc.thePlayer.inventory.mainInventory.length > 8) {
            // Salvar itens originais
            ItemStack[] originalItems = new ItemStack[9];
            for (int i = 0; i < 9; i++) {
                originalItems[i] = mc.thePlayer.inventory.mainInventory[i];
            }

            // Reorganizar hotbar de forma "perfeita" (padrão de staff)
            for (int i = 0; i < 9; i++) {
                if (originalItems[i] != null) {
                    // Mover todos os itens para o início
                    mc.thePlayer.inventory.mainInventory[i] = null;
                }
            }

            // Colocar itens de volta em ordem "perfeita"
            int slot = 0;
            for (int i = 0; i < 9; i++) {
                if (originalItems[i] != null) {
                    mc.thePlayer.inventory.mainInventory[slot] = originalItems[i];
                    slot++;
                }
            }

            // Restaurar após 3 segundos
            new Thread(() -> {
                try {
                    Thread.sleep(3000);
                    if (mc.thePlayer != null && mc.thePlayer.inventory != null) {
                        for (int i = 0; i < 9; i++) {
                            mc.thePlayer.inventory.mainInventory[i] = originalItems[i];
                        }
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }).start();
        }
    }

    private void simulateBlockCheck() {
        if (!RatoAddonsConfigSimple.blockCheckDetection) return;

        // Spawnar 3-5 blocos aleatórios próximos ao player
        BlockPos playerPos = mc.thePlayer.getPosition();
        Random random = new Random();
        simulatedBlocks.clear();

        int blocksToSpawn = 3 + random.nextInt(3); // 3-5 blocos
        Block[] suspiciousBlocks = {Blocks.glass, Blocks.stained_glass, Blocks.stone, Blocks.wool, Blocks.clay};

        for (int i = 0; i < blocksToSpawn; i++) {
            // Encontrar posição vazia próxima
            BlockPos targetPos = null;
            for (int attempts = 0; attempts < 15; attempts++) {
                int x = playerPos.getX() + random.nextInt(7) - 3; // -3 a +3
                int y = playerPos.getY() + random.nextInt(3); // 0 a +2
                int z = playerPos.getZ() + random.nextInt(7) - 3; // -3 a +3

                BlockPos pos = new BlockPos(x, y, z);
                if (mc.theWorld.getBlockState(pos).getBlock() == Blocks.air) {
                    targetPos = pos;
                    break;
                }
            }

            if (targetPos != null) {
                // Spawnar bloco suspeito
                Block chosenBlock = suspiciousBlocks[random.nextInt(suspiciousBlocks.length)];
                mc.theWorld.setBlockState(targetPos, chosenBlock.getDefaultState());
                simulatedBlocks.add(targetPos);
            }
        }

        // Remover todos os blocos após 2-4 segundos
        new Thread(() -> {
            try {
                Thread.sleep(2000 + random.nextInt(2000)); // 2-4 segundos
                for (BlockPos pos : new ArrayList<>(simulatedBlocks)) {
                    if (mc.theWorld != null) {
                        mc.theWorld.setBlockState(pos, Blocks.air.getDefaultState());
                    }
                }
                simulatedBlocks.clear();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
    }

    private void simulateVelocityCheck() {
        if (!RatoAddonsConfigSimple.velocityCheckDetection) return;

        // Aplicar velocidade REAL para cima (lançar player)
        mc.thePlayer.motionY = 1.5; // Velocidade alta para cima
        mc.thePlayer.velocityChanged = true;

        // Aplicar também movimento horizontal aleatório
        Random random = new Random();
        mc.thePlayer.motionX = (random.nextDouble() - 0.5) * 0.5;
        mc.thePlayer.motionZ = (random.nextDouble() - 0.5) * 0.5;
    }

    private void simulatePlayerCheck() {
        if (!RatoAddonsConfigSimple.playerCheckDetection) return;

        // Spawnar player REAL próximo olhando para você
        if (simulatedPlayer != null) {
            mc.theWorld.removeEntity(simulatedPlayer);
        }

        // Criar player fake próximo
        simulatedPlayer = new EntityOtherPlayerMP(mc.theWorld, mc.thePlayer.getGameProfile());

        // Posicionar MUITO próximo ao player real (2-3 blocos)
        double distance = 2.5; // Fixo em 2.5 blocos
        double angle = Math.random() * 2 * Math.PI;
        double x = mc.thePlayer.posX + Math.cos(angle) * distance;
        double z = mc.thePlayer.posZ + Math.sin(angle) * distance;
        double y = mc.thePlayer.posY;

        simulatedPlayer.setPosition(x, y, z);

        // Fazer o player olhar diretamente para você
        double dx = mc.thePlayer.posX - x;
        double dz = mc.thePlayer.posZ - z;
        float yaw = (float) (Math.atan2(dz, dx) * 180.0 / Math.PI) - 90.0f;
        simulatedPlayer.rotationYaw = yaw;
        simulatedPlayer.rotationPitch = 0.0f;

        // Adicionar ao mundo
        mc.theWorld.spawnEntityInWorld(simulatedPlayer);

        // Remover após 4 segundos (tempo suficiente para detectar)
        new Thread(() -> {
            try {
                Thread.sleep(4000);
                if (mc.theWorld != null && simulatedPlayer != null) {
                    mc.theWorld.removeEntity(simulatedPlayer);
                    simulatedPlayer = null;
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
    }

    public static void incrementDetectedChecks() {
        detectedChecks++;
    }

    private void finishSimulation() {
        isSimulating = false;
        simulationStep = 0;

        // Restaurar câmera original para evitar bugs
        if (mc.thePlayer != null) {
            mc.thePlayer.rotationYaw = originalYaw;
            mc.thePlayer.rotationPitch = originalPitch;
        }

        Logger.sendMessage("§aTestes Completos §f" + totalChecks + "/" + totalChecks + " §aChecks detectados §f" + detectedChecks);
    }
}
