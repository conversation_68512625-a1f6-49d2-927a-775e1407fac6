package com.rato.addons.pathfinding.enhanced;

import com.rato.addons.pathfinding.professional.PathNode;
import com.rato.addons.pathfinding.professional.MucifexPathRenderer;
import com.rato.addons.util.Logger;
import net.minecraft.client.Minecraft;
import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Gerenciador principal do sistema Enhanced baseado no Mucifex
 * Combina pathfinding otimizado com movimento inteligente
 */
public class EnhancedManager {
    
    private static EnhancedManager instance;
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Componentes do sistema
    private final EnhancedPathfinder pathfinder;
    private final EnhancedWalker walker;
    private final EnhancedLookManager lookManager;
    private final MucifexPathRenderer pathRenderer;
    private final ExecutorService pathfindingExecutor;
    
    // Estado do sistema
    private boolean isActive = false;
    private boolean isCalculating = false;
    private boolean debugMode = false;
    private boolean renderingEnabled = true;
    
    // Configurações
    private float movementSpeed = 1.0f;
    private float rotationSmoothness = 0.9f;
    
    // Estatísticas
    private long pathfindingStartTime;
    private int pathCalculations = 0;
    private Vec3 currentDestination;
    private List<PathNode> currentPath;
    
    // Controle de recálculo
    private long lastRecalculation = 0;
    private static final long RECALCULATION_COOLDOWN = 2000; // 2 segundos
    
    private EnhancedManager() {
        pathfinder = new EnhancedPathfinder();
        walker = EnhancedWalker.getInstance();
        lookManager = EnhancedLookManager.getInstance();
        pathRenderer = new MucifexPathRenderer();
        pathfindingExecutor = Executors.newSingleThreadExecutor(r -> {
            Thread t = new Thread(r, "Enhanced-Pathfinding");
            t.setDaemon(true);
            return t;
        });
        
        setupCallbacks();
        MinecraftForge.EVENT_BUS.register(this);
    }
    
    public static EnhancedManager getInstance() {
        if (instance == null) {
            instance = new EnhancedManager();
        }
        return instance;
    }
    
    /**
     * Configura callbacks do sistema
     */
    private void setupCallbacks() {
        walker.setOnDestinationReached(() -> {
            Logger.sendMessage("§a[Enhanced] Destino alcançado!");
            stopPathfinding();
        });
        
        walker.setOnMovementFailed(() -> {
            if (debugMode) {
                Logger.sendMessage("§e[Enhanced] Falha no movimento, recalculando...");
            }
            scheduleRecalculation();
        });
    }
    
    /**
     * Inicia pathfinding para destino específico
     */
    public boolean startPathfinding(Vec3 destination) {
        if (mc.thePlayer == null || mc.theWorld == null) {
            return false;
        }
        
        if (isActive) {
            stopPathfinding();
        }
        
        currentDestination = destination;
        pathfindingStartTime = System.currentTimeMillis();
        pathCalculations = 0;
        
        Vec3 playerPos = getPlayerPosition();
        double distance = playerPos.distanceTo(destination);
        
        if (debugMode) {
            Logger.sendMessage("§6[Enhanced] Iniciando sistema baseado no Mucifex...");
            Logger.sendMessage("§7De: " + formatVec3(playerPos));
            Logger.sendMessage("§7Para: " + formatVec3(destination));
            Logger.sendMessage("§7Distância: " + String.format("%.1f", distance) + " blocos");
        }
        
        // Configurar componentes
        walker.setDebugMode(debugMode);
        walker.setSprintEnabled(movementSpeed > 1.0f);
        lookManager.setRotationSpeed(200.0 / rotationSmoothness);
        
        // Iniciar cálculo assíncrono
        calculatePathAsync(playerPos, destination);
        
        isActive = true;
        Logger.sendMessage("§a[Enhanced] Sistema iniciado! Calculando caminho...");
        
        return true;
    }
    
    /**
     * Calcula caminho de forma assíncrona
     */
    private void calculatePathAsync(Vec3 start, Vec3 goal) {
        if (isCalculating) return;
        
        isCalculating = true;
        pathCalculations++;
        
        CompletableFuture.supplyAsync(() -> {
            try {
                if (debugMode) {
                    Logger.sendMessage("§7[Enhanced] Calculando caminho... (" + pathCalculations + ")");
                }
                
                long startTime = System.currentTimeMillis();
                List<PathNode> path = pathfinder.findPath(start, goal);
                long calculationTime = System.currentTimeMillis() - startTime;
                
                if (debugMode && path != null) {
                    Logger.sendMessage("§a[Enhanced] Caminho calculado em " + calculationTime + "ms: " + 
                        path.size() + " nós");
                    
                    // Debug dos tipos de movimento
                    long jumpNodes = path.stream().filter(n -> n.movementType == PathNode.MovementType.JUMP).count();
                    long fallNodes = path.stream().filter(n -> n.movementType == PathNode.MovementType.FALL).count();
                    
                    if (jumpNodes > 0 || fallNodes > 0) {
                        Logger.sendMessage("§7[Enhanced] Pulos: " + jumpNodes + ", Quedas: " + fallNodes);
                    }
                }
                
                return path;
                
            } catch (Exception e) {
                Logger.sendMessage("§c[Enhanced] Erro no cálculo: " + e.getMessage());
                if (debugMode) {
                    e.printStackTrace();
                }
                return null;
            }
        }, pathfindingExecutor).thenAccept(this::onPathCalculated);
    }
    
    /**
     * Callback quando caminho é calculado
     */
    private void onPathCalculated(List<PathNode> path) {
        isCalculating = false;
        
        if (!isActive) return;
        
        if (path != null && !path.isEmpty()) {
            currentPath = path;
            
            // Configurar renderização
            if (renderingEnabled) {
                pathRenderer.setPath(path);
                pathRenderer.setEnabled(true);
            }
            
            // Iniciar movimento
            walker.startWalking(path);
            
            if (debugMode) {
                Logger.sendMessage("§a[Enhanced] Iniciando movimento inteligente!");
            }
            
        } else {
            Logger.sendMessage("§c[Enhanced] Não foi possível encontrar um caminho!");
            stopPathfinding();
        }
    }
    
    /**
     * Agenda recálculo do caminho
     */
    private void scheduleRecalculation() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastRecalculation < RECALCULATION_COOLDOWN) {
            return; // Cooldown ativo
        }
        
        lastRecalculation = currentTime;
        
        if (isActive && currentDestination != null && !isCalculating) {
            Vec3 currentPos = getPlayerPosition();

            if (debugMode) {
                Logger.sendMessage("§7[Enhanced] Recalculando caminho...");
            }

            calculatePathAsync(currentPos, currentDestination);
        }
    }
    
    /**
     * Para o pathfinding atual
     */
    public void stopPathfinding() {
        isActive = false;
        isCalculating = false;
        
        walker.stopWalking();
        lookManager.cancel();
        
        if (pathRenderer != null) {
            pathRenderer.setEnabled(false);
        }
        
        currentPath = null;
        currentDestination = null;
        
        if (debugMode) {
            Logger.sendMessage("§7[Enhanced] Sistema parado");
        }
    }
    
    /**
     * Atualização principal (chamada a cada tick)
     */
    @SubscribeEvent
    public void onTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.START || !isActive) {
            return;
        }
        
        if (mc.thePlayer == null || mc.theWorld == null) {
            stopPathfinding();
            return;
        }
        
        // Verificar se precisa recalcular periodicamente
        if (shouldPeriodicRecalculation()) {
            scheduleRecalculation();
        }
    }
    
    /**
     * Verifica se deve fazer recálculo periódico
     */
    private boolean shouldPeriodicRecalculation() {
        if (currentDestination == null || isCalculating) return false;
        
        long timeSinceStart = System.currentTimeMillis() - pathfindingStartTime;
        long timeSinceLastRecalc = System.currentTimeMillis() - lastRecalculation;
        
        // Recalcular a cada 15 segundos se estiver ativo há mais de 20 segundos
        return timeSinceStart > 20000 && timeSinceLastRecalc > 15000;
    }
    
    // Getters e Setters
    public boolean isActive() { return isActive; }
    public boolean isCalculating() { return isCalculating; }
    public void setDebugMode(boolean debug) { 
        this.debugMode = debug;
        if (walker != null) walker.setDebugMode(debug);
    }
    public boolean isDebugMode() { return debugMode; }
    
    public void setRenderingEnabled(boolean enabled) { 
        this.renderingEnabled = enabled;
        if (pathRenderer != null) {
            pathRenderer.setEnabled(enabled && isActive);
        }
    }
    public boolean isRenderingEnabled() { return renderingEnabled; }
    
    public void setMovementSpeed(float speed) { 
        this.movementSpeed = speed;
        if (walker != null) walker.setSprintEnabled(speed > 1.0f);
    }
    public float getMovementSpeed() { return movementSpeed; }
    
    public void setRotationSmoothness(float smoothness) { 
        this.rotationSmoothness = smoothness;
        if (lookManager != null) lookManager.setRotationSpeed(200.0 / smoothness);
    }
    public float getRotationSmoothness() { return rotationSmoothness; }
    
    /**
     * Retorna status detalhado do sistema
     */
    public String getDetailedStatus() {
        if (!isActive) return "Inativo";
        
        long elapsed = System.currentTimeMillis() - pathfindingStartTime;
        StringBuilder status = new StringBuilder();
        
        status.append("Ativo há ").append(elapsed / 1000).append("s");
        status.append(", Cálculos: ").append(pathCalculations);
        
        if (currentPath != null) {
            status.append(", Nós: ").append(currentPath.size());
            float progress = walker.getProgress();
            status.append(" (").append(String.format("%.1f", progress * 100)).append("%)");
        }
        
        if (isCalculating) {
            status.append(", Calculando");
        } else if (walker.isActive()) {
            status.append(", Movendo");
        }
        
        return status.toString();
    }
    
    /**
     * Retorna informações de debug
     */
    public String getDebugInfo() {
        StringBuilder info = new StringBuilder();
        info.append("§7Status: ").append(isActive ? "§aAtivo" : "§cInativo").append("\n");
        info.append("§7Destino: ").append(currentDestination != null ? formatVec3(currentDestination) : "§cNenhum").append("\n");
        info.append("§7Cálculos realizados: §f").append(pathCalculations).append("\n");
        info.append("§7Nós no caminho: §f").append(currentPath != null ? currentPath.size() : 0).append("\n");
        info.append("§7Calculando: ").append(isCalculating ? "§aSim" : "§cNão").append("\n");
        info.append("§7Walker: ").append(walker.isActive() ? "§aAtivo" : "§cInativo").append("\n");
        info.append("§7LookManager: ").append(lookManager.isActive() ? "§aAtivo" : "§cInativo").append("\n");
        info.append("§7Velocidade: §f").append(movementSpeed).append("\n");
        info.append("§7Suavidade: §f").append(rotationSmoothness).append("\n");
        info.append("§7Renderização: ").append(renderingEnabled ? "§aAtiva" : "§cInativa");
        
        if (walker.isActive()) {
            info.append("\n§7Walker Info: §f").append(walker.getDebugInfo());
        }
        
        if (lookManager.isActive()) {
            info.append("\n§7Look Info: §f").append(lookManager.getDebugInfo());
        }
        
        return info.toString();
    }
    
    /**
     * Obtém estatísticas de performance
     */
    public String getPerformanceStats() {
        if (!isActive) return "Sistema inativo";
        
        long elapsed = Math.max(1, System.currentTimeMillis() - pathfindingStartTime);
        double calculationsPerSecond = pathCalculations / (elapsed / 1000.0);
        
        StringBuilder stats = new StringBuilder();
        stats.append("Cálculos/s: ").append(String.format("%.2f", calculationsPerSecond));
        
        if (currentPath != null) {
            double nodesPerSecond = currentPath.size() / (elapsed / 1000.0);
            stats.append(", Nós/s: ").append(String.format("%.2f", nodesPerSecond));
        }
        
        return stats.toString();
    }
    
    /**
     * Força recálculo imediato
     */
    public void forceRecalculation() {
        if (isActive && currentDestination != null) {
            lastRecalculation = 0; // Reset cooldown
            scheduleRecalculation();
        }
    }
    
    /**
     * Limpa recursos
     */
    public void shutdown() {
        stopPathfinding();
        pathfindingExecutor.shutdown();
        walker.shutdown();
        lookManager.shutdown();
    }
    
    /**
     * Obtém posição correta do player (baseado no Mucifex)
     */
    private Vec3 getPlayerPosition() {
        if (mc.thePlayer == null) return new Vec3(0, 0, 0);

        // Usar Math.floor como no Mucifex para consistência
        double x = Math.floor(mc.thePlayer.posX);
        double y = Math.floor(mc.thePlayer.posY);
        double z = Math.floor(mc.thePlayer.posZ);

        Vec3 pos = new Vec3(x, y, z);

        // Verificar se a posição atual é sólida e ajustar se necessário (como no Mucifex)
        BlockPos blockPos = new BlockPos(x, y, z);
        if (mc.theWorld != null && isBlockSolid(blockPos)) {
            // Tentar uma posição acima
            blockPos = blockPos.up();
            if (!isBlockSolid(blockPos)) {
                pos = new Vec3(x, y + 1, z);
            }
        }

        return pos;
    }

    /**
     * Verifica se um bloco é sólido (versão simplificada para o manager)
     */
    private boolean isBlockSolid(BlockPos pos) {
        if (mc.theWorld == null) return false;

        try {
            return mc.theWorld.getBlockState(pos).getBlock().getMaterial().blocksMovement();
        } catch (Exception e) {
            return false;
        }
    }

    private String formatVec3(Vec3 vec) {
        return String.format("%.1f, %.1f, %.1f", vec.xCoord, vec.yCoord, vec.zCoord);
    }
}
