Minecraft Forge: Credits/Thank You

Forge is a set of tools and modifications to the Minecraft base game code to assist 
mod developers in creating new and exciting content. It has been in development for 
several years now, but I would like to take this time thank a few people who have 
helped it along it's way.

First, the people who originally created the Forge projects way back in Minecraft 
alpha. <PERSON><PERSON><PERSON> of RedPower, and SpaceToad of Buildcraft, without their acceptiance 
of me taking over the project, who knows what Minecraft modding would be today.

Secondly, someone who has worked with me, and developed some of the core features
that allow modding to be as functional, and as simple as it is, cpw. For developing
FML, which stabelized the client and server modding ecosystem. As well as the base
loading system that allows us to modify Minecraft's code as elegently as possible.

<PERSON><PERSON>, who has stepped up as the issue and pull request manager. Helping to keep me
sane as well as guiding the community into creating better additions to Forge.

Searge, Bspks, Fesh0r, ProfMobious, and all the rest over on the MCP team {of which 
I am a part}. For creating some of the core tools needed to make Minecraft modding 
both possible, and as stable as can be.
  On that note, here is some specific information of the MCP data we use:
    * Minecraft Coder Pack (MCP) *
      Forge Mod Loader and Minecraft Forge have permission to distribute and automatically 
      download components of MCP and distribute MCP data files. This permission is not 
      transitive and others wishing to redistribute the Minecraft Forge source independently
      should seek permission of MCP or remove the MCP data files and request their users 
      to download MCP separately.
      
And lastly, the countless community members who have spent time submitting bug reports, 
pull requests, and just helping out the community in general. Thank you.

--LexManos

=========================================================================

This is Forge Mod Loader.

You can find the source code at all times at https://github.com/MinecraftForge/MinecraftForge/tree/1.12.x/src/main/java/net/minecraftforge/fml

This minecraft mod is a clean open source implementation of a mod loader for minecraft servers
and minecraft clients.

The code is authored by cpw.

It began by partially implementing an API defined by the client side ModLoader, authored by Risugami.
http://www.minecraftforum.net/topic/75440-
This support has been dropped as of Minecraft release 1.7, as Risugami no longer maintains ModLoader.

It also contains suggestions and hints and generous helpings of code from LexManos, author of MinecraftForge.
http://www.minecraftforge.net/

Additionally, it contains an implementation of topological sort based on that 
published at http://keithschwarz.com/interesting/code/?dir=topological-sort

It also contains code from the Maven project for performing versioned dependency
resolution. http://maven.apache.org/

It also contains a partial repackaging of the javaxdelta library from http://sourceforge.net/projects/javaxdelta/
with credit to it's authors.

Forge Mod Loader downloads components from the Minecraft Coder Pack
(http://mcp.ocean-labs.de/index.php/Main_Page) with kind permission from the MCP team.

