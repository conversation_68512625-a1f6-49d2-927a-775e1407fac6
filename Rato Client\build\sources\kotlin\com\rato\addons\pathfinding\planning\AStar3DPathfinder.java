package com.rato.addons.pathfinding.planning;

import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;
import com.rato.addons.pathfinding.planning.PathfindingGrid3D.*;

import java.util.*;

/**
 * Implementação otimizada do algoritmo A* para pathfinding 3D
 * Inclui heurísticas avançadas e otimizações de performance
 */
public class AStar3DPathfinder {
    
    // Configurações do algoritmo
    private static final int MAX_SEARCH_NODES = 5000;
    private static final double HEURISTIC_WEIGHT = 1.2; // Peso da heurística (> 1 = mais rápido, menos ótimo)
    private static final int MAX_PATH_LENGTH = 200;
    
    /**
     * Nó do algoritmo A*
     */
    private static class AStarNode implements Comparable<AStarNode> {
        public final BlockPos position;
        public final double gCost; // Custo real do início
        public final double hCost; // Custo heurístico até o destino
        public final double fCost; // gCost + hCost
        public final AStarNode parent;
        public final PathfindingGrid3D.MovementType movementType;
        
        public AStarNode(BlockPos position, double gCost, double hCost, AStarNode parent, 
                        PathfindingGrid3D.MovementType movementType) {
            this.position = position;
            this.gCost = gCost;
            this.hCost = hCost;
            this.fCost = gCost + hCost * HEURISTIC_WEIGHT;
            this.parent = parent;
            this.movementType = movementType;
        }
        
        @Override
        public int compareTo(AStarNode other) {
            int result = Double.compare(this.fCost, other.fCost);
            if (result == 0) {
                // Tie-breaker: preferir menor hCost (mais próximo do objetivo)
                result = Double.compare(this.hCost, other.hCost);
            }
            if (result == 0) {
                // Segundo tie-breaker: preferir menor gCost (caminho mais curto)
                result = Double.compare(this.gCost, other.gCost);
            }
            return result;
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (!(obj instanceof AStarNode)) return false;
            AStarNode other = (AStarNode) obj;
            return position.equals(other.position);
        }
        
        @Override
        public int hashCode() {
            return position.hashCode();
        }
    }
    
    /**
     * Resultado do pathfinding
     */
    public static class PathfindingResult {
        public final List<Vec3> path;
        public final List<PathfindingGrid3D.MovementType> movementTypes;
        public final double totalCost;
        public final int nodesExplored;
        public final long computationTime;
        public final boolean pathFound;
        public final String failureReason;
        
        public PathfindingResult(List<Vec3> path, List<PathfindingGrid3D.MovementType> movementTypes, 
                               double totalCost, int nodesExplored, long computationTime, 
                               boolean pathFound, String failureReason) {
            this.path = path != null ? new ArrayList<>(path) : new ArrayList<>();
            this.movementTypes = movementTypes != null ? new ArrayList<>(movementTypes) : new ArrayList<>();
            this.totalCost = totalCost;
            this.nodesExplored = nodesExplored;
            this.computationTime = computationTime;
            this.pathFound = pathFound;
            this.failureReason = failureReason;
        }
        
        public static PathfindingResult failure(String reason, int nodesExplored, long computationTime) {
            return new PathfindingResult(null, null, Double.MAX_VALUE, nodesExplored, 
                                       computationTime, false, reason);
        }
        
        public static PathfindingResult success(List<Vec3> path, List<PathfindingGrid3D.MovementType> movementTypes,
                                              double totalCost, int nodesExplored, long computationTime) {
            return new PathfindingResult(path, movementTypes, totalCost, nodesExplored, 
                                       computationTime, true, null);
        }
    }
    
    /**
     * Encontra caminho usando A* otimizado
     */
    public PathfindingResult findPath(Vec3 start, Vec3 goal, BuiltPathfindingGrid grid) {
        long startTime = System.currentTimeMillis();
        
        if (grid == null) {
            return PathfindingResult.failure("Grid is null", 0, System.currentTimeMillis() - startTime);
        }
        
        BlockPos startPos = new BlockPos(start);
        BlockPos goalPos = new BlockPos(goal);
        
        // Verificar se start e goal são válidos
        PathfindingNode startNode = grid.getNode(startPos);
        PathfindingNode goalNode = grid.getNode(goalPos);
        
        if (startNode == null || !startNode.isWalkable) {
            startPos = findNearestWalkable(startPos, grid);
            if (startPos == null) {
                return PathfindingResult.failure("No walkable start position found", 0, 
                                               System.currentTimeMillis() - startTime);
            }
            startNode = grid.getNode(startPos);
        }
        
        if (goalNode == null || !goalNode.isWalkable) {
            goalPos = findNearestWalkable(goalPos, grid);
            if (goalPos == null) {
                return PathfindingResult.failure("No walkable goal position found", 0, 
                                               System.currentTimeMillis() - startTime);
            }
            goalNode = grid.getNode(goalPos);
        }
        
        // Executar A*
        return executeAStar(startPos, goalPos, grid, startTime);
    }
    
    /**
     * Executa o algoritmo A*
     */
    private PathfindingResult executeAStar(BlockPos start, BlockPos goal, BuiltPathfindingGrid grid, long startTime) {
        // Estruturas de dados do A*
        PriorityQueue<AStarNode> openSet = new PriorityQueue<>();
        Set<BlockPos> closedSet = new HashSet<>();
        Map<BlockPos, AStarNode> allNodes = new HashMap<>();
        
        // Nó inicial
        double initialHCost = calculateHeuristic(start, goal);
        AStarNode startNode = new AStarNode(start, 0, initialHCost, null, PathfindingGrid3D.MovementType.WALK);
        openSet.add(startNode);
        allNodes.put(start, startNode);
        
        int nodesExplored = 0;
        
        while (!openSet.isEmpty() && nodesExplored < MAX_SEARCH_NODES) {
            AStarNode current = openSet.poll();
            nodesExplored++;
            
            // Chegou ao destino
            if (current.position.equals(goal)) {
                long computationTime = System.currentTimeMillis() - startTime;
                return reconstructPath(current, computationTime, nodesExplored);
            }
            
            closedSet.add(current.position);
            
            // Explorar vizinhos
            PathfindingNode currentGridNode = grid.getNode(current.position);
            if (currentGridNode == null) continue;
            
            for (Connection connection : currentGridNode.getValidConnections()) {
                BlockPos neighborPos = connection.neighbor.position;
                
                if (closedSet.contains(neighborPos)) continue;
                
                double tentativeGCost = current.gCost + connection.cost;
                
                AStarNode existingNode = allNodes.get(neighborPos);
                if (existingNode != null && tentativeGCost >= existingNode.gCost) {
                    continue; // Caminho pior
                }
                
                // Criar novo nó
                double hCost = calculateHeuristic(neighborPos, goal);
                AStarNode neighborNode = new AStarNode(neighborPos, tentativeGCost, hCost, current, connection.type);
                
                allNodes.put(neighborPos, neighborNode);
                openSet.add(neighborNode);
            }
        }
        
        long computationTime = System.currentTimeMillis() - startTime;
        String failureReason = nodesExplored >= MAX_SEARCH_NODES ? "Max nodes explored" : "No path found";
        return PathfindingResult.failure(failureReason, nodesExplored, computationTime);
    }
    
    /**
     * Calcula heurística otimizada (distância euclidiana com penalidades)
     */
    private double calculateHeuristic(BlockPos from, BlockPos to) {
        double dx = to.getX() - from.getX();
        double dy = to.getY() - from.getY();
        double dz = to.getZ() - from.getZ();
        
        // Distância euclidiana 3D
        double euclideanDistance = Math.sqrt(dx * dx + dy * dy + dz * dz);
        
        // Penalidade por diferença de altura (subir/descer é mais custoso)
        double heightPenalty = Math.abs(dy) * 0.5;
        
        return euclideanDistance + heightPenalty;
    }
    
    /**
     * Reconstrói o caminho a partir do nó final
     */
    private PathfindingResult reconstructPath(AStarNode finalNode, long computationTime, int nodesExplored) {
        List<Vec3> path = new ArrayList<>();
        List<PathfindingGrid3D.MovementType> movementTypes = new ArrayList<>();
        AStarNode current = finalNode;
        double totalCost = finalNode.gCost;
        
        // Reconstruir caminho reverso
        while (current != null) {
            path.add(new Vec3(current.position.getX() + 0.5, 
                            current.position.getY(), 
                            current.position.getZ() + 0.5));
            if (current.movementType != null) {
                movementTypes.add(current.movementType);
            }
            current = current.parent;
        }
        
        // Reverter para ordem correta
        Collections.reverse(path);
        Collections.reverse(movementTypes);
        
        // Suavizar caminho
        List<Vec3> smoothedPath = smoothPath(path);
        
        // Verificar se o caminho não é muito longo
        if (smoothedPath.size() > MAX_PATH_LENGTH) {
            return PathfindingResult.failure("Path too long", nodesExplored, computationTime);
        }
        
        return PathfindingResult.success(smoothedPath, movementTypes, totalCost, nodesExplored, computationTime);
    }
    
    /**
     * Suaviza o caminho removendo waypoints desnecessários
     */
    private List<Vec3> smoothPath(List<Vec3> originalPath) {
        if (originalPath.size() <= 2) return originalPath;
        
        List<Vec3> smoothedPath = new ArrayList<>();
        smoothedPath.add(originalPath.get(0));
        
        int current = 0;
        while (current < originalPath.size() - 1) {
            int farthest = current + 1;
            
            // Encontrar o ponto mais distante com linha de visão clara
            for (int i = current + 2; i < originalPath.size(); i++) {
                if (hasDirectPath(originalPath.get(current), originalPath.get(i))) {
                    farthest = i;
                } else {
                    break;
                }
            }
            
            smoothedPath.add(originalPath.get(farthest));
            current = farthest;
        }
        
        return smoothedPath;
    }
    
    /**
     * Verifica se há caminho direto entre dois pontos
     */
    private boolean hasDirectPath(Vec3 from, Vec3 to) {
        double distance = from.distanceTo(to);
        
        // Se a distância é muito grande, não considerar caminho direto
        if (distance > 10.0) return false;
        
        // Verificação simplificada - pode ser melhorada com raycasting
        double heightDiff = Math.abs(to.yCoord - from.yCoord);
        return heightDiff <= 2.0; // Permitir diferenças de altura pequenas
    }
    
    /**
     * Encontra posição caminhável mais próxima
     */
    private BlockPos findNearestWalkable(BlockPos center, BuiltPathfindingGrid grid) {
        // Busca em espiral crescente
        for (int radius = 1; radius <= 8; radius++) {
            for (int x = -radius; x <= radius; x++) {
                for (int z = -radius; z <= radius; z++) {
                    for (int y = -3; y <= 3; y++) {
                        BlockPos candidate = center.add(x, y, z);
                        PathfindingNode node = grid.getNode(candidate);
                        if (node != null && node.isWalkable) {
                            return candidate;
                        }
                    }
                }
            }
        }
        return null;
    }
    
    /**
     * Encontra caminho com múltiplos objetivos (útil para coleta de múltiplas árvores)
     */
    public PathfindingResult findMultiTargetPath(Vec3 start, List<Vec3> goals, BuiltPathfindingGrid grid) {
        if (goals.isEmpty()) {
            return PathfindingResult.failure("No goals provided", 0, 0);
        }
        
        // Para múltiplos objetivos, usar TSP simplificado (nearest neighbor)
        List<Vec3> orderedGoals = optimizeGoalOrder(start, goals, grid);
        
        List<Vec3> completePath = new ArrayList<>();
        List<PathfindingGrid3D.MovementType> completeMovementTypes = new ArrayList<>();
        double totalCost = 0;
        int totalNodesExplored = 0;
        long totalTime = 0;
        
        Vec3 currentStart = start;
        
        for (Vec3 goal : orderedGoals) {
            PathfindingResult segmentResult = findPath(currentStart, goal, grid);
            
            if (!segmentResult.pathFound) {
                return PathfindingResult.failure("Failed to reach goal: " + goal, 
                                               totalNodesExplored + segmentResult.nodesExplored, 
                                               totalTime + segmentResult.computationTime);
            }
            
            // Adicionar segmento ao caminho completo (evitar duplicar pontos)
            if (!completePath.isEmpty() && !segmentResult.path.isEmpty()) {
                segmentResult.path.remove(0); // Remover primeiro ponto (duplicado)
            }
            
            completePath.addAll(segmentResult.path);
            completeMovementTypes.addAll(segmentResult.movementTypes);
            totalCost += segmentResult.totalCost;
            totalNodesExplored += segmentResult.nodesExplored;
            totalTime += segmentResult.computationTime;
            
            currentStart = goal;
        }
        
        return PathfindingResult.success(completePath, completeMovementTypes, totalCost, 
                                       totalNodesExplored, totalTime);
    }
    
    /**
     * Otimiza ordem dos objetivos usando nearest neighbor
     */
    private List<Vec3> optimizeGoalOrder(Vec3 start, List<Vec3> goals, BuiltPathfindingGrid grid) {
        List<Vec3> remaining = new ArrayList<>(goals);
        List<Vec3> ordered = new ArrayList<>();
        Vec3 current = start;
        
        while (!remaining.isEmpty()) {
            Vec3 nearest = null;
            double nearestDistance = Double.MAX_VALUE;
            
            for (Vec3 goal : remaining) {
                double distance = current.distanceTo(goal);
                if (distance < nearestDistance) {
                    nearestDistance = distance;
                    nearest = goal;
                }
            }
            
            if (nearest != null) {
                ordered.add(nearest);
                remaining.remove(nearest);
                current = nearest;
            } else {
                break;
            }
        }
        
        return ordered;
    }
}
