package com.rato.addons.pathfinding.movement;

import com.rato.addons.pathfinding.BaritoneStylePathfinder;
import com.rato.addons.pathfinding.BaritoneStyleRenderer;

/**
 * Gerenciador global para o sistema de movimento
 */
public class MovementManager {
    
    private static MovementManager instance;
    
    private BaritoneMovementExecutor executor;
    private BaritoneStyleRenderer renderer;

    private MovementManager() {
        this.executor = new BaritoneMovementExecutor();
        this.renderer = new BaritoneStyleRenderer();
    }
    
    public static MovementManager getInstance() {
        if (instance == null) {
            instance = new MovementManager();
        }
        return instance;
    }
    
    /**
     * Inicia execução de caminho
     */
    public void startExecution(java.util.List<BaritoneStylePathfinder.BaritoneNode> path) {
        // Parar execução anterior se houver
        stopExecution();

        // Configurar renderização
        renderer.setPath(path);
        renderer.setEnabled(true);

        // Iniciar movimento com novo executor
        executor.startExecution(path);
    }
    
    /**
     * Para execução
     */
    public void stopExecution() {
        if (executor != null) {
            executor.stopExecution();
        }
        if (renderer != null) {
            renderer.setEnabled(false);
        }
    }
    
    /**
     * Verifica se está executando
     */
    public boolean isExecuting() {
        return executor != null && executor.isExecuting();
    }
    
    /**
     * Obtém executor
     */
    public BaritoneMovementExecutor getExecutor() {
        return executor;
    }
    
    /**
     * Obtém renderer
     */
    public BaritoneStyleRenderer getRenderer() {
        return renderer;
    }

    /**
     * Limpa renderização após completar execução
     */
    public void clearRenderingAfterCompletion() {
        if (renderer != null) {
            renderer.clearPath();
            renderer.setEnabled(false);
        }
    }

    /**
     * Mantém renderização ativa enquanto executa
     */
    public void keepRenderingActive() {
        if (renderer != null && executor != null && executor.isExecuting()) {
            renderer.setEnabled(true);
        }
    }
}
