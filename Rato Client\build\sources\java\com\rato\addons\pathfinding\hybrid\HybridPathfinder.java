package com.rato.addons.pathfinding.hybrid;

import com.rato.addons.pathfinding.professional.PathNode;
import com.rato.addons.util.Logger;
import net.minecraft.block.Block;
import net.minecraft.block.material.Material;
import net.minecraft.client.Minecraft;
import net.minecraft.init.Blocks;
import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;
import net.minecraft.world.World;

import java.util.*;

/**
 * Sistema de pathfinding híbrido baseado no Mucifex e Stevebot
 * Implementa pathfinding segmentado para distâncias longas
 */
public class HybridPathfinder {
    
    private static final Minecraft mc = Minecraft.getMinecraft();
    
    // Configurações para pathfinding segmentado (aumentadas drasticamente)
    private static final double H_COST_WEIGHT = 1.0;
    private static final int MAX_ITERATIONS_SHORT = 25000;  // Aumentado
    private static final int MAX_ITERATIONS_LONG = 100000;  // Aumentado drasticamente
    private static final int MAX_UNLOADED_HITS = 2000;     // Aumentado
    private static final double GOAL_TOLERANCE = 0.5;
    private static final int SEGMENT_RADIUS = 25;
    
    // Custos de movimento - permitir todos os tipos de movimento
    private static final double COST_WALK_STRAIGHT = 1.0;
    private static final double COST_WALK_DIAGONAL = 1.414;
    private static final double COST_JUMP = 2.0;       // Ligeiramente mais caro
    private static final double COST_FALL = 1.5;       // Ligeiramente mais caro
    private static final double COST_CLIMB = 3.0;      // Moderadamente mais caro
    
    // Direções de movimento
    private static final int[][] MOVEMENT_DIRECTIONS = {
        {1, 0, 0}, {-1, 0, 0}, {0, 0, 1}, {0, 0, -1},  // Horizontal
        {1, 0, 1}, {1, 0, -1}, {-1, 0, 1}, {-1, 0, -1} // Diagonal
    };
    
    private static final int[][] VERTICAL_MOVEMENTS = {
        {0, 1, 0}, {0, -1, 0},  // Vertical puro
        {1, 1, 0}, {-1, 1, 0}, {0, 1, 1}, {0, 1, -1},  // Pulos
        {1, -1, 0}, {-1, -1, 0}, {0, -1, 1}, {0, -1, -1} // Quedas
    };
    
    /**
     * Encontra caminho usando algoritmo híbrido com pathfinding segmentado
     */
    public List<PathNode> findPath(Vec3 start, Vec3 goal) {
        if (mc.theWorld == null) {
            Logger.sendMessage("§c[Hybrid] Mundo é null!");
            return null;
        }
        
        // Conversão de coordenadas
        BlockPos startPos = new BlockPos(Math.floor(start.xCoord), Math.floor(start.yCoord), Math.floor(start.zCoord));
        BlockPos goalPos = new BlockPos((int)goal.xCoord, (int)goal.yCoord, (int)goal.zCoord);

        Logger.sendMessage("§6[Hybrid] Iniciando pathfinding híbrido...");
        Logger.sendMessage("§7De: " + startPos + " Para: " + goalPos);
        Logger.sendMessage("§c[DEBUG] Objetivo EXATO: X=" + goalPos.getX() + " Y=" + goalPos.getY() + " Z=" + goalPos.getZ());
        
        // Verificar se precisa de pathfinding segmentado
        double distance = calculateDistance(startPos, goalPos);
        Logger.sendMessage("§7[Hybrid] Distância total: " + String.format("%.1f", distance) + " blocos");
        
        // SEMPRE tentar pathfinding direto primeiro, independente da distância
        Logger.sendMessage("§a[Hybrid] Tentando pathfinding direto primeiro...");
        List<PathNode> directPath = findPathInternal(startPos, goalPos, start, goal, MAX_ITERATIONS_LONG);

        if (directPath != null && !directPath.isEmpty()) {
            Logger.sendMessage("§a[Hybrid] SUCESSO! Pathfinding direto funcionou com " + directPath.size() + " nós!");
            return directPath;
        }

        Logger.sendMessage("§c[Hybrid] Pathfinding direto falhou, usando segmentado como fallback...");

        if (distance > SEGMENT_RADIUS) {
            Logger.sendMessage("§e[Hybrid] Distância longa detectada - usando pathfinding segmentado");
            return findSegmentedPath(startPos, goalPos, start, goal);
        } else {
            Logger.sendMessage("§a[Hybrid] Distância curta - usando pathfinding direto com mais iterações");
            return findPathInternal(startPos, goalPos, start, goal, MAX_ITERATIONS_LONG * 2);
        }
    }
    
    /**
     * Pathfinding segmentado para distâncias longas (com pathfinding em cadeia)
     */
    private List<PathNode> findSegmentedPath(BlockPos startPos, BlockPos goalPos, Vec3 start, Vec3 goal) {
        Logger.sendMessage("§6[Hybrid] Iniciando pathfinding segmentado em cadeia...");

        List<PathNode> completePath = new ArrayList<>();
        BlockPos currentStart = startPos;
        Vec3 currentStartVec = start;
        int segmentCount = 0;
        int maxSegments = 5; // Máximo 5 segmentos para evitar loops infinitos

        while (segmentCount < maxSegments) {
            segmentCount++;

            // Verificar se já chegou próximo o suficiente do objetivo final
            double remainingDistance = calculateDistance(currentStart, goalPos);
            Logger.sendMessage("§7[Hybrid] Segmento " + segmentCount + " - Distância restante: " + String.format("%.1f", remainingDistance));

            if (remainingDistance <= 25) {
                Logger.sendMessage("§a[Hybrid] Distância restante pequena, tentando pathfinding direto para objetivo final");
                List<PathNode> finalPath = findDirectPath(currentStart, goalPos, currentStartVec, goal);

                if (finalPath != null && !finalPath.isEmpty()) {
                    // Remover primeiro nó se não for o primeiro segmento (evitar duplicação)
                    if (!completePath.isEmpty() && !finalPath.isEmpty()) {
                        finalPath.remove(0);
                    }
                    completePath.addAll(finalPath);
                    Logger.sendMessage("§a[Hybrid] Pathfinding em cadeia completo com " + completePath.size() + " nós total!");
                    return completePath;
                }
            }

            // Tentar múltiplos waypoints com diferentes tamanhos de segmento
            int[] segmentSizes = {15, 20, 25, 10};
            List<PathNode> segmentPath = null;

            for (int segmentSize : segmentSizes) {
                Logger.sendMessage("§7[Hybrid] Segmento " + segmentCount + " - Tentando " + segmentSize + " blocos...");

                BlockPos waypoint = findWaypointWithSize(currentStart, goalPos, segmentSize);
                Logger.sendMessage("§7[Hybrid] Waypoint: " + waypoint);

                // Pathfinding para o waypoint
                Vec3 waypointVec = new Vec3(waypoint.getX() + 0.5, waypoint.getY(), waypoint.getZ() + 0.5);
                segmentPath = findDirectPath(currentStart, waypoint, currentStartVec, waypointVec);

                if (segmentPath != null && !segmentPath.isEmpty()) {
                    Logger.sendMessage("§a[Hybrid] Segmento " + segmentCount + " encontrado com " + segmentPath.size() + " nós (tamanho " + segmentSize + ")");
                    break;
                }

                Logger.sendMessage("§c[Hybrid] Falha com segmento " + segmentSize + ", tentando próximo...");
            }

            if (segmentPath == null || segmentPath.isEmpty()) {
                Logger.sendMessage("§c[Hybrid] Falha em todos os tamanhos de segmento " + segmentCount);
                break;
            }

            // Adicionar segmento ao caminho completo
            if (!completePath.isEmpty() && !segmentPath.isEmpty()) {
                segmentPath.remove(0); // Remover primeiro nó para evitar duplicação
            }
            completePath.addAll(segmentPath);

            // Atualizar posição atual para o final do segmento
            PathNode lastNode = segmentPath.get(segmentPath.size() - 1);
            currentStart = new BlockPos(lastNode.position.xCoord, lastNode.position.yCoord, lastNode.position.zCoord);
            currentStartVec = lastNode.position;

            Logger.sendMessage("§a[Hybrid] Segmento " + segmentCount + " adicionado. Caminho total: " + completePath.size() + " nós");

            // SEMPRE tentar ir para o objetivo final após cada segmento
            double finalDistance = calculateDistance(currentStart, goalPos);
            Logger.sendMessage("§a[Hybrid] Tentando pathfinding direto para objetivo final (distância: " + String.format("%.1f", finalDistance) + ")");

            // Tentar pathfinding direto para o objetivo final com MUITAS iterações
            List<PathNode> finalPath = findPathInternal(currentStart, goalPos, currentStartVec, goal, MAX_ITERATIONS_LONG);
            if (finalPath != null && !finalPath.isEmpty()) {
                // Remover primeiro nó se não for o primeiro segmento
                if (!completePath.isEmpty() && !finalPath.isEmpty()) {
                    finalPath.remove(0);
                }
                completePath.addAll(finalPath);
                Logger.sendMessage("§a[Hybrid] SUCESSO! Pathfinding final completo com " + completePath.size() + " nós total!");
                return completePath;
            }

            Logger.sendMessage("§c[Hybrid] Falha no pathfinding direto, continuando segmentado...");

            // Se chegou muito próximo, aceitar como sucesso
            if (finalDistance <= 5.0) {
                Logger.sendMessage("§a[Hybrid] Chegou próximo o suficiente do objetivo final!");
                return completePath;
            }
        }

        if (completePath.isEmpty()) {
            Logger.sendMessage("§c[Hybrid] Falha no pathfinding segmentado, tentando pathfinding direto como fallback...");

            // Fallback: tentar pathfinding direto com muitas iterações
            List<PathNode> directPath = findPathInternal(startPos, goalPos, start, goal, MAX_ITERATIONS_LONG);
            if (directPath != null && !directPath.isEmpty()) {
                Logger.sendMessage("§a[Hybrid] Fallback direto funcionou com " + directPath.size() + " nós!");
                return directPath;
            }

            Logger.sendMessage("§c[Hybrid] Falha completa no pathfinding");
            return null;
        }

        Logger.sendMessage("§a[Hybrid] Pathfinding segmentado parcial com " + completePath.size() + " nós");
        return completePath;
    }

    /**
     * Encontra waypoint com tamanho específico
     */
    private BlockPos findWaypointWithSize(BlockPos start, BlockPos goal, int segmentSize) {
        // Calcular direção para o objetivo
        double dx = goal.getX() - start.getX();
        double dy = goal.getY() - start.getY();
        double dz = goal.getZ() - start.getZ();

        // Normalizar direção
        double distance = Math.sqrt(dx * dx + dy * dy + dz * dz);
        dx /= distance;
        dy /= distance;
        dz /= distance;

        // Waypoint na direção do objetivo
        int waypointX = start.getX() + (int)(dx * segmentSize);
        int waypointY = start.getY() + (int)(dy * segmentSize * 0.3); // Movimento vertical reduzido
        int waypointZ = start.getZ() + (int)(dz * segmentSize);

        return new BlockPos(waypointX, waypointY, waypointZ);
    }
    
    /**
     * Pathfinding direto para distâncias curtas
     */
    private List<PathNode> findDirectPath(BlockPos startPos, BlockPos goalPos, Vec3 start, Vec3 goal) {
        // Para segmentos, usar mais iterações
        double distance = calculateDistance(startPos, goalPos);
        int maxIterations = distance > 15 ? MAX_ITERATIONS_LONG : MAX_ITERATIONS_SHORT;
        Logger.sendMessage("§7[Hybrid] Usando " + maxIterations + " iterações para distância " + String.format("%.1f", distance));
        return findPathInternal(startPos, goalPos, start, goal, maxIterations);
    }
    
    /**
     * Encontra o melhor waypoint intermediário (mais conservador)
     */
    private BlockPos findBestWaypoint(BlockPos start, BlockPos goal) {
        // Calcular direção para o objetivo
        double dx = goal.getX() - start.getX();
        double dy = goal.getY() - start.getY();
        double dz = goal.getZ() - start.getZ();

        // Normalizar direção
        double distance = Math.sqrt(dx * dx + dy * dy + dz * dz);
        dx /= distance;
        dy /= distance;
        dz /= distance;

        // Usar segmento menor e mais conservador
        int segmentSize = Math.min(SEGMENT_RADIUS, (int)(distance / 2)); // Máximo metade da distância
        segmentSize = Math.max(segmentSize, 10); // Mínimo 10 blocos

        // Waypoint na direção do objetivo, mas conservador
        int waypointX = start.getX() + (int)(dx * segmentSize);
        int waypointY = start.getY() + (int)(dy * segmentSize * 0.5); // Menos movimento vertical
        int waypointZ = start.getZ() + (int)(dz * segmentSize);

        Logger.sendMessage("§7[Hybrid] Segmento calculado: " + segmentSize + " blocos");
        return new BlockPos(waypointX, waypointY, waypointZ);
    }
    
    /**
     * Calcula distância entre duas posições
     */
    private double calculateDistance(BlockPos pos1, BlockPos pos2) {
        double dx = pos2.getX() - pos1.getX();
        double dy = pos2.getY() - pos1.getY();
        double dz = pos2.getZ() - pos1.getZ();
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
    
    /**
     * Implementação interna do pathfinding A*
     */
    private List<PathNode> findPathInternal(BlockPos startPos, BlockPos goalPos, Vec3 start, Vec3 goal, int maxIterations) {
        Logger.sendMessage("§7[Hybrid] Posições validadas - Start: " + startPos + ", Goal: " + goalPos);
        
        // Estruturas do A*
        PriorityQueue<HybridNode> openSet = new PriorityQueue<>();
        Set<BlockPos> closedSet = new HashSet<>();
        Map<BlockPos, HybridNode> allNodes = new HashMap<>();
        
        // Nó inicial
        HybridNode goalNode = new HybridNode(goalPos, null, null, this);
        HybridNode startNode = new HybridNode(startPos, null, goalNode, this);
        startNode.setGCost(0);

        openSet.add(startNode);
        allNodes.put(startPos, startNode);
        
        int iterations = 0;
        HybridNode bestNode = startNode;
        long startTime = System.currentTimeMillis();
        
        while (!openSet.isEmpty() && iterations < maxIterations) {
            HybridNode current = openSet.poll();
            iterations++;

            // Atualizar melhor nó
            if (current.getHCost() < bestNode.getHCost()) {
                bestNode = current;
            }

            // Verificar se chegou ao objetivo
            if (isGoalReached(current, goalPos)) {
                Logger.sendMessage("§a[Hybrid] Caminho encontrado em " + iterations + " iterações!");
                Logger.sendMessage("§a[DEBUG] Posição atual: " + current.getPos());
                Logger.sendMessage("§a[DEBUG] Objetivo: " + goalPos);
                return reconstructPath(current);
            }

            closedSet.add(current.getPos());

            // PRIORIZAR movimento horizontal - só explorar vertical se necessário
            exploreNeighbors(current, goalNode, openSet, closedSet, allNodes, MOVEMENT_DIRECTIONS, false);

            // Só explorar movimento vertical se movimento horizontal estiver limitado
            if (shouldExploreVertical(current, goalNode)) {
                exploreNeighbors(current, goalNode, openSet, closedSet, allNodes, VERTICAL_MOVEMENTS, true);
            }

            // Debug a cada 500 iterações para detectar problemas mais cedo
            if (iterations % 500 == 0) {
                Logger.sendMessage("§7[Hybrid] Iteração " + iterations + "/" + maxIterations + ", OpenSet: " + openSet.size() + ", Melhor: " + bestNode.getPos());
            }

            // Debug crítico se openSet está ficando pequeno
            if (openSet.size() <= 5 && iterations > 100) {
                Logger.sendMessage("§c[Hybrid] AVISO: OpenSet muito pequeno (" + openSet.size() + ") na iteração " + iterations);
            }
        }
        
        long elapsed = System.currentTimeMillis() - startTime;
        Logger.sendMessage("§c[Hybrid] FALHA: Pathfinding não encontrou caminho após " + iterations + " iterações em " + elapsed + "ms");

        // Retornar melhor caminho parcial se próximo o suficiente
        if (bestNode != startNode) {
            double distanceToGoal = calculateDistance(bestNode.getPos(), goalPos);
            double originalDistance = calculateDistance(startPos, goalPos);
            double progress = (originalDistance - distanceToGoal) / originalDistance;

            Logger.sendMessage("§e[Hybrid] Melhor nó: " + bestNode.getPos() + " (distância: " + String.format("%.2f", distanceToGoal) + ")");
            Logger.sendMessage("§e[Hybrid] Progresso: " + String.format("%.1f", progress * 100) + "%");

            // NUNCA aceitar caminho parcial no pathfinding direto - deve chegar ao destino
            if (maxIterations >= MAX_ITERATIONS_LONG) {
                Logger.sendMessage("§c[Hybrid] Pathfinding direto falhou - progresso insuficiente (" + String.format("%.1f", progress * 100) + "%)");
                Logger.sendMessage("§c[Hybrid] Necessário pelo menos 80% de progresso para pathfinding direto");
                return null; // Forçar uso do pathfinding segmentado
            } else {
                // Para pathfinding segmentado, aceitar progresso menor
                if (progress > 0.05 || distanceToGoal <= 25.0 || distanceToGoal < originalDistance) {
                    Logger.sendMessage("§7[Hybrid] Retornando caminho parcial (progresso suficiente)");
                    return reconstructPath(bestNode);
                }
            }
        }
        
        Logger.sendMessage("§c[Hybrid] Nenhum caminho válido encontrado!");
        return null;
    }
    
    /**
     * Verifica se chegou ao objetivo
     */
    private boolean isGoalReached(HybridNode current, BlockPos goal) {
        return goal.getX() == current.getPos().getX() && 
               goal.getY() == current.getPos().getY() && 
               goal.getZ() == current.getPos().getZ();
    }
    
    /**
     * Verifica se deve explorar movimento vertical (inteligente)
     */
    private boolean shouldExploreVertical(HybridNode current, HybridNode goal) {
        int heightDiff = Math.abs(goal.getPos().getY() - current.getPos().getY());
        if (heightDiff < 1) return false;

        int horizontalDistance = Math.abs(goal.getPos().getX() - current.getPos().getX()) +
                                Math.abs(goal.getPos().getZ() - current.getPos().getZ());

        // Se está muito longe horizontalmente, priorizar movimento horizontal
        if (horizontalDistance > 20) return false;

        // Verificar se movimento horizontal está limitado
        int blockedDirections = 0;
        for (int[] direction : MOVEMENT_DIRECTIONS) {
            BlockPos testPos = current.getPos().add(direction[0], 0, direction[2]);
            HybridNode testNode = new HybridNode(testPos, current, goal, this);
            if (!testNode.canStandAt()) {
                blockedDirections++;
            }
        }

        // Explorar vertical se mais da metade dos movimentos estão bloqueados
        // OU se está próximo do objetivo OU se há diferença de altura significativa
        return blockedDirections >= (MOVEMENT_DIRECTIONS.length / 2) ||
               horizontalDistance <= 8 ||
               heightDiff >= 3;
    }
    
    /**
     * Explora vizinhos de um nó (SIMPLIFICADO)
     */
    private void exploreNeighbors(HybridNode current, HybridNode goalNode, PriorityQueue<HybridNode> openSet,
                                 Set<BlockPos> closedSet, Map<BlockPos, HybridNode> allNodes,
                                 int[][] directions, boolean isVertical) {

        for (int[] direction : directions) {
            BlockPos neighborPos = current.getPos().add(direction[0], direction[1], direction[2]);

            if (closedSet.contains(neighborPos)) continue;

            if (!mc.theWorld.isBlockLoaded(neighborPos)) continue;

            HybridNode neighbor = new HybridNode(neighborPos, current, goalNode, this);

            if (!neighbor.canStandAt()) continue;

            HybridNode existingNode = allNodes.get(neighborPos);
            if (existingNode != null) {
                if (neighbor.getGCost() >= existingNode.getGCost()) continue;
                openSet.remove(existingNode);
            }

            allNodes.put(neighborPos, neighbor);
            openSet.add(neighbor);
        }
    }


    
    /**
     * Verifica se um movimento é válido (baseado no Stevebot)
     */
    private boolean isValidMovement(HybridNode from, HybridNode to, boolean isVertical) {
        // Validação básica
        if (!to.canStandAt()) return false;

        int dx = Math.abs(to.getPos().getX() - from.getPos().getX());
        int dy = to.getPos().getY() - from.getPos().getY();
        int dz = Math.abs(to.getPos().getZ() - from.getPos().getZ());

        // Verificar se é movimento diagonal
        boolean isDiagonal = dx > 0 && dz > 0;

        if (dy > 0) {
            // MOVIMENTO PARA CIMA (PULO)
            if (dy > 1) return false; // Só pulos de 1 bloco

            // Verificar se há espaço para pular (3 blocos de altura)
            if (!from.canWalkThrough(from.getPos().up()) ||
                !from.canWalkThrough(from.getPos().up(2))) {
                return false;
            }

            // Para movimento diagonal, verificar se não há obstáculos nos cantos
            if (isDiagonal) {
                BlockPos corner1 = from.getPos().add(dx > 0 ? 1 : -1, 0, 0);
                BlockPos corner2 = from.getPos().add(0, 0, dz > 0 ? 1 : -1);

                if (!from.canWalkThrough(corner1) || !from.canWalkThrough(corner2)) {
                    return false;
                }
            }

            to.isJump = true;
            to.isFall = false;
            return true;

        } else if (dy < 0) {
            // MOVIMENTO PARA BAIXO (QUEDA)
            if (Math.abs(dy) > 3) return false; // Queda máxima de 3 blocos

            // Verificar se o caminho de queda está livre
            for (int i = 1; i <= Math.abs(dy); i++) {
                BlockPos fallPos = from.getPos().down(i);
                if (!from.canWalkThrough(fallPos) || !from.canWalkThrough(fallPos.up())) {
                    return false;
                }
            }

            to.isJump = false;
            to.isFall = true;
            return true;

        } else {
            // MOVIMENTO HORIZONTAL (MESMO NÍVEL Y)

            // Para movimento diagonal, verificar se não há obstáculos nos cantos
            if (isDiagonal) {
                BlockPos corner1 = from.getPos().add(dx > 0 ? 1 : -1, 0, 0);
                BlockPos corner2 = from.getPos().add(0, 0, dz > 0 ? 1 : -1);

                if (!from.canWalkThrough(corner1) || !from.canWalkThrough(corner2) ||
                    !from.canWalkThrough(corner1.up()) || !from.canWalkThrough(corner2.up())) {
                    return false;
                }
            }

            to.isJump = false;
            to.isFall = false;
            return true;
        }
    }
    
    /**
     * Reconstrói o caminho a partir do nó final
     */
    private List<PathNode> reconstructPath(HybridNode endNode) {
        List<PathNode> path = new ArrayList<>();
        HybridNode current = endNode;
        
        while (current != null) {
            PathNode.MovementType moveType = PathNode.MovementType.WALK;
            PathNode pathNode = new PathNode(current.getPosition(), current.getGCost(), current.getHCost(), null, moveType);
            
            pathNode.isJump = current.isJump();
            pathNode.isFall = current.isFall();
            
            path.add(0, pathNode);
            current = current.getParent();
        }
        
        Logger.sendMessage("§7[Hybrid] Caminho reconstruído: " + path.size() + " nós");
        return path;
    }

    /**
     * Classe interna para representar um nó no pathfinding
     */
    public static class HybridNode implements Comparable<HybridNode> {
        private final BlockPos pos;
        private final Vec3 position;
        private final HybridPathfinder pathfinder;
        private HybridNode parent;
        private double gCost;
        private double hCost;
        private boolean isJump = false;
        private boolean isFall = false;

        public HybridNode(BlockPos pos, HybridNode parent, HybridNode goal, HybridPathfinder pathfinder) {
            this.pos = pos;
            this.position = new Vec3(pos.getX() + 0.5, pos.getY(), pos.getZ() + 0.5);
            this.pathfinder = pathfinder;
            this.gCost = Double.MAX_VALUE;
            this.hCost = 0;

            if (goal != null) {
                calculateHeuristic(goal);
            }

            if (parent != null) {
                setParent(parent);
            }
        }

        public boolean canStandAt() {
            World world = Minecraft.getMinecraft().theWorld;
            if (world == null) return true; // Permissivo em caso de erro

            try {
                if (!world.isBlockLoaded(pos)) return true; // Permissivo se não carregado

                // VALIDAÇÃO EXTREMAMENTE PERMISSIVA

                // 1. Verificar se há espaço livre de 2 blocos de altura
                if (!canWalkThrough(pos) || !canWalkThrough(pos.up())) {
                    return false;
                }

                // 2. Aceitar qualquer posição que tenha espaço livre
                // Não exigir chão sólido - deixar o movimento controller lidar com isso
                return true;

            } catch (Exception e) {
                return true; // Permissivo em caso de erro
            }
        }



        public boolean canWalkThrough(BlockPos pos) {
            World world = Minecraft.getMinecraft().theWorld;
            if (world == null) return false;

            Block block = world.getBlockState(pos).getBlock();

            if (block.getMaterial() == Material.air) return true;
            if (block.getMaterial().isLiquid() || block == Blocks.fire || block == Blocks.cactus) return false;

            return !block.getMaterial().blocksMovement();
        }

        public boolean canWalkOn(BlockPos pos) {
            World world = Minecraft.getMinecraft().theWorld;
            if (world == null) return false;

            Block block = world.getBlockState(pos).getBlock();
            return block.getMaterial().blocksMovement() && !block.getMaterial().isLiquid();
        }

        private void calculateHeuristic(HybridNode goal) {
            if (goal == null) {
                this.hCost = 0;
                return;
            }

            final int gx = goal.pos.getX();
            final int gy = goal.pos.getY();
            final int gz = goal.pos.getZ();
            final int px = pos.getX();
            final int py = pos.getY();
            final int pz = pos.getZ();

            int dMax = Math.max(Math.abs(px - gx), Math.abs(pz - gz));
            int dMin = Math.min(Math.abs(px - gx), Math.abs(pz - gz));

            final double cost_mult_diagonal = COST_WALK_DIAGONAL / COST_WALK_STRAIGHT;

            // Heurística base
            double baseHeuristic = (dMin * cost_mult_diagonal + (dMax - dMin)) * COST_WALK_STRAIGHT +
                                  Math.abs(py - gy) * (py < gy ? COST_FALL : COST_JUMP);

            // Penalidade por obstáculos próximos (baseado no Stevebot)
            double obstaclePenalty = calculateObstaclePenalty(goal);

            this.hCost = baseHeuristic + obstaclePenalty;
        }

        /**
         * Calcula penalidade por obstáculos próximos (baseado no Stevebot)
         */
        private double calculateObstaclePenalty(HybridNode goal) {
            double penalty = 0;

            // Direção geral para o objetivo
            int goalDx = goal.pos.getX() - pos.getX();
            int goalDz = goal.pos.getZ() - pos.getZ();

            // Normalizar direção
            int dirX = goalDx == 0 ? 0 : (goalDx > 0 ? 1 : -1);
            int dirZ = goalDz == 0 ? 0 : (goalDz > 0 ? 1 : -1);

            // Verificar obstáculos na direção do objetivo
            if (dirX != 0) {
                BlockPos checkPos = pos.add(dirX, 0, 0);
                if (!canWalkThrough(checkPos) || !canWalkThrough(checkPos.up())) {
                    penalty += 10.0; // Penalidade alta por obstáculo direto
                }
            }

            if (dirZ != 0) {
                BlockPos checkPos = pos.add(0, 0, dirZ);
                if (!canWalkThrough(checkPos) || !canWalkThrough(checkPos.up())) {
                    penalty += 10.0; // Penalidade alta por obstáculo direto
                }
            }

            // Verificar obstáculo diagonal se necessário
            if (dirX != 0 && dirZ != 0) {
                BlockPos checkPos = pos.add(dirX, 0, dirZ);
                if (!canWalkThrough(checkPos) || !canWalkThrough(checkPos.up())) {
                    penalty += 5.0; // Penalidade moderada por obstáculo diagonal
                }
            }

            return penalty;
        }

        public void setParent(HybridNode parent) {
            this.parent = parent;

            if (parent != null) {
                int dx = Math.abs(pos.getX() - parent.pos.getX());
                int dy = pos.getY() - parent.pos.getY();
                int dz = Math.abs(pos.getZ() - parent.pos.getZ());

                double baseCost;

                if (dy > 0) {
                    // Pulo - custo moderado (desencorajar mas não proibir)
                    baseCost = COST_JUMP;
                    isJump = true;
                    isFall = false;
                } else if (dy < 0) {
                    // Queda - custo baixo (movimento natural)
                    baseCost = COST_FALL;
                    isJump = false;
                    isFall = true;
                } else {
                    // Movimento horizontal - BARATO (priorizar)
                    if (dx > 0 && dz > 0) {
                        baseCost = COST_WALK_DIAGONAL;
                    } else {
                        baseCost = COST_WALK_STRAIGHT;
                    }
                    isJump = false;
                    isFall = false;
                }

                this.gCost = parent.gCost + baseCost;
            }
        }

        @Override
        public int compareTo(HybridNode other) {
            double thisFCost = this.gCost + this.hCost;
            double otherFCost = other.gCost + other.hCost;
            return Double.compare(thisFCost, otherFCost);
        }

        // Getters
        public BlockPos getPos() { return pos; }
        public Vec3 getPosition() { return position; }
        public HybridNode getParent() { return parent; }
        public double getGCost() { return gCost; }
        public double getHCost() { return hCost; }
        public boolean isJump() { return isJump; }
        public boolean isFall() { return isFall; }
        public void setGCost(double gCost) { this.gCost = gCost; }
    }
}
