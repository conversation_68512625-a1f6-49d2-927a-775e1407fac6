package com.rato.addons.pathfinding.enhanced;

import com.rato.addons.pathfinding.professional.PathNode;
import com.rato.addons.util.Logger;
import net.minecraft.client.Minecraft;
import net.minecraft.client.settings.KeyBinding;
import net.minecraft.util.Vec3;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;
import org.lwjgl.input.Keyboard;

import java.util.ArrayList;
import java.util.List;

/**
 * Sistema de movimento melhorado baseado no Walker do Mucifex
 * Implementa movimento inteligente com detecção de direção baseada em ângulos
 */
public class EnhancedWalker {
    
    private static EnhancedWalker instance;
    private final Minecraft mc = Minecraft.getMinecraft();
    private final EnhancedLookManager lookManager = EnhancedLookManager.getInstance();
    
    // Estado do walker
    private boolean isActive = false;
    private boolean debugMode = false;
    private boolean registered = false;
    
    // Caminho e navegação
    private List<PathNode> path = new ArrayList<>();
    private int currentNodeIndex = 0;
    private PathNode currentTarget;
    
    // Configurações de movimento
    private double reachDistance = 1.2;
    private boolean sprintEnabled = true;
    private boolean jumpEnabled = true;

    // Controle de pulo
    private boolean isJumping = false;
    private boolean wasOnGround = true;
    private long lastJumpTime = 0;
    private static final long JUMP_COOLDOWN = 500; // 500ms entre pulos

    // Predição de movimento (baseado no Mucifex)
    private static final double MOTION_DECAY = 0.54600006; // Valor do Mucifex
    private static final int PREDICTION_TICKS = 12; // Ticks de predição
    
    // Callbacks
    private Runnable onDestinationReached;
    private Runnable onMovementFailed;
    
    public EnhancedWalker() {
        instance = this;
    }
    
    public static EnhancedWalker getInstance() {
        if (instance == null) {
            instance = new EnhancedWalker();
        }
        return instance;
    }
    
    /**
     * Inicia movimento ao longo do caminho
     */
    public void startWalking(List<PathNode> path) {
        if (path == null || path.isEmpty()) {
            if (debugMode) {
                Logger.sendMessage("§c[EnhancedWalker] Caminho vazio ou nulo");
            }
            return;
        }
        
        this.path = new ArrayList<>(path);
        this.currentNodeIndex = 0;
        this.currentTarget = null;
        this.isActive = true;
        
        if (!registered) {
            MinecraftForge.EVENT_BUS.register(this);
            registered = true;
        }
        
        if (debugMode) {
            Logger.sendMessage("§a[EnhancedWalker] Iniciando movimento: " + path.size() + " nós");
        }
    }
    
    /**
     * Para o movimento atual
     */
    public void stopWalking() {
        isActive = false;
        currentTarget = null;
        path.clear();
        currentNodeIndex = 0;

        // Resetar estado de pulo
        isJumping = false;
        wasOnGround = true;
        lastJumpTime = 0;

        releaseAllKeys();
        lookManager.cancel();

        if (registered) {
            MinecraftForge.EVENT_BUS.unregister(this);
            registered = false;
        }

        if (debugMode) {
            Logger.sendMessage("§7[EnhancedWalker] Movimento parado");
        }
    }
    
    /**
     * Evento principal de movimento (baseado no Mucifex)
     */
    @SubscribeEvent
    public void onClientTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.START || !isActive || mc.thePlayer == null) {
            return;
        }
        
        try {
            // Verificar se o caminho ainda é válido
            if (path.isEmpty()) {
                completeMovement();
                return;
            }
            
            // Verificar se o player pulou nós (baseado no Mucifex)
            PathNode skippedTarget = checkForSkippedNodes();
            if (skippedTarget != null) {
                currentTarget = skippedTarget;
            }
            
            // Obter alvo atual
            if (currentTarget == null) {
                if (currentNodeIndex >= path.size()) {
                    completeMovement();
                    return;
                }
                currentTarget = path.get(currentNodeIndex);
            }
            
            // Verificar se chegou ao nó atual
            if (hasReachedCurrentNode()) {
                advanceToNextNode();
                return;
            }
            
            // Executar movimento
            executeMovement();
            
        } catch (Exception e) {
            if (debugMode) {
                Logger.sendMessage("§c[EnhancedWalker] Erro: " + e.getMessage());
            }
            handleMovementError();
        }
    }
    
    /**
     * Verifica se o player pulou nós (baseado no Mucifex)
     */
    private PathNode checkForSkippedNodes() {
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        
        for (int i = currentNodeIndex; i < Math.min(currentNodeIndex + 5, path.size()); i++) {
            PathNode node = path.get(i);
            
            if (isPlayerOnNode(node, playerPos)) {
                if (debugMode) {
                    Logger.sendMessage("§e[EnhancedWalker] Player pulou para nó " + i);
                }
                
                // Limpar nós anteriores
                currentNodeIndex = i;
                
                // Para pulos e quedas, não avançar automaticamente
                if (node.movementType == PathNode.MovementType.JUMP || 
                    node.movementType == PathNode.MovementType.FALL) {
                    return node;
                }
                
                // Para outros tipos, avançar para o próximo se possível
                if (i + 1 < path.size()) {
                    currentNodeIndex = i + 1;
                    return path.get(i + 1);
                }
                
                return node;
            }
        }
        
        return null;
    }
    
    /**
     * Verifica se o player está em um nó específico
     */
    private boolean isPlayerOnNode(PathNode node, Vec3 playerPos) {
        double distance = playerPos.distanceTo(node.position);
        return distance <= reachDistance;
    }
    
    /**
     * Verifica se chegou ao nó atual
     */
    private boolean hasReachedCurrentNode() {
        if (currentTarget == null) return false;
        
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        
        // Usar predição de movimento (baseado no Mucifex)
        Vec3 predictedPos = predictPlayerPosition();
        
        double currentDistance = playerPos.distanceTo(currentTarget.position);
        double predictedDistance = predictedPos.distanceTo(currentTarget.position);
        
        // Se a predição mostra que vamos passar do alvo, considerar como alcançado
        return currentDistance <= reachDistance || predictedDistance > currentDistance;
    }
    
    /**
     * Prediz posição do player baseado no movimento atual (do Mucifex)
     */
    private Vec3 predictPlayerPosition() {
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        Vec3 motion = new Vec3(mc.thePlayer.motionX, 0, mc.thePlayer.motionZ);
        
        Vec3 predictedPos = playerPos;
        Vec3 currentMotion = motion;
        
        for (int i = 0; i < PREDICTION_TICKS; i++) {
            predictedPos = predictedPos.add(currentMotion);
            currentMotion = multiplyVec3(currentMotion, MOTION_DECAY);
        }
        
        return predictedPos;
    }
    
    /**
     * Multiplica Vec3 por escalar
     */
    private Vec3 multiplyVec3(Vec3 vec, double multiplier) {
        return new Vec3(vec.xCoord * multiplier, vec.yCoord * multiplier, vec.zCoord * multiplier);
    }
    
    /**
     * Avança para o próximo nó
     */
    private void advanceToNextNode() {
        currentNodeIndex++;
        
        if (currentNodeIndex >= path.size()) {
            completeMovement();
            return;
        }
        
        currentTarget = path.get(currentNodeIndex);
        
        if (debugMode) {
            Logger.sendMessage("§7[EnhancedWalker] Avançando para nó " + currentNodeIndex + "/" + path.size());
        }
    }
    
    /**
     * Executa movimento em direção ao alvo atual
     */
    private void executeMovement() {
        if (currentTarget == null) return;

        Vec3 targetPos = currentTarget.position;

        // Calcular ângulo para o alvo
        Vec3 playerPos = mc.thePlayer.getPositionEyes(1.0f);
        Vec3 direction = targetPos.subtract(playerPos).normalize();
        double targetYaw = Math.toDegrees(Math.atan2(-direction.xCoord, direction.zCoord));

        // Configurar rotação
        double targetPitch = currentTarget.movementType == PathNode.MovementType.JUMP ? -10 : 10;
        lookManager.setTarget(targetYaw, targetPitch);

        // Executar teclas baseado no ângulo (sistema do Mucifex)
        pressKeysBasedOnAngle(targetYaw);

        // Ativar sprint se habilitado
        if (sprintEnabled) {
            KeyBinding.setKeyBindState(Keyboard.KEY_LCONTROL, true);
        }

        // Controlar pulo de forma inteligente
        handleJumpControl();
    }
    
    /**
     * Pressiona teclas baseado no ângulo (sistema do Mucifex)
     */
    private void pressKeysBasedOnAngle(double targetYaw) {
        double currentYaw = mc.thePlayer.rotationYaw;
        double difference = normalizeAngle(targetYaw - currentYaw);
        
        // Liberar todas as teclas primeiro
        releaseMovementKeys();
        
        // Sistema de direção baseado em ângulos (do Mucifex)
        if (difference > -22.5 && difference <= 22.5) {
            // Frente
            KeyBinding.setKeyBindState(Keyboard.KEY_W, true);
        } else if (difference > 22.5 && difference <= 67.5) {
            // Frente + Esquerda
            KeyBinding.setKeyBindState(Keyboard.KEY_W, true);
            KeyBinding.setKeyBindState(Keyboard.KEY_D, true);
        } else if (difference > 67.5 && difference <= 112.5) {
            // Esquerda
            KeyBinding.setKeyBindState(Keyboard.KEY_D, true);
        } else if (difference > 112.5 && difference <= 157.5) {
            // Trás + Esquerda
            KeyBinding.setKeyBindState(Keyboard.KEY_S, true);
            KeyBinding.setKeyBindState(Keyboard.KEY_D, true);
        } else if (difference > 157.5 || difference <= -157.5) {
            // Trás
            KeyBinding.setKeyBindState(Keyboard.KEY_S, true);
        } else if (difference > -67.5 && difference <= -22.5) {
            // Frente + Direita
            KeyBinding.setKeyBindState(Keyboard.KEY_W, true);
            KeyBinding.setKeyBindState(Keyboard.KEY_A, true);
        } else if (difference > -112.5 && difference <= -67.5) {
            // Direita
            KeyBinding.setKeyBindState(Keyboard.KEY_A, true);
        } else if (difference > -157.5 && difference <= -112.5) {
            // Trás + Direita
            KeyBinding.setKeyBindState(Keyboard.KEY_S, true);
            KeyBinding.setKeyBindState(Keyboard.KEY_A, true);
        }
    }
    
    /**
     * Controla o pulo de forma inteligente
     */
    private void handleJumpControl() {
        boolean currentlyOnGround = mc.thePlayer.onGround;
        long currentTime = System.currentTimeMillis();

        // Detectar quando o player sai do chão (completou o pulo)
        if (isJumping && wasOnGround && !currentlyOnGround) {
            // Player saiu do chão, liberar tecla de pulo
            KeyBinding.setKeyBindState(Keyboard.KEY_SPACE, false);
            isJumping = false;
            if (debugMode) {
                Logger.sendMessage("§7[EnhancedWalker] Pulo executado, liberando tecla");
            }
        }

        // Verificar se deve iniciar um novo pulo
        if (jumpEnabled && shouldJump() && !isJumping && currentlyOnGround &&
            (currentTime - lastJumpTime) > JUMP_COOLDOWN) {

            KeyBinding.setKeyBindState(Keyboard.KEY_SPACE, true);
            isJumping = true;
            lastJumpTime = currentTime;

            if (debugMode) {
                Logger.sendMessage("§e[EnhancedWalker] Iniciando pulo");
            }
        }

        // Atualizar estado anterior
        wasOnGround = currentlyOnGround;
    }

    /**
     * Verifica se deve pular
     */
    private boolean shouldJump() {
        if (currentTarget == null) return false;

        // Pular se o nó atual requer pulo
        if (currentTarget.movementType == PathNode.MovementType.JUMP) {
            return true;
        }

        // Pular se há obstáculo na frente
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        Vec3 direction = currentTarget.position.subtract(playerPos).normalize();
        Vec3 checkPos = playerPos.add(direction);

        return !mc.theWorld.getBlockState(new net.minecraft.util.BlockPos(checkPos)).getBlock().getMaterial().isReplaceable();
    }
    
    /**
     * Normaliza ângulo para -180 a 180
     */
    private double normalizeAngle(double angle) {
        while (angle > 180) angle -= 360;
        while (angle < -180) angle += 360;
        return angle;
    }
    
    /**
     * Libera teclas de movimento
     */
    private void releaseMovementKeys() {
        KeyBinding.setKeyBindState(Keyboard.KEY_W, false);
        KeyBinding.setKeyBindState(Keyboard.KEY_A, false);
        KeyBinding.setKeyBindState(Keyboard.KEY_S, false);
        KeyBinding.setKeyBindState(Keyboard.KEY_D, false);
    }
    
    /**
     * Libera todas as teclas
     */
    private void releaseAllKeys() {
        releaseMovementKeys();
        KeyBinding.setKeyBindState(Keyboard.KEY_SPACE, false);
        KeyBinding.setKeyBindState(Keyboard.KEY_LCONTROL, false);
    }
    
    /**
     * Completa movimento com sucesso
     */
    private void completeMovement() {
        if (debugMode) {
            Logger.sendMessage("§a[EnhancedWalker] Destino alcançado!");
        }
        
        stopWalking();
        
        if (onDestinationReached != null) {
            onDestinationReached.run();
        }
    }
    
    /**
     * Lida com erro de movimento
     */
    private void handleMovementError() {
        if (debugMode) {
            Logger.sendMessage("§c[EnhancedWalker] Erro de movimento detectado");
        }
        
        stopWalking();
        
        if (onMovementFailed != null) {
            onMovementFailed.run();
        }
    }
    
    // Getters e Setters
    public boolean isActive() { return isActive; }
    public void setDebugMode(boolean debug) { this.debugMode = debug; }
    public boolean isDebugMode() { return debugMode; }
    
    public double getReachDistance() { return reachDistance; }
    public void setReachDistance(double distance) { this.reachDistance = Math.max(0.5, distance); }
    
    public boolean isSprintEnabled() { return sprintEnabled; }
    public void setSprintEnabled(boolean enabled) { this.sprintEnabled = enabled; }
    
    public boolean isJumpEnabled() { return jumpEnabled; }
    public void setJumpEnabled(boolean enabled) { this.jumpEnabled = enabled; }
    
    public void setOnDestinationReached(Runnable callback) { this.onDestinationReached = callback; }
    public void setOnMovementFailed(Runnable callback) { this.onMovementFailed = callback; }
    
    /**
     * Obtém progresso atual
     */
    public float getProgress() {
        if (path.isEmpty()) return 1.0f;
        return (float) currentNodeIndex / path.size();
    }
    
    /**
     * Obtém informações de debug
     */
    public String getDebugInfo() {
        if (!isActive) return "Inativo";
        
        StringBuilder info = new StringBuilder();
        info.append("Nó: ").append(currentNodeIndex).append("/").append(path.size());
        
        if (currentTarget != null) {
            Vec3 playerPos = mc.thePlayer.getPositionVector();
            double distance = playerPos.distanceTo(currentTarget.position);
            info.append(", Distância: ").append(String.format("%.1f", distance));
            info.append(", Tipo: ").append(currentTarget.movementType);
        }
        
        return info.toString();
    }
    
    /**
     * Limpa recursos
     */
    public void shutdown() {
        stopWalking();
        lookManager.shutdown();
    }
}
