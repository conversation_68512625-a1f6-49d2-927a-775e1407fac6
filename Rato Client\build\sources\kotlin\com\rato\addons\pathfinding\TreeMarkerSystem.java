package com.rato.addons.pathfinding;

import com.rato.addons.util.Logger;
import net.minecraft.client.Minecraft;
import net.minecraft.init.Blocks;
import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;
import net.minecraft.block.Block;

import java.util.*;

/**
 * Sistema de marcação manual de árvores para pathfinding otimizado
 * Permite marcar árvores específicas e encontrar as melhores posições para quebra
 */
public class TreeMarkerSystem {
    
    private static final Minecraft mc = Minecraft.getMinecraft();
    private static TreeMarkerSystem instance;
    
    // Árvores marcadas por região
    private final Map<String, List<MarkedTree>> markedTreesByRegion = new HashMap<>();
    private String currentRegion = "default";
    
    // Configurações
    private static final double MAX_BREAKING_DISTANCE = 4.5;
    private static final int TREE_SCAN_RADIUS = 3;
    
    public static TreeMarkerSystem getInstance() {
        if (instance == null) {
            instance = new TreeMarkerSystem();
        }
        return instance;
    }
    
    /**
     * Classe para representar uma árvore marcada
     */
    public static class MarkedTree {
        public final BlockPos treeBase;
        public final List<BlockPos> logBlocks;
        public final Vec3 optimalBreakingPosition;
        public final BlockPos targetLogBlock;
        public final double distanceFromPlayer;
        public final long markedTime;
        
        public MarkedTree(BlockPos treeBase, List<BlockPos> logBlocks, Vec3 optimalPosition, 
                         BlockPos targetLog, double distance) {
            this.treeBase = treeBase;
            this.logBlocks = new ArrayList<>(logBlocks);
            this.optimalBreakingPosition = optimalPosition;
            this.targetLogBlock = targetLog;
            this.distanceFromPlayer = distance;
            this.markedTime = System.currentTimeMillis();
        }
    }
    
    /**
     * Marca uma árvore na posição atual do cursor/crosshair
     */
    public boolean markTreeAtCrosshair() {
        if (mc.thePlayer == null || mc.objectMouseOver == null) {
            Logger.sendMessage("§cNenhum bloco selecionado!");
            return false;
        }
        
        BlockPos targetPos = mc.objectMouseOver.getBlockPos();
        return markTree(targetPos);
    }
    
    /**
     * Marca uma árvore na posição especificada
     */
    public boolean markTree(BlockPos pos) {
        if (mc.theWorld == null || mc.thePlayer == null) return false;
        
        // Verificar se é um bloco de madeira
        Block block = mc.theWorld.getBlockState(pos).getBlock();
        if (!isLogBlock(block)) {
            Logger.sendMessage("§cEste não é um bloco de madeira válido!");
            return false;
        }
        
        // Analisar a árvore completa
        TreeAnalysisResult analysis = analyzeTree(pos);
        if (analysis == null || analysis.logBlocks.isEmpty()) {
            Logger.sendMessage("§cNão foi possível analisar a árvore!");
            return false;
        }
        
        // Encontrar a melhor posição para quebrar
        OptimalBreakingPosition optimal = findOptimalBreakingPosition(analysis);
        if (optimal == null) {
            Logger.sendMessage("§cNão foi possível encontrar posição válida para quebrar esta árvore!");
            return false;
        }
        
        // Criar árvore marcada
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        double distance = playerPos.distanceTo(optimal.standingPosition);
        
        MarkedTree markedTree = new MarkedTree(
            analysis.basePosition,
            analysis.logBlocks,
            optimal.standingPosition,
            optimal.targetLogBlock,
            distance
        );
        
        // Adicionar à região atual
        markedTreesByRegion.computeIfAbsent(currentRegion, k -> new ArrayList<>()).add(markedTree);
        
        Logger.sendMessage("§a✓ Árvore marcada! Posição: " + 
            String.format("%.1f, %.1f, %.1f", optimal.standingPosition.xCoord, 
                         optimal.standingPosition.yCoord, optimal.standingPosition.zCoord) +
            " §7| Dist: " + String.format("%.1f", distance) + "m");
        
        return true;
    }
    
    /**
     * Analisa uma árvore completa a partir de um bloco de madeira
     */
    private TreeAnalysisResult analyzeTree(BlockPos startPos) {
        Set<BlockPos> visited = new HashSet<>();
        List<BlockPos> logBlocks = new ArrayList<>();
        Queue<BlockPos> toCheck = new LinkedList<>();
        
        toCheck.add(startPos);
        BlockPos basePosition = startPos;
        
        while (!toCheck.isEmpty()) {
            BlockPos current = toCheck.poll();
            if (visited.contains(current)) continue;
            visited.add(current);
            
            Block block = mc.theWorld.getBlockState(current).getBlock();
            if (!isLogBlock(block)) continue;
            
            logBlocks.add(current);
            
            // Encontrar a base da árvore (Y mais baixo)
            if (current.getY() < basePosition.getY()) {
                basePosition = current;
            }
            
            // Verificar blocos adjacentes (incluindo diagonais)
            for (int x = -1; x <= 1; x++) {
                for (int y = -1; y <= 1; y++) {
                    for (int z = -1; z <= 1; z++) {
                        if (x == 0 && y == 0 && z == 0) continue;
                        
                        BlockPos adjacent = current.add(x, y, z);
                        if (!visited.contains(adjacent) && 
                            adjacent.distanceSq(startPos) <= TREE_SCAN_RADIUS * TREE_SCAN_RADIUS) {
                            toCheck.add(adjacent);
                        }
                    }
                }
            }
        }
        
        if (logBlocks.isEmpty()) return null;
        
        return new TreeAnalysisResult(basePosition, logBlocks);
    }
    
    /**
     * Encontra a posição ótima para quebrar a árvore
     */
    private OptimalBreakingPosition findOptimalBreakingPosition(TreeAnalysisResult analysis) {
        if (mc.thePlayer == null) return null;
        
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        List<PositionCandidate> candidates = new ArrayList<>();
        
        // Para cada bloco de madeira, verificar posições ao redor
        for (BlockPos logBlock : analysis.logBlocks) {
            // Verificar posições adjacentes ao bloco de madeira
            for (int x = -2; x <= 2; x++) {
                for (int z = -2; z <= 2; z++) {
                    if (x == 0 && z == 0) continue; // Pular o próprio bloco
                    
                    BlockPos standingPos = logBlock.add(x, 0, z);
                    
                    // Verificar se é uma posição válida para ficar
                    if (isValidStandingPosition(standingPos)) {
                        Vec3 standingVec = new Vec3(standingPos.getX() + 0.5, standingPos.getY(), standingPos.getZ() + 0.5);
                        double distanceToLog = standingVec.distanceTo(new Vec3(logBlock.getX() + 0.5, logBlock.getY() + 0.5, logBlock.getZ() + 0.5));
                        
                        // Verificar se está no alcance de quebra
                        if (distanceToLog <= MAX_BREAKING_DISTANCE) {
                            // Verificar linha de visão
                            if (hasLineOfSight(standingVec, logBlock)) {
                                double distanceFromPlayer = playerPos.distanceTo(standingVec);
                                boolean isVisibleFromPlayer = hasLineOfSightFromPlayer(standingVec);
                                
                                candidates.add(new PositionCandidate(standingVec, logBlock, distanceFromPlayer, 
                                                                   distanceToLog, isVisibleFromPlayer));
                            }
                        }
                    }
                }
            }
        }
        
        if (candidates.isEmpty()) return null;
        
        // Ordenar candidatos por prioridade
        candidates.sort((a, b) -> {
            // 1. PRIORIDADE MÁXIMA: Posições visíveis ao player
            if (a.isVisibleFromPlayer != b.isVisibleFromPlayer) {
                return Boolean.compare(b.isVisibleFromPlayer, a.isVisibleFromPlayer);
            }
            
            // 2. Menor distância do player
            if (Math.abs(a.distanceFromPlayer - b.distanceFromPlayer) > 0.5) {
                return Double.compare(a.distanceFromPlayer, b.distanceFromPlayer);
            }
            
            // 3. Menor distância do bloco alvo
            return Double.compare(a.distanceToTarget, b.distanceToTarget);
        });
        
        PositionCandidate best = candidates.get(0);
        return new OptimalBreakingPosition(best.standingPosition, best.targetLogBlock);
    }
    
    /**
     * Verifica se um bloco é de madeira
     */
    private boolean isLogBlock(Block block) {
        return block == Blocks.log || block == Blocks.log2;
    }
    
    /**
     * Verifica se uma posição é válida para o player ficar
     */
    private boolean isValidStandingPosition(BlockPos pos) {
        if (mc.theWorld == null) return false;
        
        Block blockAtPos = mc.theWorld.getBlockState(pos).getBlock();
        Block blockAbove = mc.theWorld.getBlockState(pos.up()).getBlock();
        Block blockBelow = mc.theWorld.getBlockState(pos.down()).getBlock();
        
        // Deve ter ar nos 2 blocos de altura e chão sólido embaixo
        boolean hasSpace = (blockAtPos == Blocks.air || !blockAtPos.isFullBlock()) &&
                          (blockAbove == Blocks.air || !blockAbove.isFullBlock());
        
        boolean hasSolidGround = blockBelow.isFullBlock() && 
                               blockBelow != Blocks.water && 
                               blockBelow != Blocks.flowing_water;
        
        return hasSpace && hasSolidGround;
    }
    
    /**
     * Verifica linha de visão entre posição e bloco alvo
     */
    private boolean hasLineOfSight(Vec3 from, BlockPos targetBlock) {
        Vec3 to = new Vec3(targetBlock.getX() + 0.5, targetBlock.getY() + 0.5, targetBlock.getZ() + 0.5);
        Vec3 eyeLevel = new Vec3(from.xCoord, from.yCoord + 1.6, from.zCoord);
        
        return hasLineOfSightBetweenPoints(eyeLevel, to, targetBlock);
    }
    
    /**
     * Verifica se o player tem linha de visão para uma posição
     */
    private boolean hasLineOfSightFromPlayer(Vec3 targetPos) {
        if (mc.thePlayer == null) return false;
        
        Vec3 playerEyePos = mc.thePlayer.getPositionEyes(1.0f);
        Vec3 targetEyeLevel = new Vec3(targetPos.xCoord, targetPos.yCoord + 1.6, targetPos.zCoord);
        
        return hasLineOfSightBetweenPoints(playerEyePos, targetEyeLevel, null);
    }
    
    /**
     * Função auxiliar para verificar linha de visão entre dois pontos
     */
    private boolean hasLineOfSightBetweenPoints(Vec3 from, Vec3 to, BlockPos ignoreBlock) {
        Vec3 direction = to.subtract(from);
        double rayDistance = direction.lengthVector();
        int steps = Math.max(1, (int)(rayDistance * 2));
        
        for (int i = 1; i < steps; i++) {
            double t = (double) i / steps;
            Vec3 scaledDirection = new Vec3(direction.xCoord * t, direction.yCoord * t, direction.zCoord * t);
            Vec3 currentPos = from.add(scaledDirection);
            BlockPos checkPos = new BlockPos(currentPos);
            
            if (ignoreBlock != null && checkPos.equals(ignoreBlock)) continue;
            
            Block block = mc.theWorld.getBlockState(checkPos).getBlock();
            if (block != Blocks.air && block.isFullBlock()) {
                return false;
            }
        }
        
        return true;
    }
    
    // ====================== CLASSES AUXILIARES ======================
    
    private static class TreeAnalysisResult {
        public final BlockPos basePosition;
        public final List<BlockPos> logBlocks;
        
        public TreeAnalysisResult(BlockPos basePosition, List<BlockPos> logBlocks) {
            this.basePosition = basePosition;
            this.logBlocks = logBlocks;
        }
    }
    
    private static class OptimalBreakingPosition {
        public final Vec3 standingPosition;
        public final BlockPos targetLogBlock;
        
        public OptimalBreakingPosition(Vec3 standingPosition, BlockPos targetLogBlock) {
            this.standingPosition = standingPosition;
            this.targetLogBlock = targetLogBlock;
        }
    }
    
    private static class PositionCandidate {
        public final Vec3 standingPosition;
        public final BlockPos targetLogBlock;
        public final double distanceFromPlayer;
        public final double distanceToTarget;
        public final boolean isVisibleFromPlayer;
        
        public PositionCandidate(Vec3 standingPosition, BlockPos targetLogBlock, double distanceFromPlayer,
                               double distanceToTarget, boolean isVisibleFromPlayer) {
            this.standingPosition = standingPosition;
            this.targetLogBlock = targetLogBlock;
            this.distanceFromPlayer = distanceFromPlayer;
            this.distanceToTarget = distanceToTarget;
            this.isVisibleFromPlayer = isVisibleFromPlayer;
        }
    }
    
    // ====================== GETTERS E MANAGEMENT ======================
    
    /**
     * Obtém todas as árvores marcadas na região atual
     */
    public List<MarkedTree> getMarkedTrees() {
        return markedTreesByRegion.getOrDefault(currentRegion, new ArrayList<>());
    }
    
    /**
     * Obtém a árvore marcada mais próxima do player
     */
    public MarkedTree getNearestMarkedTree() {
        if (mc.thePlayer == null) return null;
        
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        return getMarkedTrees().stream()
            .min(Comparator.comparingDouble(tree -> 
                playerPos.distanceTo(tree.optimalBreakingPosition)))
            .orElse(null);
    }
    
    /**
     * Remove uma árvore marcada
     */
    public boolean removeMarkedTree(MarkedTree tree) {
        List<MarkedTree> trees = markedTreesByRegion.get(currentRegion);
        if (trees != null) {
            boolean removed = trees.remove(tree);
            if (removed) {
                Logger.sendMessage("§7Árvore removida da lista");
            }
            return removed;
        }
        return false;
    }
    
    /**
     * Limpa todas as árvores marcadas na região atual
     */
    public void clearMarkedTrees() {
        markedTreesByRegion.put(currentRegion, new ArrayList<>());
        Logger.sendMessage("§7Todas as árvores marcadas foram removidas");
    }
    
    /**
     * Define a região atual
     */
    public void setCurrentRegion(String region) {
        this.currentRegion = region;
        Logger.sendMessage("§7Região alterada para: " + region);
    }
    
    /**
     * Obtém a região atual
     */
    public String getCurrentRegion() {
        return currentRegion;
    }
    
    /**
     * Obtém estatísticas das árvores marcadas
     */
    public String getStats() {
        int totalTrees = getMarkedTrees().size();
        return String.format("§7Região: %s | Árvores: %d", currentRegion, totalTrees);
    }
}
