package com.rato.addons.visuals.nametags;

import com.rato.addons.config.RatoAddonsConfigSimple;
import com.rato.addons.visuals.font.LiquidTextRenderer;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.FontRenderer;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.monster.EntityZombie;
import net.minecraft.entity.player.EntityPlayer;
import org.lwjgl.opengl.GL11;

import java.awt.*;
import java.util.List;

/**
 * Renderizador de Nametags baseado no LiquidBounce
 * Mostra informações customizadas sobre entidades
 */
public class NametagRenderer {

    private static final Minecraft mc = Minecraft.getMinecraft();

    // Fonte customizada estilo LiquidBounce
    private static final LiquidTextRenderer liquidFont = LiquidTextRenderer.getFont("Segoe UI", Font.PLAIN, 16);

    /**
     * Renderiza nametags para todas as entidades
     */
    public void render(float partialTicks) {
        if (mc.theWorld == null || mc.thePlayer == null)
            return;

        List<Entity> entities = mc.theWorld.loadedEntityList;

        for (Entity entity : entities) {
            if (!shouldRenderNametag(entity))
                continue;

            // Calcular posição interpolada
            double x = entity.lastTickPosX + (entity.posX - entity.lastTickPosX) * partialTicks;
            double y = entity.lastTickPosY + (entity.posY - entity.lastTickPosY) * partialTicks;
            double z = entity.lastTickPosZ + (entity.posZ - entity.lastTickPosZ) * partialTicks;

            // Ajustar para posição relativa da câmera
            double renderX = x - mc.getRenderManager().viewerPosX;
            double renderY = y - mc.getRenderManager().viewerPosY;
            double renderZ = z - mc.getRenderManager().viewerPosZ;

            // Renderizar nametag customizado
            renderCustomNametag(entity, renderX, renderY, renderZ);
        }
    }

    /**
     * Verifica se deve renderizar nametag para a entidade
     */
    private boolean shouldRenderNametag(Entity entity) {
        if (entity == mc.thePlayer)
            return false;
        if (entity.isDead)
            return false;

        // Verificar tipos habilitados
        if (entity instanceof EntityZombie && RatoAddonsConfigSimple.visualsNametagsZombies) {
            return true;
        }

        if (entity instanceof EntityPlayer && RatoAddonsConfigSimple.visualsNametagsPlayers) {
            return true;
        }

        return false;
    }

    /**
     * Renderiza nametag customizado
     */
    private void renderCustomNametag(Entity entity, double x, double y, double z) {
        // Usar FontRenderer padrão do Minecraft

        // Construir texto do nametag
        StringBuilder nametagText = new StringBuilder();

        // Nome da entidade
        String entityName = getEntityName(entity);
        nametagText.append("§f").append(entityName);

        // Informações de saúde
        if (RatoAddonsConfigSimple.visualsNametagsHealth && entity instanceof EntityLivingBase) {
            EntityLivingBase livingEntity = (EntityLivingBase) entity;
            float health = livingEntity.getHealth();
            float maxHealth = livingEntity.getMaxHealth();
            String healthColor = getHealthColor(health, maxHealth);
            nametagText.append(" ").append(healthColor).append(String.format("%.1f", health));
        }

        // Informações de distância
        if (RatoAddonsConfigSimple.visualsNametagsDistance) {
            double distance = mc.thePlayer.getDistanceToEntity(entity);
            nametagText.append(" §7[").append(String.format("%.1f", distance)).append("m]");
        }

        String finalText = nametagText.toString();

        // Configurar renderização
        setupNametagRender(x, y + entity.height + 0.5, z);

        // Calcular dimensões do texto usando fonte customizada
        int textWidth = liquidFont.getStringWidth(finalText);
        int textHeight = liquidFont.getHeight();

        // Renderizar fundo
        if (RatoAddonsConfigSimple.visualsNametagsBackground) {
            drawNametagBackground(textWidth, textHeight);
        }

        // Renderizar texto com fonte customizada e suporte a cores do Minecraft
        liquidFont.drawString(finalText, -textWidth / 2f, -textHeight / 2f, 0xFFFFFFFF, true);

        // Restaurar estado
        restoreNametagRender();
    }

    /**
     * Obtém o nome da entidade
     */
    private String getEntityName(Entity entity) {
        if (entity instanceof EntityPlayer) {
            return ((EntityPlayer) entity).getName();
        } else if (entity instanceof EntityZombie) {
            return "Zombie";
        }

        return entity.getName();
    }

    /**
     * Obtém a cor baseada na saúde
     */
    private String getHealthColor(float health, float maxHealth) {
        float healthPercentage = health / maxHealth;

        if (healthPercentage > 0.75f) {
            return "§a"; // Verde
        } else if (healthPercentage > 0.5f) {
            return "§e"; // Amarelo
        } else if (healthPercentage > 0.25f) {
            return "§6"; // Laranja
        } else {
            return "§c"; // Vermelho
        }
    }

    /**
     * Configura renderização do nametag
     */
    private void setupNametagRender(double x, double y, double z) {
        GlStateManager.pushMatrix();
        GlStateManager.translate(x, y, z);
        GlStateManager.rotate(-mc.getRenderManager().playerViewY, 0.0F, 1.0F, 0.0F);
        GlStateManager.rotate(mc.getRenderManager().playerViewX, 1.0F, 0.0F, 0.0F);

        // Escala menor e baseada na distância para melhor visibilidade
        double distance = Math.sqrt(x * x + y * y + z * z);
        float baseScale = RatoAddonsConfigSimple.visualsNametagsScale * 0.5f; // Reduzir pela metade
        float distanceScale = Math.max(0.3f, Math.min(1.0f, (float) (distance / 20.0))); // Escala baseada na distância
        float finalScale = baseScale * distanceScale;

        GlStateManager.scale(-finalScale, -finalScale, finalScale);

        // Configuração para não interferir com a mão do player
        GlStateManager.disableLighting();
        GlStateManager.enableBlend();
        GlStateManager.blendFunc(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA);

        // CORREÇÃO: Configurar depth test para que a mão do player apareça na frente
        GlStateManager.disableDepth(); // Desabilitar depth test para nametags
        GlStateManager.depthMask(false);
    }

    /**
     * Desenha fundo do nametag
     */
    private void drawNametagBackground(int textWidth, int textHeight) {
        int padding = 2;
        int bgWidth = textWidth + padding * 2;
        int bgHeight = textHeight + padding * 2;

        // Cor do fundo
        Color bgColor = new Color(0, 0, 0, 100);

        GlStateManager.disableTexture2D();
        GlStateManager.color(bgColor.getRed() / 255f, bgColor.getGreen() / 255f,
                bgColor.getBlue() / 255f, bgColor.getAlpha() / 255f);

        GL11.glBegin(GL11.GL_QUADS);
        GL11.glVertex2f(-bgWidth / 2f, -bgHeight / 2f);
        GL11.glVertex2f(bgWidth / 2f, -bgHeight / 2f);
        GL11.glVertex2f(bgWidth / 2f, bgHeight / 2f);
        GL11.glVertex2f(-bgWidth / 2f, bgHeight / 2f);
        GL11.glEnd();

        GlStateManager.enableTexture2D();
    }

    /**
     * Restaura estado da renderização
     */
    private void restoreNametagRender() {
        // CORREÇÃO: Restaurar estado corretamente para que a mão do player apareça
        GlStateManager.enableDepth();
        GlStateManager.depthMask(true);
        GlStateManager.enableLighting();
        GlStateManager.disableBlend();
        GlStateManager.popMatrix();
    }
}
