package com.rato.addons.event;

import com.rato.addons.failsafe.FailsafeManager;
import net.minecraft.network.Packet;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.network.FMLNetworkEvent;

public class PacketHandler {
    
    @SubscribeEvent
    public void onPacketReceived(FMLNetworkEvent.ClientCustomPacketEvent event) {
        // Este evento pode não capturar todos os pacotes
        // Vamos usar uma abordagem diferente
    }
    
    // Método para ser chamado quando um pacote é recebido
    public static void handleIncomingPacket(Packet packet) {
        if (packet == null) return;
        
        // Enviar para todos os failsafes
        FailsafeManager manager = FailsafeManager.getInstance();
        if (manager != null) {
            // Processar packet em cada failsafe
            manager.getFailsafes().forEach(failsafe -> {
                try {
                    failsafe.onReceivedPacketDetection(packet);
                } catch (Exception e) {
                    // Ignorar erros para não quebrar o jogo
                }
            });
        }
    }
}
