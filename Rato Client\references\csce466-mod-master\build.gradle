import org.gradle.internal.os.OperatingSystem

buildscript {
	repositories {
		maven { url = 'https://files.minecraftforge.net/maven' }
		maven { url = 'https://repo.spongepowered.org/maven' }
		mavenCentral()
	}
	dependencies {
		classpath group: 'net.minecraftforge.gradle', name: 'ForgeGradle', version: '5.1.+', changing: true
		classpath 'org.spongepowered:mixingradle:0.7-SNAPSHOT'
	}
}

plugins {
	id 'eclipse'
	id 'maven-publish'
	id 'org.jetbrains.kotlin.jvm' version '1.8.0'
	id 'org.jetbrains.kotlin.plugin.serialization' version '1.8.0'
	id 'net.minecraftforge.gradle' version '5.1.+'
}
apply plugin: 'org.spongepowered.mixin'

version = '1.0'
group = 'edu.unl.csce466' // http://maven.apache.org/guides/mini/guide-naming-conventions.html
archivesBaseName = 'csce466-mod'

// Mojang ships Java 17 to end users in 1.18+, so your mod should target Java 17.
java.toolchain.languageVersion = JavaLanguageVersion.of(17)

println "Java: ${System.getProperty 'java.version'}, JVM: ${System.getProperty 'java.vm.version'} (${System.getProperty 'java.vendor'}), Arch: ${System.getProperty 'os.arch'}"

jarJar.enable()

tasks.named('jarJar') {
	// ...
}

minecraft {
	// The mappings can be changed at any time and must be in the following format.
	// Channel:   Version:
	// official   MCVersion             Official field/method names from Mojang mapping files
	// parchment  YYYY.MM.DD-MCVersion  Open community-sourced parameter names and javadocs layered on top of official
	//
	// You must be aware of the Mojang license when using the 'official' or 'parchment' mappings.
	// See more information here: https://github.com/MinecraftForge/MCPConfig/blob/master/Mojang.md
	//
	// Parchment is an unofficial project maintained by ParchmentMC, separate from MinecraftForge
	// Additional setup is needed to use their mappings: https://parchmentmc.org/docs/getting-started
	//
	// Use non-default mappings at your own risk. They may not always work.
	// Simply re-run your setup task after changing the mappings to update your workspace.
	mappings channel: 'official', version: '1.19.3'

	// accessTransformer = file('src/main/resources/META-INF/accesstransformer.cfg') // Currently, this location cannot be changed from the default.

	// Default run configurations.
	// These can be tweaked, removed, or duplicated as needed.
	runs {
		client {
			workingDirectory project.file('run')

			// Recommended logging data for a userdev environment
			// The markers can be added/remove as needed separated by commas.
			// "SCAN": For mods scan.
			// "REGISTRIES": For firing of registry events.
			// "REGISTRYDUMP": For getting the contents of all registries.
			property 'forge.logging.markers', 'REGISTRIES'

			// Recommended logging level for the console
			// You can set various levels here.
			// Please read: https://stackoverflow.com/questions/2031163/when-to-use-the-different-log-levels
			property 'forge.logging.console.level', 'debug'

			// Comma-separated list of namespaces to load gametests from. Empty = all namespaces.
			property 'forge.enabledGameTestNamespaces', 'examplemod'

			mods {
				examplemod {
					source sourceSets.main
				}
			}
		}

		server {
			workingDirectory project.file('run')

			property 'forge.logging.markers', 'REGISTRIES'

			property 'forge.logging.console.level', 'debug'

			property 'forge.enabledGameTestNamespaces', 'examplemod'

			mods {
				examplemod {
					source sourceSets.main
				}
			}
		}

		// This run config launches GameTestServer and runs all registered gametests, then exits.
		// By default, the server will crash when no gametests are provided.
		// The gametest system is also enabled by default for other run configs under the /test command.
		gameTestServer {
			workingDirectory project.file('run')

			property 'forge.logging.markers', 'REGISTRIES'

			property 'forge.logging.console.level', 'debug'

			property 'forge.enabledGameTestNamespaces', 'examplemod'

			mods {
				examplemod {
					source sourceSets.main
				}
			}
		}

		data {
			workingDirectory project.file('run')

			property 'forge.logging.markers', 'REGISTRIES'

			property 'forge.logging.console.level', 'debug'

			// Specify the modid for data generation, where to output the resulting resource, and where to look for existing resources.
			args '--mod', 'examplemod', '--all', '--output', file('src/generated/resources/'), '--existing', file('src/main/resources/')

			mods {
				examplemod {
					source sourceSets.main
				}
			}
		}
	}
}

// Include resources generated by data generators.
sourceSets.main.resources { srcDir 'src/generated/resources' }

repositories {
	// Put repositories for dependencies here
	// ForgeGradle automatically adds the Forge maven and Maven Central for you

	// If you have mod jar dependencies in ./libs, you can declare them as a repository like so:
	// flatDir {
	//     dir 'libs'
	// }
	mavenCentral()
	maven { url "https://raw.githubusercontent.com/kotlin-graphics/mary/master" }
	maven { url "https://dl.bintray.com/kotlin/kotlin-dev" }
	maven { url "https://oss.sonatype.org/content/repositories/snapshots/" }
	maven { url "https://thedarkcolour.github.io/KotlinForForge/" }
	maven { url 'https://jitpack.io' }
	maven { url = 'https://repo.spongepowered.org/repository/maven-public/' }
}

ext {
	lwjglVersion = '3.3.1'
	imguiVersion = '1.86.10'
}

// minecraft.runs.all {
// 	// Set the minecraft_classpath token to the paths of all jars in the library configuration
// 	// This is added with the actual Minecraft classpath to get the real classpath information later on
// 	lazyToken('minecraft_classpath') {
// 		configurations.library.copyRecursive().resolve().collect { it.absolutePath }.join(File.pathSeparator)
// 	}
// }

dependencies {
	// Specify the version of Minecraft to use. If this is any group other than 'net.minecraft', it is assumed
	// that the dep is a ForgeGradle 'patcher' dependency, and its patches will be applied.
	// The userdev artifact is a special name and will get all sorts of transformations applied to it.
	minecraft 'net.minecraftforge:forge:1.19.3-44.1.23'

	// Mixins
	// classpath 'org.spongepowered:mixingradle:0.7.+'
	annotationProcessor 'org.spongepowered:mixin:0.8.5:processor'
	// clientAnnotationProcessor 'org.spongepowered:mixin:0.8.5:processor'
	// apiAnnotationProcessor 'org.spongepowered:mixin:0.8.5:processor'

	// Real mod deobf dependency examples - these get remapped to your current mappings
	// implementation fg.deobf("com.tterrag.registrate:Registrate:MC${mc_version}-${registrate_version}") // Adds registrate as a dependency

	// Examples using mod jars from ./libs
	// implementation fg.deobf("blank:coolmod-${mc_version}:${coolmod_version}")

	// For more info...
	// http://www.gradle.org/docs/current/userguide/artifact_dependencies_tutorial.html
	// http://www.gradle.org/docs/current/userguide/dependency_management.html
	// implementation fg.deobf("org.jetbrains.kotlin:kotlin-reflect:1.8.20")
	// implementation fg.deobf("org.jetbrains.kotlin:kotlin-stdlib:1.8.20")
	
	// implementation fg.deobf("kotlin.graphics:uno-core:0.7.10")
	// implementation fg.deobf("kotlin.graphics:glm:0.9.9.1-build-9")

	// Spair/ImGui
	// implementation platform("org.lwjgl:lwjgl-bom:$lwjglVersion")

	// ['', '-opengl', '-glfw'].each {
	// 	implementation "org.lwjgl:lwjgl$it:$lwjglVersion"
	// 	implementation "org.lwjgl:lwjgl$it::natives-windows"
	// 	implementation "org.lwjgl:lwjgl$it::natives-linux"
	// }
	
	['binding', 'lwjgl3', 'natives-linux', 'natives-windows'].each {
		minecraftLibrary (group: "io.github.spair", name: "imgui-java-$it", version: "$imguiVersion") {
			exclude group: "org.lwjgl"
		}
		jarJar(group: "io.github.spair", name: "imgui-java-${it}", version: "[0,)") {
			transitive(false)
			exclude group: "org.lwjgl"
		}
	}
	// implementation "io.github.spair:imgui-java-binding:$imguiVersion"
	// implementation "io.github.spair:imgui-java-lwjgl3:$imguiVersion"
	// implementation "io.github.spair:imgui-java-natives-windows:$imguiVersion"
	// implementation "io.github.spair:imgui-java-natives-linux:$imguiVersion"
}

mixin {
	// add sourceSets.main, 'forge-mixin.refmap.json'
	config 'forge-mixin.json'

    debug.verbose = true
    debug.export = true
}

// Example for how to get properties into the manifest for reading at runtime.
jar {
	manifest {
		attributes([
				"Specification-Title"     : "examplemod",
				"Specification-Vendor"    : "examplemodsareus",
				"Specification-Version"   : "1", // We are version 1 of ourselves
				"Implementation-Title"    : project.name,
				"Implementation-Version"  : project.jar.archiveVersion,
				"Implementation-Vendor"   : "examplemodsareus",
				"Implementation-Timestamp": new Date().format("yyyy-MM-dd'T'HH:mm:ssZ"),
				"MixinConfigs"			  : "forge-mixin.json"
		])
	}
	// from configurations.library.collect { it.isDirectory() ? it : zipTree(it) }
}

// Example configuration to allow publishing using the maven-publish plugin
// This is the preferred method to reobfuscate your jar file
jar.finalizedBy('reobfJar')
// However if you are in a multi-project build, dev time needs unobfed jar files, so you can delay the obfuscation until publishing by doing
// publish.dependsOn('reobfJar')

publishing {
	publications {
		mavenJava(MavenPublication) {
			artifact jar
		}
	}
	repositories {
		maven {
			url "file://${project.projectDir}/mcmodsrepo"
		}
	}
}

tasks.withType(JavaCompile).configureEach {
	options.encoding = 'UTF-8' // Use the UTF-8 charset for Java compilation
}
