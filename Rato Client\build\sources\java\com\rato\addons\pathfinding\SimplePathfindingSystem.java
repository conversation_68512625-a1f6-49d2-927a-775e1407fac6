package com.rato.addons.pathfinding;

import net.minecraft.client.Minecraft;
import net.minecraft.util.Vec3;
import net.minecraft.util.BlockPos;
import net.minecraft.block.Block;
import net.minecraft.init.Blocks;
import net.minecraft.client.settings.KeyBinding;
import com.rato.addons.util.Logger;

import java.util.*;

/**
 * Sistema de pathfinding simplificado e funcional
 * Foca na funcionalidade básica sem complexidade desnecessária
 */
public class SimplePathfindingSystem {
    
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Estado do sistema
    private boolean isActive = false;
    private List<Vec3> currentPath = new ArrayList<>();
    private int currentWaypointIndex = 0;
    private Vec3 targetPosition = null;
    
    // Configurações
    private static final double WAYPOINT_REACH_DISTANCE = 1.5;
    private static final double MAX_PATHFIND_DISTANCE = 50.0;
    private static final int MAX_PATH_LENGTH = 100;

    // Detecção de árvores
    private static final int TREE_SCAN_RADIUS = 20;
    private static final double MAX_TREE_DISTANCE = 30.0;

    // Controle de pulo
    private boolean isJumping = false;
    private boolean wasOnGround = true;
    private long lastJumpTime = 0;
    private static final long JUMP_COOLDOWN = 500; // 500ms entre pulos
    
    /**
     * Inicia pathfinding para a árvore mais próxima
     */
    public boolean startTreePathfinding() {
        if (mc.thePlayer == null || mc.theWorld == null) {
            Logger.sendMessage("§cJogador ou mundo não disponível!");
            return false;
        }
        
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        Vec3 nearestTree = findNearestTree(playerPos);
        
        if (nearestTree == null) {
            Logger.sendMessage("§eNenhuma árvore encontrada na área!");
            return false;
        }
        
        return startPathfinding(nearestTree);
    }
    
    /**
     * Inicia pathfinding para uma posição específica
     */
    public boolean startPathfinding(Vec3 target) {
        if (mc.thePlayer == null || mc.theWorld == null) {
            return false;
        }
        
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        double distance = playerPos.distanceTo(target);
        
        if (distance > MAX_PATHFIND_DISTANCE) {
            Logger.sendMessage("§cAlvo muito distante! Máximo: " + MAX_PATHFIND_DISTANCE + " blocos");
            return false;
        }
        
        List<Vec3> path = calculateSimplePath(playerPos, target);
        
        if (path.isEmpty()) {
            Logger.sendMessage("§cNão foi possível calcular caminho!");
            return false;
        }
        
        this.currentPath = path;
        this.currentWaypointIndex = 0;
        this.targetPosition = target;
        this.isActive = true;
        
        Logger.sendMessage("§aPathfinding iniciado! Caminho: " + path.size() + " waypoints");
        Logger.sendMessage("§7Alvo: " + String.format("%.1f, %.1f, %.1f", target.xCoord, target.yCoord, target.zCoord));
        
        return true;
    }
    
    /**
     * Para o pathfinding
     */
    public void stopPathfinding() {
        if (!isActive) return;
        
        isActive = false;
        releaseMovementKeys();
        
        Logger.sendMessage("§7Pathfinding parado");
    }
    
    /**
     * Atualiza o sistema (chamado a cada tick)
     */
    public void update() {
        if (!isActive || mc.thePlayer == null || currentPath.isEmpty()) {
            return;
        }
        
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        
        // Verificar se chegou ao waypoint atual
        if (currentWaypointIndex < currentPath.size()) {
            Vec3 currentWaypoint = currentPath.get(currentWaypointIndex);
            double distance = playerPos.distanceTo(currentWaypoint);
            
            if (distance <= WAYPOINT_REACH_DISTANCE) {
                currentWaypointIndex++;
                
                if (currentWaypointIndex >= currentPath.size()) {
                    // Chegou ao destino
                    completePathfinding();
                    return;
                }
                
                Logger.sendMessage("§7Waypoint " + currentWaypointIndex + "/" + currentPath.size());
            }
            
            // Mover em direção ao waypoint atual
            moveTowardsWaypoint(currentWaypoint);
        }
    }
    
    /**
     * Calcula um caminho simples entre duas posições
     */
    private List<Vec3> calculateSimplePath(Vec3 start, Vec3 end) {
        List<Vec3> path = new ArrayList<>();
        
        // Pathfinding simples em linha reta com ajustes de altura
        Vec3 direction = end.subtract(start);
        double totalDistance = start.distanceTo(end);
        int steps = Math.max(1, (int) Math.ceil(totalDistance / 2.0)); // Waypoint a cada 2 blocos
        
        for (int i = 1; i <= steps; i++) {
            double t = (double) i / steps;
            Vec3 scaledDirection = new Vec3(direction.xCoord * t, direction.yCoord * t, direction.zCoord * t);
            Vec3 waypoint = start.add(scaledDirection);
            
            // Ajustar altura para terreno
            waypoint = adjustHeightForTerrain(waypoint);
            
            if (waypoint != null) {
                path.add(waypoint);
            }
        }
        
        // Garantir que o destino final está incluído
        if (!path.isEmpty() && path.get(path.size() - 1).distanceTo(end) > 1.0) {
            Vec3 adjustedEnd = adjustHeightForTerrain(end);
            if (adjustedEnd != null) {
                path.add(adjustedEnd);
            }
        }
        
        return path.size() > MAX_PATH_LENGTH ? path.subList(0, MAX_PATH_LENGTH) : path;
    }
    
    /**
     * Ajusta altura de um waypoint para seguir o terreno naturalmente
     */
    private Vec3 adjustHeightForTerrain(Vec3 position) {
        if (mc.theWorld == null || mc.thePlayer == null) return position;

        // Usar altura do player atual como referência para seguir terreno
        int playerY = (int) mc.thePlayer.posY;
        int searchRange = 15; // Buscar em um range maior

        // Encontrar o chão mais próximo da altura atual
        int bestGroundY = findBestGroundLevel(position, playerY, searchRange);

        if (bestGroundY != -1) {
            // Verificar se há espaço para o jogador (2 blocos de altura)
            BlockPos groundPos = new BlockPos(position.xCoord, bestGroundY, position.zCoord);

            if (hasPlayerSpace(groundPos)) {
                return new Vec3(position.xCoord, bestGroundY + 1, position.zCoord);
            }
        }

        // Fallback: manter altura original se não encontrar melhor
        return position;
    }

    /**
     * Encontra o melhor nível de chão considerando a altura atual
     */
    private int findBestGroundLevel(Vec3 position, int referenceY, int searchRange) {
        BlockPos basePos = new BlockPos(position.xCoord, position.yCoord, position.zCoord);
        int bestY = -1;
        int minHeightDiff = Integer.MAX_VALUE;

        // Procurar em um range ao redor da altura de referência
        for (int y = referenceY - searchRange; y <= referenceY + searchRange; y++) {
            if (y < 1 || y > 255) continue;

            BlockPos checkPos = new BlockPos(basePos.getX(), y, basePos.getZ());
            Block block = mc.theWorld.getBlockState(checkPos).getBlock();

            if (isValidGroundBlock(block)) {
                int heightDiff = Math.abs(y - referenceY);

                // Preferir o chão mais próximo da altura atual
                if (heightDiff < minHeightDiff) {
                    minHeightDiff = heightDiff;
                    bestY = y;
                }
            }
        }

        return bestY;
    }

    /**
     * Verifica se há espaço para o player em uma posição
     */
    private boolean hasPlayerSpace(BlockPos groundPos) {
        Block above1 = mc.theWorld.getBlockState(groundPos.up()).getBlock();
        Block above2 = mc.theWorld.getBlockState(groundPos.up(2)).getBlock();

        return (above1 == Blocks.air || above1 == Blocks.leaves || !above1.isFullBlock()) &&
               (above2 == Blocks.air || above2 == Blocks.leaves || !above2.isFullBlock());
    }

    /**
     * Verifica se um bloco é válido como chão
     */
    private boolean isValidGroundBlock(Block block) {
        return block != Blocks.air &&
               block != Blocks.water &&
               block != Blocks.flowing_water &&
               block != Blocks.lava &&
               block != Blocks.flowing_lava &&
               block.getMaterial().blocksMovement() &&
               block.isFullBlock();
    }
    
    /**
     * Move o jogador em direção a um waypoint
     */
    private void moveTowardsWaypoint(Vec3 waypoint) {
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        Vec3 direction = waypoint.subtract(playerPos).normalize();
        
        // Calcular rotação necessária
        float targetYaw = (float) Math.toDegrees(Math.atan2(-direction.xCoord, direction.zCoord));
        float currentYaw = mc.thePlayer.rotationYaw;
        float yawDifference = targetYaw - currentYaw;
        
        // Normalizar diferença de ângulo
        while (yawDifference > 180) yawDifference -= 360;
        while (yawDifference < -180) yawDifference += 360;
        
        // Aplicar rotação suave
        if (Math.abs(yawDifference) > 5.0f) {
            float rotationStep = Math.signum(yawDifference) * Math.min(Math.abs(yawDifference), 10.0f);
            mc.thePlayer.rotationYaw = currentYaw + rotationStep;
        }
        
        // Liberar todas as teclas primeiro
        releaseMovementKeys();
        
        // Aplicar movimento
        if (Math.abs(yawDifference) < 45.0f) { // Só mover se estiver olhando na direção certa
            KeyBinding.setKeyBindState(mc.gameSettings.keyBindForward.getKeyCode(), true);

            // Controlar pulo de forma inteligente
            if (waypoint.yCoord > playerPos.yCoord + 0.5) {
                handleJumpControl();
            }
        }
    }
    
    /**
     * Encontra a árvore mais próxima
     */
    private Vec3 findNearestTree(Vec3 playerPos) {
        Vec3 nearestTree = null;
        double nearestDistance = Double.MAX_VALUE;
        
        BlockPos centerPos = new BlockPos(playerPos);
        
        // Escanear área ao redor do jogador
        for (int x = -TREE_SCAN_RADIUS; x <= TREE_SCAN_RADIUS; x++) {
            for (int z = -TREE_SCAN_RADIUS; z <= TREE_SCAN_RADIUS; z++) {
                for (int y = -5; y <= 10; y++) {
                    BlockPos checkPos = centerPos.add(x, y, z);
                    Block block = mc.theWorld.getBlockState(checkPos).getBlock();
                    
                    if (isLogBlock(block)) {
                        // Verificar se é base de árvore (tem chão embaixo)
                        Block below = mc.theWorld.getBlockState(checkPos.down()).getBlock();
                        if (below.isFullBlock() || isLogBlock(below)) {
                            
                            Vec3 treePos = new Vec3(checkPos.getX() + 0.5, checkPos.getY(), checkPos.getZ() + 0.5);
                            double distance = playerPos.distanceTo(treePos);
                            
                            if (distance < nearestDistance && distance <= MAX_TREE_DISTANCE) {
                                // Encontrar posição de quebra ao redor da árvore
                                Vec3 breakingPos = findBreakingPosition(treePos, playerPos);
                                if (breakingPos != null) {
                                    nearestTree = breakingPos;
                                    nearestDistance = distance;
                                }
                            }
                        }
                    }
                }
            }
        }
        
        return nearestTree;
    }
    
    /**
     * Encontra posição para quebrar uma árvore
     */
    private Vec3 findBreakingPosition(Vec3 treePos, Vec3 playerPos) {
        // Testar posições ao redor da árvore
        for (double radius = 2.0; radius <= 4.0; radius += 0.5) {
            for (double angle = 0; angle < 2 * Math.PI; angle += Math.PI / 4) {
                double x = treePos.xCoord + radius * Math.cos(angle);
                double z = treePos.zCoord + radius * Math.sin(angle);
                
                Vec3 candidatePos = new Vec3(x, treePos.yCoord, z);
                candidatePos = adjustHeightForTerrain(candidatePos);
                
                if (candidatePos != null) {
                    // Verificar se pode quebrar a árvore desta posição
                    double distanceToTree = candidatePos.distanceTo(treePos);
                    if (distanceToTree <= 4.5) { // Alcance de quebra
                        return candidatePos;
                    }
                }
            }
        }
        
        return null;
    }
    
    /**
     * Verifica se um bloco é um log de árvore
     */
    private boolean isLogBlock(Block block) {
        return block == Blocks.log || block == Blocks.log2;
    }
    
    /**
     * Controla o pulo de forma inteligente
     */
    private void handleJumpControl() {
        boolean currentlyOnGround = mc.thePlayer.onGround;
        long currentTime = System.currentTimeMillis();

        // Detectar quando o player sai do chão (completou o pulo)
        if (isJumping && wasOnGround && !currentlyOnGround) {
            // Player saiu do chão, liberar tecla de pulo
            KeyBinding.setKeyBindState(mc.gameSettings.keyBindJump.getKeyCode(), false);
            isJumping = false;
        }

        // Verificar se deve iniciar um novo pulo
        if (!isJumping && currentlyOnGround && (currentTime - lastJumpTime) > JUMP_COOLDOWN) {
            KeyBinding.setKeyBindState(mc.gameSettings.keyBindJump.getKeyCode(), true);
            isJumping = true;
            lastJumpTime = currentTime;
        }

        // Atualizar estado anterior
        wasOnGround = currentlyOnGround;
    }

    /**
     * Libera todas as teclas de movimento
     */
    private void releaseMovementKeys() {
        KeyBinding.setKeyBindState(mc.gameSettings.keyBindForward.getKeyCode(), false);
        KeyBinding.setKeyBindState(mc.gameSettings.keyBindBack.getKeyCode(), false);
        KeyBinding.setKeyBindState(mc.gameSettings.keyBindLeft.getKeyCode(), false);
        KeyBinding.setKeyBindState(mc.gameSettings.keyBindRight.getKeyCode(), false);
        KeyBinding.setKeyBindState(mc.gameSettings.keyBindJump.getKeyCode(), false);

        // Resetar estado de pulo
        isJumping = false;
        wasOnGround = true;
    }
    
    /**
     * Completa o pathfinding
     */
    private void completePathfinding() {
        isActive = false;
        releaseMovementKeys();
        
        Logger.sendMessage("§aChegou ao destino!");
        
        if (targetPosition != null) {
            Vec3 playerPos = mc.thePlayer.getPositionVector();
            double finalDistance = playerPos.distanceTo(targetPosition);
            Logger.sendMessage("§7Distância final: " + String.format("%.1f", finalDistance) + " blocos");
        }
    }
    
    // Getters
    public boolean isActive() { return isActive; }
    public int getCurrentWaypoint() { return currentWaypointIndex; }
    public int getTotalWaypoints() { return currentPath.size(); }
    public double getProgress() { 
        return currentPath.isEmpty() ? 0.0 : (double) currentWaypointIndex / currentPath.size() * 100.0; 
    }
    
    /**
     * Obtém estatísticas do sistema
     */
    public String getStats() {
        if (!isActive) return "Sistema inativo";
        
        StringBuilder stats = new StringBuilder();
        stats.append("Pathfinding Ativo\n");
        stats.append("Waypoint: ").append(currentWaypointIndex).append("/").append(currentPath.size()).append("\n");
        stats.append("Progresso: ").append(String.format("%.1f", getProgress())).append("%\n");
        
        if (targetPosition != null) {
            stats.append("Alvo: ").append(String.format("%.1f, %.1f, %.1f", 
                targetPosition.xCoord, targetPosition.yCoord, targetPosition.zCoord));
        }
        
        return stats.toString();
    }
}
