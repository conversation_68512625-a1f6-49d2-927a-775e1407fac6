package com.rato.addons.pathfinding.enhanced;

import com.rato.addons.pathfinding.professional.PathNode;
import com.rato.addons.util.Logger;
import net.minecraft.block.Block;
import net.minecraft.block.BlockSlab;
import net.minecraft.block.BlockStairs;
import net.minecraft.block.material.Material;
import net.minecraft.client.Minecraft;
import net.minecraft.init.Blocks;
import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;
import net.minecraft.world.World;

import java.util.*;

/**
 * Sistema de pathfinding melhorado baseado no Mucifex
 * Implementa as melhores técnicas do Mucifex com otimizações adicionais
 */
public class EnhancedPathfinder {
    
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Configurações otimizadas baseadas no Mucifex
    private static final int MAX_ITERATIONS = 10000;
    private static final double GOAL_TOLERANCE = 1.0;
    private static final int STRAIGHT_COST = 10;
    private static final int DIAGONAL_COST = 14;
    
    // Direções de movimento (baseado no Mucifex)
    private static final int[][] MOVEMENT_DIRECTIONS = {
        // Movimento horizontal (prioridade)
        {1, 0, 0}, {-1, 0, 0}, {0, 0, 1}, {0, 0, -1},
        // Movimento diagonal
        {1, 0, 1}, {1, 0, -1}, {-1, 0, 1}, {-1, 0, -1},
        // Movimento vertical
        {0, 1, 0}, {0, -1, 0},
        // Movimento diagonal com altura
        {1, 1, 0}, {-1, 1, 0}, {0, 1, 1}, {0, 1, -1},
        {1, -1, 0}, {-1, -1, 0}, {0, -1, 1}, {0, -1, -1}
    };
    
    /**
     * Nó melhorado baseado no AStarNode do Mucifex
     */
    public static class EnhancedNode implements Comparable<EnhancedNode> {
        private final int x, y, z;
        private final BlockPos blockPos;
        private double hCost;
        private int gCost;
        private EnhancedNode parent;
        
        // Flags de movimento (como no Mucifex)
        private boolean isJumpNode = false;
        private boolean isFallNode = false;
        private MovementType movementType = MovementType.WALK;
        
        public enum MovementType {
            WALK, JUMP, FALL, DIAGONAL, CLIMB
        }
        
        public EnhancedNode(BlockPos pos, EnhancedNode parent, EnhancedNode endNode) {
            this.x = pos.getX();
            this.y = pos.getY();
            this.z = pos.getZ();
            this.blockPos = pos;
            
            calculateHeuristic(endNode);
            if (parent != null) {
                setParent(parent);
            }
        }
        
        /**
         * Verifica se o nó pode ser atravessado (versão simplificada e mais permissiva)
         */
        public boolean canBeTraversed() {
            try {
                World world = Minecraft.getMinecraft().theWorld;
                if (world == null) return false;

                // Verificar se os blocos para o player estão livres (2 blocos de altura)
                // Se há blocos sólidos onde o player ficaria, não pode atravessar
                if (isBlockSolid(blockPos) || isBlockSolid(blockPos.up())) {
                    return false;
                }

                // Verificar se há chão sólido ou se é um movimento válido
                boolean hasSolidGround = isBlockSolid(blockPos.down());

                // Se tem chão sólido, é válido
                if (hasSolidGround) {
                    return true;
                }

                // Se não tem chão, verificar se é um movimento de queda válido
                if (parent != null) {
                    int heightDiff = y - parent.getY();

                    // Movimento para cima (pulo)
                    if (heightDiff > 0) {
                        // Verificar se pode pular (máximo 1 bloco)
                        if (heightDiff <= 1) {
                            setJumpNode(true);
                            return true;
                        }
                        return false;
                    }

                    // Movimento para baixo (queda)
                    if (heightDiff < 0) {
                        // Verificar queda segura (máximo 3 blocos)
                        if (Math.abs(heightDiff) <= 3) {
                            // Verificar se vai ter chão onde cair
                            int fallDistance = 0;
                            BlockPos checkPos = blockPos.down();
                            while (fallDistance < 4) {
                                if (isBlockSolid(checkPos)) {
                                    setFallNode(true);
                                    return true;
                                }
                                fallDistance++;
                                checkPos = checkPos.down();
                            }
                        }
                        return false;
                    }

                    // Movimento horizontal sem chão - verificar se é continuação de queda
                    if (parent.isFallNode) {
                        setFallNode(true);
                        return true;
                    }
                }

                // Se não tem parent e não tem chão, não é válido
                return false;

            } catch (Exception e) {
                System.out.println("[Enhanced] Erro em canBeTraversed: " + e.getMessage());
                return false;
            }
        }
        
        /**
         * Verifica se um bloco é sólido (baseado exatamente no Mucifex)
         */
        private boolean isBlockSolid(BlockPos pos) {
            World world = Minecraft.getMinecraft().theWorld;
            if (world == null) return false;

            Block block = world.getBlockState(pos).getBlock();

            // Fast path for air blocks (most common case)
            if (block.getMaterial() == Material.air) {
                return false;
            }

            // Fast path for full solid blocks
            if (block.getMaterial().blocksMovement() && !block.getMaterial().isLiquid()) {
                // Check if it's a full cube (most solid blocks)
                if (block.isFullCube()) {
                    return true;
                }
            }

            // For non-standard blocks, check specific types (compatível com 1.8.9)
            return block.isBlockSolid(world, pos, null) ||
                   block instanceof BlockSlab ||
                   block instanceof BlockStairs ||
                   block == Blocks.stained_glass ||
                   block == Blocks.glass_pane ||
                   block == Blocks.iron_bars ||
                   block == Blocks.oak_fence ||
                   block == Blocks.nether_brick_fence ||
                   block == Blocks.oak_fence_gate ||
                   block == Blocks.ender_chest ||
                   block == Blocks.trapped_chest ||
                   block == Blocks.chest ||
                   block == Blocks.glass ||
                   block == Blocks.cactus ||
                   block == Blocks.cobblestone_wall ||
                   block == Blocks.skull ||
                   block == Blocks.sand ||
                   block == Blocks.gravel;
        }
        
        /**
         * Calcula heurística diagonal (baseado no Mucifex)
         */
        private void calculateHeuristic(EnhancedNode endNode) {
            if (endNode == null) {
                this.hCost = 0;
                return;
            }
            
            int dx = Math.abs(endNode.getX() - x);
            int dy = Math.abs(endNode.getY() - y);
            int dz = Math.abs(endNode.getZ() - z);
            
            // Heurística diagonal do Mucifex
            int max = Math.max(dx, dz);
            int min = Math.min(dx, dz);
            
            this.hCost = (STRAIGHT_COST * max) + ((DIAGONAL_COST - (2 * STRAIGHT_COST)) * min) + (STRAIGHT_COST * dy);
        }
        
        /**
         * Conta paredes adjacentes (baseado no Mucifex)
         */
        private int countAdjacentWalls() {
            int wallCount = 0;
            World world = Minecraft.getMinecraft().theWorld;
            if (world == null) return 0;

            if (isBlockSolid(new BlockPos(x + 1, y, z))) wallCount++;
            if (isBlockSolid(new BlockPos(x - 1, y, z))) wallCount++;
            if (isBlockSolid(new BlockPos(x, y, z + 1))) wallCount++;
            if (isBlockSolid(new BlockPos(x, y, z - 1))) wallCount++;

            return wallCount;
        }
        
        /**
         * Define parent e calcula gCost (baseado no Mucifex)
         */
        public void setParent(EnhancedNode parent) {
            this.parent = parent;
            
            int xDiff = Math.abs(x - parent.getX());
            int yDiff = Math.abs(y - parent.getY());
            int zDiff = Math.abs(z - parent.getZ());
            
            // Calcular custo base
            int baseCost;
            if (xDiff > 0 && zDiff > 0) {
                // Movimento diagonal
                baseCost = DIAGONAL_COST + (yDiff * STRAIGHT_COST);
                movementType = MovementType.DIAGONAL;
            } else {
                // Movimento reto
                baseCost = (xDiff + yDiff + zDiff) * STRAIGHT_COST;
                movementType = MovementType.WALK;
            }
            
            // Penalidade por paredes adjacentes (anti wall-hugging)
            int wallPenalty = countAdjacentWalls() * 8;
            
            // Penalidade por movimento vertical
            if (yDiff > 0) {
                if (isJumpNode) {
                    baseCost += 5; // Penalidade por pulo
                    movementType = MovementType.JUMP;
                } else if (isFallNode) {
                    baseCost += 2; // Penalidade menor por queda
                    movementType = MovementType.FALL;
                }
            }
            
            this.gCost = parent.getGCost() + baseCost + wallPenalty;
        }
        
        public double getTotalCost() {
            return hCost + gCost;
        }
        
        @Override
        public int compareTo(EnhancedNode other) {
            int totalCostComparison = Double.compare(this.getTotalCost(), other.getTotalCost());
            if (totalCostComparison != 0) return totalCostComparison;
            return Double.compare(this.hCost, other.hCost);
        }
        
        @Override
        public boolean equals(Object obj) {
            if (!(obj instanceof EnhancedNode)) return false;
            EnhancedNode other = (EnhancedNode) obj;
            return x == other.x && y == other.y && z == other.z;
        }
        
        @Override
        public int hashCode() {
            return Objects.hash(x, y, z);
        }
        
        // Getters
        public int getX() { return x; }
        public int getY() { return y; }
        public int getZ() { return z; }
        public int getGCost() { return gCost; }
        public EnhancedNode getParent() { return parent; }
        public BlockPos getBlockPos() { return blockPos; }
        public boolean isJumpNode() { return isJumpNode; }
        public boolean isFallNode() { return isFallNode; }
        public MovementType getMovementType() { return movementType; }
        
        public void setJumpNode(boolean jumpNode) { 
            this.isJumpNode = jumpNode;
            if (jumpNode) this.movementType = MovementType.JUMP;
        }
        
        public void setFallNode(boolean fallNode) { 
            this.isFallNode = fallNode;
            if (fallNode) this.movementType = MovementType.FALL;
        }
        
        public Vec3 asVec3() {
            return new Vec3(x + 0.5, y, z + 0.5);
        }
    }
    
    /**
     * Encontra caminho usando A* melhorado com debug
     */
    public List<PathNode> findPath(Vec3 start, Vec3 goal) {
        if (mc.theWorld == null) {
            System.out.println("[Enhanced] Mundo é null!");
            return null;
        }

        // Conversão correta como no Mucifex (usando Math.floor)
        BlockPos startPos = new BlockPos(Math.floor(start.xCoord), Math.floor(start.yCoord), Math.floor(start.zCoord));
        BlockPos goalPos = new BlockPos(Math.floor(goal.xCoord), Math.floor(goal.yCoord), Math.floor(goal.zCoord));

        System.out.println("[Enhanced] Iniciando pathfinding de " + startPos + " para " + goalPos);

        // Estruturas do A*
        PriorityQueue<EnhancedNode> openSet = new PriorityQueue<>();
        Set<BlockPos> closedSet = new HashSet<>();
        Map<BlockPos, EnhancedNode> allNodes = new HashMap<>();

        // Nó inicial
        EnhancedNode goalNode = new EnhancedNode(goalPos, null, null);
        EnhancedNode startNode = new EnhancedNode(startPos, null, goalNode);
        startNode.gCost = 0;

        // Verificar se o nó inicial é válido
        if (!startNode.canBeTraversed()) {
            System.out.println("[Enhanced] Posição inicial não é traversável!");
            // Tentar encontrar posição próxima válida
            for (int[] direction : MOVEMENT_DIRECTIONS) {
                BlockPos altStart = startPos.add(direction[0], direction[1], direction[2]);
                EnhancedNode altNode = new EnhancedNode(altStart, null, goalNode);
                if (altNode.canBeTraversed()) {
                    startNode = altNode;
                    startPos = altStart;
                    System.out.println("[Enhanced] Usando posição alternativa: " + altStart);
                    break;
                }
            }
        }

        openSet.add(startNode);
        allNodes.put(startPos, startNode);

        int iterations = 0;
        EnhancedNode bestNode = startNode;

        while (!openSet.isEmpty() && iterations < MAX_ITERATIONS) {
            EnhancedNode current = openSet.poll();
            iterations++;

            // Atualizar melhor nó
            if (current.hCost < bestNode.hCost) {
                bestNode = current;
            }

            // Verificar se chegou ao objetivo
            if (current.blockPos.distanceSq(goalPos) <= GOAL_TOLERANCE * GOAL_TOLERANCE) {
                System.out.println("[Enhanced] Caminho encontrado em " + iterations + " iterações!");
                return reconstructPath(current);
            }

            closedSet.add(current.blockPos);

            // Explorar vizinhos
            int validNeighbors = 0;
            for (int[] direction : MOVEMENT_DIRECTIONS) {
                BlockPos neighborPos = current.blockPos.add(direction[0], direction[1], direction[2]);

                if (closedSet.contains(neighborPos)) continue;

                EnhancedNode neighbor = new EnhancedNode(neighborPos, current, goalNode);

                if (!neighbor.canBeTraversed()) continue;

                validNeighbors++;

                EnhancedNode existingNode = allNodes.get(neighborPos);
                if (existingNode != null && neighbor.gCost >= existingNode.gCost) {
                    continue;
                }

                allNodes.put(neighborPos, neighbor);
                openSet.add(neighbor);
            }

            // Debug a cada 1000 iterações
            if (iterations % 1000 == 0) {
                System.out.println("[Enhanced] Iteração " + iterations + ", OpenSet: " + openSet.size() +
                    ", ClosedSet: " + closedSet.size() + ", Vizinhos válidos: " + validNeighbors);
            }
        }

        System.out.println("[Enhanced] Pathfinding terminou após " + iterations + " iterações");
        System.out.println("[Enhanced] OpenSet final: " + openSet.size() + ", ClosedSet: " + closedSet.size());

        // Retornar melhor caminho parcial se não encontrou completo
        if (bestNode != startNode) {
            System.out.println("[Enhanced] Retornando caminho parcial para o melhor nó");
            return reconstructPath(bestNode);
        }

        return null; // Caminho não encontrado
    }
    
    /**
     * Reconstrói caminho a partir do nó final
     */
    private List<PathNode> reconstructPath(EnhancedNode endNode) {
        List<PathNode> path = new ArrayList<>();
        EnhancedNode current = endNode;
        
        while (current != null) {
            PathNode.MovementType moveType = convertMovementType(current.getMovementType());
            PathNode pathNode = new PathNode(current.asVec3(), current.gCost, current.hCost, null, moveType);
            
            // Definir flags especiais
            pathNode.isJump = current.isJumpNode();
            pathNode.isFall = current.isFallNode();
            
            path.add(0, pathNode);
            current = current.getParent();
        }
        
        return optimizePath(path);
    }
    
    /**
     * Otimiza o caminho removendo nós desnecessários
     */
    private List<PathNode> optimizePath(List<PathNode> path) {
        if (path.size() <= 2) return path;
        
        List<PathNode> optimized = new ArrayList<>();
        optimized.add(path.get(0)); // Sempre manter primeiro nó
        
        for (int i = 1; i < path.size() - 1; i++) {
            PathNode current = path.get(i);
            PathNode next = path.get(i + 1);
            
            // Manter nós de pulo e queda
            if (current.isJump || current.isFall || next.isJump || next.isFall) {
                optimized.add(current);
                continue;
            }
            
            // Manter nós com mudança significativa de direção
            if (i > 0) {
                PathNode prev = path.get(i - 1);
                Vec3 dir1 = current.position.subtract(prev.position).normalize();
                Vec3 dir2 = next.position.subtract(current.position).normalize();
                
                double dot = dir1.dotProduct(dir2);
                if (dot < 0.8) { // Mudança de direção significativa
                    optimized.add(current);
                }
            }
        }
        
        optimized.add(path.get(path.size() - 1)); // Sempre manter último nó
        return optimized;
    }
    
    /**
     * Converte tipo de movimento
     */
    private PathNode.MovementType convertMovementType(EnhancedNode.MovementType type) {
        switch (type) {
            case JUMP: return PathNode.MovementType.JUMP;
            case FALL: return PathNode.MovementType.FALL;
            case DIAGONAL: return PathNode.MovementType.DIAGONAL;
            case CLIMB: return PathNode.MovementType.CLIMB;
            default: return PathNode.MovementType.WALK;
        }
    }
}
