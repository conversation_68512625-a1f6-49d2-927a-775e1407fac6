package com.rato.addons.features;

import com.rato.addons.util.Logger;
import net.minecraft.client.Minecraft;
import net.minecraft.util.Vec3;
import net.minecraft.util.BlockPos;
import net.minecraft.block.Block;

import java.util.List;
import java.util.ArrayList;

/**
 * Máquina de Estados Finitos (FSM) para controle avançado do foraging
 * Implementa comportamento inteligente com transições baseadas em condições
 */
public class ForagingStateMachine {
    
    private final Minecraft mc = Minecraft.getMinecraft();
    private final ForagingMacro foragingMacro;
    
    // Estados da máquina
    public enum State {
        SEARCHING_TARGET("Procurando alvo", "Escaneando área para árvores válidas"),
        NAVIGATING("Navegando", "Movendo-se para árvore selecionada"),
        POSITIONING("Posicionando", "Ajustando posição para quebra ótima"),
        INTERACTING("Interagindo", "Executando sequência de quebra"),
        COLLECTING("Coletando", "Aguardando coleta de itens"),
        ERROR_RECOVERY("Recuperando", "Recuperando de erro ou situação inesperada");
        
        private final String displayName;
        private final String description;
        
        State(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }
        
        public String getDisplayName() { return displayName; }
        public String getDescription() { return description; }
    }
    
    // Estado atual e histórico
    private State currentState = State.SEARCHING_TARGET;
    private State previousState = null;
    private long stateStartTime = 0;
    private int stateTransitions = 0;
    
    // Dados do alvo atual
    private BlockPos currentTarget = null;
    private List<Vec3> currentPath = null;
    private double targetUtility = 0.0;
    
    // Configurações de timeout
    private static final long NAVIGATION_TIMEOUT = 30000; // 30 segundos
    private static final long POSITIONING_TIMEOUT = 5000; // 5 segundos
    private static final long INTERACTION_TIMEOUT = 3000; // 3 segundos
    private static final long COLLECTION_TIMEOUT = 2000; // 2 segundos
    
    // Contadores de performance
    private int successfulInteractions = 0;
    private int failedInteractions = 0;
    private long totalNavigationTime = 0;
    
    public ForagingStateMachine(ForagingMacro foragingMacro) {
        this.foragingMacro = foragingMacro;
        enterState(State.SEARCHING_TARGET);
    }
    
    /**
     * Atualiza a máquina de estados
     */
    public void update() {
        if (!foragingMacro.isActive()) return;
        
        // Verificar timeout do estado atual
        if (checkStateTimeout()) {
            handleTimeout();
            return;
        }
        
        // Executar lógica do estado atual
        switch (currentState) {
            case SEARCHING_TARGET:
                updateSearchingTarget();
                break;
            case NAVIGATING:
                updateNavigating();
                break;
            case POSITIONING:
                updatePositioning();
                break;
            case INTERACTING:
                updateInteracting();
                break;
            case COLLECTING:
                updateCollecting();
                break;
            case ERROR_RECOVERY:
                updateErrorRecovery();
                break;
        }
    }
    
    /**
     * Estado: Procurando alvo
     */
    private void updateSearchingTarget() {
        // Usar sistema de seleção de alvo do ForagingMacro
        List<ForagingMacro.TreeCandidate> candidates = foragingMacro.scanForValidTrees();
        
        if (candidates.isEmpty()) {
            // Nenhuma árvore encontrada - aguardar
            if (getStateTime() > 5000) { // 5 segundos sem árvores
                Logger.sendMessage("§7Nenhuma árvore válida encontrada - aguardando...");
                enterState(State.SEARCHING_TARGET); // Reset timer
            }
            return;
        }
        
        // Selecionar melhor alvo usando função de utilidade
        ForagingMacro.TreeCandidate bestTarget = selectBestTarget(candidates);
        if (bestTarget != null) {
            currentTarget = bestTarget.basePosition;
            targetUtility = calculateUtility(bestTarget);
            
            Logger.sendMessage("§aAlvo selecionado: " + currentTarget + " (utilidade: " + 
                String.format("%.2f", targetUtility) + ")");
            
            transitionTo(State.NAVIGATING);
        }
    }
    
    /**
     * Estado: Navegando
     */
    private void updateNavigating() {
        if (currentTarget == null) {
            transitionTo(State.SEARCHING_TARGET);
            return;
        }
        
        // Verificar se chegou próximo ao alvo
        double distance = mc.thePlayer.getPositionVector().distanceTo(new Vec3(currentTarget));
        if (distance < 4.0) {
            transitionTo(State.POSITIONING);
            return;
        }
        
        // Verificar se o pathfinding está ativo
        if (!com.rato.addons.pathfinding.WaypointPathfinder.getInstance().isActive()) {
            // Iniciar pathfinding
            Vec3 playerPos = mc.thePlayer.getPositionVector();
            Vec3 targetPos = new Vec3(currentTarget.getX() + 0.5, currentTarget.getY(), currentTarget.getZ() + 0.5);
            
            List<Vec3> path = foragingMacro.generateNaturalPath(playerPos, targetPos);
            com.rato.addons.pathfinding.WaypointPathfinder.getInstance().startPathfinding(path);
            
            Logger.sendMessage("§7Iniciando navegação para " + currentTarget);
        }
    }
    
    /**
     * Estado: Posicionando
     */
    private void updatePositioning() {
        if (currentTarget == null) {
            transitionTo(State.SEARCHING_TARGET);
            return;
        }
        
        // Verificar se está na posição ideal para quebra
        double distance = mc.thePlayer.getPositionVector().distanceTo(new Vec3(currentTarget));
        if (distance > 4.5) {
            // Muito longe - voltar para navegação
            transitionTo(State.NAVIGATING);
            return;
        }
        
        // Verificar se está olhando para o alvo
        if (foragingMacro.isLookingAtBlock(currentTarget, 5.0f)) {
            transitionTo(State.INTERACTING);
        } else {
            // Ajustar olhar para o alvo
            foragingMacro.lookAtBlock(currentTarget);
        }
    }
    
    /**
     * Estado: Interagindo
     */
    private void updateInteracting() {
        if (currentTarget == null) {
            transitionTo(State.SEARCHING_TARGET);
            return;
        }
        
        // Verificar se a árvore ainda existe
        Block block = mc.theWorld.getBlockState(currentTarget).getBlock();
        if (block != foragingMacro.getCurrentArea().getLogBlock()) {
            // Árvore já foi quebrada - sucesso!
            onInteractionSuccess();
            return;
        }
        
        // Executar sequência de quebra
        if (!foragingMacro.isBreakingBlock()) {
            foragingMacro.startBlockBreaking();
        } else {
            foragingMacro.continueBlockBreaking();
        }
    }
    
    /**
     * Estado: Coletando
     */
    private void updateCollecting() {
        // Aguardar coleta de itens
        if (getStateTime() > COLLECTION_TIMEOUT) {
            // Adicionar árvore ao cooldown
            foragingMacro.addBrokenTreeWithCooldown(currentTarget);
            foragingMacro.addToRecentTreePositions(currentTarget);
            
            // Voltar para busca
            currentTarget = null;
            transitionTo(State.SEARCHING_TARGET);
        }
    }
    
    /**
     * Estado: Recuperação de erro
     */
    private void updateErrorRecovery() {
        Logger.sendMessage("§cRecuperando de erro - reiniciando busca...");
        
        // Limpar estado atual
        currentTarget = null;
        currentPath = null;
        
        // Parar pathfinding
        com.rato.addons.pathfinding.WaypointPathfinder.getInstance().stopPathfinding();
        
        // Voltar para busca
        transitionTo(State.SEARCHING_TARGET);
    }
    
    /**
     * Seleciona melhor alvo usando função de utilidade multi-objetivo
     */
    private ForagingMacro.TreeCandidate selectBestTarget(List<ForagingMacro.TreeCandidate> candidates) {
        ForagingMacro.TreeCandidate bestCandidate = null;
        double bestUtility = -1.0;
        
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        
        for (ForagingMacro.TreeCandidate candidate : candidates) {
            double utility = calculateUtility(candidate);
            
            if (utility > bestUtility) {
                bestUtility = utility;
                bestCandidate = candidate;
            }
        }
        
        return bestCandidate;
    }
    
    /**
     * Calcula utilidade de um alvo usando função multi-objetivo
     */
    private double calculateUtility(ForagingMacro.TreeCandidate candidate) {
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        double distance = Math.sqrt(candidate.basePosition.distanceSq(new BlockPos(playerPos)));
        
        // Componentes da utilidade
        double densityValue = candidate.logCount * 2.0 + candidate.leafCount * 0.5 + candidate.height;
        double distanceCost = distance / 50.0; // Normalizar
        double explorationBonus = foragingMacro.calculateExplorationBonus(candidate.basePosition);
        double cooldownPenalty = foragingMacro.isTreeInCooldown(candidate.basePosition) ? 0.1 : 1.0;
        
        // Função de utilidade: Utility(T) = w₁ * Density(T) - w₂ * Distance(T) + w₃ * Exploration(T) * w₄ * Cooldown(T)
        double w1 = 1.0; // Peso da densidade
        double w2 = 0.5; // Peso da distância
        double w3 = 2.0; // Peso da exploração
        double w4 = 10.0; // Peso do cooldown
        
        return (w1 * densityValue - w2 * distanceCost + w3 * explorationBonus) * w4 * cooldownPenalty;
    }
    
    /**
     * Transição para novo estado
     */
    private void transitionTo(State newState) {
        if (newState == currentState) return;
        
        previousState = currentState;
        currentState = newState;
        stateStartTime = System.currentTimeMillis();
        stateTransitions++;
        
        Logger.sendMessage("§6FSM: " + previousState.getDisplayName() + " → " + newState.getDisplayName());
    }
    
    /**
     * Entra em um estado (usado para inicialização)
     */
    private void enterState(State state) {
        currentState = state;
        stateStartTime = System.currentTimeMillis();
        Logger.sendMessage("§6FSM: Entrando em " + state.getDisplayName());
    }
    
    /**
     * Verifica timeout do estado atual
     */
    private boolean checkStateTimeout() {
        long stateTime = getStateTime();
        
        switch (currentState) {
            case NAVIGATING:
                return stateTime > NAVIGATION_TIMEOUT;
            case POSITIONING:
                return stateTime > POSITIONING_TIMEOUT;
            case INTERACTING:
                return stateTime > INTERACTION_TIMEOUT;
            case COLLECTING:
                return stateTime > COLLECTION_TIMEOUT;
            default:
                return false;
        }
    }
    
    /**
     * Lida com timeout do estado
     */
    private void handleTimeout() {
        Logger.sendMessage("§cTimeout no estado " + currentState.getDisplayName());
        failedInteractions++;
        transitionTo(State.ERROR_RECOVERY);
    }
    
    /**
     * Chamado quando interação é bem-sucedida
     */
    private void onInteractionSuccess() {
        successfulInteractions++;
        totalNavigationTime += getStateTime();
        
        Logger.sendMessage("§aInteração bem-sucedida! (" + successfulInteractions + " sucessos)");
        transitionTo(State.COLLECTING);
    }
    
    /**
     * Obtém tempo no estado atual
     */
    private long getStateTime() {
        return System.currentTimeMillis() - stateStartTime;
    }
    
    // Getters para UI
    public State getCurrentState() { return currentState; }
    public String getStateDescription() { return currentState.getDescription(); }
    public long getCurrentStateTime() { return getStateTime(); }
    public int getStateTransitions() { return stateTransitions; }
    public int getSuccessfulInteractions() { return successfulInteractions; }
    public int getFailedInteractions() { return failedInteractions; }
    public double getSuccessRate() {
        int total = successfulInteractions + failedInteractions;
        return total > 0 ? (double) successfulInteractions / total * 100.0 : 0.0;
    }
}
