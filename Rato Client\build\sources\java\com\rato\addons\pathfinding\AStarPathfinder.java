package com.rato.addons.pathfinding;

import net.minecraft.client.Minecraft;
import net.minecraft.util.Vec3;
import net.minecraft.util.BlockPos;
import net.minecraft.block.Block;
import net.minecraft.block.BlockSlab;
import net.minecraft.block.BlockStairs;
import net.minecraft.block.BlockFarmland;
import net.minecraft.block.state.IBlockState;
import net.minecraft.init.Blocks;

import java.util.*;

/**
 * Sistema avançado de pathfinding usando algoritmo A* com NavMesh
 * Implementa busca otimizada com heurística de Manhattan e custos adaptativos
 */
public class AStarPathfinder {
    
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Configurações do algoritmo (baseado no Baritone)
    private static final int MAX_SEARCH_NODES = 5000; // Aumentado para melhor pathfinding
    private static final double MOVEMENT_COST_WALK = 1.0;
    private static final double MOVEMENT_COST_JUMP = 1.4;
    private static final double MOVEMENT_COST_FALL = 1.2;
    private static final double MOVEMENT_COST_DIAGONAL = 1.414; // sqrt(2)
    private static final double MOVEMENT_COST_WATER = 2.5; // Custo para andar na água
    private static final double MOVEMENT_COST_SOUL_SAND = 1.8; // Custo para soul sand
    private static final double MOVEMENT_COST_BRIDGE = 3.0; // Custo para colocar blocos
    private static final double MINIMUM_IMPROVEMENT = 0.01; // Melhoria mínima para reprocessar nó
    
    // Direções de movimento (8 direções + vertical)
    private static final int[][] MOVEMENT_DIRECTIONS = {
        {1, 0, 0}, {-1, 0, 0}, {0, 0, 1}, {0, 0, -1}, // Cardinais
        {1, 0, 1}, {1, 0, -1}, {-1, 0, 1}, {-1, 0, -1}, // Diagonais
        {0, 1, 0}, {0, -1, 0} // Vertical
    };
    
    /**
     * Nó do grafo de navegação
     */
    private static class PathNode implements Comparable<PathNode> {
        public final BlockPos position;
        public final double gCost; // Custo real do início até este nó
        public final double hCost; // Custo heurístico até o destino
        public final double fCost; // gCost + hCost
        public final PathNode parent;
        
        public PathNode(BlockPos position, double gCost, double hCost, PathNode parent) {
            this.position = position;
            this.gCost = gCost;
            this.hCost = hCost;
            this.fCost = gCost + hCost;
            this.parent = parent;
        }
        
        @Override
        public int compareTo(PathNode other) {
            int result = Double.compare(this.fCost, other.fCost);
            if (result == 0) {
                result = Double.compare(this.hCost, other.hCost); // Tie-breaker
            }
            return result;
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (!(obj instanceof PathNode)) return false;
            PathNode other = (PathNode) obj;
            return position.equals(other.position);
        }
        
        @Override
        public int hashCode() {
            return position.hashCode();
        }
    }
    
    /**
     * Encontra caminho usando algoritmo A* otimizado
     */
    public List<Vec3> findPath(Vec3 start, Vec3 goal) {
        if (mc.theWorld == null) return new ArrayList<>();
        
        BlockPos startPos = new BlockPos(start);
        BlockPos goalPos = new BlockPos(goal);
        
        // Verificar se o destino é alcançável
        if (!canStandAt(goalPos)) {
            goalPos = findNearestWalkable(goalPos);
            if (goalPos == null) return new ArrayList<>();
        }
        
        // Estruturas de dados do A*
        PriorityQueue<PathNode> openSet = new PriorityQueue<>();
        Set<BlockPos> closedSet = new HashSet<>();
        Map<BlockPos, PathNode> allNodes = new HashMap<>();
        
        // Nó inicial
        PathNode startNode = new PathNode(startPos, 0, manhattanDistance(startPos, goalPos), null);
        openSet.add(startNode);
        allNodes.put(startPos, startNode);
        
        int nodesExplored = 0;
        
        while (!openSet.isEmpty() && nodesExplored < MAX_SEARCH_NODES) {
            PathNode current = openSet.poll();
            nodesExplored++;
            
            // Chegou ao destino
            if (current.position.equals(goalPos)) {
                return reconstructPath(current);
            }
            
            closedSet.add(current.position);
            
            // Explorar vizinhos
            for (int[] direction : MOVEMENT_DIRECTIONS) {
                BlockPos neighborPos = current.position.add(direction[0], direction[1], direction[2]);
                
                if (closedSet.contains(neighborPos)) continue;
                
                // Verificar se o movimento é válido
                MovementResult movement = analyzeMovement(current.position, neighborPos);
                if (!movement.isValid) continue;

                // Usar posição ajustada se disponível
                BlockPos finalPos = movement.adjustedPosition != null ? movement.adjustedPosition : neighborPos;

                if (closedSet.contains(finalPos)) continue;

                double tentativeGCost = current.gCost + movement.cost;

                PathNode existingNode = allNodes.get(finalPos);
                if (existingNode != null && tentativeGCost >= existingNode.gCost + MINIMUM_IMPROVEMENT) {
                    continue; // Caminho pior ou melhoria insuficiente
                }

                // Criar novo nó com posição ajustada
                double hCost = manhattanDistance(finalPos, goalPos);
                PathNode neighborNode = new PathNode(finalPos, tentativeGCost, hCost, current);

                allNodes.put(finalPos, neighborNode);
                openSet.add(neighborNode);
            }
        }
        
        // Caminho não encontrado - retornar caminho direto
        return Arrays.asList(start, goal);
    }
    
    /**
     * Analisa movimento entre duas posições (melhorado baseado no Mucifex/Stevebot)
     */
    private MovementResult analyzeMovement(BlockPos from, BlockPos to) {
        MovementResult result = new MovementResult();

        // Primeiro, tentar ajustar a altura do destino para seguir o terreno
        BlockPos adjustedTo = adjustPositionToTerrainAdvanced(to, from);

        // Verificar se a posição ajustada é caminhável
        if (!canBeTraversed(from, adjustedTo)) {
            result.isValid = false;
            return result;
        }

        int dx = adjustedTo.getX() - from.getX();
        int dy = adjustedTo.getY() - from.getY();
        int dz = adjustedTo.getZ() - from.getZ();

        // Calcular custo base do movimento
        double baseCost = calculateBaseCost(dx, dy, dz, from, adjustedTo);
        if (baseCost < 0) {
            result.isValid = false;
            return result;
        }

        // Aplicar penalidades adicionais
        double wallPenalty = countAdjacentWalls(adjustedTo) * 0.15; // Reduzido para ser menos restritivo
        double heightPenalty = Math.abs(dy) * 0.1; // Penalidade leve para mudanças de altura

        result.cost = baseCost + wallPenalty + heightPenalty;
        result.isValid = true;
        result.adjustedPosition = adjustedTo;
        return result;
    }

    /**
     * Calcula custo base do movimento baseado na direção e terreno (baseado no Baritone)
     */
    private double calculateBaseCost(int dx, int dy, int dz, BlockPos from, BlockPos to) {
        double baseCost = -1;

        // Movimento horizontal
        if (dy == 0) {
            if (Math.abs(dx) + Math.abs(dz) == 1) {
                baseCost = MOVEMENT_COST_WALK; // Movimento cardinal
            } else if (Math.abs(dx) == 1 && Math.abs(dz) == 1) {
                baseCost = MOVEMENT_COST_DIAGONAL; // Movimento diagonal
            }
        }
        // Movimento para cima (pulo)
        else if (dy == 1) {
            if (Math.abs(dx) + Math.abs(dz) <= 1) {
                baseCost = MOVEMENT_COST_JUMP;
            }
        }
        // Movimento para baixo (queda/step down)
        else if (dy == -1) {
            if (Math.abs(dx) + Math.abs(dz) <= 1) {
                baseCost = MOVEMENT_COST_FALL;
            }
        }
        // Movimento para baixo mais extremo (queda controlada)
        else if (dy <= -2 && dy >= -4) {
            if (Math.abs(dx) + Math.abs(dz) == 0) { // Apenas queda vertical
                baseCost = MOVEMENT_COST_FALL * Math.abs(dy);
            }
        }

        if (baseCost < 0) {
            return -1; // Movimento inválido
        }

        // Aplicar modificadores de terreno (baseado no Baritone)
        baseCost = applyTerrainModifiers(baseCost, from, to);

        return baseCost;
    }

    /**
     * Aplica modificadores de custo baseado no tipo de terreno
     */
    private double applyTerrainModifiers(double baseCost, BlockPos from, BlockPos to) {
        // Verificar bloco de destino
        Block destBlock = mc.theWorld.getBlockState(to).getBlock();
        Block destBlockBelow = mc.theWorld.getBlockState(to.down()).getBlock();

        // Água aumenta o custo
        if (destBlock == Blocks.water || destBlock == Blocks.flowing_water) {
            baseCost *= MOVEMENT_COST_WATER;
        }

        // Soul sand aumenta o custo
        if (destBlockBelow == Blocks.soul_sand) {
            baseCost *= MOVEMENT_COST_SOUL_SAND;
        }

        // Gelo reduz ligeiramente o custo (mais rápido)
        if (destBlockBelow == Blocks.ice || destBlockBelow == Blocks.packed_ice) {
            baseCost *= 0.9;
        }

        // Verificar se precisa colocar bloco (bridging)
        if (!canWalkOn(to.down()) && needsBridging(from, to)) {
            baseCost += MOVEMENT_COST_BRIDGE;
        }

        return baseCost;
    }

    /**
     * Verifica se o movimento precisa de bridging
     */
    private boolean needsBridging(BlockPos from, BlockPos to) {
        // Se não há chão sólido embaixo do destino, pode precisar de bridge
        if (!canWalkOn(to.down())) {
            // Verificar se é uma queda controlada ou se realmente precisa de bridge
            int heightDiff = to.getY() - from.getY();
            return heightDiff >= 0; // Não precisa de bridge para quedas
        }
        return false;
    }

    /**
     * Ajusta posição para seguir o terreno de forma avançada (baseado no Mucifex/Stevebot)
     */
    private BlockPos adjustPositionToTerrainAdvanced(BlockPos target, BlockPos from) {
        // Primeiro, tentar a altura original
        if (canStandAt(target)) {
            return target;
        }

        // Procurar o melhor chão seguindo o terreno natural
        BlockPos bestCandidate = findBestTerrainPosition(target, from);
        if (bestCandidate != null) {
            return bestCandidate;
        }

        // Se não encontrou, retornar posição original
        return target;
    }

    /**
     * Encontra a melhor posição seguindo o terreno natural
     */
    private BlockPos findBestTerrainPosition(BlockPos target, BlockPos from) {
        int fromY = from.getY();
        int targetX = target.getX();
        int targetZ = target.getZ();

        // Procurar em ordem de preferência: mesmo nível, um pouco acima, um pouco abaixo
        int[] yOffsets = {0, 1, -1, 2, -2, 3, -3};

        for (int yOffset : yOffsets) {
            BlockPos candidate = new BlockPos(targetX, fromY + yOffset, targetZ);

            // Verificar se pode ficar em pé nesta posição
            if (canStandAt(candidate)) {
                // Verificar se é uma transição natural do terreno
                if (isNaturalTerrainTransition(from, candidate)) {
                    return candidate;
                }
            }

            // Verificar se é um slab (meio bloco)
            BlockPos slabCandidate = checkForSlabPosition(candidate);
            if (slabCandidate != null && canStandAt(slabCandidate)) {
                return slabCandidate;
            }
        }

        return null;
    }

    /**
     * Verifica se há um slab na posição e retorna a posição ajustada
     */
    private BlockPos checkForSlabPosition(BlockPos pos) {
        Block blockBelow = mc.theWorld.getBlockState(pos.down()).getBlock();

        // Verificar se é um slab
        if (blockBelow instanceof BlockSlab) {
            BlockSlab slab = (BlockSlab) blockBelow;
            IBlockState state = mc.theWorld.getBlockState(pos.down());

            // Se é um slab inferior, a posição Y deve ser ajustada para +0.5
            if (!slab.isDouble() && state.getValue(BlockSlab.HALF) == BlockSlab.EnumBlockHalf.BOTTOM) {
                // Para pathfinding, tratamos como se fosse meio bloco acima
                return pos; // Mantém a posição, mas sabemos que é um slab
            }
        }

        return null;
    }

    /**
     * Conta paredes adjacentes (baseado no Mucifex)
     */
    private int countAdjacentWalls(BlockPos pos) {
        int wallCount = 0;

        // Verificar as 4 direções cardinais
        BlockPos[] adjacent = {
            pos.add(1, 0, 0),
            pos.add(-1, 0, 0),
            pos.add(0, 0, 1),
            pos.add(0, 0, -1)
        };

        for (BlockPos adjPos : adjacent) {
            Block block = mc.theWorld.getBlockState(adjPos).getBlock();
            if (!canWalkThrough(block)) {
                wallCount++;
            }
        }

        return wallCount;
    }
    
    /**
     * Verifica se pode ficar em pé em uma posição (baseado no Stevebot)
     */
    private boolean canStandAt(BlockPos pos) {
        if (mc.theWorld == null) return false;

        // Verificar se há chão sólido embaixo
        if (!canWalkOn(pos.down())) {
            return false;
        }

        // Verificar se há espaço para o player (2 blocos de altura)
        return canMoveThrough(pos, 2);
    }

    /**
     * Verifica se pode caminhar sobre um bloco (chão sólido)
     */
    private boolean canWalkOn(BlockPos pos) {
        Block block = mc.theWorld.getBlockState(pos).getBlock();

        // Ar não é chão
        if (block == Blocks.air) {
            return false;
        }

        // Líquidos não são chão sólido
        if (block == Blocks.water || block == Blocks.flowing_water ||
            block == Blocks.lava || block == Blocks.flowing_lava) {
            return false;
        }

        // Blocos que não são sólidos
        if (block == Blocks.leaves || block == Blocks.leaves2) {
            return false;
        }

        // Verificar se é um bloco sólido
        return block.isFullBlock() || block instanceof BlockSlab ||
               block instanceof BlockStairs || block instanceof BlockFarmland;
    }

    /**
     * Verifica se pode mover através de uma posição com altura específica
     */
    private boolean canMoveThrough(BlockPos pos, int height) {
        for (int i = 0; i < height; i++) {
            BlockPos checkPos = pos.up(i);
            Block block = mc.theWorld.getBlockState(checkPos).getBlock();

            if (!canWalkThrough(block)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Verifica se uma transição de terreno é natural
     */
    private boolean isNaturalTerrainTransition(BlockPos from, BlockPos to) {
        int heightDiff = Math.abs(to.getY() - from.getY());

        // Transições muito extremas não são naturais
        if (heightDiff > 3) {
            return false;
        }

        // Verificar se não há obstáculos no caminho
        return hasLineOfSight(from, to);
    }

    /**
     * Verifica se há linha de visão entre duas posições
     */
    private boolean hasLineOfSight(BlockPos from, BlockPos to) {
        Vec3 fromVec = new Vec3(from.getX() + 0.5, from.getY() + 0.5, from.getZ() + 0.5);
        Vec3 toVec = new Vec3(to.getX() + 0.5, to.getY() + 0.5, to.getZ() + 0.5);

        double distance = fromVec.distanceTo(toVec);
        int steps = (int) Math.ceil(distance * 2); // Mais passos para melhor precisão

        for (int i = 1; i < steps; i++) {
            double t = (double) i / steps;
            Vec3 direction = toVec.subtract(fromVec);
            Vec3 scaledDirection = new Vec3(direction.xCoord * t, direction.yCoord * t, direction.zCoord * t);
            Vec3 point = fromVec.add(scaledDirection);
            BlockPos checkPos = new BlockPos(point);

            Block block = mc.theWorld.getBlockState(checkPos).getBlock();
            if (!canWalkThrough(block)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Verifica se pode atravessar de uma posição para outra (baseado no Mucifex)
     */
    private boolean canBeTraversed(BlockPos from, BlockPos to) {
        try {
            // Verificar se a posição atual e o espaço da cabeça estão livres
            if (!canMoveThrough(to, 2)) {
                return false;
            }

            // Verificar se há chão sólido para ficar em pé
            if (canWalkOn(to.down())) {
                return true; // Movimento normal no chão
            }

            // Verificar se é um movimento de pulo válido
            if (to.getY() == from.getY() + 1) {
                return canJump(from, to);
            }

            // Verificar se é um movimento de queda válido
            if (to.getY() < from.getY()) {
                return canFall(from, to);
            }

            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Verifica se pode pular de uma posição para outra
     */
    private boolean canJump(BlockPos from, BlockPos to) {
        // Verificar se há espaço para pular (3 blocos de altura)
        if (!canMoveThrough(from, 3)) {
            return false;
        }

        // Verificar se o destino é válido
        return canStandAt(to);
    }

    /**
     * Verifica se pode cair de uma posição para outra
     */
    private boolean canFall(BlockPos from, BlockPos to) {
        int fallDistance = from.getY() - to.getY();

        // Limitar queda máxima para evitar dano
        if (fallDistance > 3) {
            return false;
        }

        // Verificar se o destino é válido
        return canStandAt(to);
    }

    /**
     * Verifica se pode caminhar através de um bloco
     */
    private boolean canWalkThrough(Block block) {
        // Ar é sempre passável
        if (block == Blocks.air) {
            return true;
        }

        // Blocos especiais que são passáveis (baseado no Baritone)
        if (block == Blocks.fire ||
            block == Blocks.tripwire ||
            block == Blocks.web ||
            block == Blocks.vine ||
            block == Blocks.ladder ||
            block == Blocks.waterlily ||
            block == Blocks.carpet ||
            block == Blocks.snow_layer ||
            block == Blocks.tallgrass ||
            block == Blocks.double_plant ||
            block == Blocks.red_flower ||
            block == Blocks.yellow_flower) {
            return true;
        }

        // Folhas são passáveis
        if (block == Blocks.leaves || block == Blocks.leaves2) {
            return true;
        }

        // Portas e portões (podem ser abertos)
        if (block instanceof net.minecraft.block.BlockDoor ||
            block instanceof net.minecraft.block.BlockFenceGate ||
            block instanceof net.minecraft.block.BlockTrapDoor) {
            return true;
        }

        // Líquidos são passáveis mas com custo (água)
        if (block == Blocks.water || block == Blocks.flowing_water) {
            return true; // Água é passável mas com custo maior
        }

        // Lava e blocos perigosos não são passáveis
        if (block == Blocks.lava || block == Blocks.flowing_lava ||
            block == Blocks.cactus) {
            return false;
        }

        // Usar verificação do Minecraft para outros blocos
        return !block.getMaterial().blocksMovement() || !block.isFullBlock();
    }

    /**
     * Verifica se um bloco é válido como chão
     */
    private boolean isValidGroundBlock(BlockPos pos) {
        Block block = mc.theWorld.getBlockState(pos).getBlock();

        // Ar não é chão válido
        if (block == Blocks.air) {
            return false;
        }

        // Líquidos não são chão válido
        if (block == Blocks.water || block == Blocks.flowing_water ||
            block == Blocks.lava || block == Blocks.flowing_lava) {
            return false;
        }

        // Blocos perigosos não são chão válido
        if (block == Blocks.fire || block == Blocks.cactus) {
            return false;
        }

        // Verificar se é um bloco sólido
        return block.getMaterial().blocksMovement() && block.isFullBlock();
    }
    

    
    /**
     * Encontra posição caminhável mais próxima seguindo o terreno (baseado no Mucifex)
     */
    private BlockPos findNearestWalkable(BlockPos center) {
        // Usar altura do player como referência
        int playerY = mc.thePlayer != null ? (int) mc.thePlayer.posY : center.getY();

        // Primeiro, procurar o chão mais próximo na posição central
        BlockPos groundAtCenter = findGroundNear(center, playerY, 10);
        if (groundAtCenter != null && canStandAt(groundAtCenter)) {
            return groundAtCenter;
        }

        // Procurar em círculos concêntricos, priorizando terreno próximo
        for (int radius = 1; radius <= 10; radius++) {
            for (int x = -radius; x <= radius; x++) {
                for (int z = -radius; z <= radius; z++) {
                    // Só verificar a borda do círculo atual
                    if (Math.abs(x) == radius || Math.abs(z) == radius) {
                        BlockPos basePos = new BlockPos(center.getX() + x, playerY, center.getZ() + z);

                        // Procurar chão próximo desta posição
                        BlockPos groundPos = findGroundNear(basePos, playerY, 8);
                        if (groundPos != null && canStandAt(groundPos)) {
                            return groundPos;
                        }
                    }
                }
            }
        }

        return null;
    }

    /**
     * Encontra chão próximo a uma posição (inspirado no Mucifex)
     */
    private BlockPos findGroundNear(BlockPos pos, int referenceY, int range) {
        int bestY = -1;
        int minHeightDiff = Integer.MAX_VALUE;

        // Procurar em um range ao redor da altura de referência
        for (int y = referenceY - range; y <= referenceY + range; y++) {
            if (y < 1 || y > 255) continue;

            BlockPos checkPos = new BlockPos(pos.getX(), y, pos.getZ());

            if (isValidGroundBlock(checkPos)) {
                int heightDiff = Math.abs(y - referenceY);

                // Preferir o chão mais próximo da altura atual
                if (heightDiff < minHeightDiff) {
                    minHeightDiff = heightDiff;
                    bestY = y;
                }
            }
        }

        if (bestY != -1) {
            return new BlockPos(pos.getX(), bestY + 1, pos.getZ()); // Uma posição acima do chão
        }

        return null;
    }
    
    /**
     * Calcula distância de Manhattan (heurística admissível)
     */
    private double manhattanDistance(BlockPos a, BlockPos b) {
        return Math.abs(a.getX() - b.getX()) + 
               Math.abs(a.getY() - b.getY()) + 
               Math.abs(a.getZ() - b.getZ());
    }
    
    /**
     * Reconstrói o caminho a partir do nó final
     */
    private List<Vec3> reconstructPath(PathNode finalNode) {
        List<Vec3> path = new ArrayList<>();
        PathNode current = finalNode;
        
        while (current != null) {
            path.add(new Vec3(current.position.getX() + 0.5, 
                            current.position.getY(), 
                            current.position.getZ() + 0.5));
            current = current.parent;
        }
        
        Collections.reverse(path);
        return smoothPath(path);
    }
    
    /**
     * Suaviza o caminho removendo waypoints desnecessários
     */
    private List<Vec3> smoothPath(List<Vec3> originalPath) {
        if (originalPath.size() <= 2) return originalPath;
        
        List<Vec3> smoothedPath = new ArrayList<>();
        smoothedPath.add(originalPath.get(0));
        
        int current = 0;
        while (current < originalPath.size() - 1) {
            int farthest = current + 1;
            
            // Encontrar o ponto mais distante com linha de visão clara
            for (int i = current + 2; i < originalPath.size(); i++) {
                if (hasLineOfSight(originalPath.get(current), originalPath.get(i))) {
                    farthest = i;
                } else {
                    break;
                }
            }
            
            smoothedPath.add(originalPath.get(farthest));
            current = farthest;
        }
        
        return smoothedPath;
    }
    
    /**
     * Verifica linha de visão entre dois pontos
     */
    private boolean hasLineOfSight(Vec3 from, Vec3 to) {
        // Implementação simplificada - pode ser melhorada
        double distance = from.distanceTo(to);
        int steps = (int) Math.ceil(distance);
        
        for (int i = 0; i <= steps; i++) {
            double t = (double) i / steps;
            Vec3 direction = to.subtract(from);
            Vec3 scaledDirection = new Vec3(direction.xCoord * t, direction.yCoord * t, direction.zCoord * t);
            Vec3 point = from.add(scaledDirection);
            BlockPos blockPos = new BlockPos(point);

            if (!canStandAt(blockPos)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Resultado da análise de movimento
     */
    private static class MovementResult {
        boolean isValid = false;
        double cost = 0.0;
        BlockPos adjustedPosition = null;
    }
}
