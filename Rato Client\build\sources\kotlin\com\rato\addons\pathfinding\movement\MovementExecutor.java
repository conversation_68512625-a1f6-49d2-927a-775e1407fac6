package com.rato.addons.pathfinding.movement;

import com.rato.addons.pathfinding.BaritoneStylePathfinder;
import com.rato.addons.util.Logger;
import net.minecraft.client.Minecraft;
import net.minecraft.client.settings.KeyBinding;
import net.minecraft.util.BlockPos;
import net.minecraft.util.MathHelper;
import net.minecraft.util.Vec3;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;

import java.util.List;

/**
 * Executor de movimento baseado no Baritone - Versão Corrigida
 * Movimento suave, rotação natural, detecção dinâmica de nodes
 */
public class MovementExecutor {

    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Estado do movimento
    private List<BaritoneStylePathfinder.BaritoneNode> currentPath;
    private int currentNodeIndex = 0;
    private boolean isExecuting = false;
    private boolean registered = false;
    
    // Configurações de movimento (baseado no Baritone)
    private float movementSpeed = 1.0f;
    private float rotationSpeed = 3.0f; // Muito mais suave
    private double arrivalThreshold = 0.8; // Mais tolerante para evitar nodes pulados
    private boolean sprintEnabled = true;
    private boolean jumpEnabled = true;

    // Controle de rotação suave (baseado no Baritone)
    private float targetYaw;
    private float targetPitch;
    private boolean needsRotation = false;
    private boolean smoothRotation = true;
    
    // Detecção de travamento
    private Vec3 lastPosition;
    private long lastMovementTime;
    private int stuckCounter = 0;
    private static final int MAX_STUCK_TICKS = 60; // 3 segundos
    
    // Controle de pulo
    private boolean shouldJump = false;
    private int jumpCooldown = 0;
    
    /**
     * Inicia execução do caminho
     */
    public void startExecution(List<BaritoneStylePathfinder.BaritoneNode> path) {
        if (path == null || path.isEmpty()) {
            Logger.sendMessage("§cCaminho vazio para executar!");
            return;
        }
        
        this.currentPath = path;
        this.currentNodeIndex = 0;
        this.isExecuting = true;
        this.stuckCounter = 0;
        this.lastPosition = mc.thePlayer.getPositionVector();
        this.lastMovementTime = System.currentTimeMillis();
        
        if (!registered) {
            MinecraftForge.EVENT_BUS.register(this);
            registered = true;
        }
        
        Logger.sendMessage("§a[MovementExecutor] Iniciando execução do caminho com " + path.size() + " nodes");
    }
    
    /**
     * Para a execução
     */
    public void stopExecution() {
        if (!isExecuting) return;
        
        isExecuting = false;
        currentPath = null;
        currentNodeIndex = 0;
        
        // Parar todos os inputs
        releaseAllKeys();
        
        if (registered) {
            MinecraftForge.EVENT_BUS.unregister(this);
            registered = false;
        }
        
        Logger.sendMessage("§c[MovementExecutor] Execução parada");
    }
    
    /**
     * Tick principal do movimento
     */
    @SubscribeEvent
    public void onClientTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.START || !isExecuting || mc.thePlayer == null) {
            return;
        }
        
        try {
            // Verificar se chegou ao final
            if (currentNodeIndex >= currentPath.size()) {
                Logger.sendMessage("§a[MovementExecutor] Caminho completado!");
                stopExecution();
                return;
            }
            
            // Obter node atual
            BaritoneStylePathfinder.BaritoneNode currentNode = currentPath.get(currentNodeIndex);
            Vec3 targetPos = new Vec3(currentNode.position.getX() + 0.5, 
                                    currentNode.position.getY(), 
                                    currentNode.position.getZ() + 0.5);
            
            Vec3 playerPos = mc.thePlayer.getPositionVector();
            
            // Verificar se chegou no node atual
            if (hasReachedNode(playerPos, targetPos, currentNode)) {
                currentNodeIndex++;
                if (currentNodeIndex < currentPath.size()) {
                    Logger.sendMessage("§7[MovementExecutor] Node " + currentNodeIndex + "/" + currentPath.size() + 
                                     " (" + currentNode.movementType.getName() + ")");
                }
                return;
            }
            
            // Executar movimento baseado no tipo
            executeMovement(currentNode, targetPos, playerPos);
            
            // Verificar se está travado
            checkStuckDetection(playerPos);
            
            // Reduzir cooldown de pulo
            if (jumpCooldown > 0) {
                jumpCooldown--;
            }
            
        } catch (Exception e) {
            Logger.sendMessage("§c[MovementExecutor] Erro: " + e.getMessage());
            stopExecution();
        }
    }
    
    /**
     * Executa movimento baseado no tipo do node
     */
    private void executeMovement(BaritoneStylePathfinder.BaritoneNode node, Vec3 targetPos, Vec3 playerPos) {
        // Calcular rotação necessária
        calculateRotation(targetPos, playerPos);
        
        // Aplicar rotação suave
        if (needsRotation) {
            applyRotation();
        }
        
        // Executar movimento específico
        switch (node.movementType) {
            case TRAVERSE:
                executeTraverse(targetPos, playerPos);
                break;
            case ASCEND:
                executeAscend(targetPos, playerPos);
                break;
            case DESCEND:
                executeDescend(targetPos, playerPos);
                break;
            case DIAGONAL:
                executeDiagonal(targetPos, playerPos);
                break;
            case FALL:
                executeFall(targetPos, playerPos);
                break;
        }
    }
    
    /**
     * Movimento horizontal normal
     */
    private void executeTraverse(Vec3 targetPos, Vec3 playerPos) {
        setKeyPressed(mc.gameSettings.keyBindForward, true);
        
        if (sprintEnabled && !isInWater()) {
            setKeyPressed(mc.gameSettings.keyBindSprint, true);
        }
        
        // Ajustar movimento lateral se necessário
        adjustLateralMovement(targetPos, playerPos);
    }
    
    /**
     * Movimento de pulo (ascend)
     */
    private void executeAscend(Vec3 targetPos, Vec3 playerPos) {
        setKeyPressed(mc.gameSettings.keyBindForward, true);
        
        // Pular se necessário e não está em cooldown
        if (jumpEnabled && jumpCooldown <= 0 && shouldJumpNow(targetPos, playerPos)) {
            setKeyPressed(mc.gameSettings.keyBindJump, true);
            jumpCooldown = 10; // Cooldown de meio segundo
        }
        
        adjustLateralMovement(targetPos, playerPos);
    }
    
    /**
     * Movimento de descida
     */
    private void executeDescend(Vec3 targetPos, Vec3 playerPos) {
        setKeyPressed(mc.gameSettings.keyBindForward, true);
        
        // Não sprintar em descidas para melhor controle
        setKeyPressed(mc.gameSettings.keyBindSprint, false);
        
        adjustLateralMovement(targetPos, playerPos);
    }
    
    /**
     * Movimento diagonal
     */
    private void executeDiagonal(Vec3 targetPos, Vec3 playerPos) {
        setKeyPressed(mc.gameSettings.keyBindForward, true);
        
        if (sprintEnabled && !isInWater()) {
            setKeyPressed(mc.gameSettings.keyBindSprint, true);
        }
        
        // Movimento diagonal precisa de ajuste lateral mais preciso
        adjustLateralMovement(targetPos, playerPos);
    }
    
    /**
     * Movimento de queda
     */
    private void executeFall(Vec3 targetPos, Vec3 playerPos) {
        setKeyPressed(mc.gameSettings.keyBindForward, true);
        
        // Não sprintar em quedas
        setKeyPressed(mc.gameSettings.keyBindSprint, false);
        
        adjustLateralMovement(targetPos, playerPos);
    }
    
    /**
     * Ajusta movimento lateral (A/D) baseado na posição
     */
    private void adjustLateralMovement(Vec3 targetPos, Vec3 playerPos) {
        double deltaX = targetPos.xCoord - playerPos.xCoord;
        double deltaZ = targetPos.zCoord - playerPos.zCoord;
        
        // Calcular ângulo relativo à rotação atual
        float yaw = mc.thePlayer.rotationYaw;
        double relativeX = deltaX * Math.cos(Math.toRadians(-yaw)) - deltaZ * Math.sin(Math.toRadians(-yaw));
        double relativeZ = deltaX * Math.sin(Math.toRadians(-yaw)) + deltaZ * Math.cos(Math.toRadians(-yaw));
        
        // Ajustar movimento lateral se necessário
        if (Math.abs(relativeX) > 0.1) {
            if (relativeX > 0) {
                setKeyPressed(mc.gameSettings.keyBindRight, true);
                setKeyPressed(mc.gameSettings.keyBindLeft, false);
            } else {
                setKeyPressed(mc.gameSettings.keyBindLeft, true);
                setKeyPressed(mc.gameSettings.keyBindRight, false);
            }
        } else {
            setKeyPressed(mc.gameSettings.keyBindLeft, false);
            setKeyPressed(mc.gameSettings.keyBindRight, false);
        }
    }
    
    /**
     * Calcula rotação necessária para o target
     */
    private void calculateRotation(Vec3 targetPos, Vec3 playerPos) {
        double deltaX = targetPos.xCoord - playerPos.xCoord;
        double deltaZ = targetPos.zCoord - playerPos.zCoord;
        double deltaY = targetPos.yCoord - (playerPos.yCoord + mc.thePlayer.getEyeHeight());
        
        double horizontalDistance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
        
        targetYaw = (float) (Math.atan2(deltaZ, deltaX) * 180.0 / Math.PI) - 90.0f;
        targetPitch = (float) -(Math.atan2(deltaY, horizontalDistance) * 180.0 / Math.PI);
        
        // Normalizar yaw
        while (targetYaw > 180.0f) targetYaw -= 360.0f;
        while (targetYaw < -180.0f) targetYaw += 360.0f;
        
        // Verificar se precisa rotacionar
        float yawDiff = Math.abs(MathHelper.wrapAngleTo180_float(targetYaw - mc.thePlayer.rotationYaw));
        needsRotation = yawDiff > 5.0f; // Só rotacionar se diferença for significativa
    }
    
    /**
     * Aplica rotação suave
     */
    private void applyRotation() {
        float currentYaw = mc.thePlayer.rotationYaw;
        float currentPitch = mc.thePlayer.rotationPitch;
        
        float yawDiff = MathHelper.wrapAngleTo180_float(targetYaw - currentYaw);
        float pitchDiff = targetPitch - currentPitch;
        
        // Aplicar rotação suave
        float maxRotationSpeed = rotationSpeed;
        float yawChange = MathHelper.clamp_float(yawDiff, -maxRotationSpeed, maxRotationSpeed);
        float pitchChange = MathHelper.clamp_float(pitchDiff, -maxRotationSpeed, maxRotationSpeed);
        
        mc.thePlayer.rotationYaw += yawChange;
        mc.thePlayer.rotationPitch += pitchChange;
        
        // Limitar pitch
        mc.thePlayer.rotationPitch = MathHelper.clamp_float(mc.thePlayer.rotationPitch, -90.0f, 90.0f);
    }
    
    /**
     * Verifica se chegou no node
     */
    private boolean hasReachedNode(Vec3 playerPos, Vec3 targetPos, BaritoneStylePathfinder.BaritoneNode node) {
        double horizontalDistance = Math.sqrt(
            Math.pow(targetPos.xCoord - playerPos.xCoord, 2) + 
            Math.pow(targetPos.zCoord - playerPos.zCoord, 2)
        );
        
        double verticalDistance = Math.abs(targetPos.yCoord - playerPos.yCoord);
        
        // Diferentes thresholds para diferentes tipos de movimento
        double horizontalThreshold = arrivalThreshold;
        double verticalThreshold = 1.5;
        
        switch (node.movementType) {
            case FALL:
                verticalThreshold = 0.5; // Mais preciso para quedas
                break;
            case ASCEND:
                verticalThreshold = 1.0; // Menos restritivo para pulos
                break;
        }
        
        return horizontalDistance <= horizontalThreshold && verticalDistance <= verticalThreshold;
    }
    
    /**
     * Verifica se deve pular agora
     */
    private boolean shouldJumpNow(Vec3 targetPos, Vec3 playerPos) {
        // Pular se target está acima e player está no chão
        return targetPos.yCoord > playerPos.yCoord + 0.5 && mc.thePlayer.onGround;
    }
    
    /**
     * Verifica se está na água
     */
    private boolean isInWater() {
        return mc.thePlayer.isInWater();
    }
    
    /**
     * Detecção de travamento
     */
    private void checkStuckDetection(Vec3 currentPos) {
        if (lastPosition != null) {
            double distance = currentPos.distanceTo(lastPosition);
            
            if (distance < 0.1) { // Não se moveu muito
                stuckCounter++;
                if (stuckCounter >= MAX_STUCK_TICKS) {
                    Logger.sendMessage("§e[MovementExecutor] Detectado travamento, tentando contornar...");
                    handleStuckSituation();
                    stuckCounter = 0;
                }
            } else {
                stuckCounter = 0;
                lastMovementTime = System.currentTimeMillis();
            }
        }
        
        lastPosition = currentPos;
    }
    
    /**
     * Lida com situação de travamento
     */
    private void handleStuckSituation() {
        // Tentar pular para sair do travamento
        if (jumpEnabled && mc.thePlayer.onGround) {
            setKeyPressed(mc.gameSettings.keyBindJump, true);
        }
        
        // Tentar movimento lateral
        setKeyPressed(mc.gameSettings.keyBindLeft, true);
        
        // Agendar para parar movimento lateral
        new Thread(() -> {
            try {
                Thread.sleep(500);
                setKeyPressed(mc.gameSettings.keyBindLeft, false);
            } catch (InterruptedException e) {
                // Ignore
            }
        }).start();
    }
    
    /**
     * Define estado de uma tecla
     */
    private void setKeyPressed(KeyBinding key, boolean pressed) {
        if (key.isPressed() != pressed) {
            if (pressed) {
                key.onTick(key.getKeyCode());
            }
            KeyBinding.setKeyBindState(key.getKeyCode(), pressed);
        }
    }
    
    /**
     * Libera todas as teclas
     */
    private void releaseAllKeys() {
        setKeyPressed(mc.gameSettings.keyBindForward, false);
        setKeyPressed(mc.gameSettings.keyBindBack, false);
        setKeyPressed(mc.gameSettings.keyBindLeft, false);
        setKeyPressed(mc.gameSettings.keyBindRight, false);
        setKeyPressed(mc.gameSettings.keyBindJump, false);
        setKeyPressed(mc.gameSettings.keyBindSprint, false);
        setKeyPressed(mc.gameSettings.keyBindSneak, false);
    }
    
    /**
     * Verifica se está executando
     */
    public boolean isExecuting() {
        return isExecuting;
    }
    
    /**
     * Configurações
     */
    public void setMovementSpeed(float speed) {
        this.movementSpeed = speed;
    }
    
    public void setRotationSpeed(float speed) {
        this.rotationSpeed = speed;
    }
    
    public void setSprintEnabled(boolean enabled) {
        this.sprintEnabled = enabled;
    }
    
    public void setJumpEnabled(boolean enabled) {
        this.jumpEnabled = enabled;
    }
}
