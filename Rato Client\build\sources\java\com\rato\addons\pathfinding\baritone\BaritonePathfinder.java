package com.rato.addons.pathfinding.baritone;

import com.rato.addons.pathfinding.professional.PathNode;
import com.rato.addons.util.Logger;
import net.minecraft.block.Block;
import net.minecraft.client.Minecraft;
import net.minecraft.init.Blocks;
import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;
import net.minecraft.world.World;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Sistema de pathfinding baseado no Baritone
 * Implementa cálculo segmentado, cache de chunks e A* otimizado
 */
public class BaritonePathfinder {
    
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Configurações do algoritmo
    private static final int SEGMENT_LENGTH = 100; // Tamanho de cada segmento
    private static final int MAX_CALCULATION_TIME_MS = 2000; // Timeout por segmento
    private static final int RENDER_DISTANCE_LIMIT = 16; // Limite de render distance
    private static final double MINIMUM_IMPROVEMENT = 0.01; // Melhoria mínima para repropagação
    
    // Cache de chunks
    private final ChunkCache chunkCache = new ChunkCache();
    
    // Estado do pathfinding
    private volatile boolean isCalculating = false;
    private volatile boolean shouldStop = false;
    private PathSegment currentSegment;
    private Queue<PathSegment> segmentQueue = new LinkedList<>();
    
    // Thread de cálculo
    private Thread calculationThread;
    
    // Callbacks
    private Runnable onPathFound;
    private Runnable onPathNotFound;
    private Runnable onSegmentComplete;
    
    /**
     * Representa um segmento de caminho
     */
    public static class PathSegment {
        public final Vec3 start;
        public final Vec3 end;
        public final List<PathNode> nodes;
        public final boolean isComplete;
        public final double totalCost;
        
        public PathSegment(Vec3 start, Vec3 end, List<PathNode> nodes, boolean isComplete, double totalCost) {
            this.start = start;
            this.end = end;
            this.nodes = nodes != null ? new ArrayList<>(nodes) : new ArrayList<>();
            this.isComplete = isComplete;
            this.totalCost = totalCost;
        }
    }
    
    /**
     * Cache de chunks simplificado (AIR, SOLID, WATER, AVOID)
     */
    public static class ChunkCache {
        private final Map<Long, CachedChunk> cache = new ConcurrentHashMap<>();
        private static final long CACHE_LIFETIME = 300000; // 5 minutos
        
        public enum BlockType {
            AIR(0),
            SOLID(1),
            WATER(2),
            AVOID(3);
            
            public final int value;
            BlockType(int value) { this.value = value; }
        }
        
        public static class CachedChunk {
            private final byte[] data = new byte[16 * 16 * 256]; // 2 bits por bloco compactado
            private final long timestamp = System.currentTimeMillis();
            
            public void setBlock(int x, int y, int z, BlockType type) {
                int index = (y * 16 + z) * 16 + x;
                if (index >= 0 && index < data.length) {
                    data[index] = (byte) type.value;
                }
            }
            
            public BlockType getBlock(int x, int y, int z) {
                int index = (y * 16 + z) * 16 + x;
                if (index >= 0 && index < data.length) {
                    int value = data[index] & 0xFF;
                    for (BlockType type : BlockType.values()) {
                        if (type.value == value) return type;
                    }
                }
                return BlockType.AIR;
            }
            
            public boolean isExpired() {
                return System.currentTimeMillis() - timestamp > CACHE_LIFETIME;
            }
        }
        
        public BlockType getBlockType(BlockPos pos) {
            long chunkKey = getChunkKey(pos.getX() >> 4, pos.getZ() >> 4);
            CachedChunk chunk = cache.get(chunkKey);
            
            if (chunk == null || chunk.isExpired()) {
                chunk = cacheChunk(pos.getX() >> 4, pos.getZ() >> 4);
                cache.put(chunkKey, chunk);
            }
            
            return chunk.getBlock(pos.getX() & 15, pos.getY(), pos.getZ() & 15);
        }
        
        private CachedChunk cacheChunk(int chunkX, int chunkZ) {
            CachedChunk chunk = new CachedChunk();
            World world = Minecraft.getMinecraft().theWorld;
            
            if (world == null) return chunk;
            
            for (int x = 0; x < 16; x++) {
                for (int z = 0; z < 16; z++) {
                    for (int y = 0; y < 256; y++) {
                        BlockPos pos = new BlockPos(chunkX * 16 + x, y, chunkZ * 16 + z);
                        Block block = world.getBlockState(pos).getBlock();
                        chunk.setBlock(x, y, z, classifyBlock(block));
                    }
                }
            }
            
            return chunk;
        }
        
        private BlockType classifyBlock(Block block) {
            if (block == Blocks.air) return BlockType.AIR;
            if (block == Blocks.water || block == Blocks.flowing_water) return BlockType.WATER;
            if (block == Blocks.lava || block == Blocks.flowing_lava || 
                block == Blocks.fire || block == Blocks.cactus) return BlockType.AVOID;
            if (block.getMaterial().isSolid()) return BlockType.SOLID;
            return BlockType.AIR;
        }
        
        private long getChunkKey(int x, int z) {
            return ((long) x << 32) | (z & 0xFFFFFFFFL);
        }
        
        public void clearExpired() {
            cache.entrySet().removeIf(entry -> entry.getValue().isExpired());
        }
        
        public void clear() {
            cache.clear();
        }
    }
    
    /**
     * Inicia pathfinding segmentado para um destino
     */
    public boolean startPathfinding(Vec3 start, Vec3 goal) {
        if (isCalculating) {
            stopPathfinding();
        }
        
        shouldStop = false;
        segmentQueue.clear();
        
        // Calcular segmentos necessários
        List<Vec3> waypoints = calculateWaypoints(start, goal);
        
        for (int i = 0; i < waypoints.size() - 1; i++) {
            Vec3 segmentStart = waypoints.get(i);
            Vec3 segmentEnd = waypoints.get(i + 1);
            segmentQueue.offer(new PathSegment(segmentStart, segmentEnd, null, false, 0));
        }
        
        // Iniciar cálculo do primeiro segmento
        if (!segmentQueue.isEmpty()) {
            calculateNextSegment();
            return true;
        }
        
        return false;
    }
    
    /**
     * Calcula waypoints intermediários para segmentação
     */
    private List<Vec3> calculateWaypoints(Vec3 start, Vec3 goal) {
        List<Vec3> waypoints = new ArrayList<>();
        waypoints.add(start);
        
        double distance = start.distanceTo(goal);
        if (distance <= SEGMENT_LENGTH) {
            waypoints.add(goal);
            return waypoints;
        }
        
        // Calcular waypoints intermediários
        Vec3 direction = goal.subtract(start).normalize();
        int segments = (int) Math.ceil(distance / SEGMENT_LENGTH);
        
        for (int i = 1; i < segments; i++) {
            double segmentDistance = i * SEGMENT_LENGTH;
            Vec3 waypoint = start.addVector(
                direction.xCoord * segmentDistance,
                direction.yCoord * segmentDistance,
                direction.zCoord * segmentDistance
            );
            waypoints.add(waypoint);
        }
        
        waypoints.add(goal);
        return waypoints;
    }
    
    /**
     * Calcula o próximo segmento em thread separada
     */
    private void calculateNextSegment() {
        if (segmentQueue.isEmpty()) {
            if (onPathFound != null) onPathFound.run();
            return;
        }
        
        PathSegment segment = segmentQueue.poll();
        isCalculating = true;
        
        calculationThread = new Thread(() -> {
            try {
                PathSegment result = calculateSegmentPath(segment);
                
                if (!shouldStop) {
                    currentSegment = result;
                    
                    if (onSegmentComplete != null) {
                        onSegmentComplete.run();
                    }
                    
                    // Calcular próximo segmento
                    calculateNextSegment();
                }
            } catch (Exception e) {
                Logger.sendMessage("§c[Baritone] Erro no cálculo: " + e.getMessage());
                if (onPathNotFound != null) onPathNotFound.run();
            } finally {
                isCalculating = false;
            }
        });
        
        calculationThread.setDaemon(true);
        calculationThread.start();
    }
    
    /**
     * Calcula caminho para um segmento usando A* otimizado
     */
    private PathSegment calculateSegmentPath(PathSegment segment) {
        long startTime = System.currentTimeMillis();
        
        // Estruturas do A*
        PriorityQueue<BaritoneNode> openSet = new PriorityQueue<>();
        Set<BlockPos> closedSet = new HashSet<>();
        Map<BlockPos, BaritoneNode> allNodes = new HashMap<>();
        
        // Nó inicial
        BlockPos startPos = new BlockPos(segment.start);
        BlockPos goalPos = new BlockPos(segment.end);
        
        BaritoneNode startNode = new BaritoneNode(startPos, 0, heuristic(startPos, goalPos), null);
        openSet.add(startNode);
        allNodes.put(startPos, startNode);
        
        BaritoneNode bestNode = startNode;
        int edgeOfLoadedChunks = 0;
        
        while (!openSet.isEmpty() && !shouldStop) {
            // Verificar timeout
            if (System.currentTimeMillis() - startTime > MAX_CALCULATION_TIME_MS) {
                break;
            }
            
            BaritoneNode current = openSet.poll();
            
            // Atualizar melhor nó
            if (current.hCost < bestNode.hCost) {
                bestNode = current;
            }
            
            // Verificar se chegou ao objetivo
            if (current.pos.equals(goalPos) || current.pos.distanceSq(goalPos) <= 2) {
                List<PathNode> path = reconstructPath(current);
                return new PathSegment(segment.start, segment.end, path, true, current.gCost);
            }
            
            closedSet.add(current.pos);
            
            // Explorar vizinhos
            for (BlockPos neighbor : getNeighbors(current.pos)) {
                if (shouldStop || closedSet.contains(neighbor)) continue;
                
                // Verificar se está na borda dos chunks carregados
                if (isAtEdgeOfLoadedChunks(neighbor)) {
                    edgeOfLoadedChunks++;
                    if (edgeOfLoadedChunks > 50) break; // Limite configurável
                }
                
                double movementCost = getMovementCost(current.pos, neighbor);
                if (movementCost < 0) continue; // Movimento inválido
                
                double tentativeGCost = current.gCost + movementCost;
                
                BaritoneNode existingNode = allNodes.get(neighbor);
                if (existingNode != null && tentativeGCost >= existingNode.gCost + MINIMUM_IMPROVEMENT) {
                    continue; // Melhoria insuficiente
                }
                
                double hCost = heuristic(neighbor, goalPos);
                BaritoneNode neighborNode;
                
                if (existingNode != null) {
                    existingNode.gCost = tentativeGCost;
                    existingNode.fCost = tentativeGCost + hCost;
                    existingNode.parent = current;
                    neighborNode = existingNode;
                } else {
                    neighborNode = new BaritoneNode(neighbor, tentativeGCost, hCost, current);
                    allNodes.put(neighbor, neighborNode);
                }
                
                if (!openSet.contains(neighborNode)) {
                    openSet.add(neighborNode);
                }
            }
        }
        
        // Retornar melhor caminho parcial
        List<PathNode> partialPath = reconstructPath(bestNode);
        return new PathSegment(segment.start, segment.end, partialPath, false, bestNode.gCost);
    }
    
    /**
     * Nó do A* otimizado para Baritone
     */
    private static class BaritoneNode implements Comparable<BaritoneNode> {
        public final BlockPos pos;
        public double gCost;
        public double hCost;
        public double fCost;
        public BaritoneNode parent;
        
        public BaritoneNode(BlockPos pos, double gCost, double hCost, BaritoneNode parent) {
            this.pos = pos;
            this.gCost = gCost;
            this.hCost = hCost;
            this.fCost = gCost + hCost;
            this.parent = parent;
        }
        
        @Override
        public int compareTo(BaritoneNode other) {
            int fCostComparison = Double.compare(this.fCost, other.fCost);
            if (fCostComparison != 0) return fCostComparison;
            return Double.compare(this.hCost, other.hCost);
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (!(obj instanceof BaritoneNode)) return false;
            return pos.equals(((BaritoneNode) obj).pos);
        }
        
        @Override
        public int hashCode() {
            return pos.hashCode();
        }
    }
    
    /**
     * Calcula heurística (distância Manhattan otimizada)
     */
    private double heuristic(BlockPos from, BlockPos to) {
        int dx = Math.abs(to.getX() - from.getX());
        int dy = Math.abs(to.getY() - from.getY());
        int dz = Math.abs(to.getZ() - from.getZ());
        
        // Heurística Manhattan com peso para movimento vertical
        return dx + dz + dy * 1.5;
    }
    
    /**
     * Obtém vizinhos válidos para um bloco
     */
    private List<BlockPos> getNeighbors(BlockPos pos) {
        List<BlockPos> neighbors = new ArrayList<>();
        
        // Movimentos horizontais
        neighbors.add(pos.add(1, 0, 0));
        neighbors.add(pos.add(-1, 0, 0));
        neighbors.add(pos.add(0, 0, 1));
        neighbors.add(pos.add(0, 0, -1));
        
        // Movimentos diagonais
        neighbors.add(pos.add(1, 0, 1));
        neighbors.add(pos.add(1, 0, -1));
        neighbors.add(pos.add(-1, 0, 1));
        neighbors.add(pos.add(-1, 0, -1));
        
        // Movimentos verticais
        neighbors.add(pos.add(0, 1, 0)); // Pulo
        neighbors.add(pos.add(0, -1, 0)); // Queda
        
        return neighbors;
    }
    
    /**
     * Calcula custo de movimento entre dois blocos com detecção inteligente de pulo
     */
    private double getMovementCost(BlockPos from, BlockPos to) {
        ChunkCache.BlockType fromType = chunkCache.getBlockType(from);
        ChunkCache.BlockType toType = chunkCache.getBlockType(to);
        ChunkCache.BlockType toGroundType = chunkCache.getBlockType(to.down());
        ChunkCache.BlockType toHeadType = chunkCache.getBlockType(to.up());

        // Verificar se o destino tem espaço para o player (2 blocos de altura)
        if (toType == ChunkCache.BlockType.SOLID || toHeadType == ChunkCache.BlockType.SOLID) {
            return -1; // Não pode mover para bloco sólido
        }

        if (toType == ChunkCache.BlockType.AVOID) return 100; // Evitar blocos perigosos

        // Calcular diferenças de posição
        int dx = to.getX() - from.getX();
        int dy = to.getY() - from.getY();
        int dz = to.getZ() - from.getZ();
        int horizontalDistance = Math.abs(dx) + Math.abs(dz);

        double baseCost = 1.0;

        // Analisar tipo de movimento necessário
        if (dy == 0) {
            // Movimento horizontal - verificar se precisa pular
            if (horizontalDistance == 1) {
                // Movimento direto - verificar obstáculo
                if (needsJumpForMovement(from, to)) {
                    baseCost = 1.5; // Custo de pulo
                } else {
                    baseCost = 1.0; // Movimento normal
                }
            } else if (horizontalDistance == 2) {
                baseCost = 1.414; // Movimento diagonal
            } else {
                return -1; // Movimento muito distante
            }
        } else if (dy == 1) {
            // Movimento para cima - sempre requer pulo
            if (horizontalDistance <= 1) {
                baseCost = 1.5; // Pulo vertical ou diagonal
            } else {
                return -1; // Pulo muito distante
            }
        } else if (dy == -1) {
            // Movimento para baixo - queda controlada
            if (horizontalDistance <= 1) {
                baseCost = 1.2; // Queda
            } else {
                return -1; // Queda muito distante
            }
        } else if (Math.abs(dy) > 1) {
            return -1; // Movimento vertical extremo
        }

        // Verificar se há chão sólido no destino
        if (toGroundType == ChunkCache.BlockType.AIR && toType != ChunkCache.BlockType.WATER) {
            // Verificar queda segura
            int fallDistance = 0;
            BlockPos checkPos = to.down();
            while (fallDistance < 4 && chunkCache.getBlockType(checkPos) == ChunkCache.BlockType.AIR) {
                fallDistance++;
                checkPos = checkPos.down();
            }
            if (fallDistance >= 4) return -1; // Queda muito alta
            if (fallDistance > 0) baseCost *= (1.0 + fallDistance * 0.2); // Penalidade por queda
        }

        // Modificadores por tipo de bloco
        if (toType == ChunkCache.BlockType.WATER) baseCost *= 1.8;

        return baseCost;
    }

    /**
     * Verifica se precisa pular para um movimento específico
     */
    private boolean needsJumpForMovement(BlockPos from, BlockPos to) {
        // Verificar se há obstáculo no caminho direto
        int dx = to.getX() - from.getX();
        int dz = to.getZ() - from.getZ();

        // Posição intermediária (bloco entre origem e destino)
        BlockPos intermediate = from.add(dx, 0, dz);
        ChunkCache.BlockType intermediateType = chunkCache.getBlockType(intermediate);

        // Se há bloco sólido no caminho, precisa pular
        if (intermediateType == ChunkCache.BlockType.SOLID) {
            // Verificar se pode pular por cima
            ChunkCache.BlockType aboveIntermediate = chunkCache.getBlockType(intermediate.up());
            return aboveIntermediate != ChunkCache.BlockType.SOLID;
        }

        // Verificar se o destino está um bloco acima
        ChunkCache.BlockType toGroundType = chunkCache.getBlockType(to.down());
        ChunkCache.BlockType fromGroundType = chunkCache.getBlockType(from.down());

        if (toGroundType == ChunkCache.BlockType.SOLID && fromGroundType == ChunkCache.BlockType.SOLID) {
            return to.getY() > from.getY(); // Precisa pular para subir
        }

        return false;
    }
    
    /**
     * Verifica se está na borda dos chunks carregados
     */
    private boolean isAtEdgeOfLoadedChunks(BlockPos pos) {
        if (mc.theWorld == null) return true;
        
        int renderDistance = mc.gameSettings.renderDistanceChunks;
        BlockPos playerPos = mc.thePlayer.getPosition();
        
        int chunkDistance = Math.max(
            Math.abs((pos.getX() >> 4) - (playerPos.getX() >> 4)),
            Math.abs((pos.getZ() >> 4) - (playerPos.getZ() >> 4))
        );
        
        return chunkDistance >= renderDistance - 2;
    }
    
    /**
     * Reconstrói caminho a partir do nó final
     */
    private List<PathNode> reconstructPath(BaritoneNode endNode) {
        List<PathNode> path = new ArrayList<>();
        BaritoneNode current = endNode;
        
        while (current != null) {
            Vec3 pos = new Vec3(current.pos.getX() + 0.5, current.pos.getY(), current.pos.getZ() + 0.5);
            PathNode pathNode = new PathNode(pos, current.gCost, current.hCost, null);
            path.add(0, pathNode);
            current = current.parent;
        }
        
        return path;
    }
    
    /**
     * Para o pathfinding atual
     */
    public void stopPathfinding() {
        shouldStop = true;
        isCalculating = false;
        
        if (calculationThread != null && calculationThread.isAlive()) {
            calculationThread.interrupt();
        }
        
        segmentQueue.clear();
        currentSegment = null;
    }
    
    // Getters e Setters
    public boolean isCalculating() { return isCalculating; }
    public PathSegment getCurrentSegment() { return currentSegment; }
    public ChunkCache getChunkCache() { return chunkCache; }
    
    public void setOnPathFound(Runnable callback) { this.onPathFound = callback; }
    public void setOnPathNotFound(Runnable callback) { this.onPathNotFound = callback; }
    public void setOnSegmentComplete(Runnable callback) { this.onSegmentComplete = callback; }
}
