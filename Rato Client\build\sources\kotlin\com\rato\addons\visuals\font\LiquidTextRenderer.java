package com.rato.addons.visuals.font;

import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.FontRenderer;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.Tessellator;
import net.minecraft.client.renderer.WorldRenderer;
import net.minecraft.client.renderer.vertex.DefaultVertexFormats;
import org.lwjgl.BufferUtils;
import org.lwjgl.opengl.GL11;

import java.awt.*;
import java.awt.font.FontRenderContext;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.nio.ByteBuffer;
import java.util.HashMap;
import java.util.Map;

/**
 * Sistema de renderização de texto baseado no LiquidBounce
 * Implementa cache de texturas e renderização suave
 */
public class LiquidTextRenderer {

    private static final Minecraft mc = Minecraft.getMinecraft();

    // Cache de fontes (estilo LiquidBounce)
    private static final Map<String, LiquidTextRenderer> fontCache = new HashMap<>();

    // Instâncias padrão com fontes de alta qualidade
    public static final LiquidTextRenderer DEFAULT = getFont("Segoe UI", Font.PLAIN, 18);
    public static final LiquidTextRenderer BOLD = getFont("Segoe UI", Font.BOLD, 18);
    public static final LiquidTextRenderer LARGE = getFont("Segoe UI", Font.PLAIN, 22);
    public static final LiquidTextRenderer SMALL = getFont("Segoe UI", Font.PLAIN, 14);

    private final Font font;
    private final FontMetrics fontMetrics;
    private final int fontHeight;
    private final boolean antiAlias;

    // Cache de texturas para caracteres (estilo LiquidBounce)
    private final Map<Character, CharTexture> charCache = new HashMap<>();

    private LiquidTextRenderer(Font font, boolean antiAlias) {
        this.font = font;
        this.antiAlias = antiAlias;

        // Obter métricas da fonte
        BufferedImage tempImg = new BufferedImage(1, 1, BufferedImage.TYPE_INT_ARGB);
        Graphics2D tempGraphics = tempImg.createGraphics();

        if (antiAlias) {
            tempGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            tempGraphics.setRenderingHint(RenderingHints.KEY_FRACTIONALMETRICS,
                    RenderingHints.VALUE_FRACTIONALMETRICS_ON);
        }

        tempGraphics.setFont(font);
        this.fontMetrics = tempGraphics.getFontMetrics();
        this.fontHeight = fontMetrics.getHeight();

        tempGraphics.dispose();
    }

    /**
     * Obtém uma instância de fonte (com cache)
     */
    public static LiquidTextRenderer getFont(String name, int style, int size) {
        String key = name + "_" + style + "_" + size;
        return fontCache.computeIfAbsent(key, k -> {
            Font font = new Font(name, style, size);
            return new LiquidTextRenderer(font, true);
        });
    }

    /**
     * Classe para armazenar dados de textura de caractere
     */
    private static class CharTexture {
        public final int textureId;
        public final int width;
        public final int height;

        public CharTexture(int textureId, int width, int height) {
            this.textureId = textureId;
            this.width = width;
            this.height = height;
        }
    }

    /**
     * Gera textura para um caractere (estilo LiquidBounce)
     */
    private CharTexture generateCharTexture(char character) {
        // Calcular dimensões do caractere
        Rectangle2D bounds = fontMetrics.getStringBounds(String.valueOf(character), null);
        int charWidth = (int) Math.ceil(bounds.getWidth()) + 8; // Padding
        int charHeight = (int) Math.ceil(bounds.getHeight()) + 8;

        if (charWidth <= 0)
            charWidth = 8;
        if (charHeight <= 0)
            charHeight = fontHeight;

        // Criar imagem para o caractere
        BufferedImage charImage = new BufferedImage(charWidth, charHeight, BufferedImage.TYPE_INT_ARGB);
        Graphics2D graphics = charImage.createGraphics();

        // Configurar renderização de alta qualidade (estilo LiquidBounce melhorado)
        if (antiAlias) {
            graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            graphics.setRenderingHint(RenderingHints.KEY_FRACTIONALMETRICS, RenderingHints.VALUE_FRACTIONALMETRICS_ON);
            graphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            graphics.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            graphics.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL, RenderingHints.VALUE_STROKE_PURE);
        }

        graphics.setFont(font);
        graphics.setColor(Color.WHITE);

        // Desenhar caractere
        graphics.drawString(String.valueOf(character), 4, fontMetrics.getAscent() + 4);
        graphics.dispose();

        // Converter para textura OpenGL
        int textureId = GL11.glGenTextures();
        GL11.glBindTexture(GL11.GL_TEXTURE_2D, textureId);

        // Configurar parâmetros da textura para melhor qualidade (compatível com OpenGL
        // 1.1)
        GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_MIN_FILTER, GL11.GL_LINEAR);
        GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_MAG_FILTER, GL11.GL_LINEAR);
        GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_WRAP_S, GL11.GL_CLAMP);
        GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_WRAP_T, GL11.GL_CLAMP);

        // Upload da imagem para GPU
        uploadImageToTexture(charImage);

        return new CharTexture(textureId, charWidth, charHeight);
    }

    /**
     * Upload da imagem para textura OpenGL
     */
    private void uploadImageToTexture(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();

        int[] pixels = new int[width * height];
        image.getRGB(0, 0, width, height, pixels, 0, width);

        // Converter para ByteBuffer (RGBA)
        ByteBuffer buffer = BufferUtils.createByteBuffer(width * height * 4);

        for (int pixel : pixels) {
            buffer.put((byte) ((pixel >> 16) & 0xFF)); // Red
            buffer.put((byte) ((pixel >> 8) & 0xFF)); // Green
            buffer.put((byte) (pixel & 0xFF)); // Blue
            buffer.put((byte) ((pixel >> 24) & 0xFF)); // Alpha
        }

        ((java.nio.Buffer) buffer).flip(); // Cast para compatibilidade Java 8

        GL11.glTexImage2D(GL11.GL_TEXTURE_2D, 0, GL11.GL_RGBA, width, height, 0,
                GL11.GL_RGBA, GL11.GL_UNSIGNED_BYTE, buffer);
    }

    /**
     * Desenha uma string com renderização customizada (estilo LiquidBounce)
     */
    public float drawString(String text, float x, float y, int color, boolean shadow) {
        if (text == null || text.isEmpty())
            return x;

        GlStateManager.pushMatrix();
        GlStateManager.enableBlend();
        GlStateManager.blendFunc(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA);
        GlStateManager.enableTexture2D();

        float currentX = x;

        // Desenhar sombra primeiro
        if (shadow) {
            drawStringInternal(text, x + 1, y + 1,
                    (color & 0xFF000000) != 0 ? ((color >> 24 & 0xFF) / 4 << 24) : 0x40000000);
        }

        // Desenhar texto principal
        currentX = drawStringInternal(text, x, y, color);

        GlStateManager.disableBlend();
        GlStateManager.popMatrix();

        return currentX;
    }

    /**
     * Renderização interna da string
     */
    private float drawStringInternal(String text, float x, float y, int color) {
        float currentX = x;
        int currentColor = color;

        for (int i = 0; i < text.length(); i++) {
            char character = text.charAt(i);

            // CORREÇÃO: Processar códigos de cor do Minecraft
            if (character == '§' && i + 1 < text.length()) {
                char colorCode = text.charAt(i + 1);
                currentColor = getColorFromCode(colorCode, color);
                i++; // Pular próximo caractere
                continue;
            }

            // Extrair componentes de cor atual
            float alpha = ((currentColor >> 24) & 0xFF) / 255.0f;
            float red = ((currentColor >> 16) & 0xFF) / 255.0f;
            float green = ((currentColor >> 8) & 0xFF) / 255.0f;
            float blue = (currentColor & 0xFF) / 255.0f;

            GlStateManager.color(red, green, blue, alpha);

            if (character == ' ') {
                currentX += getCharWidth(' ');
                continue;
            }

            CharTexture charTexture = charCache.computeIfAbsent(character, this::generateCharTexture);

            // Renderizar caractere
            GL11.glBindTexture(GL11.GL_TEXTURE_2D, charTexture.textureId);

            Tessellator tessellator = Tessellator.getInstance();
            WorldRenderer worldRenderer = tessellator.getWorldRenderer();

            worldRenderer.begin(GL11.GL_QUADS, DefaultVertexFormats.POSITION_TEX);
            worldRenderer.pos(currentX, y + charTexture.height, 0).tex(0, 1).endVertex();
            worldRenderer.pos(currentX + charTexture.width, y + charTexture.height, 0).tex(1, 1).endVertex();
            worldRenderer.pos(currentX + charTexture.width, y, 0).tex(1, 0).endVertex();
            worldRenderer.pos(currentX, y, 0).tex(0, 0).endVertex();

            tessellator.draw();

            currentX += charTexture.width - 8; // Remover padding
        }

        return currentX;
    }

    /**
     * CORREÇÃO: Converte código de cor do Minecraft para cor RGB
     */
    private int getColorFromCode(char code, int defaultColor) {
        switch (code) {
            case '0':
                return 0xFF000000; // Preto
            case '1':
                return 0xFF0000AA; // Azul escuro
            case '2':
                return 0xFF00AA00; // Verde escuro
            case '3':
                return 0xFF00AAAA; // Ciano escuro
            case '4':
                return 0xFFAA0000; // Vermelho escuro
            case '5':
                return 0xFFAA00AA; // Roxo escuro
            case '6':
                return 0xFFFFAA00; // Laranja
            case '7':
                return 0xFFAAAAAA; // Cinza claro
            case '8':
                return 0xFF555555; // Cinza escuro
            case '9':
                return 0xFF5555FF; // Azul
            case 'a':
            case 'A':
                return 0xFF55FF55; // Verde
            case 'b':
            case 'B':
                return 0xFF55FFFF; // Ciano
            case 'c':
            case 'C':
                return 0xFFFF5555; // Vermelho
            case 'd':
            case 'D':
                return 0xFFFF55FF; // Rosa
            case 'e':
            case 'E':
                return 0xFFFFFF55; // Amarelo
            case 'f':
            case 'F':
                return 0xFFFFFFFF; // Branco
            default:
                return defaultColor;
        }
    }

    /**
     * Obtém a largura de um caractere
     */
    public int getCharWidth(char character) {
        if (character == ' ') {
            return fontMetrics.charWidth(' ');
        }

        CharTexture charTexture = charCache.get(character);
        if (charTexture != null) {
            return charTexture.width - 8; // Remover padding
        }

        return fontMetrics.charWidth(character);
    }

    /**
     * Obtém a largura de uma string
     */
    public int getStringWidth(String text) {
        if (text == null || text.isEmpty())
            return 0;

        int width = 0;
        for (int i = 0; i < text.length(); i++) {
            char character = text.charAt(i);

            // CORREÇÃO: Pular códigos de cor na contagem de largura
            if (character == '§' && i + 1 < text.length()) {
                i++; // Pular próximo caractere
                continue;
            }

            width += getCharWidth(character);
        }
        return width;
    }

    /**
     * Obtém a altura da fonte
     */
    public int getHeight() {
        return fontHeight;
    }

    /**
     * Desenha string sem sombra
     */
    public float drawString(String text, float x, float y, int color) {
        return drawString(text, x, y, color, false);
    }

    /**
     * Desenha string centralizada
     */
    public void drawCenteredString(String text, float x, float y, int color, boolean shadow) {
        float width = getStringWidth(text);
        drawString(text, x - width / 2f, y, color, shadow);
    }

    /**
     * Limpa o cache de texturas
     */
    public void clearCache() {
        for (CharTexture charTexture : charCache.values()) {
            GL11.glDeleteTextures(charTexture.textureId);
        }
        charCache.clear();
    }
}
