package com.rato.addons.pathfinding.baritone;

import com.rato.addons.pathfinding.professional.MovementController;
import com.rato.addons.pathfinding.professional.PathNode;
import com.rato.addons.pathfinding.professional.MucifexPathRenderer;
import com.rato.addons.util.Logger;
import net.minecraft.client.Minecraft;
import net.minecraft.util.Vec3;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;

import java.util.ArrayList;
import java.util.List;

/**
 * Gerenciador do sistema de pathfinding baseado no Baritone
 */
public class BaritoneManager {
    
    private static BaritoneManager instance;
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Componentes do sistema
    private final BaritonePathfinder pathfinder;
    private final MovementController movementController;
    private final MucifexPathRenderer pathRenderer;
    
    // Estado do sistema
    private boolean isActive = false;
    private boolean debugMode = false;
    private boolean renderingEnabled = true;
    
    // Configurações
    private float movementSpeed = 1.0f;
    private float rotationSmoothness = 0.8f;
    
    // Estatísticas
    private long pathfindingStartTime;
    private int segmentsCalculated = 0;
    private Vec3 currentDestination;
    private List<PathNode> currentPath = new ArrayList<>();
    
    // Controle de execução
    private boolean executingPath = false;
    private int currentSegmentIndex = 0;
    
    private BaritoneManager() {
        pathfinder = new BaritonePathfinder();
        movementController = new MovementController();
        pathRenderer = new MucifexPathRenderer();
        
        setupCallbacks();
        MinecraftForge.EVENT_BUS.register(this);
    }
    
    public static BaritoneManager getInstance() {
        if (instance == null) {
            instance = new BaritoneManager();
        }
        return instance;
    }
    
    /**
     * Configura callbacks do sistema
     */
    private void setupCallbacks() {
        pathfinder.setOnPathFound(() -> {
            if (debugMode) {
                Logger.sendMessage("§a[Baritone] Pathfinding completo!");
            }
            startExecution();
        });
        
        pathfinder.setOnPathNotFound(() -> {
            Logger.sendMessage("§c[Baritone] Não foi possível encontrar um caminho!");
            stopPathfinding();
        });
        
        pathfinder.setOnSegmentComplete(() -> {
            BaritonePathfinder.PathSegment segment = pathfinder.getCurrentSegment();
            if (segment != null) {
                segmentsCalculated++;
                
                if (debugMode) {
                    Logger.sendMessage("§7[Baritone] Segmento " + segmentsCalculated + " calculado: " + 
                        segment.nodes.size() + " nós");
                }
                
                // Adicionar nós do segmento ao caminho atual
                currentPath.addAll(segment.nodes);
                
                // Atualizar renderização
                if (renderingEnabled) {
                    pathRenderer.setPath(currentPath);
                    pathRenderer.setEnabled(true);
                }
                
                // Iniciar execução se ainda não começou
                if (!executingPath && !currentPath.isEmpty()) {
                    startExecution();
                }
            }
        });
        
        movementController.setOnDestinationReached(() -> {
            Logger.sendMessage("§a[Baritone] Destino alcançado!");
            stopPathfinding();
        });
        
        movementController.setOnMovementFailed(() -> {
            if (debugMode) {
                Logger.sendMessage("§e[Baritone] Falha no movimento, recalculando...");
            }
            // Recalcular a partir da posição atual
            recalculateFromCurrentPosition();
        });
    }
    
    /**
     * Inicia pathfinding para destino específico
     */
    public boolean startPathfinding(Vec3 destination) {
        if (mc.thePlayer == null || mc.theWorld == null) {
            return false;
        }
        
        if (isActive) {
            stopPathfinding();
        }
        
        currentDestination = destination;
        pathfindingStartTime = System.currentTimeMillis();
        segmentsCalculated = 0;
        currentPath.clear();
        executingPath = false;
        currentSegmentIndex = 0;
        
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        
        if (debugMode) {
            Logger.sendMessage("§6[Baritone] Iniciando pathfinding segmentado...");
            Logger.sendMessage("§7De: " + formatVec3(playerPos));
            Logger.sendMessage("§7Para: " + formatVec3(destination));
            Logger.sendMessage("§7Distância: " + String.format("%.1f", playerPos.distanceTo(destination)) + " blocos");
        }
        
        // Configurar movimento
        movementController.setMovementSpeed(movementSpeed);
        movementController.setRotationSmoothness(rotationSmoothness);
        
        // Iniciar pathfinding
        boolean success = pathfinder.startPathfinding(playerPos, destination);
        
        if (success) {
            isActive = true;
            Logger.sendMessage("§a[Baritone] Sistema iniciado! Calculando segmentos...");
        } else {
            Logger.sendMessage("§c[Baritone] Falha ao iniciar pathfinding!");
        }
        
        return success;
    }
    
    /**
     * Inicia execução do caminho
     */
    private void startExecution() {
        if (currentPath.isEmpty()) return;
        
        executingPath = true;
        movementController.setPath(currentPath);
        
        if (debugMode) {
            Logger.sendMessage("§a[Baritone] Iniciando execução do caminho: " + currentPath.size() + " nós");
        }
    }
    
    /**
     * Recalcula caminho a partir da posição atual
     */
    private void recalculateFromCurrentPosition() {
        if (!isActive || currentDestination == null) return;
        
        Vec3 currentPos = mc.thePlayer.getPositionVector();
        
        if (debugMode) {
            Logger.sendMessage("§7[Baritone] Recalculando a partir da posição atual...");
        }
        
        // Parar execução atual
        executingPath = false;
        movementController.stop();
        
        // Limpar caminho atual
        currentPath.clear();
        segmentsCalculated = 0;
        
        // Reiniciar pathfinding
        pathfinder.startPathfinding(currentPos, currentDestination);
    }
    
    /**
     * Para o pathfinding atual
     */
    public void stopPathfinding() {
        isActive = false;
        executingPath = false;
        
        pathfinder.stopPathfinding();
        movementController.stop();
        
        if (pathRenderer != null) {
            pathRenderer.setEnabled(false);
        }
        
        currentPath.clear();
        currentDestination = null;
        
        if (debugMode) {
            Logger.sendMessage("§7[Baritone] Sistema parado");
        }
    }
    
    /**
     * Atualização principal (chamada a cada tick)
     */
    @SubscribeEvent
    public void onTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.START || !isActive) {
            return;
        }
        
        if (mc.thePlayer == null || mc.theWorld == null) {
            stopPathfinding();
            return;
        }
        
        // Atualizar movimento se estiver executando
        if (executingPath) {
            movementController.update();
        }
        
        // Limpar cache expirado periodicamente
        if (System.currentTimeMillis() % 5000 < 50) { // A cada 5 segundos
            pathfinder.getChunkCache().clearExpired();
        }
        
        // Verificar se precisa recalcular (detecção de mudanças no mundo)
        if (shouldRecalculatePath()) {
            recalculateFromCurrentPosition();
        }
    }
    
    /**
     * Verifica se deve recalcular o caminho
     */
    private boolean shouldRecalculatePath() {
        if (!executingPath || currentDestination == null) return false;
        
        // Recalcular se o player estiver preso por muito tempo
        if (movementController.isMoving()) {
            long timeSinceStart = System.currentTimeMillis() - pathfindingStartTime;
            return timeSinceStart > 30000; // 30 segundos
        }
        
        return false;
    }
    
    // Getters e Setters
    public boolean isActive() { return isActive; }
    public boolean isExecutingPath() { return executingPath; }
    public void setDebugMode(boolean debug) { this.debugMode = debug; }
    public boolean isDebugMode() { return debugMode; }
    
    public void setRenderingEnabled(boolean enabled) { 
        this.renderingEnabled = enabled;
        if (pathRenderer != null) {
            pathRenderer.setEnabled(enabled && isActive);
        }
    }
    public boolean isRenderingEnabled() { return renderingEnabled; }
    
    public void setMovementSpeed(float speed) { this.movementSpeed = speed; }
    public float getMovementSpeed() { return movementSpeed; }
    
    public void setRotationSmoothness(float smoothness) { this.rotationSmoothness = smoothness; }
    public float getRotationSmoothness() { return rotationSmoothness; }
    
    /**
     * Retorna status detalhado do sistema
     */
    public String getDetailedStatus() {
        if (!isActive) return "Inativo";
        
        long elapsed = System.currentTimeMillis() - pathfindingStartTime;
        StringBuilder status = new StringBuilder();
        
        status.append("Ativo há ").append(elapsed / 1000).append("s");
        status.append(", Segmentos: ").append(segmentsCalculated);
        status.append(", Nós: ").append(currentPath.size());
        
        if (executingPath) {
            status.append(", Executando");
            float progress = movementController.getProgress();
            status.append(" (").append(String.format("%.1f", progress * 100)).append("%)");
        } else if (pathfinder.isCalculating()) {
            status.append(", Calculando");
        }
        
        return status.toString();
    }
    
    /**
     * Retorna informações de debug
     */
    public String getDebugInfo() {
        StringBuilder info = new StringBuilder();
        info.append("§7Status: ").append(isActive ? "§aAtivo" : "§cInativo").append("\n");
        info.append("§7Destino: ").append(currentDestination != null ? formatVec3(currentDestination) : "§cNenhum").append("\n");
        info.append("§7Segmentos calculados: §f").append(segmentsCalculated).append("\n");
        info.append("§7Nós no caminho: §f").append(currentPath.size()).append("\n");
        info.append("§7Executando: ").append(executingPath ? "§aSim" : "§cNão").append("\n");
        info.append("§7Calculando: ").append(pathfinder.isCalculating() ? "§aSim" : "§cNão").append("\n");
        info.append("§7Velocidade: §f").append(movementSpeed).append("\n");
        info.append("§7Suavidade: §f").append(rotationSmoothness).append("\n");
        info.append("§7Renderização: ").append(renderingEnabled ? "§aAtiva" : "§cInativa");
        
        // Informações do cache
        BaritonePathfinder.ChunkCache cache = pathfinder.getChunkCache();
        if (cache != null) {
            info.append("\n§7Cache de chunks: §f").append("Ativo");
        }
        
        return info.toString();
    }
    
    /**
     * Limpa cache de chunks
     */
    public void clearChunkCache() {
        pathfinder.getChunkCache().clear();
        if (debugMode) {
            Logger.sendMessage("§a[Baritone] Cache de chunks limpo!");
        }
    }
    
    /**
     * Obtém estatísticas de performance
     */
    public String getPerformanceStats() {
        if (!isActive) return "Sistema inativo";
        
        long elapsed = System.currentTimeMillis() - pathfindingStartTime;
        double segmentsPerSecond = segmentsCalculated / Math.max(1.0, elapsed / 1000.0);
        double nodesPerSecond = currentPath.size() / Math.max(1.0, elapsed / 1000.0);
        
        return String.format("Segmentos/s: %.2f, Nós/s: %.2f", segmentsPerSecond, nodesPerSecond);
    }
    
    private String formatVec3(Vec3 vec) {
        return String.format("%.1f, %.1f, %.1f", vec.xCoord, vec.yCoord, vec.zCoord);
    }
}
