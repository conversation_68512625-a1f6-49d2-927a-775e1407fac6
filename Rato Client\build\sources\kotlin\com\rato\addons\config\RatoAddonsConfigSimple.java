package com.rato.addons.config;

import org.lwjgl.input.Keyboard;

/**
 * Configuração simplificada para substituir OneConfig
 * Fornece compatibilidade com o sistema antigo
 */
public class RatoAddonsConfigSimple {

    // Classe para substituir OneColor
    public static class SimpleColor {
        private int rgb;

        public SimpleColor(int r, int g, int b, int a) {
            this.rgb = (a << 24) | (r << 16) | (g << 8) | b;
        }

        public SimpleColor(int rgb) {
            this.rgb = rgb;
        }

        public int getRGB() {
            return rgb;
        }

        public int getRed() {
            return (rgb >> 16) & 0xFF;
        }

        public int getGreen() {
            return (rgb >> 8) & 0xFF;
        }

        public int getBlue() {
            return rgb & 0xFF;
        }

        public int getAlpha() {
            return (rgb >> 24) & 0xFF;
        }

        public void setRGB(int rgb) {
            this.rgb = rgb;
        }
    }

    // Classe para substituir OneKeyBind
    public static class SimpleKeyBind {
        private int keyCode;

        public SimpleKeyBind(int keyCode) {
            this.keyCode = keyCode;
        }

        public int getKeyCode() {
            return keyCode;
        }

        public void setKeyCode(int keyCode) {
            this.keyCode = keyCode;
        }

        public boolean isActive() {
            return org.lwjgl.input.Keyboard.isKeyDown(keyCode);
        }

        public String getKeyName() {
            return Keyboard.getKeyName(keyCode);
        }

        public String getDisplay() {
            return Keyboard.getKeyName(keyCode);
        }

        public boolean isPressed() {
            return Keyboard.isKeyDown(keyCode);
        }
    }

    // Configurações de Combat
    public static boolean aimbotEnabled = false;
    public static float aimbotFOV = 90.0f;
    public static float aimbotRange = 4.5f;
    public static float aimbotSmoothness = 0.15f;
    public static float aimbotSpeed = 1.0f;
    public static boolean aimbotPrediction = true;
    public static boolean aimbotRaytrace = true;

    // Configurações de Visuals
    public static boolean playerESP = false;
    public static boolean mobESP = false;
    public static float espRange = 50.0f;
    public static boolean espShowNames = true;
    public static boolean esp2D = false;
    public static boolean esp3D = true;
    public static boolean espShowHealth = true;
    public static SimpleColor espPlayerColor = new SimpleColor(0, 255, 0, 255); // Verde
    public static SimpleColor espMobColor = new SimpleColor(255, 0, 0, 255); // Vermelho

    // Configurações de Movement/Pathfinding
    public static boolean pathfindingEnabled = false;
    public static SimpleKeyBind pathfindingToggle = new SimpleKeyBind(Keyboard.KEY_P);
    public static int riskTolerance = 50;
    public static boolean humanMovement = true;
    public static float pathfindingRotationSpeed = 5.0f;
    public static float pathfindingSmoothness = 0.8f;
    public static float pathfindingHighSpeedSmoothness = 0.6f;
    public static boolean pathfindingAutoSpeedDetection = true;
    public static boolean pathfindingHumanMovement = true;
    public static boolean pathfindingNaturalMovement = true;

    // Configurações de Foraging
    public static int foraging3DScanRadius = 15;
    public static int foraging3DScanHeight = 10;
    public static boolean foragingUse3DPathfinding = false;
    public static int foragingTreeScanRadius = 50;
    public static double foragingExplorationDistance = 20.0;
    public static int foragingLogDensity = 1;
    public static boolean foragingRequireLeaves = true;
    public static int foragingTreeBreakDelay = 500;
    public static int foragingTreeCooldown = 30;
    public static float pathfindingWaypointDensity = 1.0f;
    public static float pathfindingJumpPenalty = 2.0f;
    public static boolean pathfindingAvoidStructures = true;
    public static float pathfindingStructurePenalty = 5.0f;
    public static boolean pathfindingVisualization = true;
    public static boolean allowBlockBreaking = false;
    public static boolean allowRiskyJumps = false;
    public static boolean pathVisualization = true;
    public static int movementSpeed = 5;
    public static float cameraRotationSpeed = 1.0f;
    public static float cameraSmoothness = 0.05f;
    public static boolean naturalMovement = true;
    public static float jumpPenalty = 3.0f;

    // Configurações de Player
    public static boolean freecamEnabled = false;
    public static SimpleKeyBind freecamToggle = new SimpleKeyBind(Keyboard.KEY_G);
    public static int freecamSpeed = 5;
    public static boolean inventoryPIP = false;
    public static int pipPosition = 0; // Top Right
    public static SimpleColor pipBorderColor = new SimpleColor(57, 255, 20, 255); // Verde neon

    // Configurações de World
    public static boolean miningHelper = false;
    public static SimpleKeyBind miningToggle = new SimpleKeyBind(Keyboard.KEY_M);
    public static int miningSpeed = 50;
    public static boolean autoFarm = false;
    public static SimpleKeyBind farmingToggle = new SimpleKeyBind(Keyboard.KEY_F);
    public static int cropType = 0; // Wheat
    public static boolean foragingEnabled = false;
    public static int foragingArea = 0; // Park
    public static int treeScanRadius = 50;
    public static int treeBreakDelay = 500;

    // Configurações de Misc
    public static boolean emergencyStopAll = false;
    public static SimpleKeyBind staffCheckResponse = new SimpleKeyBind(Keyboard.KEY_H);
    public static SimpleKeyBind staffCheckResponseKey = new SimpleKeyBind(Keyboard.KEY_H);
    public static boolean manualPause = false;
    public static SimpleKeyBind manualPauseKey = new SimpleKeyBind(Keyboard.KEY_PAUSE);
    public static SimpleKeyBind emergencyStop = new SimpleKeyBind(Keyboard.KEY_END);

    // Configurações de Rift
    public static boolean riftAutofarm = false;
    public static boolean riftShowArea = true;
    public static boolean riftDebugMode = false;
    public static boolean riftPauseOnPlayer = true;
    public static float riftHealthThreshold = 5.0f;
    public static float riftPlayerDetectionRange = 10.0f;
    public static int riftAttackSpeed = 10; // CPS
    public static SimpleColor riftAreaColor = new SimpleColor(0, 255, 255, 100); // Ciano transparente
    public static SimpleColor riftZombieESPColor = new SimpleColor(255, 85, 85, 150); // Vermelho
    public static SimpleColor riftPlayerESPColor = new SimpleColor(85, 255, 85, 150); // Verde

    // Configurações adicionais de Visuals
    public static SimpleColor visualsZombieESPColor = new SimpleColor(255, 85, 85, 150); // Vermelho
    public static SimpleColor visualsPlayerESPColor = new SimpleColor(85, 255, 85, 150); // Verde
    public static SimpleColor visualsZombieGlowColor = new SimpleColor(255, 85, 85, 255); // Vermelho
    public static SimpleColor visualsPlayerGlowColor = new SimpleColor(85, 255, 85, 255); // Verde

    // Configurações de ESP/Visuals adicionais
    public static boolean riftESPEnabled = true;
    public static int riftESPMode = 0; // 0=Box, 1=Outline, 2=Glow
    public static boolean riftESPOutline = true;
    public static boolean riftESPZombies = true;
    public static boolean riftESPPlayers = true;
    public static boolean visualsESPEnabled = true;
    public static int visualsESPMode = 0;
    public static boolean visualsESPGlow = false;
    public static float visualsESPGlowRadius = 2.0f;
    public static boolean visualsESPZombies = true;
    public static boolean visualsESPPlayers = true;
    public static boolean visualsGlowEnabled = false;
    public static boolean visualsGlowZombies = true;
    public static boolean visualsGlowPlayers = true;
    public static boolean visualsNametagsEnabled = true;
    public static boolean visualsNametagsZombies = true;
    public static boolean visualsNametagsPlayers = true;
    public static boolean visualsNametagsHealth = true;
    public static boolean visualsNametagsDistance = true;
    public static boolean visualsNametagsBackground = true;
    public static float visualsNametagsScale = 1.0f;

    // Configurações de Failsafe/Staff Check
    public static boolean rotationDetection = true;
    public static boolean tpDetection = true;
    public static boolean itemSwapDetection = true;
    public static boolean blockCheckDetection = true;
    public static boolean velocityCheckDetection = true;
    public static boolean playerCheckDetection = true;
    public static boolean discordWebhook = false;
    public static String webhookUrl = "";
    public static boolean soundAlert = true;
    public static float rotationThreshold = 30.0f;
    public static float playerCheckRange = 10.0f;
    public static boolean autoPause = true;
    public static boolean autoClicker = false;
    public static boolean autoScreenshot = false;

    /**
     * Sincronizar configurações com o CustomConfigManager
     */
    public static void syncWithCustomConfig() {
        // Combat
        aimbotEnabled = CustomConfigManager.getBoolean("combat", "aimbot_enabled");
        aimbotFOV = CustomConfigManager.getFloat("combat", "aimbot_fov");
        aimbotRange = CustomConfigManager.getFloat("combat", "aimbot_range");
        aimbotSmoothness = CustomConfigManager.getFloat("combat", "aimbot_smoothness");
        aimbotSpeed = CustomConfigManager.getFloat("combat", "aimbot_speed");
        aimbotPrediction = CustomConfigManager.getBoolean("combat", "aimbot_prediction");
        aimbotRaytrace = CustomConfigManager.getBoolean("combat", "aimbot_raytrace");

        // Visuals
        playerESP = CustomConfigManager.getBoolean("visuals", "player_esp");
        mobESP = CustomConfigManager.getBoolean("visuals", "mob_esp");
        espRange = CustomConfigManager.getFloat("visuals", "esp_range");
        espShowNames = CustomConfigManager.getBoolean("visuals", "esp_show_names");
        esp2D = CustomConfigManager.getBoolean("visuals", "esp_2d");
        esp3D = CustomConfigManager.getBoolean("visuals", "esp_3d");
        espShowHealth = CustomConfigManager.getBoolean("visuals", "esp_show_health");
        espPlayerColor.setRGB(CustomConfigManager.getInt("visuals", "player_esp_color"));
        espMobColor.setRGB(CustomConfigManager.getInt("visuals", "mob_esp_color"));

        // Movement
        pathfindingEnabled = CustomConfigManager.getBoolean("movement", "pathfinding_enabled");
        riskTolerance = CustomConfigManager.getInt("movement", "risk_tolerance");
        humanMovement = CustomConfigManager.getBoolean("movement", "human_movement");
        allowBlockBreaking = CustomConfigManager.getBoolean("movement", "allow_block_breaking");
        allowRiskyJumps = CustomConfigManager.getBoolean("movement", "allow_risky_jumps");
        pathVisualization = CustomConfigManager.getBoolean("movement", "path_visualization");
        movementSpeed = CustomConfigManager.getInt("movement", "movement_speed");
        cameraRotationSpeed = CustomConfigManager.getFloat("movement", "camera_rotation_speed");
        cameraSmoothness = CustomConfigManager.getFloat("movement", "camera_smoothness");
        naturalMovement = CustomConfigManager.getBoolean("movement", "natural_movement");
        jumpPenalty = CustomConfigManager.getFloat("movement", "jump_penalty");

        // Player
        freecamEnabled = CustomConfigManager.getBoolean("player", "freecam_enabled");
        freecamSpeed = CustomConfigManager.getInt("player", "freecam_speed");
        inventoryPIP = CustomConfigManager.getBoolean("player", "inventory_pip");
        pipPosition = CustomConfigManager.getInt("player", "pip_position");
        pipBorderColor.setRGB(CustomConfigManager.getInt("player", "pip_border_color"));

        // World
        miningHelper = CustomConfigManager.getBoolean("world", "mining_helper");
        miningSpeed = CustomConfigManager.getInt("world", "mining_speed");
        autoFarm = CustomConfigManager.getBoolean("world", "auto_farm");
        cropType = CustomConfigManager.getInt("world", "crop_type");
        foragingEnabled = CustomConfigManager.getBoolean("world", "foraging_enabled");
        foragingArea = CustomConfigManager.getInt("world", "foraging_area");
        treeScanRadius = CustomConfigManager.getInt("world", "tree_scan_radius");
        treeBreakDelay = CustomConfigManager.getInt("world", "tree_break_delay");

        // Misc
        emergencyStopAll = CustomConfigManager.getBoolean("misc", "emergency_stop_all");
        // staffCheckResponse é um SimpleKeyBind, não sincronizar com boolean
        manualPause = CustomConfigManager.getBoolean("misc", "manual_pause");
        riftAutofarm = CustomConfigManager.getBoolean("misc", "rift_autofarm");
        riftShowArea = CustomConfigManager.getBoolean("misc", "rift_show_area");
        riftDebugMode = CustomConfigManager.getBoolean("misc", "rift_debug_mode");
        riftPauseOnPlayer = CustomConfigManager.getBoolean("misc", "rift_pause_on_player");
        riftHealthThreshold = CustomConfigManager.getFloat("misc", "rift_health_threshold");
        riftPlayerDetectionRange = CustomConfigManager.getFloat("misc", "rift_player_detection_range");
        riftAttackSpeed = CustomConfigManager.getInt("misc", "rift_attack_speed");
        riftAreaColor.setRGB(CustomConfigManager.getInt("misc", "rift_area_color"));
        riftZombieESPColor.setRGB(CustomConfigManager.getInt("misc", "rift_zombie_esp_color"));
        riftPlayerESPColor.setRGB(CustomConfigManager.getInt("misc", "rift_player_esp_color"));
    }

    /**
     * Salvar configurações de volta para o CustomConfigManager
     */
    public static void saveToCustomConfig() {
        // Combat
        CustomConfigManager.setBoolean("combat", "aimbot_enabled", aimbotEnabled);
        CustomConfigManager.setFloat("combat", "aimbot_fov", aimbotFOV);
        CustomConfigManager.setFloat("combat", "aimbot_range", aimbotRange);
        CustomConfigManager.setFloat("combat", "aimbot_smoothness", aimbotSmoothness);
        CustomConfigManager.setFloat("combat", "aimbot_speed", aimbotSpeed);
        CustomConfigManager.setBoolean("combat", "aimbot_prediction", aimbotPrediction);
        CustomConfigManager.setBoolean("combat", "aimbot_raytrace", aimbotRaytrace);

        // Visuals
        CustomConfigManager.setBoolean("visuals", "player_esp", playerESP);
        CustomConfigManager.setBoolean("visuals", "mob_esp", mobESP);
        CustomConfigManager.setFloat("visuals", "esp_range", espRange);
        CustomConfigManager.setBoolean("visuals", "esp_show_names", espShowNames);
        CustomConfigManager.setBoolean("visuals", "esp_2d", esp2D);
        CustomConfigManager.setBoolean("visuals", "esp_3d", esp3D);
        CustomConfigManager.setBoolean("visuals", "esp_show_health", espShowHealth);
        CustomConfigManager.setInt("visuals", "player_esp_color", espPlayerColor.getRGB());
        CustomConfigManager.setInt("visuals", "mob_esp_color", espMobColor.getRGB());

        // Salvar outras categorias...
        CustomConfigManager.saveConfig();
    }
}
