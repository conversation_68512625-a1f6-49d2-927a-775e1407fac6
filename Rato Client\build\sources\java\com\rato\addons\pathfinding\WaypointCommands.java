package com.rato.addons.pathfinding;

import com.rato.addons.util.Logger;
import net.minecraft.client.Minecraft;
import net.minecraft.util.Vec3;
import net.minecraftforge.client.event.ClientChatReceivedEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.InputEvent;
import org.lwjgl.input.Keyboard;

import java.util.ArrayList;
import java.util.List;

/**
 * Sistema de comandos para gerenciar waypoints no chat
 */
public class WaypointCommands {
    
    private static final Minecraft mc = Minecraft.getMinecraft();

    // Sistema de delay para criação de waypoints
    private long lastWaypointTime = 0;
    private static final long WAYPOINT_DELAY = 500; // 500ms de delay
    private static WaypointCommands instance;
    
    public static WaypointCommands getInstance() {
        if (instance == null) {
            instance = new WaypointCommands();
        }
        return instance;
    }
    
    @SubscribeEvent
    public void onKeyInput(InputEvent.KeyInputEvent event) {
        // Teclas rápidas para waypoints
        if (Keyboard.isKeyDown(Keyboard.KEY_LCONTROL)) {
            if (Keyboard.isKeyDown(Keyboard.KEY_1)) {
                // Ctrl + 1: Adicionar waypoint (com delay para segurar)
                addWaypointWithDelay();
            } else if (Keyboard.isKeyDown(Keyboard.KEY_2)) {
                // Ctrl + 2: Remover último waypoint
                WaypointPathfinder.getInstance().removeLastWaypoint();
            } else if (Keyboard.isKeyDown(Keyboard.KEY_3)) {
                // Ctrl + 3: Listar waypoints
                WaypointPathfinder.getInstance().listWaypoints();
            } else if (Keyboard.isKeyDown(Keyboard.KEY_4)) {
                // Ctrl + 4: Limpar todos waypoints
                WaypointPathfinder.getInstance().clearWaypoints();
            } else if (Keyboard.isKeyDown(Keyboard.KEY_5)) {
                // Ctrl + 5: Iniciar pathfinding
                WaypointPathfinder.getInstance().startPathfindingWithDefinedWaypoints();
            } else if (Keyboard.isKeyDown(Keyboard.KEY_6)) {
                // Ctrl + 6: Teste de pathfinding inteligente
                testIntelligentPathfinding();
            }
        }
    }
    
    @SubscribeEvent
    public void onChatReceived(ClientChatReceivedEvent event) {
        // Não interceptar aqui - usar apenas teclas de atalho
        // O sistema de chat não funciona bem para comandos complexos
    }

    /**
     * Processa comando de waypoint manualmente
     */
    public void processWaypointCommand(String fullCommand) {
        String[] parts = fullCommand.toLowerCase().split(" ");
        if (parts.length < 2) {
            showWaypointHelp();
            return;
        }

        String command = parts[1].toLowerCase();
            
            switch (command) {
                case "add":
                case "a":
                    WaypointPathfinder.getInstance().addWaypointAtCurrentPosition();
                    break;
                    
                case "remove":
                case "rem":
                case "r":
                    WaypointPathfinder.getInstance().removeLastWaypoint();
                    break;
                    
                case "list":
                case "l":
                    WaypointPathfinder.getInstance().listWaypoints();
                    break;
                    
                case "clear":
                case "c":
                    WaypointPathfinder.getInstance().clearWaypoints();
                    break;
                    
                case "start":
                case "s":
                    WaypointPathfinder.getInstance().startPathfindingWithDefinedWaypoints();
                    break;
                    
                case "stop":
                    WaypointPathfinder.getInstance().stopPathfinding();
                    break;

                case "baritone":
                case "b":
                    if (parts.length < 5) {
                        Logger.sendMessage("§cUso: .wp baritone <x> <y> <z>");
                        return;
                    }
                    try {
                        double x = Double.parseDouble(parts[2]);
                        double y = Double.parseDouble(parts[3]);
                        double z = Double.parseDouble(parts[4]);
                        testBaritonePathfinding(x, y, z);
                    } catch (NumberFormatException e) {
                        Logger.sendMessage("§cCoordenadas inválidas!");
                    }
                    break;

                case "goto":
                case "g":
                    if (parts.length < 5) {
                        Logger.sendMessage("§cUso: .wp goto <x> <y> <z>");
                        return;
                    }
                    try {
                        double x = Double.parseDouble(parts[2]);
                        double y = Double.parseDouble(parts[3]);
                        double z = Double.parseDouble(parts[4]);

                        Vec3 destination = new Vec3(x, y, z);
                        Vec3 playerPos = mc.thePlayer.getPositionVector();

                        List<Vec3> gotoWaypoints = new ArrayList<>();
                        gotoWaypoints.add(playerPos);
                        gotoWaypoints.add(destination);

                        Logger.sendMessage("§6Iniciando pathfinding inteligente para: " +
                            String.format("%.1f, %.1f, %.1f", x, y, z));
                        Logger.sendMessage("§7Distância: " + String.format("%.1f blocos", playerPos.distanceTo(destination)));

                        WaypointPathfinder.getInstance().startPathfinding(gotoWaypoints);
                    } catch (NumberFormatException e) {
                        Logger.sendMessage("§cCoordenadas inválidas!");
                    }
                    break;

                case "help":
                case "h":
                default:
                    showWaypointHelp();
                    break;
            }
    }
    
    /**
     * Adiciona waypoint com delay para permitir segurar Ctrl+1
     */
    private void addWaypointWithDelay() {
        long currentTime = System.currentTimeMillis();

        // Verificar se passou o delay
        if (currentTime - lastWaypointTime >= WAYPOINT_DELAY) {
            Vec3 playerPos = mc.thePlayer.getPositionVector();

            // Verificar se não há waypoint muito próximo
            if (!hasNearbyWaypoint(playerPos, 3.0)) {
                WaypointPathfinder.getInstance().addWaypointAtCurrentPosition();
                lastWaypointTime = currentTime;
            }
        }
    }

    /**
     * Verifica se há waypoint próximo à posição
     */
    private boolean hasNearbyWaypoint(Vec3 position, double minDistance) {
        for (Vec3 waypoint : WaypointPathfinder.getInstance().getWaypoints()) {
            if (position.distanceTo(waypoint) < minDistance) {
                return true;
            }
        }
        return false;
    }

    /**
     * Mostra ajuda dos comandos de waypoint
     */
    private void showWaypointHelp() {
        Logger.sendMessage("§6=== Comandos de Waypoint ===");
        Logger.sendMessage("§7.wp add §f- Adicionar waypoint na posição atual");
        Logger.sendMessage("§7.wp remove §f- Remover último waypoint");
        Logger.sendMessage("§7.wp list §f- Listar todos os waypoints");
        Logger.sendMessage("§7.wp clear §f- Limpar todos os waypoints");
        Logger.sendMessage("§7.wp start §f- Iniciar pathfinding");
        Logger.sendMessage("§7.wp stop §f- Parar pathfinding");
        Logger.sendMessage("§7.wp goto <x> <y> <z> §f- Ir para coordenada (inteligente)");
        Logger.sendMessage("§6=== Teclas Rápidas ===");
        Logger.sendMessage("§7Ctrl+1 §f- Adicionar waypoint");
        Logger.sendMessage("§7Ctrl+2 §f- Remover último");
        Logger.sendMessage("§7Ctrl+3 §f- Listar waypoints");
        Logger.sendMessage("§7Ctrl+4 §f- Limpar todos");
        Logger.sendMessage("§7Ctrl+5 §f- Iniciar pathfinding");
        Logger.sendMessage("§7Ctrl+6 §f- Teste pathfinding inteligente");
        Logger.sendMessage("§7P §f- Ativar/desativar pathfinding");
    }

    /**
     * Testa pathfinding inteligente para coordenadas distantes
     */
    private void testIntelligentPathfinding() {
        if (mc.thePlayer == null) return;

        Vec3 playerPos = mc.thePlayer.getPositionVector();

        // Coordenadas de teste (1000 blocos de distância)
        double testX = playerPos.xCoord + 1000;
        double testY = 70;
        double testZ = playerPos.zCoord + 1000;

        Vec3 destination = new Vec3(testX, testY, testZ);

        List<Vec3> gotoWaypoints = new ArrayList<>();
        gotoWaypoints.add(playerPos);
        gotoWaypoints.add(destination);

        Logger.sendMessage("§6=== TESTE PATHFINDING INTELIGENTE ===");
        Logger.sendMessage("§7Destino: " + String.format("%.1f, %.1f, %.1f", testX, testY, testZ));
        Logger.sendMessage("§7Distância: " + String.format("%.1f blocos", playerPos.distanceTo(destination)));
        Logger.sendMessage("§7Iniciando análise de terreno...");

        WaypointPathfinder.getInstance().startPathfinding(gotoWaypoints);
    }

    /**
     * Testa o novo sistema de pathfinding baseado no Baritone
     */
    private void testBaritonePathfinding(double x, double y, double z) {
        Vec3 start = mc.thePlayer.getPositionVector();
        Vec3 goal = new Vec3(x, y, z);

        Logger.sendMessage("§a=== PATHFINDING BARITONE-STYLE ===");
        Logger.sendMessage("§7Destino: " + String.format("%.1f, %.1f, %.1f", x, y, z));
        Logger.sendMessage("§7Distância: " + String.format("%.1f blocos", start.distanceTo(goal)));
        Logger.sendMessage("§7Iniciando cálculo com movimentos modulares...");

        // Usar o novo sistema
        BaritoneStylePathfinder pathfinder = new BaritoneStylePathfinder();
        BaritoneStyleRenderer renderer = new BaritoneStyleRenderer();

        // Calcular caminho em thread separada
        new Thread(() -> {
            long startTime = System.currentTimeMillis();
            BaritoneStylePathfinder.PathfindingResult result = pathfinder.calculatePath(start, goal);

            if (result.success) {
                Logger.sendMessage("§a✓ Caminho encontrado!");
                Logger.sendMessage("§7• Nodes: " + result.path.size());
                Logger.sendMessage("§7• Tempo: " + result.timeMs + "ms");
                Logger.sendMessage("§7• Nodes explorados: " + result.nodesExplored);

                if (result.isPartial) {
                    Logger.sendMessage("§e⚠ Caminho parcial - não chegou ao destino exato");
                }

                // Mostrar tipos de movimento usados
                showMovementTypes(result.path);

                // Ativar renderização
                renderer.setPath(result.path);
                renderer.setEnabled(true);

                Logger.sendMessage("§aRenderização ativada por 30 segundos");

                // Desativar após 30 segundos
                new Thread(() -> {
                    try {
                        Thread.sleep(30000);
                        renderer.setEnabled(false);
                        Logger.sendMessage("§7Renderização desativada");
                    } catch (InterruptedException e) {
                        // Ignore
                    }
                }).start();

            } else {
                Logger.sendMessage("§c✗ Falha no pathfinding");
                Logger.sendMessage("§c• Erro: " + result.errorMessage);
                Logger.sendMessage("§7• Nodes explorados: " + result.nodesExplored);
                Logger.sendMessage("§7• Tempo: " + result.timeMs + "ms");
            }
        }).start();
    }

    /**
     * Mostra os tipos de movimento usados no caminho
     */
    private void showMovementTypes(List<BaritoneStylePathfinder.BaritoneNode> path) {
        if (path.size() <= 1) return;

        int traverse = 0, ascend = 0, descend = 0, diagonal = 0, fall = 0;

        for (BaritoneStylePathfinder.BaritoneNode node : path) {
            switch (node.movementType) {
                case TRAVERSE: traverse++; break;
                case ASCEND: ascend++; break;
                case DESCEND: descend++; break;
                case DIAGONAL: diagonal++; break;
                case FALL: fall++; break;
            }
        }

        Logger.sendMessage("§7• Movimentos: §fTraverse(" + traverse + ") §6Ascend(" + ascend +
                         ") §dDescend(" + descend + ") §aDiagonal(" + diagonal + ") §cFall(" + fall + ")");
    }
}
