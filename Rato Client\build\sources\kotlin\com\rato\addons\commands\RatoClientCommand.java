package com.rato.addons.commands;

import com.rato.addons.pathfinding.professional.PathfindingManager;
import com.rato.addons.pathfinding.baritone.BaritoneManager;
import com.rato.addons.pathfinding.mucifex.MucifexManager;
import com.rato.addons.pathfinding.enhanced.EnhancedManager;
import com.rato.addons.pathfinding.hybrid.HybridManager;
import com.rato.addons.util.Logger;
import net.minecraft.client.Minecraft;
import net.minecraft.command.CommandBase;
import net.minecraft.command.CommandException;
import net.minecraft.command.ICommandSender;
import net.minecraft.util.ChatComponentText;
import net.minecraft.util.Vec3;

/**
 * Comando principal do RatoClient
 * Uso: /ratoclient <subcomando> [argumentos]
 */
public class RatoClientCommand extends CommandBase {
    
    private final Minecraft mc = Minecraft.getMinecraft();
    private final PathfindingManager pathfindingManager = PathfindingManager.getInstance();
    private final BaritoneManager baritoneManager = BaritoneManager.getInstance();
    private final MucifexManager mucifexManager = MucifexManager.getInstance();
    private final EnhancedManager enhancedManager = EnhancedManager.getInstance();
    private final HybridManager hybridManager = HybridManager.getInstance();
    private final com.rato.addons.pathfinding.movement.MovementManager movementManager = com.rato.addons.pathfinding.movement.MovementManager.getInstance();
    
    @Override
    public String getCommandName() {
        return "ratoclient";
    }
    
    @Override
    public String getCommandUsage(ICommandSender sender) {
        return "/ratoclient <goto|stop|status|help> [argumentos]";
    }
    
    @Override
    public int getRequiredPermissionLevel() {
        return 0; // Qualquer jogador pode usar
    }
    
    @Override
    public void processCommand(ICommandSender sender, String[] args) throws CommandException {
        if (args.length == 0) {
            showHelp();
            return;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "goto":
                handleGotoCommand(args);
                break;

            case "baritone":
                handleBaritoneCommand(args);
                break;

            case "mucifex":
                handleMucifexCommand(args);
                break;

            case "enhanced":
                handleEnhancedCommand(args);
                break;

            case "hybrid":
                handleHybridCommand(args);
                break;
                
            case "stop":
                handleStopCommand();
                break;
                
            case "status":
                handleStatusCommand();
                break;
                
            case "help":
            case "ajuda":
                showHelp();
                break;
                
            case "config":
                handleConfigCommand(args);
                break;
                
            case "debug":
                handleDebugCommand(args);
                break;
                
            default:
                Logger.sendMessage("§cComando inválido! Use /ratoclient help para ver os comandos disponíveis.");
                break;
        }
    }
    
    /**
     * Processa comando goto - Agora usando o sistema Baritone-Style aprimorado
     */
    private void handleGotoCommand(String[] args) {
        if (args.length < 4) {
            Logger.sendMessage("§cUso: /ratoclient goto <x> <y> <z>");
            Logger.sendMessage("§7Exemplo: /ratoclient goto 100 64 200");
            return;
        }

        if (mc.thePlayer == null || mc.theWorld == null) {
            Logger.sendMessage("§cJogador ou mundo não disponível!");
            return;
        }

        try {
            double x = Double.parseDouble(args[1]);
            double y = Double.parseDouble(args[2]);
            double z = Double.parseDouble(args[3]);

            Vec3 start = mc.thePlayer.getPositionVector();
            Vec3 goal = new Vec3(x, y, z);
            double distance = start.distanceTo(goal);

            Logger.sendMessage("§6[Rato Client] §7Sistema Baritone-Style Aprimorado");
            Logger.sendMessage("§7Destino: §f" + String.format("%.1f, %.1f, %.1f", x, y, z));
            Logger.sendMessage("§7Distância: §f" + String.format("%.1f blocos", distance));
            Logger.sendMessage("§7Iniciando pathfinding com movimentos modulares...");

            // Usar o novo sistema Baritone-Style com execução de movimento
            com.rato.addons.pathfinding.BaritoneStylePathfinder pathfinder = new com.rato.addons.pathfinding.BaritoneStylePathfinder();

            // Calcular caminho em thread separada
            new Thread(() -> {
                com.rato.addons.pathfinding.BaritoneStylePathfinder.PathfindingResult result = pathfinder.calculatePath(start, goal);

                if (result.success) {
                    Logger.sendMessage("§a✓ Caminho encontrado!");
                    Logger.sendMessage("§7• Nodes: " + result.path.size());
                    Logger.sendMessage("§7• Tempo: " + result.timeMs + "ms");
                    Logger.sendMessage("§7• Nodes explorados: " + result.nodesExplored);

                    if (result.isPartial) {
                        Logger.sendMessage("§e⚠ Caminho parcial - chegando o mais próximo possível");
                    }

                    // Mostrar tipos de movimento usados
                    showMovementTypes(result.path);

                    // INICIAR MOVIMENTO REAL COM RENDERIZAÇÃO
                    movementManager.startExecution(result.path);

                    Logger.sendMessage("§a[Rato Client] §7Movimento iniciado! Seguindo caminho...");
                    Logger.sendMessage("§7Use §f/ratoclient stop §7para parar a qualquer momento.");

                    // Manter renderização ativa enquanto executa
                    new Thread(() -> {
                        try {
                            while (movementManager.isExecuting()) {
                                movementManager.keepRenderingActive();
                                Thread.sleep(1000); // Verificar a cada segundo
                            }
                        } catch (InterruptedException e) {
                            // Ignore
                        }
                    }).start();

                } else {
                    Logger.sendMessage("§c✗ Falha no pathfinding");
                    Logger.sendMessage("§c• Erro: " + result.errorMessage);
                    Logger.sendMessage("§7• Nodes explorados: " + result.nodesExplored);
                    Logger.sendMessage("§7• Tempo: " + result.timeMs + "ms");
                    Logger.sendMessage("§7Tentando sistema Hybrid como fallback...");

                    // Fallback para o sistema antigo
                    boolean success = hybridManager.startPathfinding(goal);
                    if (success) {
                        Logger.sendMessage("§a[Rato Client] §7Fallback Hybrid iniciado!");
                    } else {
                        Logger.sendMessage("§c[Rato Client] §7Falha completa no pathfinding!");
                    }
                }
            }).start();

        } catch (NumberFormatException e) {
            Logger.sendMessage("§cCoordenadas inválidas! Use números válidos.");
            Logger.sendMessage("§7Exemplo: /ratoclient goto 100 64 200");
        }
    }

    /**
     * Mostra os tipos de movimento usados no caminho
     */
    private void showMovementTypes(java.util.List<com.rato.addons.pathfinding.BaritoneStylePathfinder.BaritoneNode> path) {
        if (path.size() <= 1) return;

        int traverse = 0, ascend = 0, descend = 0, diagonal = 0, fall = 0;
        java.util.List<String> jumpLocations = new java.util.ArrayList<>();

        for (int i = 0; i < path.size(); i++) {
            com.rato.addons.pathfinding.BaritoneStylePathfinder.BaritoneNode node = path.get(i);
            switch (node.movementType) {
                case TRAVERSE: traverse++; break;
                case ASCEND:
                    ascend++;
                    // Registrar localização do pulo
                    jumpLocations.add(String.format("§6[%d,%d,%d]",
                        node.position.getX(), node.position.getY(), node.position.getZ()));
                    break;
                case DESCEND: descend++; break;
                case DIAGONAL: diagonal++; break;
                case FALL: fall++; break;
            }
        }

        Logger.sendMessage("§7• Movimentos: §fTraverse(" + traverse + ") §6Ascend(" + ascend +
                         ") §dDescend(" + descend + ") §aDiagonal(" + diagonal + ") §cFall(" + fall + ")");

        // Mostrar localizações dos pulos se houver
        if (!jumpLocations.isEmpty()) {
            Logger.sendMessage("§6• Pulos necessários em: " + String.join(" ", jumpLocations));
            Logger.sendMessage("§7  (Coordenadas onde o bot irá pular automaticamente)");
        }
    }
    
    /**
     * Processa comando stop
     */
    private void handleStopCommand() {
        boolean wasActive = false;

        // Parar MovementManager primeiro (novo sistema)
        if (movementManager.isExecuting()) {
            movementManager.stopExecution();
            wasActive = true;
        }

        if (pathfindingManager.isActive()) {
            pathfindingManager.stopPathfinding();
            wasActive = true;
        }

        if (baritoneManager.isActive()) {
            baritoneManager.stopPathfinding();
            wasActive = true;
        }

        if (mucifexManager.isActive()) {
            mucifexManager.stopPathfinding();
            wasActive = true;
        }

        if (enhancedManager.isActive()) {
            enhancedManager.stopPathfinding();
            wasActive = true;
        }

        if (hybridManager.isActive()) {
            hybridManager.stopPathfinding();
            wasActive = true;
        }

        if (wasActive) {
            Logger.sendMessage("§a[Rato Client] §7Sistema parado!");
        } else {
            Logger.sendMessage("§e[Rato Client] §7Nenhum sistema ativo.");
        }
    }
    
    /**
     * Processa comando status
     */
    private void handleStatusCommand() {
        Logger.sendMessage("§6[Rato Client] §7Status do Sistema:");
        
        if (pathfindingManager.isActive()) {
            String stats = pathfindingManager.getDetailedStatus();
            Logger.sendMessage("§aAtivo: §7" + stats);
        } else {
            Logger.sendMessage("§7Inativo");
        }
        
        // Mostrar configurações atuais
        Logger.sendMessage("§7Configurações:");
        Logger.sendMessage("§7- Velocidade de movimento: §f" + pathfindingManager.getMovementSpeed());
        Logger.sendMessage("§7- Suavização de rotação: §f" + pathfindingManager.getRotationSmoothness());
        Logger.sendMessage("§7- Renderização visual: §f" + (pathfindingManager.isRenderingEnabled() ? "Ativada" : "Desativada"));
    }
    
    /**
     * Processa comando config
     */
    private void handleConfigCommand(String[] args) {
        if (args.length < 3) {
            showConfigHelp();
            return;
        }
        
        String setting = args[1].toLowerCase();
        String value = args[2];
        
        switch (setting) {
            case "speed":
            case "velocidade":
                try {
                    float speed = Float.parseFloat(value);
                    if (speed > 0 && speed <= 2.0f) {
                        pathfindingManager.setMovementSpeed(speed);
                        Logger.sendMessage("§a[Rato Client] §7Velocidade definida para: §f" + speed);
                    } else {
                        Logger.sendMessage("§cVelocidade deve estar entre 0.1 e 2.0");
                    }
                } catch (NumberFormatException e) {
                    Logger.sendMessage("§cValor inválido para velocidade!");
                }
                break;
                
            case "smoothness":
            case "suavidade":
                try {
                    float smoothness = Float.parseFloat(value);
                    if (smoothness > 0 && smoothness <= 1.0f) {
                        pathfindingManager.setRotationSmoothness(smoothness);
                        Logger.sendMessage("§a[Rato Client] §7Suavidade definida para: §f" + smoothness);
                    } else {
                        Logger.sendMessage("§cSuavidade deve estar entre 0.1 e 1.0");
                    }
                } catch (NumberFormatException e) {
                    Logger.sendMessage("§cValor inválido para suavidade!");
                }
                break;
                
            case "render":
            case "visual":
                boolean enable = value.equalsIgnoreCase("true") || value.equalsIgnoreCase("on") || value.equalsIgnoreCase("sim");
                pathfindingManager.setRenderingEnabled(enable);
                Logger.sendMessage("§a[Rato Client] §7Renderização visual: §f" + (enable ? "Ativada" : "Desativada"));
                break;
                
            default:
                showConfigHelp();
                break;
        }
    }
    
    /**
     * Processa comando baritone
     */
    private void handleBaritoneCommand(String[] args) {
        if (args.length < 2) {
            showBaritoneHelp();
            return;
        }

        String action = args[1].toLowerCase();

        switch (action) {
            case "goto":
                if (args.length < 5) {
                    Logger.sendMessage("§cUso: /ratoclient baritone goto <x> <y> <z>");
                    return;
                }

                try {
                    double x = Double.parseDouble(args[2]);
                    double y = Double.parseDouble(args[3]);
                    double z = Double.parseDouble(args[4]);

                    Vec3 destination = new Vec3(x, y, z);
                    Vec3 playerPos = mc.thePlayer.getPositionVector();
                    double distance = playerPos.distanceTo(destination);

                    Logger.sendMessage("§6[Baritone] §7Iniciando pathfinding segmentado...");
                    Logger.sendMessage("§7Destino: §f" + String.format("%.1f, %.1f, %.1f", x, y, z));
                    Logger.sendMessage("§7Distância: §f" + String.format("%.1f", distance) + " blocos");

                    boolean success = baritoneManager.startPathfinding(destination);

                    if (success) {
                        Logger.sendMessage("§a[Baritone] §7Sistema iniciado com sucesso!");
                        Logger.sendMessage("§7Calculando segmentos automaticamente...");
                    } else {
                        Logger.sendMessage("§c[Baritone] §7Falha ao iniciar sistema!");
                    }

                } catch (NumberFormatException e) {
                    Logger.sendMessage("§cCoordenadas inválidas!");
                }
                break;

            case "stop":
                if (baritoneManager.isActive()) {
                    baritoneManager.stopPathfinding();
                    Logger.sendMessage("§a[Baritone] §7Sistema parado!");
                } else {
                    Logger.sendMessage("§e[Baritone] §7Sistema não está ativo.");
                }
                break;

            case "status":
                Logger.sendMessage("§6[Baritone] §7Status do Sistema:");
                Logger.sendMessage("§7" + baritoneManager.getDetailedStatus());
                Logger.sendMessage("§7Performance: " + baritoneManager.getPerformanceStats());
                break;

            case "debug":
                if (args.length > 2) {
                    boolean enable = args[2].equalsIgnoreCase("on") || args[2].equalsIgnoreCase("true");
                    baritoneManager.setDebugMode(enable);
                    Logger.sendMessage("§a[Baritone] §7Debug: " + (enable ? "Ativado" : "Desativado"));
                } else {
                    Logger.sendMessage("§7[Baritone] Debug Info:");
                    Logger.sendMessage(baritoneManager.getDebugInfo());
                }
                break;

            case "clearcache":
                baritoneManager.clearChunkCache();
                Logger.sendMessage("§a[Baritone] §7Cache de chunks limpo!");
                break;

            default:
                showBaritoneHelp();
                break;
        }
    }

    /**
     * Processa comando mucifex
     */
    private void handleMucifexCommand(String[] args) {
        if (args.length < 2) {
            showMucifexHelp();
            return;
        }

        String action = args[1].toLowerCase();

        switch (action) {
            case "goto":
                if (args.length < 5) {
                    Logger.sendMessage("§cUso: /ratoclient mucifex goto <x> <y> <z>");
                    return;
                }

                try {
                    double x = Double.parseDouble(args[2]);
                    double y = Double.parseDouble(args[3]);
                    double z = Double.parseDouble(args[4]);

                    Vec3 destination = new Vec3(x, y, z);
                    Vec3 playerPos = mc.thePlayer.getPositionVector();
                    double distance = playerPos.distanceTo(destination);

                    Logger.sendMessage("§6[Mucifex] §7Iniciando pathfinding otimizado...");
                    Logger.sendMessage("§7Destino: §f" + String.format("%.1f, %.1f, %.1f", x, y, z));
                    Logger.sendMessage("§7Distância: §f" + String.format("%.1f", distance) + " blocos");

                    boolean success = mucifexManager.startPathfinding(destination);

                    if (success) {
                        Logger.sendMessage("§a[Mucifex] §7Sistema iniciado com sucesso!");
                        Logger.sendMessage("§7Calculando caminho assincronamente...");
                    } else {
                        Logger.sendMessage("§c[Mucifex] §7Falha ao iniciar sistema!");
                    }

                } catch (NumberFormatException e) {
                    Logger.sendMessage("§cCoordenadas inválidas!");
                }
                break;

            case "stop":
                if (mucifexManager.isActive()) {
                    mucifexManager.stopPathfinding();
                    Logger.sendMessage("§a[Mucifex] §7Sistema parado!");
                } else {
                    Logger.sendMessage("§e[Mucifex] §7Sistema não está ativo.");
                }
                break;

            case "status":
                Logger.sendMessage("§6[Mucifex] §7Status do Sistema:");
                Logger.sendMessage("§7" + mucifexManager.getDetailedStatus());
                Logger.sendMessage("§7Performance: " + mucifexManager.getPerformanceStats());
                break;

            case "debug":
                if (args.length > 2) {
                    boolean enable = args[2].equalsIgnoreCase("on") || args[2].equalsIgnoreCase("true");
                    mucifexManager.setDebugMode(enable);
                    Logger.sendMessage("§a[Mucifex] §7Debug: " + (enable ? "Ativado" : "Desativado"));
                } else {
                    Logger.sendMessage("§7[Mucifex] Debug Info:");
                    Logger.sendMessage(mucifexManager.getDebugInfo());
                }
                break;

            case "recalc":
                mucifexManager.forceRecalculation();
                Logger.sendMessage("§a[Mucifex] §7Recálculo forçado!");
                break;

            default:
                showMucifexHelp();
                break;
        }
    }

    /**
     * Processa comando debug
     */
    private void handleDebugCommand(String[] args) {
        if (args.length < 2) {
            Logger.sendMessage("§7Debug: /ratoclient debug <on|off|info>");
            return;
        }
        
        String action = args[1].toLowerCase();
        
        switch (action) {
            case "on":
                pathfindingManager.setDebugMode(true);
                Logger.sendMessage("§a[Rato Client] §7Modo debug ativado!");
                break;
                
            case "off":
                pathfindingManager.setDebugMode(false);
                Logger.sendMessage("§a[Rato Client] §7Modo debug desativado!");
                break;
                
            case "info":
                Logger.sendMessage("§6[Rato Client] §7Informações de Debug:");
                Logger.sendMessage(pathfindingManager.getDebugInfo());
                break;

            case "clearcache":
                pathfindingManager.clearTerrainCache();
                Logger.sendMessage("§a[Rato Client] §7Cache de terreno limpo!");
                break;

            default:
                Logger.sendMessage("§7Debug: /ratoclient debug <on|off|info|clearcache>");
                break;
        }
    }
    
    /**
     * Mostra ajuda principal
     */
    private void showHelp() {
        Logger.sendMessage("§6=== RATO CLIENT - PATHFINDING PROFISSIONAL ===");
        Logger.sendMessage("§f/ratoclient goto <x> <y> <z> §7- Navegar (Baritone-Style Aprimorado)");
        Logger.sendMessage("§f/ratoclient hybrid <comando> §7- Sistema Híbrido Mucifex + Stevebot");
        Logger.sendMessage("§f/ratoclient enhanced <comando> §7- Sistema Enhanced baseado no Mucifex");
        Logger.sendMessage("§f/ratoclient mucifex <comando> §7- Sistema Mucifex otimizado");
        Logger.sendMessage("§f/ratoclient baritone <comando> §7- Sistema Baritone avançado");
        Logger.sendMessage("§f/ratoclient stop §7- Parar pathfinding atual");
        Logger.sendMessage("§f/ratoclient status §7- Ver status do sistema");
        Logger.sendMessage("§f/ratoclient config <setting> <value> §7- Configurar sistema");
        Logger.sendMessage("§f/ratoclient debug <on|off|info|clearcache> §7- Modo debug");
        Logger.sendMessage("§f/ratoclient help §7- Mostrar esta ajuda");
        Logger.sendMessage("");
        Logger.sendMessage("§a✨ NOVO: §7Comando principal agora usa Baritone-Style!");
        Logger.sendMessage("§7• Movimentos modulares com validação rigorosa");
        Logger.sendMessage("§7• Renderização visual com cores por movimento");
        Logger.sendMessage("§7• Fallback automático para Hybrid");
        Logger.sendMessage("");
        Logger.sendMessage("§eExemplos:");
        Logger.sendMessage("§7/ratoclient goto 100 64 200");
        Logger.sendMessage("§7/ratoclient config speed 1.2");
        Logger.sendMessage("§7/ratoclient config render true");
    }
    
    /**
     * Mostra ajuda de configuração
     */
    private void showConfigHelp() {
        Logger.sendMessage("§6=== CONFIGURAÇÕES DO PATHFINDING ===");
        Logger.sendMessage("§f/ratoclient config speed <0.1-2.0> §7- Velocidade de movimento");
        Logger.sendMessage("§f/ratoclient config smoothness <0.1-1.0> §7- Suavidade da rotação");
        Logger.sendMessage("§f/ratoclient config render <true|false> §7- Renderização visual");
        Logger.sendMessage("");
        Logger.sendMessage("§eExemplos:");
        Logger.sendMessage("§7/ratoclient config speed 1.5");
        Logger.sendMessage("§7/ratoclient config smoothness 0.8");
        Logger.sendMessage("§7/ratoclient config render true");
    }

    /**
     * Mostra ajuda do Baritone
     */
    private void showBaritoneHelp() {
        Logger.sendMessage("§6=== BARITONE - PATHFINDING AVANÇADO ===");
        Logger.sendMessage("§f/ratoclient baritone goto <x> <y> <z> §7- Pathfinding segmentado");
        Logger.sendMessage("§f/ratoclient baritone stop §7- Parar Baritone");
        Logger.sendMessage("§f/ratoclient baritone status §7- Status detalhado");
        Logger.sendMessage("§f/ratoclient baritone debug <on|off> §7- Modo debug");
        Logger.sendMessage("§f/ratoclient baritone clearcache §7- Limpar cache de chunks");
        Logger.sendMessage("");
        Logger.sendMessage("§eCaracterísticas do Baritone:");
        Logger.sendMessage("§7• Cálculo segmentado para longas distâncias");
        Logger.sendMessage("§7• Cache inteligente de chunks");
        Logger.sendMessage("§7• Otimização A* avançada");
        Logger.sendMessage("§7• Execução em thread separada");
        Logger.sendMessage("§7• Detecção automática de obstáculos");
    }

    /**
     * Mostra ajuda do Mucifex
     */
    private void showMucifexHelp() {
        Logger.sendMessage("§6=== MUCIFEX - PATHFINDING OTIMIZADO ===");
        Logger.sendMessage("§f/ratoclient mucifex goto <x> <y> <z> §7- Pathfinding inteligente");
        Logger.sendMessage("§f/ratoclient mucifex stop §7- Parar Mucifex");
        Logger.sendMessage("§f/ratoclient mucifex status §7- Status detalhado");
        Logger.sendMessage("§f/ratoclient mucifex debug <on|off> §7- Modo debug");
        Logger.sendMessage("§f/ratoclient mucifex recalc §7- Forçar recálculo");
        Logger.sendMessage("");
        Logger.sendMessage("§eCaracterísticas do Mucifex:");
        Logger.sendMessage("§7• A* otimizado com cache inteligente");
        Logger.sendMessage("§7• Detecção automática de pulos");
        Logger.sendMessage("§7• Execução assíncrona não-bloqueante");
        Logger.sendMessage("§7• Recálculo automático em falhas");
        Logger.sendMessage("§7• Análise avançada de terreno");
        Logger.sendMessage("§7• Sistema de cooldown para recálculos");
    }

    /**
     * Processa comando enhanced
     */
    private void handleEnhancedCommand(String[] args) {
        if (args.length < 2) {
            showEnhancedHelp();
            return;
        }

        String action = args[1].toLowerCase();

        switch (action) {
            case "goto":
                if (args.length < 5) {
                    Logger.sendMessage("§cUso: /ratoclient enhanced goto <x> <y> <z>");
                    return;
                }

                try {
                    double x = Double.parseDouble(args[2]);
                    double y = Double.parseDouble(args[3]);
                    double z = Double.parseDouble(args[4]);

                    Vec3 destination = new Vec3(x, y, z);
                    Vec3 playerPos = mc.thePlayer.getPositionVector();
                    double distance = playerPos.distanceTo(destination);

                    Logger.sendMessage("§6[Enhanced] §7Iniciando sistema baseado no Mucifex...");
                    Logger.sendMessage("§7Destino: §f" + String.format("%.1f, %.1f, %.1f", x, y, z));
                    Logger.sendMessage("§7Distância: §f" + String.format("%.1f", distance) + " blocos");

                    boolean success = enhancedManager.startPathfinding(destination);

                    if (success) {
                        Logger.sendMessage("§a[Enhanced] §7Sistema iniciado com sucesso!");
                        Logger.sendMessage("§7Movimento inteligente com rotação suave...");
                    } else {
                        Logger.sendMessage("§c[Enhanced] §7Falha ao iniciar sistema!");
                    }

                } catch (NumberFormatException e) {
                    Logger.sendMessage("§cCoordenadas inválidas!");
                }
                break;

            case "stop":
                if (enhancedManager.isActive()) {
                    enhancedManager.stopPathfinding();
                    Logger.sendMessage("§a[Enhanced] §7Sistema parado!");
                } else {
                    Logger.sendMessage("§e[Enhanced] §7Sistema não está ativo.");
                }
                break;

            case "status":
                Logger.sendMessage("§6[Enhanced] §7Status do Sistema:");
                Logger.sendMessage("§7" + enhancedManager.getDetailedStatus());
                Logger.sendMessage("§7Performance: " + enhancedManager.getPerformanceStats());
                break;

            case "debug":
                if (args.length > 2) {
                    boolean enable = args[2].equalsIgnoreCase("on") || args[2].equalsIgnoreCase("true");
                    enhancedManager.setDebugMode(enable);
                    Logger.sendMessage("§a[Enhanced] §7Debug: " + (enable ? "Ativado" : "Desativado"));
                } else {
                    Logger.sendMessage("§7[Enhanced] Debug Info:");
                    Logger.sendMessage(enhancedManager.getDebugInfo());
                }
                break;

            case "recalc":
                enhancedManager.forceRecalculation();
                Logger.sendMessage("§a[Enhanced] §7Recálculo forçado!");
                break;

            default:
                showEnhancedHelp();
                break;
        }
    }

    /**
     * Mostra ajuda do Enhanced
     */
    private void showEnhancedHelp() {
        Logger.sendMessage("§6=== ENHANCED - PATHFINDING BASEADO NO MUCIFEX ===");
        Logger.sendMessage("§f/ratoclient enhanced goto <x> <y> <z> §7- Pathfinding inteligente");
        Logger.sendMessage("§f/ratoclient enhanced stop §7- Parar Enhanced");
        Logger.sendMessage("§f/ratoclient enhanced status §7- Status detalhado");
        Logger.sendMessage("§f/ratoclient enhanced debug <on|off> §7- Modo debug");
        Logger.sendMessage("§f/ratoclient enhanced recalc §7- Forçar recálculo");
        Logger.sendMessage("");
        Logger.sendMessage("§eCaracterísticas do Enhanced:");
        Logger.sendMessage("§7• A* otimizado baseado no Mucifex original");
        Logger.sendMessage("§7• Detecção inteligente de pulos e quedas");
        Logger.sendMessage("§7• Sistema de movimento por ângulos");
        Logger.sendMessage("§7• Rotação suave com predição");
        Logger.sendMessage("§7• Otimização de caminho automática");
        Logger.sendMessage("§7• Movimento natural como player real");
    }

    /**
     * Processa comando hybrid
     */
    private void handleHybridCommand(String[] args) {
        if (args.length < 2) {
            showHybridHelp();
            return;
        }

        String action = args[1].toLowerCase();

        switch (action) {
            case "goto":
                if (args.length < 5) {
                    Logger.sendMessage("§cUso: /ratoclient hybrid goto <x> <y> <z>");
                    return;
                }

                try {
                    double x = Double.parseDouble(args[2]);
                    double y = Double.parseDouble(args[3]);
                    double z = Double.parseDouble(args[4]);

                    Vec3 destination = new Vec3(x, y, z);
                    Vec3 playerPos = mc.thePlayer.getPositionVector();
                    double distance = playerPos.distanceTo(destination);

                    Logger.sendMessage("§6[Hybrid] §7Iniciando sistema híbrido Mucifex + Stevebot...");
                    Logger.sendMessage("§7Destino: §f" + String.format("%.1f, %.1f, %.1f", x, y, z));
                    Logger.sendMessage("§7Distância: §f" + String.format("%.1f", distance) + " blocos");

                    boolean success = hybridManager.startPathfinding(destination);

                    if (success) {
                        Logger.sendMessage("§a[Hybrid] §7Sistema híbrido iniciado com sucesso!");
                        Logger.sendMessage("§7Combinando as melhores técnicas do Mucifex e Stevebot...");
                    } else {
                        Logger.sendMessage("§c[Hybrid] §7Falha ao iniciar sistema híbrido!");
                    }

                } catch (NumberFormatException e) {
                    Logger.sendMessage("§cCoordenadas inválidas!");
                }
                break;

            case "stop":
                if (hybridManager.isActive()) {
                    hybridManager.stopPathfinding();
                    Logger.sendMessage("§a[Hybrid] §7Sistema híbrido parado!");
                } else {
                    Logger.sendMessage("§e[Hybrid] §7Sistema híbrido não está ativo.");
                }
                break;

            case "status":
                Logger.sendMessage("§6[Hybrid] §7Status do Sistema Híbrido:");
                Logger.sendMessage("§7" + hybridManager.getDetailedStatus());
                Logger.sendMessage("§7Performance: " + hybridManager.getPerformanceStats());
                break;

            case "debug":
                if (args.length > 2) {
                    boolean enable = args[2].equalsIgnoreCase("on") || args[2].equalsIgnoreCase("true");
                    hybridManager.setDebugMode(enable);
                    Logger.sendMessage("§a[Hybrid] §7Debug: " + (enable ? "Ativado" : "Desativado"));
                } else {
                    Logger.sendMessage("§7[Hybrid] Debug Info:");
                    Logger.sendMessage(hybridManager.getDebugInfo());
                }
                break;

            case "recalc":
                hybridManager.forceRecalculation();
                Logger.sendMessage("§a[Hybrid] §7Recálculo híbrido forçado!");
                break;

            default:
                showHybridHelp();
                break;
        }
    }

    /**
     * Mostra ajuda do Hybrid
     */
    private void showHybridHelp() {
        Logger.sendMessage("§6=== HYBRID - MUCIFEX + STEVEBOT ===");
        Logger.sendMessage("§f/ratoclient hybrid goto <x> <y> <z> §7- Pathfinding híbrido");
        Logger.sendMessage("§f/ratoclient hybrid stop §7- Parar Hybrid");
        Logger.sendMessage("§f/ratoclient hybrid status §7- Status detalhado");
        Logger.sendMessage("§f/ratoclient hybrid debug <on|off> §7- Modo debug");
        Logger.sendMessage("§f/ratoclient hybrid recalc §7- Forçar recálculo");
        Logger.sendMessage("");
        Logger.sendMessage("§eCaracterísticas do Sistema Híbrido:");
        Logger.sendMessage("§7• Combina A* do Mucifex com validações do Stevebot");
        Logger.sendMessage("§7• Detecção inteligente de slabs e stairs");
        Logger.sendMessage("§7• Sistema de custos adaptativos");
        Logger.sendMessage("§7• Movimento por 8 direções como Stevebot");
        Logger.sendMessage("§7• Otimização de caminho avançada");
        Logger.sendMessage("§7• Validação robusta de movimento");
        Logger.sendMessage("§7• Performance otimizada com threads");
    }
}
