package com.rato.addons.pathfinding.movement;

import com.rato.addons.config.RatoAddonsConfigSimple;
import net.minecraft.client.Minecraft;
import net.minecraft.util.MathHelper;
import net.minecraft.util.Vec3;
import net.minecraftforge.client.event.RenderWorldLastEvent;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;

/**
 * Controlador de câmera simples baseado no Baritone
 * Sem complexidade desnecessária - apenas rotação suave e eficaz
 */
public class SimpleCameraController {

    private final Minecraft mc = Minecraft.getMinecraft();

    // Estado simples
    private float targetYaw;
    private float targetPitch;
    private boolean hasTarget = false;
    private boolean registered = false;

    // Configurações adaptativas para diferentes velocidades - AUMENTADAS
    private static final float BASE_ROTATION_SPEED = 3.0f; // Velocidade base muito mais alta
    private static final float MIN_THRESHOLD = 0.5f; // Threshold menor para parar
    private static final float MOVEMENT_THRESHOLD = 8.0f; // Threshold menor para permitir movimento

    // Sistema de interpolação adaptativo
    private long lastFrameTime = 0;
    private float targetYawSmooth = 0;
    private float currentYawSmooth = 0;

    // Detecção de velocidade avançada para adaptação
    private Vec3 lastPlayerPos = null;
    private float currentPlayerSpeed = 0.0f;
    private float averageSpeed = 0.0f;
    private final float[] speedHistory = new float[10]; // Histórico de velocidade
    private int speedHistoryIndex = 0;
    private static final float NORMAL_SPEED_THRESHOLD = 0.3f; // Velocidade normal de caminhada
    private static final float HIGH_SPEED_THRESHOLD = 1.0f; // Speed I
    private static final float VERY_HIGH_SPEED_THRESHOLD = 2.0f; // Speed II+

    // Configurações dinâmicas baseadas na velocidade
    private float dynamicLerpFactor = 0.01f;
    private float dynamicRotationSpeed = 1.0f;

    // Constante para velocidade de rotação base - AUMENTADA
    private static final float ROTATION_SPEED = 4.0f;

    /**
     * Ativa o controlador
     */
    public void enable() {
        if (!registered) {
            MinecraftForge.EVENT_BUS.register(this);
            registered = true;
        }
    }

    /**
     * Desativa o controlador
     */
    public void disable() {
        if (registered) {
            MinecraftForge.EVENT_BUS.unregister(this);
            registered = false;
            hasTarget = false;
        }
    }

    /**
     * Define target de rotação
     */
    public void setTargetYaw(float yaw) {
        this.targetYaw = normalizeYaw(yaw);
        this.targetYawSmooth = this.targetYaw;
        this.hasTarget = true;

        // Inicializar posição atual se necessário
        if (mc.thePlayer != null && currentYawSmooth == 0) {
            currentYawSmooth = mc.thePlayer.rotationYaw;
        }

        // Atualizar detecção de velocidade
        updateSpeedDetection();
    }

    /**
     * Verifica se pode começar a andar - MAIS PERMISSIVO
     */
    public boolean canStartMoving() {
        if (!hasTarget || mc.thePlayer == null)
            return true;

        float currentYaw = mc.thePlayer.rotationYaw;
        float yawDiff = Math.abs(MathHelper.wrapAngleTo180_float(targetYaw - currentYaw));

        // Permitir movimento mesmo com diferença maior - evita parar muito
        return yawDiff < MOVEMENT_THRESHOLD || yawDiff < 25.0f; // Mais permissivo
    }

    /**
     * Verifica se está próximo do target
     */
    public boolean isNearTarget() {
        if (!hasTarget || mc.thePlayer == null)
            return true;

        float currentYaw = mc.thePlayer.rotationYaw;
        float yawDiff = Math.abs(MathHelper.wrapAngleTo180_float(targetYaw - currentYaw));

        return yawDiff < MIN_THRESHOLD;
    }

    /**
     * Renderização - movimento ultra-fluido independente de TPS
     */
    @SubscribeEvent
    public void onRenderWorld(RenderWorldLastEvent event) {
        if (!hasTarget || mc.thePlayer == null) {
            return;
        }

        try {
            // Atualizar detecção de velocidade a cada frame
            updateSpeedDetection();

            // Calcular delta time baseado em nano time para máxima precisão
            long currentTime = System.nanoTime();
            float deltaTime = lastFrameTime > 0 ? (currentTime - lastFrameTime) / 1_000_000_000.0f : 0.016f; // 16ms
                                                                                                             // default
            lastFrameTime = currentTime;

            // Adaptar delta time baseado na velocidade (velocidades altas precisam de mais
            // tempo)
            deltaTime = adaptDeltaTimeForSpeed(deltaTime);

            // Interpolação adaptativa baseada na velocidade
            float lerpFactor = calculateDynamicLerpFactor(deltaTime);

            // Calcular diferença angular
            float yawDiff = MathHelper.wrapAngleTo180_float(targetYawSmooth - currentYawSmooth);

            // Aplicar interpolação suave
            currentYawSmooth += yawDiff * lerpFactor;
            currentYawSmooth = normalizeYaw(currentYawSmooth);

            // Aplicar ao player com velocidade adaptativa
            float playerYaw = mc.thePlayer.rotationYaw;
            float finalYawDiff = MathHelper.wrapAngleTo180_float(currentYawSmooth - playerYaw);

            if (Math.abs(finalYawDiff) > MIN_THRESHOLD) {
                // Rotação de emergência para diferenças muito grandes
                if (Math.abs(finalYawDiff) > 90.0f) {
                    // Rotação instantânea para diferenças extremas
                    float emergencyRotation = finalYawDiff * 0.8f; // 80% da diferença instantaneamente
                    mc.thePlayer.rotationYaw += emergencyRotation;
                } else {
                    // Velocidade baseada na diferença angular e velocidade do jogador
                    float speed = calculateAdaptiveSpeed(Math.abs(finalYawDiff));
                    float rotationAmount = finalYawDiff * speed * deltaTime * 60.0f; // 60 FPS base
                    mc.thePlayer.rotationYaw += rotationAmount;
                }

                mc.thePlayer.rotationYaw = normalizeYaw(mc.thePlayer.rotationYaw);
            } else {
                // Chegou próximo o suficiente
                hasTarget = false;
            }

        } catch (Exception e) {
            // Falha silenciosa
            hasTarget = false;
        }
    }

    /**
     * Atualiza detecção de velocidade do jogador
     */
    private void updateSpeedDetection() {
        if (mc.thePlayer == null)
            return;

        Vec3 currentPos = mc.thePlayer.getPositionVector();

        if (lastPlayerPos != null) {
            // Calcular velocidade atual
            double distance = currentPos.distanceTo(lastPlayerPos);
            currentPlayerSpeed = (float) (distance * 20.0); // Converter para blocos/segundo (20 TPS)

            // Atualizar histórico de velocidade
            speedHistory[speedHistoryIndex] = currentPlayerSpeed;
            speedHistoryIndex = (speedHistoryIndex + 1) % speedHistory.length;

            // Calcular velocidade média
            float totalSpeed = 0;
            for (float speed : speedHistory) {
                totalSpeed += speed;
            }
            averageSpeed = totalSpeed / speedHistory.length;

            // Atualizar configurações dinâmicas baseadas na velocidade
            updateDynamicSettings();
        }

        lastPlayerPos = currentPos;
    }

    /**
     * Atualiza configurações dinâmicas baseadas na velocidade detectada
     */
    private void updateDynamicSettings() {
        if (!RatoAddonsConfigSimple.pathfindingAutoSpeedDetection) {
            // Se detecção automática estiver desabilitada, usar configurações padrão
            dynamicLerpFactor = RatoAddonsConfigSimple.pathfindingSmoothness;
            dynamicRotationSpeed = RatoAddonsConfigSimple.pathfindingRotationSpeed;
            return;
        }

        if (averageSpeed > VERY_HIGH_SPEED_THRESHOLD) {
            // Speed II+ - Ainda rápido mas um pouco mais suave
            dynamicLerpFactor = RatoAddonsConfigSimple.pathfindingSmoothness * 0.8f; // Menos suave
            dynamicRotationSpeed = RatoAddonsConfigSimple.pathfindingRotationSpeed * 0.8f; // Menos redução
        } else if (averageSpeed > HIGH_SPEED_THRESHOLD) {
            // Speed I - Rotação rápida
            dynamicLerpFactor = RatoAddonsConfigSimple.pathfindingSmoothness * 0.9f; // Pouco mais suave
            dynamicRotationSpeed = RatoAddonsConfigSimple.pathfindingRotationSpeed * 0.9f; // Pouco mais lento
        } else if (averageSpeed > NORMAL_SPEED_THRESHOLD) {
            // Velocidade normal - Configurações padrão mas mais rápidas
            dynamicLerpFactor = RatoAddonsConfigSimple.pathfindingSmoothness;
            dynamicRotationSpeed = RatoAddonsConfigSimple.pathfindingRotationSpeed * 1.2f; // Mais rápido
        } else {
            // Velocidade baixa - Rotação muito responsiva
            dynamicLerpFactor = RatoAddonsConfigSimple.pathfindingSmoothness * 1.5f;
            dynamicRotationSpeed = RatoAddonsConfigSimple.pathfindingRotationSpeed * 1.5f; // Muito mais rápido
        }
    }

    /**
     * Adapta delta time baseado na velocidade para evitar bugs com speed alto
     */
    private float adaptDeltaTimeForSpeed(float deltaTime) {
        // Para velocidades muito altas, permitir delta time maior para suavizar
        if (averageSpeed > VERY_HIGH_SPEED_THRESHOLD) {
            return MathHelper.clamp_float(deltaTime, 0.001f, 0.1f); // Até 100ms para speed muito alto
        } else if (averageSpeed > HIGH_SPEED_THRESHOLD) {
            return MathHelper.clamp_float(deltaTime, 0.001f, 0.08f); // Até 80ms para speed alto
        } else {
            return MathHelper.clamp_float(deltaTime, 0.001f, 0.05f); // Padrão: até 50ms
        }
    }

    /**
     * Calcula fator de interpolação dinâmico baseado na velocidade - MAIS
     * RESPONSIVO
     */
    private float calculateDynamicLerpFactor(float deltaTime) {
        // Usar configuração dinâmica baseada na velocidade
        float baseLerpFactor = dynamicLerpFactor;

        // Aplicar interpolação mais responsiva - menos exponencial
        float responsiveFactor = 1.0f - (float) Math.pow(1.0f - baseLerpFactor, deltaTime * 30.0f); // Reduzido de 60
                                                                                                    // para 30

        // Garantir um mínimo de responsividade
        return Math.max(responsiveFactor, 0.1f); // Mínimo de 10% por frame
    }

    /**
     * Calcula velocidade adaptativa para movimento fluido - MUITO MAIS RÁPIDA
     */
    private float calculateAdaptiveSpeed(float angleDiff) {
        // Usar velocidade dinâmica baseada na detecção de speed
        float baseSpeed = dynamicRotationSpeed;

        // Ajustar baseado na diferença angular - MULTIPLICADORES MAIORES
        if (angleDiff > 90.0f) {
            return baseSpeed * 5.0f; // Muito rápido para grandes rotações
        } else if (angleDiff > 45.0f) {
            return baseSpeed * 4.0f; // Rápido para rotações médias
        } else if (angleDiff > 15.0f) {
            return baseSpeed * 3.0f; // Médio-rápido
        } else if (angleDiff > 5.0f) {
            return baseSpeed * 2.0f; // Rápido
        } else {
            return baseSpeed * 1.5f; // Base mais rápida para ajustes finos
        }
    }

    /**
     * Normaliza yaw
     */
    private float normalizeYaw(float yaw) {
        while (yaw > 180.0f)
            yaw -= 360.0f;
        while (yaw < -180.0f)
            yaw += 360.0f;
        return yaw;
    }

    /**
     * Para interpolação
     */
    public void stopInterpolation() {
        hasTarget = false;
    }

    /**
     * Verifica se está interpolando
     */
    public boolean isInterpolating() {
        return hasTarget;
    }

    /**
     * Obtém informações de debug sobre velocidade (para debugging)
     */
    public String getSpeedDebugInfo() {
        if (!RatoAddonsConfigSimple.pathfindingAutoSpeedDetection) {
            return "§7Auto Speed Detection: §cDisabled";
        }

        String speedLevel;
        if (averageSpeed > VERY_HIGH_SPEED_THRESHOLD) {
            speedLevel = "§cVery High (Speed II+)";
        } else if (averageSpeed > HIGH_SPEED_THRESHOLD) {
            speedLevel = "§6High (Speed I)";
        } else if (averageSpeed > NORMAL_SPEED_THRESHOLD) {
            speedLevel = "§aNormal";
        } else {
            speedLevel = "§7Low";
        }

        return String.format("§7Speed: %s §7(%.2f b/s) | Lerp: §e%.4f §7| Rot: §e%.2f",
                speedLevel, averageSpeed, dynamicLerpFactor, dynamicRotationSpeed);
    }
}
