package com.rato.addons.util;

import com.rato.addons.config.RatoAddonsConfigSimple;
import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.OpenGlHelper;
import net.minecraft.client.shader.Framebuffer;
import org.lwjgl.BufferUtils;
import org.lwjgl.opengl.GL11;
import org.lwjgl.opengl.GL12;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Base64;

public class DiscordWebhook {
    
    private static final Minecraft mc = Minecraft.getMinecraft();
    
    public static void sendStaffCheckAlert(String checkType, String details, String extraInfo) {
        if (!RatoAddonsConfigSimple.discordWebhook || RatoAddonsConfigSimple.webhookUrl.isEmpty()) {
            return;
        }

        // Tirar screenshot na thread principal (OpenGL context)
        BufferedImage screenshot = null;
        if (RatoAddonsConfigSimple.autoScreenshot) {
            screenshot = takeScreenshot();
        }

        // Processar upload e envio em thread separada
        final BufferedImage finalScreenshot = screenshot;
        new Thread(() -> {
            try {
                String screenshotUrl = null;
                if (finalScreenshot != null) {
                    screenshotUrl = uploadScreenshot(finalScreenshot);
                }

                // Criar embed
                String embed = createStaffCheckEmbed(checkType, details, extraInfo, screenshotUrl);

                // Enviar para Discord
                sendToDiscord(embed);

            } catch (Exception e) {
                Logger.sendMessage("§c[DISCORD] §fWebhook failed");
            }
        }).start();
    }
    
    private static String uploadScreenshot(BufferedImage screenshot) {
        try {
            // Salvar temporariamente
            File tempFile = File.createTempFile("staffcheck_", ".png");
            ImageIO.write(screenshot, "png", tempFile);

            // Upload para serviço de imagem
            String imageUrl = uploadToImgur(tempFile);

            // Deletar arquivo temporário
            tempFile.delete();

            return imageUrl;

        } catch (Exception e) {
            Logger.sendMessage("§c[DISCORD] §fScreenshot upload failed");
            return null;
        }
    }
    
    private static BufferedImage takeScreenshot() {
        try {
            Framebuffer framebuffer = mc.getFramebuffer();

            if (OpenGlHelper.isFramebufferEnabled()) {
                int width = framebuffer.framebufferTextureWidth;
                int height = framebuffer.framebufferTextureHeight;

                // Bind framebuffer
                framebuffer.bindFramebuffer(false);

                // Criar buffer para pixels
                IntBuffer pixelBuffer = BufferUtils.createIntBuffer(width * height);
                GL11.glReadPixels(0, 0, width, height, GL12.GL_BGRA, GL12.GL_UNSIGNED_INT_8_8_8_8_REV, pixelBuffer);

                // Converter para BufferedImage
                int[] pixelValues = new int[width * height];
                pixelBuffer.get(pixelValues);

                BufferedImage bufferedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);

                // Flip verticalmente (OpenGL tem origem no canto inferior esquerdo)
                for (int y = 0; y < height; y++) {
                    for (int x = 0; x < width; x++) {
                        int pixel = pixelValues[y * width + x];
                        bufferedImage.setRGB(x, height - 1 - y, pixel);
                    }
                }

                // Unbind framebuffer
                mc.getFramebuffer().unbindFramebuffer();

                return bufferedImage;
            }

            return null;
        } catch (Exception e) {
            Logger.sendMessage("§c[DISCORD] §fScreenshot error: " + e.getMessage());
            return null;
        }
    }
    
    private static String uploadToImgur(File imageFile) {
        try {
            // Upload para imgur usando API pública
            URL url = new URL("https://api.imgur.com/3/image");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod("POST");
            connection.setRequestProperty("Authorization", "Client-ID 546c25a59c58ad7"); // Client ID público
            connection.setDoOutput(true);

            // Ler arquivo como bytes
            byte[] imageBytes = java.nio.file.Files.readAllBytes(imageFile.toPath());
            String base64Image = Base64.getEncoder().encodeToString(imageBytes);

            // Criar payload
            String payload = "image=" + java.net.URLEncoder.encode(base64Image, "UTF-8");

            try (OutputStream os = connection.getOutputStream()) {
                os.write(payload.getBytes(StandardCharsets.UTF_8));
            }

            int responseCode = connection.getResponseCode();
            if (responseCode == 200) {
                // Ler resposta
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }

                    // Parse JSON simples para extrair URL
                    String responseStr = response.toString();
                    if (responseStr.contains("\"link\":\"")) {
                        int start = responseStr.indexOf("\"link\":\"") + 8;
                        int end = responseStr.indexOf("\"", start);
                        return responseStr.substring(start, end).replace("\\", "");
                    }
                }
            }

            connection.disconnect();
            return null;

        } catch (Exception e) {
            return null;
        }
    }
    
    private static String createStaffCheckEmbed(String checkType, String details, String extraInfo, String screenshotUrl) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timestamp = sdf.format(new Date());
        
        String playerName = mc.thePlayer != null ? mc.thePlayer.getName() : "Unknown";
        String serverIP = mc.getCurrentServerData() != null ? mc.getCurrentServerData().serverIP : "Singleplayer";
        
        // Determinar cor baseada no tipo de check
        int color = getColorForCheckType(checkType);
        
        StringBuilder embed = new StringBuilder();
        embed.append("{\n");
        embed.append("  \"embeds\": [{\n");
        embed.append("    \"title\": \"🚨 STAFF CHECK DETECTED\",\n");
        embed.append("    \"description\": \"**Type:** ").append(checkType).append("\\n");
        embed.append("**Details:** ").append(details).append("\\n");
        if (extraInfo != null && !extraInfo.isEmpty()) {
            embed.append("**Extra Info:** ").append(extraInfo).append("\\n");
        }
        embed.append("\\n**Player:** ").append(playerName).append("\\n");
        embed.append("**Server:** ").append(serverIP).append("\\n");
        embed.append("**Time:** ").append(timestamp).append("\",\n");
        embed.append("    \"color\": ").append(color).append(",\n");
        
        if (screenshotUrl != null) {
            embed.append("    \"image\": {\n");
            embed.append("      \"url\": \"").append(screenshotUrl).append("\"\n");
            embed.append("    },\n");
        }
        
        embed.append("    \"footer\": {\n");
        embed.append("      \"text\": \"RatoAddons Staff Check System\",\n");
        embed.append("      \"icon_url\": \"https://cdn.discordapp.com/emojis/1234567890.png\"\n");
        embed.append("    },\n");
        embed.append("    \"timestamp\": \"").append(new Date().toInstant().toString()).append("\"\n");
        embed.append("  }]\n");
        embed.append("}");
        
        return embed.toString();
    }
    
    private static int getColorForCheckType(String checkType) {
        switch (checkType.toLowerCase()) {
            case "rotation":
                return 0xFF4444; // Vermelho
            case "teleport":
                return 0x44FF44; // Verde
            case "item swap":
                return 0x4444FF; // Azul
            case "block check":
                return 0xFFAA00; // Laranja
            case "velocity check":
                return 0xFF00FF; // Magenta
            case "player check":
                return 0x00FFFF; // Ciano
            default:
                return 0xFF0000; // Vermelho padrão
        }
    }
    
    private static void sendToDiscord(String jsonPayload) throws IOException {
        URL url = new URL(RatoAddonsConfigSimple.webhookUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("User-Agent", "RatoAddons/1.0");
        connection.setDoOutput(true);
        
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = jsonPayload.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }
        
        int responseCode = connection.getResponseCode();
        if (responseCode == 204) {
            Logger.sendMessage("§a[DISCORD] §fStaff check alert sent successfully!");
        } else {
            Logger.sendMessage("§c[DISCORD] §fFailed to send alert. Response code: " + responseCode);
        }
        
        connection.disconnect();
    }
    
    public static void sendTestMessage() {
        sendStaffCheckAlert("Test", "This is a test message from RatoAddons", "Testing webhook functionality");
    }
}
