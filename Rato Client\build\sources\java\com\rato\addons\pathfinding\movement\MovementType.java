package com.rato.addons.pathfinding.movement;

import com.rato.addons.util.Logger;
import net.minecraft.client.Minecraft;
import net.minecraft.block.Block;
import net.minecraft.init.Blocks;
import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;

/**
 * Sistema de movimentos modulares baseado no Baritone
 * Cada tipo de movimento tem sua própria lógica de validação e custo
 */
public enum MovementType {

    TRAVERSE("traverse") {
        @Override
        public MovementResult calculateCost(BlockPos from, BlockPos to, Minecraft mc) {
            // Movimento horizontal - AGORA INCLUI SUBIDAS NATURAIS
            int yDiff = to.getY() - from.getY();

            // Permitir subidas naturais de até 1 bloco (slabs, stairs, etc)
            if (yDiff < 0 || yDiff > 1) {
                return MovementResult.invalid();
            }

            // Verificar se pode andar no destino
            if (!canWalkOn(to.down(), mc) || !canWalkThrough(to, mc) || !canWalkThrough(to.up(), mc)) {
                return MovementResult.invalid();
            }

            // Para movimento com subida, verificar se é natural (sem pulo)
            if (yDiff == 1) {
                // Se movimento natural estiver desabilitado, não permitir subidas no TRAVERSE
                if (!com.rato.addons.config.RatoAddonsConfigSimple.pathfindingNaturalMovement) {
                    return MovementResult.invalid(); // Forçar uso de ASCEND
                }

                double fromHeight = getActualBlockHeight(from.down(), mc);
                double toHeight = getActualBlockHeight(to.down(), mc);
                double realHeightDiff = (to.getY() + toHeight) - (from.getY() + fromHeight);

                // Se a diferença real é pequena, é movimento natural (slab para bloco, stairs,
                // etc)
                if (realHeightDiff > 1.0) { // Reduzido de 1.1 para 1.0 para ser mais restritivo
                    return MovementResult.invalid(); // Muito alto, precisa de ASCEND
                }
            }

            double cost = 1.3; // Custo ligeiramente maior para movimento cardinal

            // Custo um pouco maior para subidas naturais cardinais
            if (yDiff == 1) {
                cost = 1.5; // Penalidade moderada para subida cardinal
            }

            // Aplicar modificadores de terreno
            Block destBlockBelow = mc.theWorld.getBlockState(to.down()).getBlock();
            if (destBlockBelow == Blocks.soul_sand) {
                cost *= 1.8;
            } else if (destBlockBelow == Blocks.ice || destBlockBelow == Blocks.packed_ice) {
                cost *= 0.9;
            } else if (destBlockBelow instanceof net.minecraft.block.BlockSlab) {
                cost *= 0.5; // SLABS são preferíveis para movimento natural
            } else if (destBlockBelow instanceof net.minecraft.block.BlockStairs) {
                cost *= 0.7; // Stairs são boas para movimento
            } else if (destBlockBelow.getUnlocalizedName().toLowerCase().contains("stone") ||
                    destBlockBelow.getUnlocalizedName().toLowerCase().contains("brick") ||
                    destBlockBelow.getUnlocalizedName().toLowerCase().contains("concrete")) {
                cost *= 0.8; // Blocos sólidos são bons para movimento
            }

            // Verificar se há água
            Block destBlock = mc.theWorld.getBlockState(to).getBlock();
            if (destBlock == Blocks.water || destBlock == Blocks.flowing_water) {
                cost *= 2.5;
            }

            // Aplicar penalidade por proximidade de estruturas
            cost *= calculateClearancePenalty(to, mc);

            return MovementResult.valid(cost);
        }
    },

    ASCEND("ascend") {
        @Override
        public MovementResult calculateCost(BlockPos from, BlockPos to, Minecraft mc) {
            // Movimento para cima (pulo de 1 bloco)
            if (to.getY() - from.getY() != 1) {
                return MovementResult.invalid();
            }

            // Verificar altura real dos blocos para determinar se precisa pular
            double fromHeight = getActualBlockHeight(from.down(), mc);
            double toHeight = getActualBlockHeight(to.down(), mc);
            double realHeightDiff = (to.getY() + toHeight) - (from.getY() + fromHeight);

            // NOVA LÓGICA: Verificar se realmente precisa pular
            boolean needsJump = determineIfJumpRequired(from, to, mc, realHeightDiff);
            if (!needsJump) {
                return MovementResult.invalid(); // Use TRAVERSE se não precisa pular
            }

            // Verificar se pode pular
            if (!canWalkThrough(from.up(), mc) || !canWalkThrough(from.up(2), mc)) {
                return MovementResult.invalid(); // Não há espaço para pular
            }

            // Verificar destino
            if (!canWalkOn(to.down(), mc) || !canWalkThrough(to, mc) || !canWalkThrough(to.up(), mc)) {
                return MovementResult.invalid();
            }

            // SISTEMA CONSERVADOR - Prefere caminhos longos e planos
            double jumpCost = com.rato.addons.config.RatoAddonsConfigSimple.pathfindingJumpPenalty;

            // PENALIDADE ALTA para qualquer pulo
            jumpCost *= 15.0; // Penalidade muito alta para desencorajar pulos

            // Calcular diferença de altura
            double heightDiff = to.getY() - from.getY();

            // PENALIDADE EXTRA baseada na altura do pulo
            if (heightDiff > 0) {
                jumpCost *= (1.0 + heightDiff * 10.0); // Penalidade crescente por altura
            }

            // Verificar se há slabs próximas - MUITO preferível usar slabs
            if (hasSlabsInRadius(from, mc, 3) || hasSlabsInRadius(to, mc, 3)) {
                jumpCost *= 5.0; // Penalidade MASSIVA quando há slabs próximas
            }

            // Verificar se há alternativas naturais próximas
            if (com.rato.addons.config.RatoAddonsConfigSimple.pathfindingNaturalMovement) {
                if (hasNaturalAlternativeNearby(from, to, mc)) {
                    jumpCost *= 8.0; // Penalidade MUITO ALTA se há alternativas
                }
            }

            // Aplicar penalidade por proximidade de estruturas
            jumpCost *= calculateClearancePenalty(to, mc);

            return MovementResult.valid(jumpCost);
        }
    },

    DESCEND("descend") {
        @Override
        public MovementResult calculateCost(BlockPos from, BlockPos to, Minecraft mc) {
            // Movimento para baixo (queda de 1 bloco)
            if (from.getY() - to.getY() != 1) {
                return MovementResult.invalid();
            }

            // Verificar destino
            if (!canWalkOn(to.down(), mc) || !canWalkThrough(to, mc) || !canWalkThrough(to.up(), mc)) {
                return MovementResult.invalid();
            }

            return MovementResult.valid(1.2); // Custo de queda
        }
    },

    DIAGONAL("diagonal") {
        @Override
        public MovementResult calculateCost(BlockPos from, BlockPos to, Minecraft mc) {
            // Movimento diagonal
            int dx = Math.abs(to.getX() - from.getX());
            int dz = Math.abs(to.getZ() - from.getZ());

            if (dx != 1 || dz != 1 || from.getY() != to.getY()) {
                return MovementResult.invalid();
            }

            // Verificar se pode andar no destino
            if (!canWalkOn(to.down(), mc) || !canWalkThrough(to, mc) || !canWalkThrough(to.up(), mc)) {
                return MovementResult.invalid();
            }

            // Verificar se não há obstáculos nas posições intermediárias
            BlockPos intermediate1 = new BlockPos(from.getX(), from.getY(), to.getZ());
            BlockPos intermediate2 = new BlockPos(to.getX(), from.getY(), from.getZ());

            if (!canWalkThrough(intermediate1, mc) || !canWalkThrough(intermediate2, mc)) {
                return MovementResult.invalid(); // Obstáculo no caminho diagonal
            }

            return MovementResult.valid(1.0); // Custo natural para movimento diagonal (preferido)
        }
    },

    FALL("fall") {
        @Override
        public MovementResult calculateCost(BlockPos from, BlockPos to, Minecraft mc) {
            // Queda de múltiplos blocos
            int fallDistance = from.getY() - to.getY();

            if (fallDistance < 2 || fallDistance > 4) {
                return MovementResult.invalid(); // Queda muito pequena ou muito grande
            }

            // Verificar destino
            if (!canWalkOn(to.down(), mc) || !canWalkThrough(to, mc) || !canWalkThrough(to.up(), mc)) {
                return MovementResult.invalid();
            }

            // Verificar se não há obstáculos no caminho da queda
            for (int y = from.getY() - 1; y > to.getY(); y--) {
                BlockPos checkPos = new BlockPos(to.getX(), y, to.getZ());
                if (!canWalkThrough(checkPos, mc)) {
                    return MovementResult.invalid();
                }
            }

            return MovementResult.valid(1.2 * fallDistance); // Custo proporcional à queda
        }
    };

    private final String name;

    MovementType(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    /**
     * Calcula o custo do movimento
     */
    public abstract MovementResult calculateCost(BlockPos from, BlockPos to, Minecraft mc);

    /**
     * Verifica se pode andar sobre um bloco (método público)
     */
    public static boolean canWalkOn(BlockPos pos, Minecraft mc) {
        Block block = mc.theWorld.getBlockState(pos).getBlock();

        // Ar não é chão
        if (block == Blocks.air) {
            return false;
        }

        // Líquidos não são chão sólido
        if (block == Blocks.water || block == Blocks.flowing_water ||
                block == Blocks.lava || block == Blocks.flowing_lava) {
            return false;
        }

        // Verificar se é um bloco sólido
        return block.isFullBlock() ||
                block instanceof net.minecraft.block.BlockSlab ||
                block instanceof net.minecraft.block.BlockStairs ||
                block instanceof net.minecraft.block.BlockFarmland;
    }

    /**
     * Verifica se pode andar através de um bloco (método público)
     */
    public static boolean canWalkThrough(BlockPos pos, Minecraft mc) {
        Block block = mc.theWorld.getBlockState(pos).getBlock();

        // Ar é sempre passável
        if (block == Blocks.air) {
            return true;
        }

        // Blocos especiais passáveis
        if (block == Blocks.vine || block == Blocks.ladder ||
                block == Blocks.waterlily || block == Blocks.carpet ||
                block == Blocks.snow_layer || block == Blocks.tallgrass ||
                block == Blocks.red_flower || block == Blocks.yellow_flower ||
                block == Blocks.leaves || block == Blocks.leaves2) {
            return true;
        }

        // Água é passável mas com custo
        if (block == Blocks.water || block == Blocks.flowing_water) {
            return true;
        }

        // Lava e blocos perigosos não são passáveis
        if (block == Blocks.lava || block == Blocks.flowing_lava ||
                block == Blocks.cactus || block == Blocks.fire) {
            return false;
        }

        return !block.getMaterial().blocksMovement();
    }

    /**
     * Calcula altura real do bloco (importante para slabs e escadas)
     */
    public static double getActualBlockHeight(BlockPos pos, Minecraft mc) {
        try {
            Block block = mc.theWorld.getBlockState(pos).getBlock();

            // Ar não tem altura
            if (block == Blocks.air) {
                return 0.0;
            }

            // Slabs têm altura 0.5 - MELHORADO para detectar tipo
            if (block instanceof net.minecraft.block.BlockSlab) {
                net.minecraft.block.state.IBlockState state = mc.theWorld.getBlockState(pos);
                try {
                    // Verificar se é slab inferior ou superior
                    if (state.getValue(
                            net.minecraft.block.BlockSlab.HALF) == net.minecraft.block.BlockSlab.EnumBlockHalf.BOTTOM) {
                        return 0.5; // Slab inferior - meio bloco
                    } else {
                        return 1.0; // Slab superior - bloco completo
                    }
                } catch (Exception e) {
                    return 0.5; // Fallback para slab padrão
                }
            }

            // Escadas têm altura variável, mas consideramos 0.75 para pathfinding
            if (block instanceof net.minecraft.block.BlockStairs) {
                return 0.75;
            }

            // Farmland é ligeiramente menor que 1.0
            if (block instanceof net.minecraft.block.BlockFarmland) {
                return 0.9375; // 15/16 blocos
            }

            // Blocos sólidos normais
            if (block.isFullBlock() || block.getMaterial().isSolid()) {
                return 1.0;
            }

            return 0.0;
        } catch (Exception e) {
            return 0.0;
        }
    }

    /**
     * Verifica se há alternativa natural VIÁVEL para um pulo
     */
    private static boolean hasNaturalAlternativeNearby(BlockPos from, BlockPos to, Minecraft mc) {
        // Só verificar se movimento natural estiver ativado
        if (!com.rato.addons.config.RatoAddonsConfigSimple.pathfindingNaturalMovement) {
            return false;
        }

        // Verificar blocos em um raio pequeno para alternativas viáveis
        int searchRadius = 2;
        for (int x = -searchRadius; x <= searchRadius; x++) {
            for (int z = -searchRadius; z <= searchRadius; z++) {
                if (x == 0 && z == 0)
                    continue; // Pular posição atual

                BlockPos checkPos = from.add(x, 0, z);
                Block block = mc.theWorld.getBlockState(checkPos).getBlock();

                // Se há stairs/slabs que podem criar rota alternativa
                if (block instanceof net.minecraft.block.BlockStairs ||
                        block instanceof net.minecraft.block.BlockSlab) {

                    // Verificar se essa alternativa é realmente viável (não muito longe do
                    // objetivo)
                    double alternativeDistance = checkPos.distanceSq(to);
                    double directDistance = from.distanceSq(to);

                    // Só considerar alternativa se não for muito mais longa
                    if (alternativeDistance <= directDistance * 1.5) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * Verifica se há slabs em um raio específico
     */
    private static boolean hasSlabsInRadius(BlockPos center, Minecraft mc, int radius) {
        if (mc.theWorld == null)
            return false;

        for (int x = -radius; x <= radius; x++) {
            for (int z = -radius; z <= radius; z++) {
                if (x == 0 && z == 0)
                    continue; // Pular centro

                BlockPos checkPos = center.add(x, 0, z);
                Block block = mc.theWorld.getBlockState(checkPos.down()).getBlock();

                if (block instanceof net.minecraft.block.BlockSlab) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Verifica se há alternativa natural para um pulo (stairs, slabs próximos) -
     * LEGACY
     */
    private static boolean hasNaturalAlternative(BlockPos from, BlockPos to, Minecraft mc) {
        return hasNaturalAlternativeNearby(from, to, mc);
    }

    /**
     * Verifica se há espaço livre ao redor da posição (evita andar muito próximo de
     * estruturas)
     */
    public static boolean hasAdequateClearance(BlockPos pos, Minecraft mc) {
        // Verificar espaço livre em um raio de 1 bloco horizontalmente
        int clearanceRadius = 1;
        int solidBlocksNearby = 0;

        for (int x = -clearanceRadius; x <= clearanceRadius; x++) {
            for (int z = -clearanceRadius; z <= clearanceRadius; z++) {
                if (x == 0 && z == 0)
                    continue; // Pular posição central

                BlockPos checkPos = pos.add(x, 0, z);
                Block block = mc.theWorld.getBlockState(checkPos).getBlock();

                // Contar blocos sólidos próximos
                if (block.getMaterial().isSolid() && block != Blocks.air) {
                    solidBlocksNearby++;
                }
            }
        }

        // Se há muitos blocos sólidos próximos, não tem clearance adequado
        return solidBlocksNearby <= 3; // Máximo 3 blocos sólidos dos 8 adjacentes
    }

    /**
     * Calcula penalidade por proximidade de estruturas
     */
    public static double calculateClearancePenalty(BlockPos pos, Minecraft mc) {
        // Se evitar estruturas estiver desabilitado, não aplicar penalidade
        if (!com.rato.addons.config.RatoAddonsConfigSimple.pathfindingAvoidStructures) {
            return 1.0;
        }

        if (!hasAdequateClearance(pos, mc)) {
            return com.rato.addons.config.RatoAddonsConfigSimple.pathfindingStructurePenalty;
        }
        return 1.0; // Sem penalidade
    }

    /**
     * Determina se o movimento realmente requer pulo
     */
    private static boolean determineIfJumpRequired(BlockPos from, BlockPos to, Minecraft mc, double realHeightDiff) {
        if (mc.theWorld == null)
            return false;

        // Verificar se há slabs envolvidos
        Block fromBlock = mc.theWorld.getBlockState(from.down()).getBlock();
        Block toBlock = mc.theWorld.getBlockState(to.down()).getBlock();
        boolean hasSlabs = (fromBlock instanceof net.minecraft.block.BlockSlab) ||
                (toBlock instanceof net.minecraft.block.BlockSlab);

        // 1. Para movimentos com slabs, ser mais permissivo
        if (hasSlabs) {
            if (realHeightDiff <= 1.2) {
                // Movimento natural com slabs - não pular
                return false;
            }
        } else {
            // 2. Para blocos normais, usar lógica original
            if (realHeightDiff <= 0.8) {
                // Mas ainda pode precisar se há obstáculo no caminho
                return hasObstacleRequiringJump(from, to, mc);
            }
        }

        // 3. Se diferença é muito significativa, provavelmente precisa pular
        if (realHeightDiff > 1.2) {
            return true;
        }

        // 4. Verificar se há obstáculo no caminho horizontal (mas não para slabs)
        if (!hasSlabs) {
            return hasObstacleRequiringJump(from, to, mc);
        }

        return false;
    }

    /**
     * Verifica se há obstáculo no caminho que requer pulo
     */
    private static boolean hasObstacleRequiringJump(BlockPos from, BlockPos to, Minecraft mc) {
        if (mc.theWorld == null)
            return false;

        // Calcular direção do movimento
        int dx = to.getX() - from.getX();
        int dz = to.getZ() - from.getZ();

        // Se movimento é puramente vertical, não há obstáculo horizontal
        if (dx == 0 && dz == 0)
            return false;

        // Normalizar direção
        int stepX = dx == 0 ? 0 : (dx > 0 ? 1 : -1);
        int stepZ = dz == 0 ? 0 : (dz > 0 ? 1 : -1);

        // Verificar obstáculo no caminho
        BlockPos obstaclePos = from.add(stepX, 0, stepZ);
        Block obstacleBlock = mc.theWorld.getBlockState(obstaclePos).getBlock();

        // Se não pode andar através do obstáculo, há um obstáculo sólido
        if (!canWalkThrough(obstaclePos, mc)) {
            // Há obstáculo - verificar se pode pular por cima
            // Precisa pular se há espaço acima do obstáculo
            return canWalkThrough(obstaclePos.up(), mc) && canWalkThrough(obstaclePos.up(2), mc);
        }

        return false; // Não há obstáculo
    }

    /**
     * Resultado de um cálculo de movimento
     */
    public static class MovementResult {
        public final boolean isValid;
        public final double cost;

        private MovementResult(boolean isValid, double cost) {
            this.isValid = isValid;
            this.cost = cost;
        }

        public static MovementResult valid(double cost) {
            return new MovementResult(true, cost);
        }

        public static MovementResult invalid() {
            return new MovementResult(false, Double.MAX_VALUE);
        }
    }
}
