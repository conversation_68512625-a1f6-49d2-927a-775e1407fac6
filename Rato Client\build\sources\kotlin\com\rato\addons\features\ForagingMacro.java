package com.rato.addons.features;

import com.rato.addons.config.RatoAddonsConfigSimple;
import com.rato.addons.pathfinding.WaypointPathfinder;
import com.rato.addons.util.Logger;
import net.minecraft.block.Block;
import net.minecraft.block.BlockLog;
import net.minecraft.client.Minecraft;
import net.minecraft.init.Blocks;
import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;

import java.util.*;
import java.util.Set;
import java.util.HashSet;
import java.util.Map;
import java.util.HashMap;

import com.rato.addons.pathfinding.AStarPathfinder;
import com.rato.addons.pathfinding.AdvancedPathfinder;
import com.rato.addons.pathfinding.TreeMarkerSystem;
import com.rato.addons.features.ForagingStateMachine;
import com.rato.addons.features.AutoPetSwap;
import com.rato.addons.features.IntelligentTreeSelector;
import com.rato.addons.commands.SimplePathCommand;

/**
 * Sistema de macro de foraging com rotas pré-definidas
 * Suporta diferentes áreas: Dark Oak, Birch, Spruce, Acacia, Jungle
 */
public class ForagingMacro {
    
    private static ForagingMacro instance;
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Estado do macro
    private boolean isActive = false;
    private ForagingArea currentArea = ForagingArea.DARK_OAK;
    private ForagingState currentState = ForagingState.IDLE;

    // NOVO: Sistema de marcação de árvores
    private boolean useMarkedTrees = false; // Toggle entre sistema antigo e novo
    
    // Sistema de árvores
    private BlockPos targetTree = null;
    private long lastTreeScan = 0;
    private static final long TREE_SCAN_INTERVAL = 500; // 0.5 segundos (mais rápido para Treecapitator)

    // Sistema de cooldown para árvores quebradas
    private Map<BlockPos, Long> brokenTreesWithCooldown = new HashMap<>();
    private long lastCooldownCleanup = 0;
    private static final long COOLDOWN_CLEANUP_INTERVAL = 10000; // 10 segundos

    // Sistema de exploração de área para evitar ficar nas mesmas árvores
    private BlockPos lastTreePosition = null;
    private List<BlockPos> recentTreePositions = new ArrayList<>();
    private static final int MAX_RECENT_TREES = 5; // Lembrar últimas 5 árvores

    // Sistemas avançados
    private ForagingStateMachine stateMachine;
    private AStarPathfinder aStarPathfinder;
    private AdvancedPathfinder advancedPathfinder;
    private IntelligentTreeSelector treeSelector;
    private AutoPetSwap autoPetSwap;

    // NOVO: Sistema de pathfinding 3D
    private com.rato.addons.pathfinding.ForagingPathfindingSystem pathfindingSystem3D;
    
    // Estatísticas
    private int treesChopped = 0;
    private long sessionStartTime = 0;
    
    public enum ForagingArea {
        DARK_OAK("Dark Oak", Blocks.log2), // Dark Oak e Acacia são log2
        BIRCH("Birch", Blocks.log), // Birch é log
        SPRUCE("Spruce", Blocks.log), // Spruce é log
        ACACIA("Acacia", Blocks.log2), // Acacia é log2
        JUNGLE("Jungle", Blocks.log); // Jungle é log
        
        private final String displayName;
        private final Block logBlock;
        
        ForagingArea(String displayName, Block logBlock) {
            this.displayName = displayName;
            this.logBlock = logBlock;
        }
        
        public String getDisplayName() { return displayName; }
        public Block getLogBlock() { return logBlock; }
    }
    
    public enum ForagingState {
        IDLE("Idle"),
        SCANNING("Scanning for trees"),
        WALKING_TO_TREE("Walking to tree"),
        CHOPPING("Chopping tree"),
        COLLECTING("Collecting drops");
        
        private final String displayName;
        
        ForagingState(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() { return displayName; }
    }
    
    public static ForagingMacro getInstance() {
        if (instance == null) {
            instance = new ForagingMacro();
            // Inicializar sistemas avançados
            instance.stateMachine = new ForagingStateMachine(instance);
            instance.aStarPathfinder = new AStarPathfinder();
            instance.advancedPathfinder = new AdvancedPathfinder();
            instance.treeSelector = new IntelligentTreeSelector();
            instance.autoPetSwap = AutoPetSwap.getInstance();

            // NOVO: Inicializar sistema de pathfinding 3D
            instance.pathfindingSystem3D = new com.rato.addons.pathfinding.ForagingPathfindingSystem();
            instance.setupPathfindingCallbacks();
        }
        return instance;
    }
    
    /**
     * NOVO: Configura callbacks do sistema de pathfinding 3D
     */
    private void setupPathfindingCallbacks() {
        if (pathfindingSystem3D != null) {
            pathfindingSystem3D.setOnTreeReached(() -> {
                // Árvore alcançada - iniciar quebra
                currentState = ForagingState.CHOPPING;
                if (pathfindingSystem3D.getCurrentTarget() != null) {
                    targetTree = pathfindingSystem3D.getCurrentTarget().optimalBreakPoint;
                    Logger.sendMessage("§aÁrvore alcançada! Iniciando quebra...");

                    // Otimizar pet para quebra
                    if (autoPetSwap != null) {
                        autoPetSwap.optimizeForTreeBreaking();
                    }
                }
            });

            pathfindingSystem3D.setOnNoTreesFound(() -> {
                Logger.sendMessage("§eNenhuma árvore encontrada. Aguardando respawn...");
                currentState = ForagingState.SCANNING;
            });

            pathfindingSystem3D.setOnPathfindingFailed(() -> {
                Logger.sendMessage("§cFalha no pathfinding. Tentando método tradicional...");
                // Fallback para sistema antigo
                currentState = ForagingState.SCANNING;
            });
        }
    }

    /**
     * Inicia o macro de foraging
     */
    public void startForaging() {
        if (isActive) {
            Logger.sendMessage("§cForaging macro já está ativo!");
            return;
        }

        if (!RatoAddonsConfigSimple.foragingEnabled) {
            Logger.sendMessage("§cForaging macro está desabilitado nas configurações!");
            return;
        }

        isActive = true;
        sessionStartTime = System.currentTimeMillis();
        treesChopped = 0;
        currentState = ForagingState.SCANNING;

        Logger.sendMessage("§aForaging macro iniciado!");
        Logger.sendMessage("§7Área selecionada: §e" + currentArea.getDisplayName());
        Logger.sendMessage("§7Modo: §eTreecapitator §7(1 bloco quebra árvore inteira)");

        // NOVO: Verificar se deve usar pathfinding 3D
        if (RatoAddonsConfigSimple.foragingUse3DPathfinding && pathfindingSystem3D != null) {
            Logger.sendMessage("§a✓ Sistema de pathfinding 3D ativado!");
            pathfindingSystem3D.startForagingPathfinding();
        } else {
            // Usar sistema tradicional
            Logger.sendMessage("§7Usando sistema de pathfinding tradicional");
            startAreaRoute();
        }
    }
    
    /**
     * Para o macro de foraging
     */
    public void stopForaging() {
        if (!isActive) {
            Logger.sendMessage("§cForaging macro não está ativo!");
            return;
        }
        
        isActive = false;
        currentState = ForagingState.IDLE;
        targetTree = null;

        // Reset block breaking state
        isBreakingBlock = false;
        blockBreakStartTime = 0;

        // Stop any active attack
        if (mc.gameSettings != null && mc.gameSettings.keyBindAttack != null) {
            mc.gameSettings.keyBindAttack.setKeyBindState(mc.gameSettings.keyBindAttack.getKeyCode(), false);
        }

        // Limpar lista de árvores em cooldown
        brokenTreesWithCooldown.clear();

        // Limpar sistema de exploração
        recentTreePositions.clear();
        lastTreePosition = null;

        // Parar pathfinding
        WaypointPathfinder.getInstance().stopPathfinding();

        // NOVO: Parar sistema de pathfinding 3D
        if (pathfindingSystem3D != null) {
            pathfindingSystem3D.stopForagingPathfinding();
        }
        
        // Mostrar estatísticas da sessão
        long sessionTime = System.currentTimeMillis() - sessionStartTime;
        int sessionMinutes = (int) (sessionTime / 60000);
        
        Logger.sendMessage("§aForaging macro parado!");
        Logger.sendMessage("§7Árvores cortadas: §e" + treesChopped);
        Logger.sendMessage("§7Tempo de sessão: §e" + sessionMinutes + " minutos");
        
        if (sessionMinutes > 0) {
            int treesPerHour = (int) ((treesChopped * 60.0) / sessionMinutes);
            Logger.sendMessage("§7Taxa: §e" + treesPerHour + " árvores/hora");
        }
    }
    
    /**
     * Alterna o estado do macro
     */
    public void toggleForaging() {
        if (isActive) {
            stopForaging();
        } else {
            startForaging();
        }
    }
    
    /**
     * Define a área de foraging
     */
    public void setForagingArea(ForagingArea area) {
        if (isActive) {
            Logger.sendMessage("§cPare o macro antes de mudar a área!");
            return;
        }

        this.currentArea = area;
        Logger.sendMessage("§7Área de foraging alterada para: §e" + area.getDisplayName());
    }

    /**
     * NOVO: Alterna entre modo automático e marcação manual
     */
    public void toggleTreeMode() {
        if (isActive) {
            Logger.sendMessage("§cPare o macro antes de mudar o modo!");
            return;
        }

        useMarkedTrees = !useMarkedTrees;

        if (useMarkedTrees) {
            Logger.sendMessage("§a✓ Modo de marcação manual ativado!");
            Logger.sendMessage("§7Use /trees mark para marcar árvores específicas");
            Logger.sendMessage("§7Use /trees list para ver árvores marcadas");
        } else {
            Logger.sendMessage("§a✓ Modo automático ativado!");
            Logger.sendMessage("§7O bot irá procurar árvores automaticamente");
        }
    }

    /**
     * NOVO: Define o modo de árvores
     */
    public void setTreeMode(boolean useMarked) {
        if (isActive) {
            Logger.sendMessage("§cPare o macro antes de mudar o modo!");
            return;
        }

        this.useMarkedTrees = useMarked;

        if (useMarkedTrees) {
            Logger.sendMessage("§a✓ Modo de marcação manual ativado!");
        } else {
            Logger.sendMessage("§a✓ Modo automático ativado!");
        }
    }

    /**
     * NOVO: Obtém o modo atual
     */
    public boolean isUsingMarkedTrees() {
        return useMarkedTrees;
    }
    
    /**
     * Inicia a rota da área selecionada
     */
    private void startAreaRoute() {
        List<Vec3> areaRoute = getAreaRoute(currentArea);
        
        if (areaRoute.isEmpty()) {
            Logger.sendMessage("§cNenhuma rota definida para a área: " + currentArea.getDisplayName());
            stopForaging();
            return;
        }
        
        Logger.sendMessage("§7Iniciando rota com " + areaRoute.size() + " waypoints");
        WaypointPathfinder.getInstance().startPathfinding(areaRoute);
    }
    
    /**
     * Obtém a rota pré-definida para cada área
     */
    private List<Vec3> getAreaRoute(ForagingArea area) {
        List<Vec3> route = new ArrayList<>();
        
        switch (area) {
            case DARK_OAK:
                // Rota para Dark Oak (Park)
                route.add(new Vec3(-273, 75, -26));
                route.add(new Vec3(-280, 75, -35));
                route.add(new Vec3(-290, 75, -45));
                route.add(new Vec3(-300, 75, -55));
                route.add(new Vec3(-310, 75, -45));
                route.add(new Vec3(-300, 75, -35));
                route.add(new Vec3(-290, 75, -25));
                route.add(new Vec3(-280, 75, -15));
                break;
                
            case BIRCH:
                // Rota para Birch (Park)
                route.add(new Vec3(-350, 75, 10));
                route.add(new Vec3(-360, 75, 20));
                route.add(new Vec3(-370, 75, 30));
                route.add(new Vec3(-380, 75, 40));
                route.add(new Vec3(-370, 75, 50));
                route.add(new Vec3(-360, 75, 40));
                route.add(new Vec3(-350, 75, 30));
                route.add(new Vec3(-340, 75, 20));
                break;
                
            case SPRUCE:
                // Rota para Spruce (Park)
                route.add(new Vec3(-200, 75, -100));
                route.add(new Vec3(-210, 75, -110));
                route.add(new Vec3(-220, 75, -120));
                route.add(new Vec3(-230, 75, -130));
                route.add(new Vec3(-220, 75, -140));
                route.add(new Vec3(-210, 75, -130));
                route.add(new Vec3(-200, 75, -120));
                route.add(new Vec3(-190, 75, -110));
                break;
                
            case ACACIA:
                // Rota para Acacia (Park)
                route.add(new Vec3(-100, 75, 50));
                route.add(new Vec3(-110, 75, 60));
                route.add(new Vec3(-120, 75, 70));
                route.add(new Vec3(-130, 75, 80));
                route.add(new Vec3(-120, 75, 90));
                route.add(new Vec3(-110, 75, 80));
                route.add(new Vec3(-100, 75, 70));
                route.add(new Vec3(-90, 75, 60));
                break;
                
            case JUNGLE:
                // Rota para Jungle (Park)
                route.add(new Vec3(-50, 75, -200));
                route.add(new Vec3(-60, 75, -210));
                route.add(new Vec3(-70, 75, -220));
                route.add(new Vec3(-80, 75, -230));
                route.add(new Vec3(-70, 75, -240));
                route.add(new Vec3(-60, 75, -230));
                route.add(new Vec3(-50, 75, -220));
                route.add(new Vec3(-40, 75, -210));
                break;
        }
        
        return route;
    }
    
    @SubscribeEvent
    public void onTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.START || !isActive || mc.thePlayer == null) return;
        
        // Lógica principal do macro
        updateForagingLogic();
    }
    
    /**
     * Lógica principal do macro de foraging
     */
    private void updateForagingLogic() {
        long currentTime = System.currentTimeMillis();

        // Atualizar sistemas avançados
        if (stateMachine != null) {
            stateMachine.update();
        }

        if (autoPetSwap != null) {
            autoPetSwap.update();
        }

        // NOVO: Atualizar sistema de pathfinding 3D
        if (pathfindingSystem3D != null && pathfindingSystem3D.isActive()) {
            pathfindingSystem3D.update();
            return; // Sistema 3D gerencia tudo
        }

        // Atualizar sistema de pathfinding simplificado
        SimplePathCommand.updateSystem();

        switch (currentState) {
            case SCANNING:
                if (currentTime - lastTreeScan > TREE_SCAN_INTERVAL) {
                    scanForTrees();
                    lastTreeScan = currentTime;
                }
                break;
                
            case WALKING_TO_TREE:
                if (targetTree != null) {
                    // Calcular distância para a árvore (não para o waypoint)
                    Vec3 playerPos = mc.thePlayer.getPositionVector();
                    Vec3 treeCenter = new Vec3(targetTree.getX() + 0.5, targetTree.getY() + 0.5, targetTree.getZ() + 0.5);
                    double distanceToTree = playerPos.distanceTo(treeCenter);

                    if (distanceToTree <= 4.5) { // Alcance de quebra
                        // Stop pathfinding when reaching tree
                        WaypointPathfinder.getInstance().stopPathfinding();
                        currentState = ForagingState.CHOPPING;
                        Logger.sendMessage("§7Chegou próximo à árvore (dist: " + String.format("%.1f", distanceToTree) + ") - iniciando quebra...");

                        // Otimizar pet para quebra
                        if (autoPetSwap != null) {
                            autoPetSwap.optimizeForTreeBreaking();
                        }
                    }
                }
                break;

            case CHOPPING:
                if (targetTree != null) {
                    chopTree();
                    // chopTree() will handle state transition after successful break
                }
                break;

            case COLLECTING:
                // Wait for collection and then scan for next tree
                // This state is managed by chopTree() method with proper timing
                break;
        }
    }
    
    /**
     * Escaneia por árvores válidas na área próxima com heurística inteligente
     * NOVO: Suporta sistema de marcação manual de árvores
     */
    private void scanForTrees() {
        if (mc.theWorld == null || mc.thePlayer == null) return;

        // NOVO: Usar sistema de marcação se ativado
        if (useMarkedTrees) {
            scanMarkedTrees();
            return;
        }

        // Sistema antigo (automático)
        BlockPos playerPos = mc.thePlayer.getPosition();
        Block targetLogBlock = currentArea.getLogBlock();

        List<TreeCandidate> validTrees = new ArrayList<>();

        // Escanear em um raio configurável
        int scanRadius = RatoAddonsConfigSimple.foragingTreeScanRadius;

        for (int x = -scanRadius; x <= scanRadius; x++) {
            for (int y = -10; y <= 10; y++) {
                for (int z = -scanRadius; z <= scanRadius; z++) {
                    BlockPos checkPos = playerPos.add(x, y, z);
                    Block block = mc.theWorld.getBlockState(checkPos).getBlock();

                    if (block == targetLogBlock) {
                        // Verificar se esta árvore já foi quebrada
                        if (!isTreeAlreadyBroken(checkPos)) {
                            // Verificar se é uma árvore válida usando heurística inteligente
                            TreeCandidate candidate = analyzeTreeCandidate(checkPos, targetLogBlock);
                            if (candidate != null && candidate.isValid() && !isTreeAlreadyBroken(candidate.basePosition)) {
                                // Verificar se já temos uma árvore muito próxima desta posição
                                boolean tooClose = false;
                                for (TreeCandidate existing : validTrees) {
                                    double distance = Math.sqrt(existing.basePosition.distanceSq(candidate.basePosition));
                                    if (distance < 3.0) { // Muito próximas - provavelmente a mesma árvore
                                        tooClose = true;
                                        break;
                                    }
                                }

                                if (!tooClose) {
                                    validTrees.add(candidate);
                                }
                            }
                        }
                    }
                }
            }
        }

        // Selecionar a melhor árvore baseado em prioridade
        TreeCandidate bestTree = selectBestTree(validTrees, playerPos);

        if (bestTree != null) {
            targetTree = bestTree.basePosition;
            currentState = ForagingState.WALKING_TO_TREE;

            // Calcular distância da última árvore para mostrar exploração
            double explorationDistance = 0;
            if (lastTreePosition != null) {
                explorationDistance = Math.sqrt(bestTree.basePosition.distanceSq(lastTreePosition));
            }

            // Otimizar pet para movimento
            if (autoPetSwap != null) {
                autoPetSwap.optimizeForMovement();
            }

            // CORREÇÃO CRÍTICA: Encontrar posição válida ADJACENTE à árvore
            Vec3 playerPosVec = mc.thePlayer.getPositionVector();
            Vec3 validTargetPos = findValidStandingPositionNearTree(bestTree.basePosition);

            if (validTargetPos == null) {
                Logger.sendMessage("§cNão foi possível encontrar posição válida próxima à árvore!");
                return; // Pular esta árvore
            }

            List<Vec3> optimizedPath = advancedPathfinder.findOptimalPath(playerPosVec, validTargetPos);
            WaypointPathfinder.getInstance().startPathfinding(optimizedPath);

            Logger.sendMessage("§6Pathfinding avançado: " + optimizedPath.size() + " waypoints otimizados");

            // Verificar se realmente é a base antes de confirmar
            Block blockBelow = mc.theWorld.getBlockState(bestTree.basePosition.down()).getBlock();
            String baseType = (blockBelow == Blocks.dirt) ? "dirt" : (blockBelow == Blocks.grass) ? "grass" : "unknown";

            Logger.sendMessage("§7Árvore válida encontrada: " + bestTree.logCount + " troncos, " +
                bestTree.leafCount + " folhas");
            Logger.sendMessage("§7Alvo: BASE BLOCK em " + bestTree.basePosition.getX() + ", " +
                bestTree.basePosition.getY() + ", " + bestTree.basePosition.getZ() + " (sobre " + baseType + ")");

            if (explorationDistance > 0) {
                Logger.sendMessage("§aExploração: " + String.format("%.1f", explorationDistance) + " blocos da última árvore");
            }
        }
    }

    /**
     * NOVO: Escaneia árvores marcadas manualmente
     */
    private void scanMarkedTrees() {
        TreeMarkerSystem treeSystem = TreeMarkerSystem.getInstance();
        List<TreeMarkerSystem.MarkedTree> markedTrees = treeSystem.getMarkedTrees();

        if (markedTrees.isEmpty()) {
            Logger.sendMessage("§cNenhuma árvore marcada encontrada! Use /trees mark para marcar árvores.");
            Logger.sendMessage("§7Ou desative o modo de marcação com /foraging mode auto");
            return;
        }

        // Encontrar a árvore marcada mais próxima que ainda existe
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        TreeMarkerSystem.MarkedTree bestMarkedTree = null;
        double bestDistance = Double.MAX_VALUE;

        for (TreeMarkerSystem.MarkedTree markedTree : markedTrees) {
            // Verificar se a árvore ainda existe
            if (!isMarkedTreeStillValid(markedTree)) {
                continue; // Árvore foi quebrada ou não existe mais
            }

            // Verificar se não está em cooldown
            if (isTreeAlreadyBroken(markedTree.treeBase)) {
                continue;
            }

            double distance = playerPos.distanceTo(markedTree.optimalBreakingPosition);
            if (distance < bestDistance) {
                bestDistance = distance;
                bestMarkedTree = markedTree;
            }
        }

        if (bestMarkedTree != null) {
            targetTree = bestMarkedTree.targetLogBlock; // Usar o bloco específico para quebrar
            currentState = ForagingState.WALKING_TO_TREE;

            // Otimizar pet para movimento
            if (autoPetSwap != null) {
                autoPetSwap.optimizeForMovement();
            }

            // Usar a posição ótima já calculada
            List<Vec3> optimizedPath = advancedPathfinder.findOptimalPath(playerPos, bestMarkedTree.optimalBreakingPosition);
            WaypointPathfinder.getInstance().startPathfinding(optimizedPath);

            Logger.sendMessage("§a✓ Árvore marcada selecionada!");
            Logger.sendMessage("§7Posição ótima: " +
                String.format("%.1f, %.1f, %.1f", bestMarkedTree.optimalBreakingPosition.xCoord,
                             bestMarkedTree.optimalBreakingPosition.yCoord, bestMarkedTree.optimalBreakingPosition.zCoord));
            Logger.sendMessage("§7Distância: " + String.format("%.1f", bestDistance) + "m");
        } else {
            Logger.sendMessage("§7Nenhuma árvore marcada válida encontrada próxima");
            Logger.sendMessage("§7Aguardando respawn ou marque mais árvores...");
        }
    }

    /**
     * Verifica se uma árvore marcada ainda é válida (existe no mundo)
     */
    private boolean isMarkedTreeStillValid(TreeMarkerSystem.MarkedTree markedTree) {
        // Verificar se o bloco alvo ainda é madeira
        Block targetBlock = mc.theWorld.getBlockState(markedTree.targetLogBlock).getBlock();
        if (targetBlock != Blocks.log && targetBlock != Blocks.log2) {
            return false;
        }

        // Verificar se pelo menos alguns blocos da árvore ainda existem
        int existingLogBlocks = 0;
        for (BlockPos logPos : markedTree.logBlocks) {
            Block block = mc.theWorld.getBlockState(logPos).getBlock();
            if (block == Blocks.log || block == Blocks.log2) {
                existingLogBlocks++;
            }
        }

        // Árvore é válida se pelo menos 50% dos blocos ainda existem
        return existingLogBlocks >= (markedTree.logBlocks.size() * 0.5);
    }

    /**
     * Analisa um candidato a árvore usando heurística inteligente
     */
    private TreeCandidate analyzeTreeCandidate(BlockPos logPos, Block targetLogBlock) {
        // Encontrar a base da árvore - SEMPRE o bloco mais baixo
        BlockPos basePos = findTreeBase(logPos, targetLogBlock);
        if (basePos == null) return null;

        // Verificar dupla se realmente é a base (tem terra/grama embaixo)
        Block blockBelow = mc.theWorld.getBlockState(basePos.down()).getBlock();
        if (blockBelow != Blocks.dirt && blockBelow != Blocks.grass) {
            return null; // Não é uma base válida
        }

        // Contar troncos e folhas conectados
        TreeCandidate candidate = new TreeCandidate(basePos);
        analyzeTreeStructure(basePos, targetLogBlock, candidate, new HashSet<>());

        return candidate;
    }

    /**
     * Encontra a base real da árvore - sempre o bloco de log mais baixo
     */
    private BlockPos findTreeBase(BlockPos startPos, Block targetLogBlock) {
        BlockPos currentPos = startPos;
        BlockPos lowestLogBlock = startPos;

        // Descer até encontrar o bloco de log mais baixo
        while (currentPos.getY() > 0) {
            BlockPos below = currentPos.down();
            Block blockBelow = mc.theWorld.getBlockState(below).getBlock();

            if (blockBelow == targetLogBlock) {
                // Continuar descendo - há mais log abaixo
                currentPos = below;
                lowestLogBlock = below;
            } else if (blockBelow == Blocks.dirt || blockBelow == Blocks.grass) {
                // Encontrou a base válida - currentPos é o log mais baixo
                return currentPos;
            } else {
                // Base inválida (não tem terra/grama embaixo)
                return null;
            }
        }

        return null;
    }

    /**
     * Analisa a estrutura completa da árvore
     */
    private void analyzeTreeStructure(BlockPos basePos, Block targetLogBlock, TreeCandidate candidate, Set<BlockPos> visited) {
        if (visited.contains(basePos) || visited.size() > 100) return; // Limite de segurança
        visited.add(basePos);

        Block block = mc.theWorld.getBlockState(basePos).getBlock();

        if (block == targetLogBlock) {
            candidate.logCount++;
            candidate.height = Math.max(candidate.height, basePos.getY() - candidate.basePosition.getY() + 1);
        } else if (isLeafBlock(block)) {
            candidate.leafCount++;
        } else {
            return; // Não é parte da árvore
        }

        // Verificar blocos adjacentes (6 direções)
        BlockPos[] adjacent = {
            basePos.up(), basePos.down(),
            basePos.north(), basePos.south(),
            basePos.east(), basePos.west()
        };

        for (BlockPos adjPos : adjacent) {
            if (!visited.contains(adjPos)) {
                Block adjBlock = mc.theWorld.getBlockState(adjPos).getBlock();
                if (adjBlock == targetLogBlock || isLeafBlock(adjBlock)) {
                    analyzeTreeStructure(adjPos, targetLogBlock, candidate, visited);
                }
            }
        }
    }

    /**
     * Verifica se é um bloco de folhas
     */
    private boolean isLeafBlock(Block block) {
        return block == Blocks.leaves || block == Blocks.leaves2;
    }

    /**
     * Seleciona a melhor árvore baseado em critérios de prioridade (favorece árvores mais distantes)
     */
    private TreeCandidate selectBestTree(List<TreeCandidate> candidates, BlockPos playerPos) {
        if (candidates.isEmpty()) return null;

        TreeCandidate bestTree = null;
        double bestScore = -1;

        for (TreeCandidate candidate : candidates) {
            double score = calculateTreeScore(candidate, playerPos);
            if (score > bestScore) {
                bestScore = score;
                bestTree = candidate;
            }
        }

        return bestTree;
    }

    /**
     * Calcula pontuação da árvore favorecendo exploração de novas áreas
     */
    private double calculateTreeScore(TreeCandidate candidate, BlockPos playerPos) {
        double distance = Math.sqrt(playerPos.distanceSq(candidate.basePosition));

        // Valor base da árvore
        double treeValue = candidate.logCount * 2 + candidate.leafCount * 0.5 + candidate.height;

        // BONUS MASSIVO para árvores sem cooldown
        double cooldownBonus = 1.0;
        if (!isTreeInCooldown(candidate.basePosition)) {
            cooldownBonus = 10.0; // Prioridade máxima para árvores disponíveis
        } else {
            cooldownBonus = 0.05; // Penalidade severa para árvores em cooldown
        }

        // BONUS DE EXPLORAÇÃO - favorecer áreas não visitadas recentemente
        double explorationBonus = calculateExplorationBonus(candidate.basePosition);

        // BONUS DE DISTÂNCIA - favorecer árvores próximas mas não muito próximas
        double distanceBonus = 1.0;
        if (distance < 3) {
            distanceBonus = 0.1; // Penalidade para árvores muito próximas (pode estar quebrada)
        } else if (distance <= 15) {
            distanceBonus = 3.0; // Bonus alto para árvores próximas
        } else if (distance <= 25) {
            distanceBonus = 2.0; // Bonus médio para árvores médias
        } else if (distance <= 35) {
            distanceBonus = 1.0; // Neutro para árvores distantes
        } else {
            distanceBonus = 0.5; // Penalidade leve para árvores muito distantes
        }

        // Pontuação final: valor * bonus de cooldown * bonus de exploração * bonus de distância
        return treeValue * cooldownBonus * explorationBonus * distanceBonus;
    }

    /**
     * Calcula bonus de exploração baseado em árvores visitadas recentemente (menos agressivo)
     */
    public double calculateExplorationBonus(BlockPos treePos) {
        double explorationBonus = 1.0;
        double minExplorationDistance = RatoAddonsConfigSimple.foragingExplorationDistance;

        // Verificar distância das árvores visitadas recentemente
        for (BlockPos recentPos : recentTreePositions) {
            double distanceToRecent = Math.sqrt(treePos.distanceSq(recentPos));

            if (distanceToRecent < minExplorationDistance * 0.5) {
                // Penalidade moderada para árvores muito próximas às recentemente visitadas
                explorationBonus *= 0.3; // Penalidade moderada (era 0.05)
            } else if (distanceToRecent < minExplorationDistance) {
                // Penalidade leve
                explorationBonus *= 0.7; // Penalidade leve (era 0.2)
            } else if (distanceToRecent > minExplorationDistance * 1.5) {
                // Bonus pequeno para árvores distantes
                explorationBonus *= 1.5; // Bonus pequeno (era 2.0)
            }
        }

        // Bonus menor para áreas não exploradas
        if (explorationBonus > 0.9) {
            explorationBonus *= 2.0; // Bonus moderado (era 5.0)
        }

        return explorationBonus;
    }

    /**
     * Classe para representar um candidato a árvore
     */
    public static class TreeCandidate {
        BlockPos basePosition;
        int logCount = 0;
        int leafCount = 0;
        int height = 0;

        TreeCandidate(BlockPos basePosition) {
            this.basePosition = basePosition;
        }

        /**
         * Verifica se a árvore é válida baseado em critérios configuráveis
         */
        boolean isValid() {
            // Critérios baseados na configuração de densidade
            int minLogs, minLeaves, minHeight;

            switch (RatoAddonsConfigSimple.foragingLogDensity) {
                case 0: // Low
                    minLogs = 2;
                    minLeaves = 3;
                    minHeight = 2;
                    break;
                case 2: // High
                    minLogs = 6;
                    minLeaves = 10;
                    minHeight = 4;
                    break;
                default: // Medium
                    minLogs = 3;
                    minLeaves = 5;
                    minHeight = 3;
                    break;
            }

            // Verificar critérios básicos
            boolean hasEnoughLogs = logCount >= minLogs;
            boolean hasEnoughHeight = height >= minHeight;

            // Verificar folhas se necessário
            boolean hasEnoughLeaves = !RatoAddonsConfigSimple.foragingRequireLeaves || leafCount >= minLeaves;

            return hasEnoughLogs && hasEnoughHeight && hasEnoughLeaves;
        }
    }

    /**
     * CORREÇÃO CRÍTICA: Encontra posição válida para ficar próximo à árvore
     * PRIORIZA POSIÇÕES VISÍVEIS AO PLAYER
     */
    private Vec3 findValidStandingPositionNearTree(BlockPos treePos) {
        if (mc.thePlayer == null) return null;

        Vec3 playerPos = mc.thePlayer.getPositionVector();
        List<PositionCandidate> candidates = new ArrayList<>();

        // Verificar posições adjacentes à árvore
        BlockPos[] adjacentPositions = {
            treePos.north(),    // Norte
            treePos.south(),    // Sul
            treePos.east(),     // Leste
            treePos.west(),     // Oeste
            treePos.north().east(),  // Nordeste
            treePos.north().west(),  // Noroeste
            treePos.south().east(),  // Sudeste
            treePos.south().west()   // Sudoeste
        };

        // Avaliar todas as posições adjacentes
        for (BlockPos adjacentPos : adjacentPositions) {
            if (isValidStandingPosition(adjacentPos) && hasLineOfSightToTree(adjacentPos, treePos)) {
                Vec3 candidateVec = new Vec3(adjacentPos.getX() + 0.5, adjacentPos.getY(), adjacentPos.getZ() + 0.5);
                double distanceFromPlayer = playerPos.distanceTo(candidateVec);
                boolean isVisibleFromPlayer = hasLineOfSightFromPlayer(candidateVec);

                candidates.add(new PositionCandidate(candidateVec, distanceFromPlayer, isVisibleFromPlayer, true));
            }
        }

        // Se não encontrou posições adjacentes, tentar em raio maior
        if (candidates.isEmpty()) {
            for (int radius = 2; radius <= 4; radius++) {
                for (int x = -radius; x <= radius; x++) {
                    for (int z = -radius; z <= radius; z++) {
                        if (Math.abs(x) == radius || Math.abs(z) == radius) { // Apenas bordas
                            BlockPos candidatePos = treePos.add(x, 0, z);

                            if (isValidStandingPosition(candidatePos) && hasLineOfSightToTree(candidatePos, treePos)) {
                                Vec3 candidateVec = new Vec3(candidatePos.getX() + 0.5, candidatePos.getY(), candidatePos.getZ() + 0.5);
                                double distanceFromPlayer = playerPos.distanceTo(candidateVec);
                                boolean isVisibleFromPlayer = hasLineOfSightFromPlayer(candidateVec);

                                candidates.add(new PositionCandidate(candidateVec, distanceFromPlayer, isVisibleFromPlayer, false));
                            }
                        }
                    }
                }
            }
        }

        // Ordenar candidatos por prioridade
        candidates.sort((a, b) -> {
            // 1. PRIORIDADE MÁXIMA: Posições visíveis ao player
            if (a.isVisibleFromPlayer != b.isVisibleFromPlayer) {
                return Boolean.compare(b.isVisibleFromPlayer, a.isVisibleFromPlayer);
            }

            // 2. Posições adjacentes têm prioridade sobre distantes
            if (a.isAdjacent != b.isAdjacent) {
                return Boolean.compare(b.isAdjacent, a.isAdjacent);
            }

            // 3. Menor distância do player
            return Double.compare(a.distanceFromPlayer, b.distanceFromPlayer);
        });

        // Retornar a melhor posição
        if (!candidates.isEmpty()) {
            PositionCandidate best = candidates.get(0);
            Logger.sendMessage("§7Posição selecionada: " +
                (best.isVisibleFromPlayer ? "§aVisível" : "§cNão visível") +
                " §7| Dist: " + String.format("%.1f", best.distanceFromPlayer));
            return best.position;
        }

        return null; // Não encontrou posição válida
    }

    /**
     * Classe auxiliar para avaliar candidatos de posição
     */
    private static class PositionCandidate {
        public final Vec3 position;
        public final double distanceFromPlayer;
        public final boolean isVisibleFromPlayer;
        public final boolean isAdjacent;

        public PositionCandidate(Vec3 position, double distanceFromPlayer, boolean isVisibleFromPlayer, boolean isAdjacent) {
            this.position = position;
            this.distanceFromPlayer = distanceFromPlayer;
            this.isVisibleFromPlayer = isVisibleFromPlayer;
            this.isAdjacent = isAdjacent;
        }
    }

    /**
     * Verifica se uma posição é válida para o player ficar
     */
    private boolean isValidStandingPosition(BlockPos pos) {
        if (mc.theWorld == null) return false;

        // Verificar se há espaço para o player (2 blocos de altura)
        Block blockAtPos = mc.theWorld.getBlockState(pos).getBlock();
        Block blockAbove = mc.theWorld.getBlockState(pos.up()).getBlock();
        Block blockBelow = mc.theWorld.getBlockState(pos.down()).getBlock();

        // Deve ter ar nos 2 blocos de altura e chão sólido embaixo
        boolean hasSpace = (blockAtPos == Blocks.air || !blockAtPos.isFullBlock()) &&
                          (blockAbove == Blocks.air || !blockAbove.isFullBlock());

        boolean hasSolidGround = blockBelow.isFullBlock() &&
                               blockBelow != Blocks.water &&
                               blockBelow != Blocks.flowing_water;

        return hasSpace && hasSolidGround;
    }

    /**
     * Verifica se há linha de visão da posição para a árvore (para quebrar)
     */
    private boolean hasLineOfSightToTree(BlockPos standingPos, BlockPos treePos) {
        // Calcular distância - deve estar no alcance de quebra (máximo 4.5 blocos)
        double distance = Math.sqrt(standingPos.distanceSq(treePos));
        if (distance > 4.5) return false;

        // Verificar se não há obstáculos entre a posição e a árvore
        Vec3 from = new Vec3(standingPos.getX() + 0.5, standingPos.getY() + 1.6, standingPos.getZ() + 0.5); // Altura dos olhos
        Vec3 to = new Vec3(treePos.getX() + 0.5, treePos.getY() + 0.5, treePos.getZ() + 0.5); // Centro da árvore

        return hasLineOfSightBetweenPoints(from, to, treePos);
    }

    /**
     * NOVA FUNÇÃO: Verifica se o player tem linha de visão para uma posição
     * Garante que o bot sempre escolha posições visíveis ao player
     */
    private boolean hasLineOfSightFromPlayer(Vec3 targetPos) {
        if (mc.thePlayer == null) return false;

        Vec3 playerEyePos = mc.thePlayer.getPositionEyes(1.0f);
        Vec3 targetEyeLevel = new Vec3(targetPos.xCoord, targetPos.yCoord + 1.6, targetPos.zCoord);

        return hasLineOfSightBetweenPoints(playerEyePos, targetEyeLevel, null);
    }

    /**
     * Função auxiliar para verificar linha de visão entre dois pontos
     */
    private boolean hasLineOfSightBetweenPoints(Vec3 from, Vec3 to, BlockPos ignoreBlock) {
        Vec3 direction = to.subtract(from);
        double rayDistance = direction.lengthVector();
        int steps = Math.max(1, (int)(rayDistance * 2)); // 2 verificações por bloco

        for (int i = 1; i < steps; i++) { // Começar de 1 para pular posição inicial
            double t = (double) i / steps;
            Vec3 scaledDirection = new Vec3(direction.xCoord * t, direction.yCoord * t, direction.zCoord * t);
            Vec3 currentPos = from.add(scaledDirection);
            BlockPos checkPos = new BlockPos(currentPos);

            // Pular se for o bloco a ser ignorado (ex: árvore alvo)
            if (ignoreBlock != null && checkPos.equals(ignoreBlock)) continue;

            Block block = mc.theWorld.getBlockState(checkPos).getBlock();
            if (block != Blocks.air && block.isFullBlock()) {
                return false; // Obstáculo encontrado
            }
        }

        return true; // Linha de visão clara
    }

    /**
     * Verifica se a posição é a base de uma árvore
     */
    private boolean isTreeBase(BlockPos pos) {
        Block blockBelow = mc.theWorld.getBlockState(pos.down()).getBlock();
        return blockBelow == Blocks.dirt || blockBelow == Blocks.grass;
    }

    // Block breaking state tracking
    private boolean isBreakingBlock = false;
    private long blockBreakStartTime = 0;
    private static final long MAX_BREAK_TIME = 5000; // 5 seconds max to break a block

    /**
     * Quebra apenas 1 bloco da árvore (Treecapitator quebra o resto automaticamente)
     */
    private void chopTree() {
        if (targetTree == null || mc.thePlayer == null || mc.playerController == null) return;

        // Verificar se está no alcance de quebra
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        Vec3 treeCenter = new Vec3(targetTree.getX() + 0.5, targetTree.getY() + 0.5, targetTree.getZ() + 0.5);
        double distanceToTree = playerPos.distanceTo(treeCenter);

        if (distanceToTree > 4.5) {
            Logger.sendMessage("§cMuito longe da árvore (dist: " + String.format("%.1f", distanceToTree) + ") - voltando para WALKING");
            currentState = ForagingState.WALKING_TO_TREE;
            return;
        }

        // Verificar se a árvore ainda existe
        Block block = mc.theWorld.getBlockState(targetTree).getBlock();
        if (block != currentArea.getLogBlock()) {
            // Árvore já foi quebrada - sucesso!
            onTreeBreakSuccess();
            return;
        }

        // Se não está quebrando ainda, iniciar quebra
        if (!isBreakingBlock) {
            startBlockBreaking();
        } else {
            // Continuar quebrando e verificar progresso
            continueBlockBreaking();
        }
    }

    /**
     * Inicia o processo de quebra do bloco
     */
    public void startBlockBreaking() {
        if (targetTree == null) return;

        // Olhar para a árvore
        lookAtBlock(targetTree);

        // Iniciar quebra
        isBreakingBlock = true;
        blockBreakStartTime = System.currentTimeMillis();

        Logger.sendMessage("§7Iniciando quebra da árvore...");

        // Múltiplas tentativas para garantir quebra
        boolean breakStarted = false;

        // Tentativa 1: onPlayerDamageBlock
        if (mc.playerController.onPlayerDamageBlock(targetTree, mc.thePlayer.getHorizontalFacing())) {
            breakStarted = true;
            Logger.sendMessage("§7Quebra iniciada com onPlayerDamageBlock");
        }

        // Tentativa 2: clickBlock
        if (mc.playerController.clickBlock(targetTree, mc.thePlayer.getHorizontalFacing())) {
            breakStarted = true;
            Logger.sendMessage("§7Quebra iniciada com clickBlock");
        }

        // Tentativa 3: Simular clique contínuo
        mc.gameSettings.keyBindAttack.setKeyBindState(mc.gameSettings.keyBindAttack.getKeyCode(), true);

        if (!breakStarted) {
            Logger.sendMessage("§cFalha ao iniciar quebra - tentando novamente...");
        }
    }

    /**
     * Continua o processo de quebra e verifica se foi completado
     */
    public void continueBlockBreaking() {
        if (targetTree == null) return;

        long currentTime = System.currentTimeMillis();

        // Verificar se o bloco foi quebrado (não existe mais)
        Block block = mc.theWorld.getBlockState(targetTree).getBlock();
        if (block != currentArea.getLogBlock()) {
            // Bloco foi quebrado com sucesso!
            onTreeBreakSuccess();
            return;
        }

        // Verificar timeout
        if (currentTime - blockBreakStartTime > MAX_BREAK_TIME) {
            Logger.sendMessage("§cTimeout na quebra do bloco - tentando novamente...");
            isBreakingBlock = false; // Reset para tentar novamente
            return;
        }

        // Continuar olhando para o bloco e mantendo ataque
        lookAtBlock(targetTree);

        // Garantir que o ataque está ativo
        if (!mc.gameSettings.keyBindAttack.isKeyDown()) {
            mc.gameSettings.keyBindAttack.setKeyBindState(mc.gameSettings.keyBindAttack.getKeyCode(), true);
        }
    }

    /**
     * Chamado quando a árvore foi quebrada com sucesso
     */
    private void onTreeBreakSuccess() {
        // Parar ataque
        mc.gameSettings.keyBindAttack.setKeyBindState(mc.gameSettings.keyBindAttack.getKeyCode(), false);

        // Reset estado de quebra
        isBreakingBlock = false;
        blockBreakStartTime = 0;

        // Registrar posição da árvore para sistema de exploração
        if (targetTree != null) {
            addToRecentTreePositions(targetTree);
            lastTreePosition = targetTree;
        }

        // Marcar árvore como quebrada com cooldown
        addBrokenTreeWithCooldown(targetTree);

        // Atualizar estatísticas
        treesChopped++;
        Logger.sendMessage("§aÁrvore quebrada com sucesso! Total: " + treesChopped);
        Logger.sendMessage("§7Forçando exploração de área distante...");

        // Transição para coleta com delay antes de procurar próxima
        currentState = ForagingState.COLLECTING;

        // Aguardar coleta de itens antes de procurar próxima árvore
        new Thread(() -> {
            try {
                Thread.sleep(RatoAddonsConfigSimple.foragingTreeBreakDelay);
                if (isActive) {
                    targetTree = null;
                    currentState = ForagingState.SCANNING;
                    Logger.sendMessage("§7Procurando árvore em área diferente...");
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
    }

    /**
     * Adiciona posição à lista de árvores visitadas recentemente
     */
    public void addToRecentTreePositions(BlockPos pos) {
        recentTreePositions.add(pos);

        // Manter apenas as últimas N árvores
        if (recentTreePositions.size() > MAX_RECENT_TREES) {
            recentTreePositions.remove(0); // Remove a mais antiga
        }

        Logger.sendMessage("§7Árvores recentes: " + recentTreePositions.size() + "/" + MAX_RECENT_TREES);
    }

    /**
     * Finaliza árvore atual e move para próxima (método legado)
     */
    private void finishTreeAndMoveNext() {
        // Usar o novo sistema de quebra
        onTreeBreakSuccess();
    }

    /**
     * Adiciona árvore à lista de quebradas com timestamp para cooldown
     */
    public void addBrokenTreeWithCooldown(BlockPos pos) {
        long currentTime = System.currentTimeMillis();
        brokenTreesWithCooldown.put(pos, currentTime);

        Logger.sendMessage("§7Árvore em cooldown: " + RatoAddonsConfigSimple.foragingTreeCooldown + "s");

        // Limpar árvores com cooldown expirado periodicamente
        if (currentTime - lastCooldownCleanup > COOLDOWN_CLEANUP_INTERVAL) {
            cleanupExpiredCooldowns();
            lastCooldownCleanup = currentTime;
        }
    }

    /**
     * Verifica se árvore está em cooldown
     */
    public boolean isTreeInCooldown(BlockPos pos) {
        if (!brokenTreesWithCooldown.containsKey(pos)) {
            return false; // Árvore nunca foi quebrada
        }

        long brokenTime = brokenTreesWithCooldown.get(pos);
        long currentTime = System.currentTimeMillis();
        long cooldownMs = RatoAddonsConfigSimple.foragingTreeCooldown * 1000L;

        boolean inCooldown = (currentTime - brokenTime) < cooldownMs;

        if (!inCooldown) {
            // Cooldown expirado - remover da lista
            brokenTreesWithCooldown.remove(pos);
            Logger.sendMessage("§aÁrvore disponível novamente!");
        }

        return inCooldown;
    }

    /**
     * Remove árvores com cooldown expirado
     */
    private void cleanupExpiredCooldowns() {
        long currentTime = System.currentTimeMillis();
        long cooldownMs = RatoAddonsConfigSimple.foragingTreeCooldown * 1000L;

        brokenTreesWithCooldown.entrySet().removeIf(entry -> {
            boolean expired = (currentTime - entry.getValue()) >= cooldownMs;
            if (expired) {
                Logger.sendMessage("§7Cooldown expirado para árvore em " + entry.getKey().getX() + ", " + entry.getKey().getZ());
            }
            return expired;
        });
    }

    /**
     * Método legado para compatibilidade - agora usa cooldown
     */
    private boolean isTreeAlreadyBroken(BlockPos pos) {
        return isTreeInCooldown(pos);
    }

    /**
     * Gera caminho natural com waypoints intermediários para movimento humano
     */
    public List<Vec3> generateNaturalPath(Vec3 start, Vec3 end) {
        List<Vec3> path = new ArrayList<>();
        path.add(start);

        double distance = start.distanceTo(end);

        // Determinar número de waypoints baseado na distância e configuração
        float densityMultiplier = RatoAddonsConfigSimple.pathfindingWaypointDensity;
        int waypointCount;

        if (distance > 30) {
            waypointCount = (int)((6 + distance / 15) * densityMultiplier); // Muitos waypoints para distâncias longas
        } else if (distance > 15) {
            waypointCount = (int)((4 + distance / 10) * densityMultiplier); // Waypoints médios
        } else if (distance > 8) {
            waypointCount = (int)((2 + distance / 5) * densityMultiplier); // Poucos waypoints para distâncias curtas
        } else {
            waypointCount = Math.max(1, (int)(2 * densityMultiplier)); // Mínimo baseado na densidade
        }

        // Garantir mínimo de waypoints para movimento natural
        waypointCount = Math.max(waypointCount, 2);

        // Gerar waypoints intermediários com variação natural
        for (int i = 1; i <= waypointCount; i++) {
            double progress = (double) i / (waypointCount + 1);

            // Interpolação linear base
            double x = start.xCoord + (end.xCoord - start.xCoord) * progress;
            double y = start.yCoord + (end.yCoord - start.yCoord) * progress;
            double z = start.zCoord + (end.zCoord - start.zCoord) * progress;

            // Adicionar variação natural para movimento humano
            Random random = new Random();
            double variation = Math.min(distance * 0.1, 3.0); // Variação baseada na distância

            x += (random.nextDouble() - 0.5) * variation;
            z += (random.nextDouble() - 0.5) * variation;

            // Ajustar altura baseado no terreno
            y = adjustHeightForTerrain(x, y, z);

            path.add(new Vec3(x, y, z));
        }

        // Adicionar waypoint final mais próximo da árvore para garantir alcance
        Vec3 finalWaypoint = new Vec3(end.xCoord, end.yCoord, end.zCoord);

        // Se a distância do último waypoint para o final for grande, adicionar waypoint intermediário
        if (path.size() > 1) {
            Vec3 lastWaypoint = path.get(path.size() - 1);
            double distanceToEnd = lastWaypoint.distanceTo(finalWaypoint);

            if (distanceToEnd > 3.0) {
                // Adicionar waypoint a 2 blocos da árvore
                Vec3 direction = finalWaypoint.subtract(lastWaypoint).normalize();
                Vec3 scaledDirection = new Vec3(direction.xCoord * 2.0, direction.yCoord * 2.0, direction.zCoord * 2.0);
                Vec3 closeWaypoint = finalWaypoint.subtract(scaledDirection);
                path.add(closeWaypoint);
            }
        }

        path.add(finalWaypoint);
        return path;
    }

    /**
     * Ajusta altura baseado no terreno para evitar obstáculos
     */
    private double adjustHeightForTerrain(double x, double y, double z) {
        if (mc.theWorld == null) return y;

        BlockPos pos = new BlockPos(x, y, z);

        // Verificar se há blocos sólidos e ajustar altura
        for (int checkY = (int)y - 2; checkY <= (int)y + 3; checkY++) {
            BlockPos checkPos = new BlockPos(x, checkY, z);
            Block block = mc.theWorld.getBlockState(checkPos).getBlock();

            if (block != Blocks.air && block.isFullBlock()) {
                return checkY + 2; // Ficar 2 blocos acima do obstáculo
            }
        }

        return y;
    }

    // Sistema de rotação suave para evitar tremor
    private float currentLookYaw = 0;
    private float currentLookPitch = 0;
    private boolean isLookingAtTarget = false;
    private BlockPos currentLookTarget = null;

    /**
     * Olha para um bloco específico com rotação suave
     */
    public void lookAtBlock(BlockPos pos) {
        if (mc.thePlayer == null) return;

        // Se já está olhando para este bloco, não fazer nada
        if (isLookingAtTarget && pos.equals(currentLookTarget)) {
            return;
        }

        Vec3 playerPos = mc.thePlayer.getPositionEyes(1.0f);
        Vec3 blockPos = new Vec3(pos.getX() + 0.5, pos.getY() + 0.5, pos.getZ() + 0.5);
        Vec3 direction = blockPos.subtract(playerPos).normalize();

        // Calcular yaw e pitch
        float targetYaw = (float) Math.toDegrees(Math.atan2(-direction.xCoord, direction.zCoord));
        double horizontalDistance = Math.sqrt(direction.xCoord * direction.xCoord + direction.zCoord * direction.zCoord);
        float targetPitch = (float) -Math.toDegrees(Math.atan2(direction.yCoord, horizontalDistance));

        // Inicializar rotação atual se necessário
        if (!isLookingAtTarget) {
            currentLookYaw = mc.thePlayer.rotationYaw;
            currentLookPitch = mc.thePlayer.rotationPitch;
        }

        // Aplicar rotação suave
        float yawDiff = normalizeAngle(targetYaw - currentLookYaw);
        float pitchDiff = targetPitch - currentLookPitch;

        // Suavização mais agressiva para evitar tremor
        float smoothingFactor = 0.2f;
        currentLookYaw += yawDiff * smoothingFactor;
        currentLookPitch += pitchDiff * smoothingFactor;

        // Aplicar ao player apenas se a diferença for significativa
        if (Math.abs(yawDiff) > 1.0f || Math.abs(pitchDiff) > 1.0f) {
            mc.thePlayer.rotationYaw = currentLookYaw;
            mc.thePlayer.rotationPitch = Math.max(-90, Math.min(90, currentLookPitch));
        }

        // Marcar como olhando para o alvo se estiver próximo
        if (Math.abs(yawDiff) < 2.0f && Math.abs(pitchDiff) < 2.0f) {
            isLookingAtTarget = true;
            currentLookTarget = pos;
        } else {
            isLookingAtTarget = false;
            currentLookTarget = null;
        }
    }

    // Getters para UI
    public boolean isActive() { return isActive; }
    public ForagingArea getCurrentArea() { return currentArea; }
    public ForagingState getCurrentState() { return currentState; }
    public int getTreesChopped() { return treesChopped; }
    public BlockPos getTargetTree() { return targetTree; }
    public boolean isBreakingBlock() { return isBreakingBlock; }

    // Métodos públicos para FSM
    public List<TreeCandidate> scanForValidTrees() {
        List<TreeCandidate> validTrees = new ArrayList<>();

        if (mc.theWorld == null || mc.thePlayer == null) return validTrees;

        BlockPos playerPos = new BlockPos(mc.thePlayer.posX, mc.thePlayer.posY, mc.thePlayer.posZ);
        Block targetLogBlock = currentArea.getLogBlock();
        int scanRadius = RatoAddonsConfigSimple.foragingTreeScanRadius;

        // Escanear área ao redor do player
        for (int x = -scanRadius; x <= scanRadius; x++) {
            for (int z = -scanRadius; z <= scanRadius; z++) {
                for (int y = -10; y <= 10; y++) {
                    BlockPos checkPos = playerPos.add(x, y, z);
                    Block block = mc.theWorld.getBlockState(checkPos).getBlock();

                    if (block == targetLogBlock) {
                        // Verificar se esta árvore já foi quebrada
                        if (!isTreeInCooldown(checkPos)) {
                            // Verificar se é uma árvore válida usando heurística inteligente
                            TreeCandidate candidate = analyzeTreeCandidate(checkPos, targetLogBlock);
                            if (candidate != null && candidate.isValid() && !isTreeInCooldown(candidate.basePosition)) {
                                // Verificar se já temos uma árvore muito próxima desta posição
                                boolean tooClose = false;
                                for (TreeCandidate existing : validTrees) {
                                    double distance = Math.sqrt(existing.basePosition.distanceSq(candidate.basePosition));
                                    if (distance < 3.0) { // Muito próximas - provavelmente a mesma árvore
                                        tooClose = true;
                                        break;
                                    }
                                }

                                if (!tooClose) {
                                    validTrees.add(candidate);
                                }
                            }
                        }
                    }
                }
            }
        }

        return validTrees;
    }

    public boolean isLookingAtBlock(BlockPos blockPos, float tolerance) {
        if (mc.thePlayer == null) return false;

        Vec3 playerPos = mc.thePlayer.getPositionVector();
        Vec3 blockCenter = new Vec3(blockPos.getX() + 0.5, blockPos.getY() + 0.5, blockPos.getZ() + 0.5);
        Vec3 direction = blockCenter.subtract(playerPos).normalize();

        // Calcular ângulos necessários
        float targetYaw = (float) Math.toDegrees(Math.atan2(-direction.xCoord, direction.zCoord));
        float targetPitch = (float) -Math.toDegrees(Math.atan2(direction.yCoord,
            Math.sqrt(direction.xCoord * direction.xCoord + direction.zCoord * direction.zCoord)));

        // Verificar diferença
        float yawDiff = Math.abs(normalizeAngle(targetYaw - mc.thePlayer.rotationYaw));
        float pitchDiff = Math.abs(targetPitch - mc.thePlayer.rotationPitch);

        return yawDiff < tolerance && pitchDiff < tolerance;
    }

    private float normalizeAngle(float angle) {
        while (angle > 180) angle -= 360;
        while (angle < -180) angle += 360;
        return angle;
    }
}
