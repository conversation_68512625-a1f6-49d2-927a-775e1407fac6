package com.rato.addons.pathfinding.mucifex;

import com.rato.addons.pathfinding.professional.PathNode;
import com.rato.addons.util.Logger;
import net.minecraft.block.Block;
import net.minecraft.block.material.Material;
import net.minecraft.client.Minecraft;
import net.minecraft.init.Blocks;
import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;
import net.minecraft.world.World;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Sistema de pathfinding inspirado no Mucifex
 * Implementa A* otimizado com detecção inteligente de movimento
 */
public class MucifexPathfinder {
    
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Configurações otimizadas
    private static final int MAX_ITERATIONS = 8000;
    private static final double GOAL_TOLERANCE = 1.0;
    private static final double HEURISTIC_WEIGHT = 1.0;
    
    // Cache de validação de blocos
    private final Map<BlockPos, BlockValidation> blockCache = new ConcurrentHashMap<>();
    private long lastCacheClean = System.currentTimeMillis();
    
    // Direções de movimento otimizadas
    private static final Vec3[] MOVEMENT_VECTORS = {
        // Movimento horizontal (prioridade alta)
        new Vec3(1, 0, 0), new Vec3(-1, 0, 0), new Vec3(0, 0, 1), new Vec3(0, 0, -1),
        // Movimento diagonal
        new Vec3(1, 0, 1), new Vec3(1, 0, -1), new Vec3(-1, 0, 1), new Vec3(-1, 0, -1),
        // Movimento com pulo
        new Vec3(1, 1, 0), new Vec3(-1, 1, 0), new Vec3(0, 1, 1), new Vec3(0, 1, -1),
        new Vec3(0, 1, 0), // Pulo vertical
        // Movimento com queda
        new Vec3(1, -1, 0), new Vec3(-1, -1, 0), new Vec3(0, -1, 1), new Vec3(0, -1, -1),
        new Vec3(0, -1, 0) // Queda vertical
    };
    
    /**
     * Validação de bloco com cache
     */
    private static class BlockValidation {
        final boolean isWalkable;
        final boolean requiresJump;
        final boolean isSafe;
        final double movementCost;
        final long timestamp;
        
        BlockValidation(boolean walkable, boolean jump, boolean safe, double cost) {
            this.isWalkable = walkable;
            this.requiresJump = jump;
            this.isSafe = safe;
            this.movementCost = cost;
            this.timestamp = System.currentTimeMillis();
        }
        
        boolean isExpired() {
            return System.currentTimeMillis() - timestamp > 30000; // 30 segundos
        }
    }
    
    /**
     * Nó otimizado para pathfinding
     */
    private static class MucifexNode implements Comparable<MucifexNode> {
        final BlockPos pos;
        final double gCost;
        final double hCost;
        final double fCost;
        final MucifexNode parent;
        final MovementType movementType;
        
        enum MovementType {
            WALK, JUMP, FALL, DIAGONAL, CLIMB
        }
        
        MucifexNode(BlockPos pos, double gCost, double hCost, MucifexNode parent, MovementType type) {
            this.pos = pos;
            this.gCost = gCost;
            this.hCost = hCost;
            this.fCost = gCost + hCost;
            this.parent = parent;
            this.movementType = type;
        }
        
        @Override
        public int compareTo(MucifexNode other) {
            int fCompare = Double.compare(this.fCost, other.fCost);
            return fCompare != 0 ? fCompare : Double.compare(this.hCost, other.hCost);
        }
        
        @Override
        public boolean equals(Object obj) {
            return obj instanceof MucifexNode && pos.equals(((MucifexNode) obj).pos);
        }
        
        @Override
        public int hashCode() {
            return pos.hashCode();
        }
    }
    
    /**
     * Encontra caminho usando A* otimizado
     */
    public List<PathNode> findPath(Vec3 start, Vec3 goal) {
        if (mc.theWorld == null) return null;
        
        BlockPos startPos = new BlockPos(start);
        BlockPos goalPos = new BlockPos(goal);
        
        // Limpar cache antigo
        cleanCache();
        
        // Estruturas do A*
        PriorityQueue<MucifexNode> openSet = new PriorityQueue<>();
        Set<BlockPos> closedSet = new HashSet<>();
        Map<BlockPos, MucifexNode> allNodes = new HashMap<>();
        
        // Nó inicial
        MucifexNode startNode = new MucifexNode(startPos, 0, heuristic(startPos, goalPos), null, MucifexNode.MovementType.WALK);
        openSet.add(startNode);
        allNodes.put(startPos, startNode);
        
        int iterations = 0;
        MucifexNode bestNode = startNode;
        
        while (!openSet.isEmpty() && iterations < MAX_ITERATIONS) {
            MucifexNode current = openSet.poll();
            iterations++;
            
            // Atualizar melhor nó
            if (current.hCost < bestNode.hCost) {
                bestNode = current;
            }
            
            // Verificar se chegou ao objetivo
            if (current.pos.distanceSq(goalPos) <= GOAL_TOLERANCE * GOAL_TOLERANCE) {
                return reconstructPath(current);
            }
            
            closedSet.add(current.pos);
            
            // Explorar vizinhos
            for (Vec3 moveVector : MOVEMENT_VECTORS) {
                BlockPos neighborPos = current.pos.add(moveVector.xCoord, moveVector.yCoord, moveVector.zCoord);
                
                if (closedSet.contains(neighborPos)) continue;
                
                MovementAnalysis movement = analyzeMovement(current.pos, neighborPos);
                if (!movement.isValid) continue;
                
                double tentativeGCost = current.gCost + movement.cost;
                
                MucifexNode existingNode = allNodes.get(neighborPos);
                if (existingNode != null && tentativeGCost >= existingNode.gCost) {
                    continue;
                }
                
                double hCost = heuristic(neighborPos, goalPos);
                MucifexNode neighborNode = new MucifexNode(neighborPos, tentativeGCost, hCost, current, movement.type);
                
                allNodes.put(neighborPos, neighborNode);
                openSet.add(neighborNode);
            }
        }
        
        // Retornar melhor caminho parcial se não encontrou completo
        return reconstructPath(bestNode);
    }
    
    /**
     * Análise de movimento entre duas posições
     */
    private MovementAnalysis analyzeMovement(BlockPos from, BlockPos to) {
        MovementAnalysis result = new MovementAnalysis();
        
        // Verificar validação básica
        BlockValidation validation = getBlockValidation(to);
        if (!validation.isWalkable || !validation.isSafe) {
            result.isValid = false;
            return result;
        }
        
        // Calcular diferenças
        int dx = to.getX() - from.getX();
        int dy = to.getY() - from.getY();
        int dz = to.getZ() - from.getZ();
        
        double distance = Math.sqrt(dx * dx + dy * dy + dz * dz);
        
        // Determinar tipo de movimento
        if (dy == 0) {
            // Movimento horizontal
            if (Math.abs(dx) + Math.abs(dz) == 1) {
                result.type = MucifexNode.MovementType.WALK;
                result.cost = 1.0;
            } else if (Math.abs(dx) + Math.abs(dz) == 2) {
                result.type = MucifexNode.MovementType.DIAGONAL;
                result.cost = 1.414;
            } else {
                result.isValid = false;
                return result;
            }
            
            // Verificar se precisa pular
            if (needsJumpForHorizontalMovement(from, to)) {
                result.type = MucifexNode.MovementType.JUMP;
                result.cost *= 1.5;
            }
            
        } else if (dy == 1) {
            // Movimento para cima
            if (canJump(from, to)) {
                result.type = MucifexNode.MovementType.JUMP;
                result.cost = 1.5 + distance * 0.1;
            } else {
                result.isValid = false;
                return result;
            }
            
        } else if (dy == -1) {
            // Movimento para baixo
            if (canFall(from, to)) {
                result.type = MucifexNode.MovementType.FALL;
                result.cost = 1.2 + distance * 0.1;
            } else {
                result.isValid = false;
                return result;
            }
            
        } else {
            // Movimento vertical extremo
            result.isValid = false;
            return result;
        }
        
        // Aplicar custo do terreno
        result.cost *= validation.movementCost;
        result.isValid = true;
        
        return result;
    }
    
    /**
     * Obtém validação de bloco com cache
     */
    private BlockValidation getBlockValidation(BlockPos pos) {
        BlockValidation cached = blockCache.get(pos);
        if (cached != null && !cached.isExpired()) {
            return cached;
        }
        
        BlockValidation validation = analyzeBlock(pos);
        blockCache.put(pos, validation);
        return validation;
    }
    
    /**
     * Analisa um bloco específico
     */
    private BlockValidation analyzeBlock(BlockPos pos) {
        World world = mc.theWorld;
        
        Block feetBlock = world.getBlockState(pos).getBlock();
        Block headBlock = world.getBlockState(pos.up()).getBlock();
        Block groundBlock = world.getBlockState(pos.down()).getBlock();
        
        // Verificar se é caminhável
        boolean walkable = isBlockPassable(feetBlock) && isBlockPassable(headBlock);
        
        // Verificar se é seguro
        boolean safe = !isDangerous(feetBlock) && !isDangerous(headBlock);
        
        // Verificar se tem chão
        boolean hasGround = isBlockSolid(groundBlock) || isBlockLiquid(feetBlock);
        
        if (!hasGround) {
            // Verificar queda segura
            int fallDistance = 0;
            BlockPos checkPos = pos.down();
            while (fallDistance < 4 && isBlockPassable(world.getBlockState(checkPos).getBlock())) {
                fallDistance++;
                checkPos = checkPos.down();
            }
            
            if (fallDistance >= 4) {
                walkable = false;
                safe = false;
            }
        }
        
        // Calcular custo de movimento
        double cost = 1.0;
        if (isBlockLiquid(feetBlock)) cost *= 1.8;
        if (isDangerous(feetBlock)) cost *= 5.0;
        
        // Verificar se requer pulo
        boolean requiresJump = false;
        for (int dx = -1; dx <= 1; dx++) {
            for (int dz = -1; dz <= 1; dz++) {
                if (dx == 0 && dz == 0) continue;
                Block neighborBlock = world.getBlockState(pos.add(dx, 0, dz)).getBlock();
                if (isBlockSolid(neighborBlock)) {
                    requiresJump = true;
                    break;
                }
            }
            if (requiresJump) break;
        }
        
        return new BlockValidation(walkable && hasGround, requiresJump, safe, cost);
    }
    
    /**
     * Verifica se precisa pular para movimento horizontal
     */
    private boolean needsJumpForHorizontalMovement(BlockPos from, BlockPos to) {
        World world = mc.theWorld;
        
        // Verificar obstáculo direto
        Vec3 direction = new Vec3(to.getX() - from.getX(), 0, to.getZ() - from.getZ()).normalize();
        BlockPos checkPos = from.add(direction.xCoord, 0, direction.zCoord);
        
        Block obstacleBlock = world.getBlockState(checkPos).getBlock();
        if (isBlockSolid(obstacleBlock)) {
            Block aboveObstacle = world.getBlockState(checkPos.up()).getBlock();
            return isBlockPassable(aboveObstacle);
        }
        
        return false;
    }
    
    /**
     * Verifica se pode pular
     */
    private boolean canJump(BlockPos from, BlockPos to) {
        World world = mc.theWorld;
        
        // Verificar espaço para pular
        Block aboveFrom = world.getBlockState(from.up(2)).getBlock();
        if (!isBlockPassable(aboveFrom)) return false;
        
        // Verificar destino
        return getBlockValidation(to).isWalkable;
    }
    
    /**
     * Verifica se pode cair
     */
    private boolean canFall(BlockPos from, BlockPos to) {
        return getBlockValidation(to).isWalkable;
    }
    
    /**
     * Calcula heurística
     */
    private double heuristic(BlockPos from, BlockPos to) {
        int dx = Math.abs(to.getX() - from.getX());
        int dy = Math.abs(to.getY() - from.getY());
        int dz = Math.abs(to.getZ() - from.getZ());
        
        // Heurística euclidiana com peso para movimento vertical
        return Math.sqrt(dx * dx + dz * dz) + dy * 1.5 * HEURISTIC_WEIGHT;
    }
    
    /**
     * Reconstrói caminho
     */
    private List<PathNode> reconstructPath(MucifexNode endNode) {
        List<PathNode> path = new ArrayList<>();
        MucifexNode current = endNode;
        
        while (current != null) {
            Vec3 pos = new Vec3(current.pos.getX() + 0.5, current.pos.getY(), current.pos.getZ() + 0.5);
            PathNode.MovementType moveType = convertMovementType(current.movementType);
            PathNode pathNode = new PathNode(pos, current.gCost, current.hCost, null, moveType);
            path.add(0, pathNode);
            current = current.parent;
        }
        
        return path;
    }
    
    /**
     * Converte tipo de movimento
     */
    private PathNode.MovementType convertMovementType(MucifexNode.MovementType type) {
        switch (type) {
            case JUMP: return PathNode.MovementType.JUMP;
            case FALL: return PathNode.MovementType.FALL;
            case DIAGONAL: return PathNode.MovementType.DIAGONAL;
            case CLIMB: return PathNode.MovementType.CLIMB;
            default: return PathNode.MovementType.WALK;
        }
    }
    
    // Métodos auxiliares
    private boolean isBlockPassable(Block block) {
        return block == Blocks.air || isBlockLiquid(block) || !block.getMaterial().isSolid();
    }
    
    private boolean isBlockSolid(Block block) {
        return block != Blocks.air && !isBlockLiquid(block) && block.getMaterial().isSolid();
    }
    
    private boolean isBlockLiquid(Block block) {
        return block == Blocks.water || block == Blocks.flowing_water ||
               block == Blocks.lava || block == Blocks.flowing_lava;
    }
    
    private boolean isDangerous(Block block) {
        return block == Blocks.lava || block == Blocks.flowing_lava ||
               block == Blocks.fire || block == Blocks.cactus;
    }
    
    private void cleanCache() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastCacheClean > 60000) { // Limpar a cada minuto
            blockCache.entrySet().removeIf(entry -> entry.getValue().isExpired());
            lastCacheClean = currentTime;
        }
    }
    
    /**
     * Classe para análise de movimento
     */
    private static class MovementAnalysis {
        boolean isValid = false;
        double cost = 1.0;
        MucifexNode.MovementType type = MucifexNode.MovementType.WALK;
    }
}
