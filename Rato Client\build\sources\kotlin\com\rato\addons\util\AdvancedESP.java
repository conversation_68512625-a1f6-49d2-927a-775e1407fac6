package com.rato.addons.util;

import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.FontRenderer;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.Tessellator;
import net.minecraft.client.renderer.WorldRenderer;
import net.minecraft.client.renderer.vertex.DefaultVertexFormats;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.util.AxisAlignedBB;
import org.lwjgl.opengl.GL11;
import org.lwjgl.util.glu.GLU;

import java.nio.FloatBuffer;
import java.nio.IntBuffer;

/**
 * Sistema de ESP 2D/3D baseado no arquivo "23 and 3d esp via jni.txt"
 * Implementa WorldToScreen e renderização 2D como no arquivo de referência
 */
public class AdvancedESP {

    private static final Minecraft mc = Minecraft.getMinecraft();

    // Matrizes OpenGL (como no arquivo de referência)
    private static float[] modelViewMatrix = new float[16];
    private static float[] projectionMatrix = new float[16];
    private static int[] viewport = new int[4];

    /**
     * Atualizar matrizes OpenGL (baseado no ActiveRenderInfo do arquivo de
     * referência)
     */
    public static void updateMatrices() {
        try {
            // Usar buffers diretos com tamanho correto (16 para todos)
            FloatBuffer modelViewBuffer = org.lwjgl.BufferUtils.createFloatBuffer(16);
            FloatBuffer projectionBuffer = org.lwjgl.BufferUtils.createFloatBuffer(16);
            IntBuffer viewportBuffer = org.lwjgl.BufferUtils.createIntBuffer(16); // 16 elementos para evitar erro

            GL11.glGetFloat(GL11.GL_MODELVIEW_MATRIX, modelViewBuffer);
            GL11.glGetFloat(GL11.GL_PROJECTION_MATRIX, projectionBuffer);
            GL11.glGetInteger(GL11.GL_VIEWPORT, viewportBuffer);

            // Rewind e get para arrays (compatibilidade Java 8)
            ((java.nio.Buffer) modelViewBuffer).rewind();
            ((java.nio.Buffer) projectionBuffer).rewind();
            ((java.nio.Buffer) viewportBuffer).rewind();

            modelViewBuffer.get(modelViewMatrix);
            projectionBuffer.get(projectionMatrix);
            viewportBuffer.get(viewport); // Só pega os primeiros 4 elementos
        } catch (Exception e) {
            // Se falhar, não fazer nada - ESP não funcionará mas não crashará
            System.err.println("[AdvancedESP] Erro ao atualizar matrizes: " + e.getMessage());
        }
    }

    /**
     * Renderizar ESP 3D melhorado para uma entidade
     */
    public static void render3DESP(EntityLivingBase entity, int color, boolean showHealth, boolean showName,
            float partialTicks) {
        if (entity == null || entity.isDead)
            return;

        // Calcular posição interpolada
        double renderPosX = mc.getRenderManager().viewerPosX;
        double renderPosY = mc.getRenderManager().viewerPosY;
        double renderPosZ = mc.getRenderManager().viewerPosZ;

        double entityX = entity.lastTickPosX + (entity.posX - entity.lastTickPosX) * partialTicks;
        double entityY = entity.lastTickPosY + (entity.posY - entity.lastTickPosY) * partialTicks;
        double entityZ = entity.lastTickPosZ + (entity.posZ - entity.lastTickPosZ) * partialTicks;

        double x = entityX - renderPosX;
        double y = entityY - renderPosY;
        double z = entityZ - renderPosZ;

        // Renderizar bounding box melhorado
        AxisAlignedBB bb = entity.getEntityBoundingBox();
        double width = bb.maxX - bb.minX;
        double height = bb.maxY - bb.minY;

        renderEnhancedBoundingBox(x, y, z, width, height, color);

        // Renderizar health bar 3D
        if (showHealth) {
            renderHealthBar3D(x, y + height + 0.3, z, entity.getHealth(), entity.getMaxHealth());
        }

        // Renderizar nome 3D
        if (showName) {
            RenderUtils.drawNameTag(entity.getName(), x, y + height + 0.6, z, 0xFFFFFFFF);
        }
    }

    /**
     * Multiplicar vetor por matriz 4x4 (baseado no arquivo de referência)
     */
    private static float[] multiply(float[] vec, float[] mat) {
        return new float[] {
                vec[0] * mat[0] + vec[1] * mat[4] + vec[2] * mat[8] + vec[3] * mat[12],
                vec[0] * mat[1] + vec[1] * mat[5] + vec[2] * mat[9] + vec[3] * mat[13],
                vec[0] * mat[2] + vec[1] * mat[6] + vec[2] * mat[10] + vec[3] * mat[14],
                vec[0] * mat[3] + vec[1] * mat[7] + vec[2] * mat[11] + vec[3] * mat[15]
        };
    }

    /**
     * WorldToScreen - Converter coordenadas 3D para 2D (baseado no arquivo de
     * referência)
     */
    public static boolean worldToScreen(double worldX, double worldY, double worldZ, float[] screen) {
        try {
            // Verificar se as matrizes foram inicializadas
            if (viewport[2] == 0 || viewport[3] == 0) {
                return false;
            }

            // Criar vetor 4D do ponto no mundo
            float[] pointInWorld = { (float) worldX, (float) worldY, (float) worldZ, 1.0f };

            // Multiplicar pela matriz ModelView e depois pela Projection (como no arquivo
            // de referência)
            float[] clipSpacePos = multiply(multiply(pointInWorld, modelViewMatrix), projectionMatrix);

            // Verificar se está atrás da camera (w <= 0)
            if (clipSpacePos[3] <= 0) {
                return false;
            }

            // Converter para NDC (Normalized Device Coordinates)
            float ndcX = clipSpacePos[0] / clipSpacePos[3];
            float ndcY = clipSpacePos[1] / clipSpacePos[3];
            float ndcZ = clipSpacePos[2] / clipSpacePos[3];

            // Verificar se está dentro do frustum (como no arquivo de referência)
            if (ndcZ < -1.0f || ndcZ > 1.0f) {
                return false;
            }

            // Converter para coordenadas de tela (como no arquivo de referência)
            screen[0] = ((ndcX + 1.0f) / 2.0f) * viewport[2]; // X
            screen[1] = ((1.0f - ndcY) / 2.0f) * viewport[3]; // Y (invertido)

            return true;
        } catch (Exception e) {
            // Se falhar, retornar false
            return false;
        }
    }

    /**
     * Renderizar bounding box melhorado
     */
    private static void renderEnhancedBoundingBox(double x, double y, double z, double width, double height,
            int color) {
        AxisAlignedBB bb = new AxisAlignedBB(x - width / 2, y, z - width / 2, x + width / 2, y + height, z + width / 2);

        // Renderizar outline com glow
        RenderUtils.drawOutlinedBoundingBox(bb, color, 2.0f);

        // Renderizar preenchimento transparente
        int fillColor = (color & 0x00FFFFFF) | 0x30000000; // Tornar transparente
        RenderUtils.drawFilledBoundingBox(bb, fillColor);
    }

    /**
     * Renderizar barra de vida 3D
     */
    private static void renderHealthBar3D(double x, double y, double z, float health, float maxHealth) {
        if (maxHealth <= 0)
            return;

        float healthPercent = Math.max(0, Math.min(1, health / maxHealth));
        double barWidth = 0.8;
        double barHeight = 0.1;

        // Background da barra
        AxisAlignedBB bgBB = new AxisAlignedBB(x - barWidth / 2, y, z - 0.02, x + barWidth / 2, y + barHeight,
                z + 0.02);
        RenderUtils.drawFilledBoundingBox(bgBB, 0xFF000000);

        // Barra de vida (verde para vermelho)
        double healthWidth = barWidth * healthPercent;
        int healthColor = getHealthColor(healthPercent);

        AxisAlignedBB healthBB = new AxisAlignedBB(x - barWidth / 2, y, z - 0.01, x - barWidth / 2 + healthWidth,
                y + barHeight, z + 0.01);
        RenderUtils.drawFilledBoundingBox(healthBB, healthColor);
    }

    /**
     * Obter cor baseada na porcentagem de vida
     */
    private static int getHealthColor(float healthPercent) {
        int red = (int) ((1.0f - healthPercent) * 255);
        int green = (int) (healthPercent * 255);
        return 0xFF000000 | (red << 16) | (green << 8);
    }

    /**
     * Renderizar ESP 2D (baseado exatamente no arquivo de referência)
     */
    public static void render2DESP(EntityLivingBase entity, int color, boolean showHealth, boolean showName,
            float partialTicks) {
        if (entity == null || entity.isDead)
            return;

        // Calcular posição interpolada (como no arquivo de referência)
        double renderPosX = mc.getRenderManager().viewerPosX;
        double renderPosY = mc.getRenderManager().viewerPosY;
        double renderPosZ = mc.getRenderManager().viewerPosZ;

        double entityX = entity.lastTickPosX + (entity.posX - entity.lastTickPosX) * partialTicks;
        double entityY = entity.lastTickPosY + (entity.posY - entity.lastTickPosY) * partialTicks;
        double entityZ = entity.lastTickPosZ + (entity.posZ - entity.lastTickPosZ) * partialTicks;

        // Calcular bounding box relativa à camera (como no arquivo de referência)
        AxisAlignedBB bb = entity.getEntityBoundingBox();
        double minX = bb.minX - entity.posX + entityX - renderPosX;
        double minY = bb.minY - entity.posY + entityY - renderPosY;
        double minZ = bb.minZ - entity.posZ + entityZ - renderPosZ;
        double maxX = bb.maxX - entity.posX + entityX - renderPosX;
        double maxY = bb.maxY - entity.posY + entityY - renderPosY;
        double maxZ = bb.maxZ - entity.posZ + entityZ - renderPosZ;

        // 8 vértices do bounding box (exatamente como no arquivo de referência)
        double[][] boxVertices = {
                { minX - 0.1, minY - 0.1, minZ - 0.1 },
                { minX - 0.1, maxY + 0.1, minZ - 0.1 },
                { maxX + 0.1, maxY + 0.1, minZ - 0.1 },
                { maxX + 0.1, minY - 0.1, minZ - 0.1 },
                { maxX + 0.1, maxY + 0.1, maxZ + 0.1 },
                { minX - 0.1, maxY + 0.1, maxZ + 0.1 },
                { minX - 0.1, minY - 0.1, maxZ + 0.1 },
                { maxX + 0.1, minY - 0.1, maxZ + 0.1 }
        };

        // Encontrar limites 2D (como no arquivo de referência)
        float w2sbbMinX = Float.MAX_VALUE, w2sbbMinY = Float.MAX_VALUE;
        float w2sbbMaxX = Float.MIN_VALUE, w2sbbMaxY = Float.MIN_VALUE;
        boolean anyVisible = false;

        for (double[] vertex : boxVertices) {
            float[] screenPos = new float[2];
            if (worldToScreen(vertex[0], vertex[1], vertex[2], screenPos)) {
                w2sbbMinX = Math.min(screenPos[0], w2sbbMinX);
                w2sbbMinY = Math.min(screenPos[1], w2sbbMinY);
                w2sbbMaxX = Math.max(screenPos[0], w2sbbMaxX);
                w2sbbMaxY = Math.max(screenPos[1], w2sbbMaxY);
                anyVisible = true;
            }
        }

        if (!anyVisible)
            return;

        // Verificar se está na tela (como no arquivo de referência)
        if (w2sbbMinX >= 0 || w2sbbMinY >= 0 || w2sbbMaxX <= viewport[2] || w2sbbMaxY <= viewport[3]) {
            float boxWidth = Math.abs(w2sbbMaxX - w2sbbMinX);
            float boxHeight = Math.abs(w2sbbMaxY - w2sbbMinY);

            // Renderizar ESP 2D
            render2DBox(w2sbbMinX, w2sbbMinY, w2sbbMaxX, w2sbbMaxY, color);

            // Renderizar health bar 2D
            if (showHealth) {
                render2DHealthBar(w2sbbMinX - 6, w2sbbMinY, w2sbbMinX - 2, w2sbbMaxY, entity.getHealth(),
                        entity.getMaxHealth());
            }

            // Renderizar nome 2D
            if (showName) {
                String name = entity.getName();
                float nameX = w2sbbMinX + (boxWidth / 2.0f);
                float nameY = w2sbbMinY - 12;
                render2DText(name, nameX, nameY, 0xFFFFFFFF);
            }
        }
    }

    /**
     * Renderizar box 2D
     */
    private static void render2DBox(float x1, float y1, float x2, float y2, int color) {
        GlStateManager.pushMatrix();
        GlStateManager.disableTexture2D();
        GlStateManager.enableBlend();
        GlStateManager.tryBlendFuncSeparate(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA, GL11.GL_ONE, GL11.GL_ZERO);

        float alpha = ((color >> 24) & 0xFF) / 255.0F;
        float red = ((color >> 16) & 0xFF) / 255.0F;
        float green = ((color >> 8) & 0xFF) / 255.0F;
        float blue = (color & 0xFF) / 255.0F;

        // Outline preto
        GlStateManager.color(0, 0, 0, alpha);
        GL11.glLineWidth(3.0f);
        draw2DOutline(x1 - 1, y1 - 1, x2 + 1, y2 + 1);

        // Box colorido
        GlStateManager.color(red, green, blue, alpha);
        GL11.glLineWidth(1.0f);
        draw2DOutline(x1, y1, x2, y2);

        GlStateManager.enableTexture2D();
        GlStateManager.disableBlend();
        GlStateManager.color(1.0F, 1.0F, 1.0F, 1.0F);
        GlStateManager.popMatrix();
    }

    /**
     * Desenhar outline 2D
     */
    private static void draw2DOutline(float x1, float y1, float x2, float y2) {
        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer buffer = tessellator.getWorldRenderer();

        buffer.begin(GL11.GL_LINE_LOOP, DefaultVertexFormats.POSITION);
        buffer.pos(x1, y1, 0).endVertex();
        buffer.pos(x2, y1, 0).endVertex();
        buffer.pos(x2, y2, 0).endVertex();
        buffer.pos(x1, y2, 0).endVertex();
        tessellator.draw();
    }

    /**
     * Renderizar health bar 2D
     */
    private static void render2DHealthBar(float x1, float y1, float x2, float y2, float health, float maxHealth) {
        if (maxHealth <= 0)
            return;

        float healthPercent = Math.max(0, Math.min(1, health / maxHealth));
        float barHeight = y2 - y1;
        float healthHeight = barHeight * healthPercent;

        GlStateManager.pushMatrix();
        GlStateManager.disableTexture2D();
        GlStateManager.enableBlend();

        // Background preto
        GlStateManager.color(0, 0, 0, 1.0f);
        draw2DFilledRect(x1 - 1, y1 - 1, x2 + 1, y2 + 1);

        // Barra de vida (verde para vermelho)
        float red = 1.0f - healthPercent;
        float green = healthPercent;
        GlStateManager.color(red, green, 0, 1.0f);
        draw2DFilledRect(x1, y2 - healthHeight, x2, y2);

        GlStateManager.enableTexture2D();
        GlStateManager.disableBlend();
        GlStateManager.color(1.0F, 1.0F, 1.0F, 1.0F);
        GlStateManager.popMatrix();
    }

    /**
     * Desenhar retângulo preenchido 2D
     */
    private static void draw2DFilledRect(float x1, float y1, float x2, float y2) {
        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer buffer = tessellator.getWorldRenderer();

        buffer.begin(GL11.GL_QUADS, DefaultVertexFormats.POSITION);
        buffer.pos(x1, y1, 0).endVertex();
        buffer.pos(x2, y1, 0).endVertex();
        buffer.pos(x2, y2, 0).endVertex();
        buffer.pos(x1, y2, 0).endVertex();
        tessellator.draw();
    }

    /**
     * Renderizar texto 2D
     */
    private static void render2DText(String text, float x, float y, int color) {
        if (mc.fontRendererObj != null) {
            int textWidth = mc.fontRendererObj.getStringWidth(text);
            mc.fontRendererObj.drawString(text, (int) (x - textWidth / 2.0f), (int) y, color);
        }
    }

    /**
     * Versão simplificada do render2DESP para compatibilidade
     */
    public static void render2DESP(EntityLivingBase entity, int color, boolean showHealth, boolean showName) {
        render2DESP(entity, color, showHealth, showName, 1.0f);
    }
}
