package com.rato.addons.features.combat;

import com.rato.addons.util.Logger;
import net.minecraft.client.Minecraft;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.monster.EntityZombie;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.MathHelper;
import net.minecraft.util.Vec3;

/**
 * Sistema de Aimbot baseado no LiquidBounce
 * Implementa rotação suave e natural estilo LiquidBounce
 */
public class LiquidAimbot {

    private static final Minecraft mc = Minecraft.getMinecraft();

    // Configurações do aimbot (estilo LiquidBounce melhorado)
    public static class AimbotSettings {
        public boolean enabled = true;
        public float range = 4.0f;
        public float fov = 90.0f;
        public boolean autoAttack = true;
        public int attackDelay = 500; // ms

        // Configurações de suavização ultra-suave (baseadas no LiquidBounce)
        public float horizontalSpeed = 0.8f; // Reduzido para movimento mais suave
        public float verticalSpeed = 0.8f; // Reduzido para movimento mais suave
        public boolean smoothRotation = true;
        public float smoothFactor = 0.08f; // Muito mais suave
        public float maxRotationSpeed = 15.0f; // Velocidade máxima por tick

        // Configurações avançadas de suavização
        public boolean predictMovement = true;
        public boolean prioritizeClosest = true;
        public boolean throughWalls = false;
        public boolean interpolateRotation = true; // Nova opção para interpolação
        public float rotationThreshold = 1.0f; // Threshold para parar rotação
    }

    public static final AimbotSettings settings = new AimbotSettings();

    // Estado interno melhorado
    private static EntityLivingBase currentTarget = null;
    private static long lastAttackTime = 0;
    private static float targetYaw = 0;
    private static float targetPitch = 0;
    private static boolean isRotating = false;

    // Sistema de interpolação suave
    private static float lastYaw = 0;
    private static float lastPitch = 0;
    private static float yawVelocity = 0;
    private static float pitchVelocity = 0;
    private static long lastUpdateTime = 0;

    /**
     * Atualiza o aimbot (chamado a cada tick)
     */
    public static void update() {
        if (!settings.enabled || mc.thePlayer == null || mc.theWorld == null) {
            currentTarget = null;
            isRotating = false;
            return;
        }

        // Encontrar melhor alvo
        EntityLivingBase newTarget = findBestTarget();

        if (newTarget != null) {
            currentTarget = newTarget;

            // Calcular rotação necessária
            float[] rotations = calculateRotations(newTarget);
            targetYaw = rotations[0];
            targetPitch = rotations[1];

            // Aplicar rotação suave
            if (settings.smoothRotation) {
                applySmoothRotation();
            } else {
                // Rotação instantânea
                mc.thePlayer.rotationYaw = targetYaw;
                mc.thePlayer.rotationPitch = targetPitch;
            }

            isRotating = true;

            // Auto attack
            if (settings.autoAttack && canAttack()) {
                attack(newTarget);
            }

        } else {
            currentTarget = null;
            isRotating = false;
        }
    }

    /**
     * Encontra o melhor alvo baseado nas configurações
     */
    private static EntityLivingBase findBestTarget() {
        EntityLivingBase bestTarget = null;
        double bestDistance = Double.MAX_VALUE;
        double bestAngle = Double.MAX_VALUE;

        for (Entity entity : mc.theWorld.loadedEntityList) {
            if (!isValidTarget(entity))
                continue;

            EntityLivingBase livingEntity = (EntityLivingBase) entity;
            double distance = mc.thePlayer.getDistanceToEntity(livingEntity);

            // Verificar alcance
            if (distance > settings.range)
                continue;

            // Verificar FOV
            float[] rotations = calculateRotations(livingEntity);
            double angle = getAngleDifference(mc.thePlayer.rotationYaw, rotations[0]);

            if (angle > settings.fov / 2.0)
                continue;

            // Verificar linha de visão
            if (!settings.throughWalls && !canSeeEntity(livingEntity))
                continue;

            // Escolher melhor alvo baseado na prioridade
            if (settings.prioritizeClosest) {
                if (distance < bestDistance) {
                    bestTarget = livingEntity;
                    bestDistance = distance;
                }
            } else {
                // Priorizar por ângulo (mais próximo do crosshair)
                if (angle < bestAngle) {
                    bestTarget = livingEntity;
                    bestAngle = angle;
                }
            }
        }

        return bestTarget;
    }

    /**
     * Verifica se uma entidade é um alvo válido
     */
    private static boolean isValidTarget(Entity entity) {
        if (!(entity instanceof EntityLivingBase))
            return false;
        if (entity == mc.thePlayer)
            return false;
        if (!entity.isEntityAlive())
            return false;

        EntityLivingBase livingEntity = (EntityLivingBase) entity;

        // Verificar tipos de entidade
        if (entity instanceof EntityZombie)
            return true;
        if (entity instanceof EntityPlayer && entity != mc.thePlayer)
            return true;

        return false;
    }

    /**
     * Calcula as rotações necessárias para mirar na entidade (estilo LiquidBounce)
     */
    private static float[] calculateRotations(EntityLivingBase entity) {
        Vec3 playerPos = mc.thePlayer.getPositionEyes(1.0f);
        Vec3 targetPos = getTargetPosition(entity);

        // Predição de movimento (estilo LiquidBounce)
        if (settings.predictMovement) {
            Vec3 velocity = new Vec3(entity.motionX, entity.motionY, entity.motionZ);
            double distance = playerPos.distanceTo(targetPos);
            double timeToReach = distance / 20.0; // Aproximação

            targetPos = targetPos.addVector(
                    velocity.xCoord * timeToReach,
                    velocity.yCoord * timeToReach,
                    velocity.zCoord * timeToReach);
        }

        Vec3 diff = targetPos.subtract(playerPos);

        double distance = Math.sqrt(diff.xCoord * diff.xCoord + diff.zCoord * diff.zCoord);
        float yaw = (float) (Math.atan2(diff.zCoord, diff.xCoord) * 180.0 / Math.PI) - 90.0f;
        float pitch = (float) (-(Math.atan2(diff.yCoord, distance) * 180.0 / Math.PI));

        return new float[] { yaw, pitch };
    }

    /**
     * Obtém a posição ideal para mirar na entidade
     */
    private static Vec3 getTargetPosition(EntityLivingBase entity) {
        // Mirar no centro da entidade (altura dos olhos)
        return new Vec3(
                entity.posX,
                entity.posY + entity.getEyeHeight() * 0.9,
                entity.posZ);
    }

    /**
     * Aplica rotação ultra-suave estilo LiquidBounce melhorado
     */
    private static void applySmoothRotation() {
        float currentYaw = mc.thePlayer.rotationYaw;
        float currentPitch = mc.thePlayer.rotationPitch;

        // Calcular diferenças angulares
        float yawDiff = normalizeAngle(targetYaw - currentYaw);
        float pitchDiff = targetPitch - currentPitch;

        // Sistema de interpolação ultra-suave com damping
        if (settings.interpolateRotation) {
            long currentTime = System.currentTimeMillis();
            float deltaTime = (currentTime - lastUpdateTime) / 1000.0f;

            if (lastUpdateTime == 0 || deltaTime > 0.1f) {
                deltaTime = 0.016f; // ~60 FPS fallback
            }

            // Calcular velocidade alvo baseada na distância angular
            float targetYawVelocity = yawDiff * settings.horizontalSpeed * 10.0f;
            float targetPitchVelocity = pitchDiff * settings.verticalSpeed * 10.0f;

            // Aplicar damping exponencial para movimento ultra-suave
            float dampingFactor = 1.0f - (float) Math.pow(1.0f - settings.smoothFactor, deltaTime * 60.0f);

            yawVelocity += (targetYawVelocity - yawVelocity) * dampingFactor;
            pitchVelocity += (targetPitchVelocity - pitchVelocity) * dampingFactor;

            // Limitar velocidade máxima
            float maxSpeed = settings.maxRotationSpeed;
            yawVelocity = MathHelper.clamp_float(yawVelocity, -maxSpeed, maxSpeed);
            pitchVelocity = MathHelper.clamp_float(pitchVelocity, -maxSpeed, maxSpeed);

            // Aplicar threshold para eliminar micro-movimentos
            if (Math.abs(yawDiff) < settings.rotationThreshold) {
                yawVelocity *= 0.3f; // Reduzir drasticamente
            }
            if (Math.abs(pitchDiff) < settings.rotationThreshold) {
                pitchVelocity *= 0.3f; // Reduzir drasticamente
            }

            // Calcular movimento final
            float smoothYaw = yawVelocity * deltaTime * 60.0f;
            float smoothPitch = pitchVelocity * deltaTime * 60.0f;

            lastUpdateTime = currentTime;

            // Aplicar rotação
            mc.thePlayer.rotationYaw = normalizeAngle(currentYaw + smoothYaw);
            mc.thePlayer.rotationPitch = MathHelper.clamp_float(currentPitch + smoothPitch, -90.0f, 90.0f);

        } else {
            // Sistema de suavização simples (fallback)
            float smoothYaw = yawDiff * settings.smoothFactor * settings.horizontalSpeed;
            float smoothPitch = pitchDiff * settings.smoothFactor * settings.verticalSpeed;

            // Limitar velocidade máxima de rotação
            smoothYaw = MathHelper.clamp_float(smoothYaw, -settings.maxRotationSpeed, settings.maxRotationSpeed);
            smoothPitch = MathHelper.clamp_float(smoothPitch, -settings.maxRotationSpeed, settings.maxRotationSpeed);

            // Aplicar rotação
            mc.thePlayer.rotationYaw = normalizeAngle(currentYaw + smoothYaw);
            mc.thePlayer.rotationPitch = MathHelper.clamp_float(currentPitch + smoothPitch, -90.0f, 90.0f);
        }

        // Atualizar estado
        lastYaw = mc.thePlayer.rotationYaw;
        lastPitch = mc.thePlayer.rotationPitch;
    }

    /**
     * Normaliza um ângulo para o intervalo [-180, 180]
     */
    private static float normalizeAngle(float angle) {
        while (angle > 180.0f)
            angle -= 360.0f;
        while (angle < -180.0f)
            angle += 360.0f;
        return angle;
    }

    /**
     * Calcula a diferença angular entre dois ângulos
     */
    private static double getAngleDifference(float angle1, float angle2) {
        float diff = Math.abs(normalizeAngle(angle1 - angle2));
        return Math.min(diff, 360.0f - diff);
    }

    /**
     * Verifica se pode ver a entidade
     */
    private static boolean canSeeEntity(EntityLivingBase entity) {
        Vec3 playerPos = mc.thePlayer.getPositionEyes(1.0f);
        Vec3 targetPos = getTargetPosition(entity);

        return mc.theWorld.rayTraceBlocks(playerPos, targetPos, false, true, false) == null;
    }

    /**
     * Verifica se pode atacar (com verificação de mira precisa)
     */
    private static boolean canAttack() {
        if (System.currentTimeMillis() - lastAttackTime < settings.attackDelay) {
            return false;
        }

        // Verificar se está realmente mirando no alvo
        if (currentTarget != null) {
            float[] angles = calculateRotations(currentTarget);
            float neededYaw = normalizeAngle(angles[0]);
            float neededPitch = MathHelper.clamp_float(angles[1], -90.0f, 90.0f);

            float currentYaw = normalizeAngle(mc.thePlayer.rotationYaw);
            float currentPitch = mc.thePlayer.rotationPitch;

            // Calcular diferença angular
            float yawDiff = Math.abs(normalizeAngle(neededYaw - currentYaw));
            float pitchDiff = Math.abs(neededPitch - currentPitch);

            // Só atacar se estiver mirando com precisão (dentro de 8 graus para ser mais
            // tolerante)
            return yawDiff <= 8.0f && pitchDiff <= 8.0f;
        }

        return false;
    }

    /**
     * Ataca a entidade
     */
    private static void attack(EntityLivingBase target) {
        if (mc.thePlayer.getDistanceToEntity(target) <= settings.range) {
            mc.playerController.attackEntity(mc.thePlayer, target);
            mc.thePlayer.swingItem();
            lastAttackTime = System.currentTimeMillis();

            Logger.addMessage("§c[LiquidAimbot] §fAtacando: " + target.getName());
        }
    }

    // Getters para estado
    public static EntityLivingBase getCurrentTarget() {
        return currentTarget;
    }

    public static boolean isRotating() {
        return isRotating;
    }

    public static float getTargetYaw() {
        return targetYaw;
    }

    public static float getTargetPitch() {
        return targetPitch;
    }
}
