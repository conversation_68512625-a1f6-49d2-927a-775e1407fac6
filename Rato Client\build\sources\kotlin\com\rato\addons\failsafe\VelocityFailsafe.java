package com.rato.addons.failsafe;

import com.rato.addons.config.RatoAddonsConfigSimple;
import com.rato.addons.util.Logger;
import net.minecraft.network.Packet;
import net.minecraft.network.play.server.S12PacketEntityVelocity;

public class VelocityFailsafe extends Failsafe {
    
    private VelocityState state = VelocityState.NONE;
    
    @Override
    public int getPriority() {
        return 3;
    }
    
    @Override
    public FailsafeManager.EmergencyType getType() {
        return FailsafeManager.EmergencyType.VELOCITY_CHECK;
    }
    
    @Override
    public boolean shouldSendNotification() {
        return RatoAddonsConfigSimple.discordWebhook;
    }
    
    @Override
    public boolean shouldPlaySound() {
        return RatoAddonsConfigSimple.soundAlert;
    }
    
    @Override
    public void onReceivedPacketDetection(Packet packet) {
        if (!isEnabled()) return;
        
        if (packet instanceof S12PacketEntityVelocity) {
            S12PacketEntityVelocity velocityPacket = (S12PacketEntityVelocity) packet;
            
            if (mc.thePlayer != null && velocityPacket.getEntityID() == mc.thePlayer.getEntityId()) {
                double motionY = velocityPacket.getMotionY() / 8000.0;
                
                if (motionY > 0.8) { // Lançamento forte para cima
                    Logger.sendMessage("§7[DEBUG] Suspicious velocity packet: Y=" + String.format("%.2f", motionY));
                    FailsafeManager.getInstance().possibleDetection(this);
                }
            }
        }
    }
    
    @Override
    public void duringFailsafeTrigger() {
        switch (state) {
            case NONE:
                FailsafeManager.getInstance().scheduleRandomDelay(300, 600);
                state = VelocityState.WAIT_BEFORE_START;
                break;
                
            case WAIT_BEFORE_START:
                stopMovement();
                state = VelocityState.LOOK_AROUND;
                FailsafeManager.getInstance().scheduleRandomDelay(400, 700);
                break;
                
            case LOOK_AROUND:
                performHumanLikeMovement();
                state = VelocityState.END;
                FailsafeManager.getInstance().scheduleRandomDelay(500, 900);
                break;
                
            case END:
                endOfFailsafeTrigger();
                break;
        }
    }
    
    @Override
    public void endOfFailsafeTrigger() {
        Logger.sendMessage("§a✓ Velocity check handled");
        FailsafeManager.getInstance().stopFailsafes();
        FailsafeManager.getInstance().scheduleDelay(1000);
    }
    
    @Override
    public void resetStates() {
        state = VelocityState.NONE;
    }
    
    private enum VelocityState {
        NONE,
        WAIT_BEFORE_START,
        LOOK_AROUND,
        END
    }
}
