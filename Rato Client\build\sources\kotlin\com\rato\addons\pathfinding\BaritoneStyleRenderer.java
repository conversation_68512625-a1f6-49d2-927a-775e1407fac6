package com.rato.addons.pathfinding;

import com.rato.addons.pathfinding.movement.MovementType;
import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.Tessellator;
import net.minecraft.client.renderer.WorldRenderer;
import net.minecraft.client.renderer.vertex.DefaultVertexFormats;
import net.minecraft.util.AxisAlignedBB;
import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;
import net.minecraftforge.client.event.RenderWorldLastEvent;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import org.lwjgl.opengl.GL11;

import java.awt.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Sistema de renderização baseado no estilo Baritone/Polar
 * Mostra como os nodes seguem o terreno naturalmente
 */
public class BaritoneStyleRenderer {
    
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Estado da renderização
    private List<BaritoneStylePathfinder.BaritoneNode> currentPath = new ArrayList<>();
    private boolean enabled = false;
    private boolean registered = false;
    
    // Configurações de renderização
    private static final int MAX_RENDERED_NODES = 500;
    private static final double MAX_RENDER_DISTANCE = 200 * 200;
    
    // Cores baseadas no tipo de movimento (MELHORADAS para destacar pulos)
    private static final Color TRAVERSE_COLOR = new Color(100, 200, 255, 160); // Azul claro para movimento normal
    private static final Color ASCEND_COLOR = new Color(255, 0, 0, 255); // VERMELHO BRILHANTE para pulos
    private static final Color DESCEND_COLOR = new Color(0, 255, 255, 160); // Ciano para quedas
    private static final Color DIAGONAL_COLOR = new Color(150, 255, 150, 140); // Verde claro para diagonal
    private static final Color FALL_COLOR = new Color(255, 100, 0, 200); // Laranja para quedas grandes
    private static final Color PATH_LINE_COLOR = new Color(255, 255, 100, 220); // Amarelo brilhante para linhas
    private static final Color CURRENT_NODE_COLOR = new Color(0, 255, 100, 255); // Verde brilhante para node atual
    private static final Color JUMP_INDICATOR_COLOR = new Color(255, 255, 0, 255); // Amarelo para indicadores de pulo
    
    /**
     * Define o caminho a ser renderizado
     */
    public void setPath(List<BaritoneStylePathfinder.BaritoneNode> path) {
        this.currentPath = path != null ? new ArrayList<>(path) : new ArrayList<>();
    }
    
    /**
     * Ativa/desativa a renderização
     */
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        
        if (enabled && !registered) {
            MinecraftForge.EVENT_BUS.register(this);
            registered = true;
        } else if (!enabled && registered) {
            MinecraftForge.EVENT_BUS.unregister(this);
            registered = false;
        }
    }
    
    /**
     * Evento principal de renderização
     */
    @SubscribeEvent
    public void onRenderWorldLast(RenderWorldLastEvent event) {
        if (!enabled || mc.thePlayer == null || currentPath.isEmpty()) {
            return;
        }
        
        try {
            Vec3 playerPos = mc.thePlayer.getPositionVector();
            
            // Configurar OpenGL
            setupRenderState();
            
            // Renderizar nodes e conexões
            renderPathNodes(playerPos, event.partialTicks);
            renderPathConnections(playerPos, event.partialTicks);
            
            // Restaurar estado OpenGL
            restoreRenderState();
            
        } catch (Exception e) {
            // Falha silenciosa para evitar crashes
        }
    }
    
    /**
     * Renderiza os nodes do caminho
     */
    private void renderPathNodes(Vec3 playerPos, float partialTicks) {
        int nodesToRender = Math.min(currentPath.size(), MAX_RENDERED_NODES);
        
        for (int i = 0; i < nodesToRender; i++) {
            BaritoneStylePathfinder.BaritoneNode node = currentPath.get(i);
            
            // Verificar distância
            Vec3 nodePos = new Vec3(node.position.getX() + 0.5, node.position.getY() + 0.5, node.position.getZ() + 0.5);
            if (playerPos.squareDistanceTo(nodePos) > MAX_RENDER_DISTANCE) {
                continue;
            }
            
            // Determinar cor baseada no tipo de movimento
            Color nodeColor = getNodeColor(node, i == 0);
            
            // Renderizar ESP do node
            renderNodeESP(node.position, nodeColor);
            
            // Renderizar indicadores especiais
            renderMovementIndicators(node);
        }
    }
    
    /**
     * Renderiza as conexões entre nodes
     */
    private void renderPathConnections(Vec3 playerPos, float partialTicks) {
        if (currentPath.size() < 2) return;
        
        GL11.glLineWidth(3.0f);
        
        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer buffer = tessellator.getWorldRenderer();
        buffer.begin(GL11.GL_LINES, DefaultVertexFormats.POSITION_COLOR);
        
        for (int i = 0; i < currentPath.size() - 1; i++) {
            BaritoneStylePathfinder.BaritoneNode current = currentPath.get(i);
            BaritoneStylePathfinder.BaritoneNode next = currentPath.get(i + 1);
            
            // Verificar distância
            Vec3 currentPos = new Vec3(current.position.getX() + 0.5, current.position.getY() + 0.5, current.position.getZ() + 0.5);
            if (playerPos.squareDistanceTo(currentPos) > MAX_RENDER_DISTANCE) {
                continue;
            }
            
            // Calcular posições das linhas
            Vec3 fromPos = getNodeRenderPosition(current);
            Vec3 toPos = getNodeRenderPosition(next);
            
            // Ajustar para posição relativa do player
            fromPos = fromPos.subtract(mc.getRenderManager().viewerPosX, 
                                     mc.getRenderManager().viewerPosY, 
                                     mc.getRenderManager().viewerPosZ);
            toPos = toPos.subtract(mc.getRenderManager().viewerPosX, 
                                 mc.getRenderManager().viewerPosY, 
                                 mc.getRenderManager().viewerPosZ);
            
            // Adicionar linha
            buffer.pos(fromPos.xCoord, fromPos.yCoord, fromPos.zCoord)
                  .color(PATH_LINE_COLOR.getRed(), PATH_LINE_COLOR.getGreen(), 
                         PATH_LINE_COLOR.getBlue(), PATH_LINE_COLOR.getAlpha()).endVertex();
            buffer.pos(toPos.xCoord, toPos.yCoord, toPos.zCoord)
                  .color(PATH_LINE_COLOR.getRed(), PATH_LINE_COLOR.getGreen(), 
                         PATH_LINE_COLOR.getBlue(), PATH_LINE_COLOR.getAlpha()).endVertex();
        }
        
        tessellator.draw();
        GL11.glLineWidth(1.0f);
    }
    
    /**
     * Determina a cor do node baseado no tipo de movimento
     */
    private Color getNodeColor(BaritoneStylePathfinder.BaritoneNode node, boolean isCurrent) {
        if (isCurrent) {
            return CURRENT_NODE_COLOR;
        }
        
        switch (node.movementType) {
            case TRAVERSE:
                return TRAVERSE_COLOR;
            case ASCEND:
                return ASCEND_COLOR;
            case DESCEND:
                return DESCEND_COLOR;
            case DIAGONAL:
                return DIAGONAL_COLOR;
            case FALL:
                return FALL_COLOR;
            default:
                return TRAVERSE_COLOR;
        }
    }
    
    /**
     * Calcula posição de renderização do node
     */
    private Vec3 getNodeRenderPosition(BaritoneStylePathfinder.BaritoneNode node) {
        double x = node.position.getX() + 0.5;
        double y = node.position.getY() + 0.1; // Ligeiramente acima do chão
        double z = node.position.getZ() + 0.5;
        
        // Ajustar altura baseado no tipo de movimento
        switch (node.movementType) {
            case ASCEND:
                y += 0.3; // Mais alto para pulos
                break;
            case FALL:
                y -= 0.2; // Mais baixo para quedas
                break;
        }
        
        return new Vec3(x, y, z);
    }
    
    /**
     * Renderiza ESP de um node
     */
    private void renderNodeESP(BlockPos pos, Color color) {
        AxisAlignedBB box = new AxisAlignedBB(
            pos.getX(), pos.getY(), pos.getZ(),
            pos.getX() + 1, pos.getY() + 1, pos.getZ() + 1
        ).offset(-mc.getRenderManager().viewerPosX, 
                -mc.getRenderManager().viewerPosY, 
                -mc.getRenderManager().viewerPosZ);
        
        renderFilledBox(box, color);
        renderBoxOutline(box, color);
    }
    
    /**
     * Renderiza indicadores especiais de movimento - MELHORADO
     */
    private void renderMovementIndicators(BaritoneStylePathfinder.BaritoneNode node) {
        switch (node.movementType) {
            case ASCEND:
                // Renderizar seta para cima BRILHANTE
                renderJumpIndicator(node.position);
                renderArrowIndicator(node.position, true);
                break;
            case FALL:
                // Renderizar seta para baixo
                renderArrowIndicator(node.position, false);
                break;
            case DIAGONAL:
                // Renderizar indicador diagonal
                renderDiagonalIndicator(node.position);
                break;
            case TRAVERSE:
                // Verificar se é subida natural
                if (node.parent != null && node.position.getY() > node.parent.position.getY()) {
                    renderNaturalClimbIndicator(node.position);
                }
                break;
        }
    }
    
    /**
     * Renderiza indicador especial para pulos - NOVO
     */
    private void renderJumpIndicator(BlockPos pos) {
        Vec3 jumpPos = new Vec3(pos.getX() + 0.5, pos.getY() + 1.8, pos.getZ() + 0.5);

        AxisAlignedBB jumpBox = new AxisAlignedBB(
            jumpPos.xCoord - 0.2, jumpPos.yCoord - 0.1, jumpPos.zCoord - 0.2,
            jumpPos.xCoord + 0.2, jumpPos.yCoord + 0.1, jumpPos.zCoord + 0.2
        ).offset(-mc.getRenderManager().viewerPosX,
                -mc.getRenderManager().viewerPosY,
                -mc.getRenderManager().viewerPosZ);

        // Renderizar indicador brilhante para pulo
        renderFilledBox(jumpBox, JUMP_INDICATOR_COLOR);
    }

    /**
     * Renderiza indicador para subida natural - NOVO
     */
    private void renderNaturalClimbIndicator(BlockPos pos) {
        Vec3 climbPos = new Vec3(pos.getX() + 0.5, pos.getY() + 1.2, pos.getZ() + 0.5);

        AxisAlignedBB climbBox = new AxisAlignedBB(
            climbPos.xCoord - 0.1, climbPos.yCoord - 0.05, climbPos.zCoord - 0.1,
            climbPos.xCoord + 0.1, climbPos.yCoord + 0.05, climbPos.zCoord + 0.1
        ).offset(-mc.getRenderManager().viewerPosX,
                -mc.getRenderManager().viewerPosY,
                -mc.getRenderManager().viewerPosZ);

        // Indicador suave para subida natural
        Color climbColor = new Color(200, 255, 200, 120);
        renderFilledBox(climbBox, climbColor);
    }

    /**
     * Renderiza indicador de seta
     */
    private void renderArrowIndicator(BlockPos pos, boolean upward) {
        double y = pos.getY() + (upward ? 1.2 : -0.2);
        Vec3 center = new Vec3(pos.getX() + 0.5, y, pos.getZ() + 0.5);
        
        AxisAlignedBB arrowBox = new AxisAlignedBB(
            center.xCoord - 0.1, center.yCoord - 0.1, center.zCoord - 0.1,
            center.xCoord + 0.1, center.yCoord + 0.1, center.zCoord + 0.1
        ).offset(-mc.getRenderManager().viewerPosX, 
                -mc.getRenderManager().viewerPosY, 
                -mc.getRenderManager().viewerPosZ);
        
        Color arrowColor = upward ? ASCEND_COLOR : FALL_COLOR;
        renderFilledBox(arrowBox, arrowColor);
    }
    
    /**
     * Renderiza indicador diagonal
     */
    private void renderDiagonalIndicator(BlockPos pos) {
        Vec3 center = new Vec3(pos.getX() + 0.5, pos.getY() + 0.6, pos.getZ() + 0.5);
        
        AxisAlignedBB indicatorBox = new AxisAlignedBB(
            center.xCoord - 0.15, center.yCoord - 0.05, center.zCoord - 0.15,
            center.xCoord + 0.15, center.yCoord + 0.05, center.zCoord + 0.15
        ).offset(-mc.getRenderManager().viewerPosX, 
                -mc.getRenderManager().viewerPosY, 
                -mc.getRenderManager().viewerPosZ);
        
        renderFilledBox(indicatorBox, DIAGONAL_COLOR);
    }
    
    /**
     * Configura estado de renderização
     */
    private void setupRenderState() {
        GlStateManager.pushMatrix();
        GlStateManager.disableTexture2D();
        GlStateManager.enableBlend();
        GlStateManager.tryBlendFuncSeparate(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA, GL11.GL_ONE, GL11.GL_ZERO);
        GlStateManager.disableDepth();
        GlStateManager.disableLighting();
        GlStateManager.disableCull();
    }
    
    /**
     * Restaura estado de renderização
     */
    private void restoreRenderState() {
        GlStateManager.enableCull();
        GlStateManager.enableLighting();
        GlStateManager.enableDepth();
        GlStateManager.disableBlend();
        GlStateManager.enableTexture2D();
        GlStateManager.popMatrix();
    }
    
    /**
     * Renderiza caixa preenchida
     */
    private void renderFilledBox(AxisAlignedBB box, Color color) {
        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer buffer = tessellator.getWorldRenderer();
        
        buffer.begin(GL11.GL_QUADS, DefaultVertexFormats.POSITION_COLOR);
        
        float r = color.getRed() / 255.0f;
        float g = color.getGreen() / 255.0f;
        float b = color.getBlue() / 255.0f;
        float a = color.getAlpha() / 255.0f;
        
        // Renderizar todas as faces da caixa
        // Face inferior
        buffer.pos(box.minX, box.minY, box.minZ).color(r, g, b, a).endVertex();
        buffer.pos(box.maxX, box.minY, box.minZ).color(r, g, b, a).endVertex();
        buffer.pos(box.maxX, box.minY, box.maxZ).color(r, g, b, a).endVertex();
        buffer.pos(box.minX, box.minY, box.maxZ).color(r, g, b, a).endVertex();
        
        // Face superior
        buffer.pos(box.minX, box.maxY, box.minZ).color(r, g, b, a).endVertex();
        buffer.pos(box.minX, box.maxY, box.maxZ).color(r, g, b, a).endVertex();
        buffer.pos(box.maxX, box.maxY, box.maxZ).color(r, g, b, a).endVertex();
        buffer.pos(box.maxX, box.maxY, box.minZ).color(r, g, b, a).endVertex();
        
        tessellator.draw();
    }
    
    /**
     * Renderiza contorno da caixa
     */
    private void renderBoxOutline(AxisAlignedBB box, Color color) {
        GL11.glLineWidth(2.0f);
        
        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer buffer = tessellator.getWorldRenderer();
        buffer.begin(GL11.GL_LINES, DefaultVertexFormats.POSITION_COLOR);
        
        float r = color.getRed() / 255.0f;
        float g = color.getGreen() / 255.0f;
        float b = color.getBlue() / 255.0f;
        float a = Math.min(1.0f, color.getAlpha() / 255.0f + 0.3f);
        
        // Arestas da caixa
        // Arestas inferiores
        buffer.pos(box.minX, box.minY, box.minZ).color(r, g, b, a).endVertex();
        buffer.pos(box.maxX, box.minY, box.minZ).color(r, g, b, a).endVertex();
        
        buffer.pos(box.maxX, box.minY, box.minZ).color(r, g, b, a).endVertex();
        buffer.pos(box.maxX, box.minY, box.maxZ).color(r, g, b, a).endVertex();
        
        buffer.pos(box.maxX, box.minY, box.maxZ).color(r, g, b, a).endVertex();
        buffer.pos(box.minX, box.minY, box.maxZ).color(r, g, b, a).endVertex();
        
        buffer.pos(box.minX, box.minY, box.maxZ).color(r, g, b, a).endVertex();
        buffer.pos(box.minX, box.minY, box.minZ).color(r, g, b, a).endVertex();
        
        // Arestas verticais
        buffer.pos(box.minX, box.minY, box.minZ).color(r, g, b, a).endVertex();
        buffer.pos(box.minX, box.maxY, box.minZ).color(r, g, b, a).endVertex();
        
        buffer.pos(box.maxX, box.minY, box.minZ).color(r, g, b, a).endVertex();
        buffer.pos(box.maxX, box.maxY, box.minZ).color(r, g, b, a).endVertex();
        
        buffer.pos(box.maxX, box.minY, box.maxZ).color(r, g, b, a).endVertex();
        buffer.pos(box.maxX, box.maxY, box.maxZ).color(r, g, b, a).endVertex();
        
        buffer.pos(box.minX, box.minY, box.maxZ).color(r, g, b, a).endVertex();
        buffer.pos(box.minX, box.maxY, box.maxZ).color(r, g, b, a).endVertex();
        
        tessellator.draw();
        GL11.glLineWidth(1.0f);
    }
    
    /**
     * Limpa o caminho atual
     */
    public void clearPath() {
        this.currentPath.clear();
    }
    
    /**
     * Verifica se a renderização está ativa
     */
    public boolean isEnabled() {
        return enabled;
    }
}
