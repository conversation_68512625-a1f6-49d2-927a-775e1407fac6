package com.rato.addons.pathfinding;

import com.rato.addons.config.RatoAddonsConfigSimple;
import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.Tessellator;
import net.minecraft.client.renderer.WorldRenderer;
import net.minecraft.client.renderer.vertex.DefaultVertexFormats;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;
import net.minecraftforge.client.event.RenderWorldLastEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import org.lwjgl.opengl.GL11;

import java.util.ArrayList;
import java.util.List;

/**
 * Ferramenta de depuração visual para pathfinding
 * Renderiza nós e arestas do NavGraph para identificar "arestas falsas"
 */
public class PathfindingDebugRenderer {
    
    private static final Minecraft mc = Minecraft.getMinecraft();
    private static PathfindingDebugRenderer instance;
    
    // Debug data
    private List<Vec3> debugNodes = new ArrayList<>();
    private List<DebugEdge> debugEdges = new ArrayList<>();
    private Vec3 stuckPosition = null;
    private Vec3 targetPosition = null;
    private boolean debugEnabled = false;
    
    public static PathfindingDebugRenderer getInstance() {
        if (instance == null) {
            instance = new PathfindingDebugRenderer();
        }
        return instance;
    }
    
    /**
     * Classe para representar uma aresta de debug
     */
    public static class DebugEdge {
        public final Vec3 from;
        public final Vec3 to;
        public final EdgeType type;
        public final boolean isValid;
        
        public DebugEdge(Vec3 from, Vec3 to, EdgeType type, boolean isValid) {
            this.from = from;
            this.to = to;
            this.type = type;
            this.isValid = isValid;
        }
    }
    
    public enum EdgeType {
        WALK, JUMP, FALL, DIAGONAL, WATER
    }
    
    @SubscribeEvent
    public void onRenderWorld(RenderWorldLastEvent event) {
        if (!debugEnabled || !RatoAddonsConfigSimple.pathfindingVisualization || mc.thePlayer == null) return;
        
        EntityPlayer player = mc.thePlayer;
        double playerX = player.lastTickPosX + (player.posX - player.lastTickPosX) * event.partialTicks;
        double playerY = player.lastTickPosY + (player.posY - player.lastTickPosY) * event.partialTicks;
        double playerZ = player.lastTickPosZ + (player.posZ - player.lastTickPosZ) * event.partialTicks;
        
        GlStateManager.pushMatrix();
        GlStateManager.translate(-playerX, -playerY, -playerZ);
        GlStateManager.disableTexture2D();
        GlStateManager.enableBlend();
        GlStateManager.tryBlendFuncSeparate(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA, GL11.GL_ONE, GL11.GL_ZERO);
        GlStateManager.disableDepth();
        GlStateManager.disableLighting();
        GlStateManager.disableCull();
        
        // Renderizar nós
        renderDebugNodes();
        
        // Renderizar arestas
        renderDebugEdges();
        
        // Renderizar posição onde ficou preso
        if (stuckPosition != null) {
            renderStuckPosition();
        }
        
        // Renderizar alvo atual
        if (targetPosition != null) {
            renderTargetPosition();
        }
        
        GlStateManager.enableDepth();
        GlStateManager.enableTexture2D();
        GlStateManager.disableBlend();
        GlStateManager.enableLighting();
        GlStateManager.enableCull();
        GlStateManager.popMatrix();
    }
    
    /**
     * Renderiza nós do NavGraph como esferas
     */
    private void renderDebugNodes() {
        for (Vec3 node : debugNodes) {
            renderSphere(node, 0.3, 100, 100, 255, 150); // Azul translúcido
        }
    }
    
    /**
     * Renderiza arestas do NavGraph como linhas coloridas
     */
    private void renderDebugEdges() {
        GL11.glLineWidth(2.0f);
        
        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer buffer = tessellator.getWorldRenderer();
        buffer.begin(GL11.GL_LINES, DefaultVertexFormats.POSITION_COLOR);
        
        for (DebugEdge edge : debugEdges) {
            int[] color = getEdgeColor(edge);
            
            buffer.pos(edge.from.xCoord, edge.from.yCoord, edge.from.zCoord)
                  .color(color[0], color[1], color[2], color[3]).endVertex();
            buffer.pos(edge.to.xCoord, edge.to.yCoord, edge.to.zCoord)
                  .color(color[0], color[1], color[2], color[3]).endVertex();
        }
        
        tessellator.draw();
        GL11.glLineWidth(1.0f);
    }
    
    /**
     * Renderiza posição onde o bot ficou preso
     */
    private void renderStuckPosition() {
        renderSphere(stuckPosition, 0.5, 255, 0, 0, 200); // Vermelho brilhante
    }
    
    /**
     * Renderiza posição alvo atual
     */
    private void renderTargetPosition() {
        renderSphere(targetPosition, 0.4, 0, 255, 0, 180); // Verde brilhante
    }
    
    /**
     * Renderiza uma esfera na posição especificada
     */
    private void renderSphere(Vec3 position, double radius, int r, int g, int b, int a) {
        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer buffer = tessellator.getWorldRenderer();
        
        buffer.begin(GL11.GL_QUADS, DefaultVertexFormats.POSITION_COLOR);
        
        // Renderizar cubo simples como aproximação de esfera
        double x = position.xCoord;
        double y = position.yCoord;
        double z = position.zCoord;
        
        // Face superior
        buffer.pos(x - radius, y + radius, z - radius).color(r, g, b, a).endVertex();
        buffer.pos(x - radius, y + radius, z + radius).color(r, g, b, a).endVertex();
        buffer.pos(x + radius, y + radius, z + radius).color(r, g, b, a).endVertex();
        buffer.pos(x + radius, y + radius, z - radius).color(r, g, b, a).endVertex();
        
        // Face inferior
        buffer.pos(x - radius, y - radius, z - radius).color(r, g, b, a).endVertex();
        buffer.pos(x + radius, y - radius, z - radius).color(r, g, b, a).endVertex();
        buffer.pos(x + radius, y - radius, z + radius).color(r, g, b, a).endVertex();
        buffer.pos(x - radius, y - radius, z + radius).color(r, g, b, a).endVertex();
        
        // Faces laterais (simplificadas)
        buffer.pos(x - radius, y - radius, z - radius).color(r, g, b, a).endVertex();
        buffer.pos(x - radius, y + radius, z - radius).color(r, g, b, a).endVertex();
        buffer.pos(x + radius, y + radius, z - radius).color(r, g, b, a).endVertex();
        buffer.pos(x + radius, y - radius, z - radius).color(r, g, b, a).endVertex();
        
        tessellator.draw();
    }
    
    /**
     * Obtém cor da aresta baseado no tipo e validade
     */
    private int[] getEdgeColor(DebugEdge edge) {
        if (!edge.isValid) {
            return new int[]{255, 0, 0, 255}; // VERMELHO = ARESTA FALSA!
        }
        
        switch (edge.type) {
            case WALK:
                return new int[]{0, 255, 0, 200}; // Verde = caminhada
            case JUMP:
                return new int[]{255, 255, 0, 200}; // Amarelo = pulo
            case FALL:
                return new int[]{255, 165, 0, 200}; // Laranja = queda
            case DIAGONAL:
                return new int[]{0, 255, 255, 200}; // Ciano = diagonal
            case WATER:
                return new int[]{0, 0, 255, 200}; // Azul = água
            default:
                return new int[]{255, 255, 255, 200}; // Branco = desconhecido
        }
    }
    
    // ====================== APIs PÚBLICAS ======================
    
    public void enableDebug() {
        this.debugEnabled = true;
    }
    
    public void disableDebug() {
        this.debugEnabled = false;
        clearDebugData();
    }
    
    public void addDebugNode(Vec3 position) {
        debugNodes.add(position);
    }
    
    public void addDebugEdge(Vec3 from, Vec3 to, EdgeType type, boolean isValid) {
        debugEdges.add(new DebugEdge(from, to, type, isValid));
    }
    
    public void setStuckPosition(Vec3 position) {
        this.stuckPosition = position;
    }
    
    public void setTargetPosition(Vec3 position) {
        this.targetPosition = position;
    }
    
    public void clearDebugData() {
        debugNodes.clear();
        debugEdges.clear();
        stuckPosition = null;
        targetPosition = null;
    }
    
    public boolean isDebugEnabled() {
        return debugEnabled;
    }
}
