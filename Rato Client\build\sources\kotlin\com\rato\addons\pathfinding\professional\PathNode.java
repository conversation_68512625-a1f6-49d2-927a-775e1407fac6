package com.rato.addons.pathfinding.professional;

import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;

/**
 * Representa um nó no sistema de pathfinding profissional
 */
public class PathNode implements Comparable<PathNode> {
    
    // Posição do nó
    public final Vec3 position;
    public final BlockPos blockPos;
    
    // Custos do A*
    public double gCost; // Custo do início até este nó
    public double hCost; // Heurística (estimativa até o destino)
    public double fCost; // gCost + hCost
    
    // Navegação
    public PathNode parent;
    
    // Tipo de movimento para chegar a este nó
    public MovementType movementType;
    
    // Metadados adicionais
    public boolean isWalkable = true;
    public boolean requiresJump = false;
    public boolean isWater = false;
    public boolean isDangerous = false;
    public double movementCost = 1.0;

    // Flags especiais para Enhanced
    public boolean isJump = false;
    public boolean isFall = false;

    // Timestamp para otimizações
    public long lastUpdated;
    
    /**
     * Tipos de movimento possíveis
     */
    public enum MovementType {
        WALK(1.0),           // Movimento normal
        DIAGONAL(1.414),     // Movimento diagonal (√2)
        JUMP(1.5),           // Pulo
        FALL(1.2),           // Queda
        CLIMB(2.0),          // Subida
        SWIM(1.8),           // Natação
        SPRINT(0.8),         // Corrida (mais rápido)
        SNEAK(2.5);          // Agachado (mais lento)
        
        public final double baseCost;
        
        MovementType(double baseCost) {
            this.baseCost = baseCost;
        }
    }
    
    /**
     * Construtor principal
     */
    public PathNode(Vec3 position, double gCost, double hCost, PathNode parent) {
        this.position = position;
        this.blockPos = new BlockPos(position);
        this.gCost = gCost;
        this.hCost = hCost;
        this.fCost = gCost + hCost;
        this.parent = parent;
        this.movementType = MovementType.WALK;
        this.lastUpdated = System.currentTimeMillis();
    }
    
    /**
     * Construtor com tipo de movimento
     */
    public PathNode(Vec3 position, double gCost, double hCost, PathNode parent, MovementType movementType) {
        this(position, gCost, hCost, parent);
        this.movementType = movementType;
        this.movementCost = movementType.baseCost;
    }
    
    /**
     * Construtor simplificado
     */
    public PathNode(Vec3 position) {
        this(position, 0, 0, null);
    }
    
    /**
     * Atualiza os custos do nó
     */
    public void updateCosts(double newGCost, double newHCost, PathNode newParent) {
        this.gCost = newGCost;
        this.hCost = newHCost;
        this.fCost = newGCost + newHCost;
        this.parent = newParent;
        this.lastUpdated = System.currentTimeMillis();
    }
    
    /**
     * Calcula o custo total de movimento considerando fatores ambientais
     */
    public double getTotalMovementCost() {
        double totalCost = movementCost;
        
        // Aplicar modificadores baseados no ambiente
        if (requiresJump) {
            totalCost *= 1.3;
        }
        
        if (isWater) {
            totalCost *= 1.5;
        }
        
        if (isDangerous) {
            totalCost *= 2.0; // Evitar áreas perigosas
        }
        
        return totalCost;
    }
    
    /**
     * Verifica se este nó é melhor que outro para o mesmo destino
     */
    public boolean isBetterThan(PathNode other) {
        if (other == null) return true;
        
        // Comparar primeiro por fCost, depois por hCost
        if (Math.abs(this.fCost - other.fCost) < 0.001) {
            return this.hCost < other.hCost;
        }
        
        return this.fCost < other.fCost;
    }
    
    /**
     * Calcula distância euclidiana para outro nó
     */
    public double distanceTo(PathNode other) {
        return this.position.distanceTo(other.position);
    }
    
    /**
     * Calcula distância euclidiana para uma posição
     */
    public double distanceTo(Vec3 pos) {
        return this.position.distanceTo(pos);
    }
    
    /**
     * Calcula distância Manhattan para outro nó
     */
    public double manhattanDistanceTo(PathNode other) {
        return manhattanDistanceTo(other.position);
    }
    
    /**
     * Calcula distância Manhattan para uma posição
     */
    public double manhattanDistanceTo(Vec3 pos) {
        return Math.abs(this.position.xCoord - pos.xCoord) +
               Math.abs(this.position.yCoord - pos.yCoord) +
               Math.abs(this.position.zCoord - pos.zCoord);
    }
    
    /**
     * Verifica se este nó está próximo de outro (dentro de uma tolerância)
     */
    public boolean isNear(PathNode other, double tolerance) {
        return distanceTo(other) <= tolerance;
    }
    
    /**
     * Verifica se este nó está próximo de uma posição
     */
    public boolean isNear(Vec3 pos, double tolerance) {
        return distanceTo(pos) <= tolerance;
    }
    
    /**
     * Cria uma cópia deste nó
     */
    public PathNode copy() {
        PathNode copy = new PathNode(this.position, this.gCost, this.hCost, this.parent, this.movementType);
        copy.isWalkable = this.isWalkable;
        copy.requiresJump = this.requiresJump;
        copy.isWater = this.isWater;
        copy.isDangerous = this.isDangerous;
        copy.movementCost = this.movementCost;
        copy.isJump = this.isJump;
        copy.isFall = this.isFall;
        copy.lastUpdated = this.lastUpdated;
        return copy;
    }
    
    /**
     * Implementação do Comparable para uso em PriorityQueue
     */
    @Override
    public int compareTo(PathNode other) {
        // Comparar por fCost primeiro
        int fCostComparison = Double.compare(this.fCost, other.fCost);
        if (fCostComparison != 0) {
            return fCostComparison;
        }
        
        // Se fCost for igual, comparar por hCost (preferir menor hCost)
        return Double.compare(this.hCost, other.hCost);
    }
    
    /**
     * Implementação do equals baseada na posição
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        PathNode pathNode = (PathNode) obj;
        return blockPos.equals(pathNode.blockPos);
    }
    
    /**
     * Implementação do hashCode baseada na posição
     */
    @Override
    public int hashCode() {
        return blockPos.hashCode();
    }
    
    /**
     * Representação em string para debug
     */
    @Override
    public String toString() {
        return String.format("PathNode{pos=(%.1f,%.1f,%.1f), f=%.2f, g=%.2f, h=%.2f, type=%s}", 
            position.xCoord, position.yCoord, position.zCoord, 
            fCost, gCost, hCost, movementType);
    }
    
    /**
     * Representação compacta para logs
     */
    public String toShortString() {
        return String.format("(%.1f,%.1f,%.1f)[f=%.1f]", 
            position.xCoord, position.yCoord, position.zCoord, fCost);
    }
}
