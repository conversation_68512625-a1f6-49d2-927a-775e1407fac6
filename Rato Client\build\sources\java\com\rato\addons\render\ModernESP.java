package com.rato.addons.render;

import com.rato.addons.config.RatoAddonsConfigSimple;
import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.Tessellator;
import net.minecraft.client.renderer.WorldRenderer;
import net.minecraft.client.renderer.vertex.DefaultVertexFormats;
import net.minecraft.entity.Entity;
import net.minecraft.entity.monster.EntityZombie;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.AxisAlignedBB;
import net.minecraftforge.client.event.RenderWorldLastEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import org.lwjgl.opengl.GL11;

import java.awt.*;
import java.util.List;

/**
 * Sistema ESP moderno baseado no LiquidBounce
 * Suporte para Box, Outline e Glow modes
 */
public class ModernESP {

    private static final Minecraft mc = Minecraft.getMinecraft();

    // Configurações de cores (serão obtidas do config)
    private static final Color OUTLINE_COLOR = new Color(255, 255, 255, 200); // Branco para outline

    // Modos de renderização
    public enum ESPMode {
        BOX,
        OUTLINE,
        GLOW
    }

    /**
     * Obtém o modo ESP atual baseado na configuração
     */
    private static ESPMode getCurrentMode() {
        switch (RatoAddonsConfigSimple.riftESPMode) {
            case 0:
                return ESPMode.BOX;
            case 1:
                return ESPMode.OUTLINE;
            case 2:
                return ESPMode.GLOW;
            default:
                return ESPMode.BOX;
        }
    }

    /**
     * Evento principal de renderização
     */
    @SubscribeEvent
    public void onRenderWorld(RenderWorldLastEvent event) {
        if (!RatoAddonsConfigSimple.riftESPEnabled)
            return;

        // Configurar OpenGL para renderização
        setupRenderState();

        try {
            // Renderizar entidades baseado no modo
            ESPMode mode = getCurrentMode();
            switch (mode) {
                case BOX:
                    renderBoxESP(event.partialTicks);
                    break;
                case OUTLINE:
                    renderOutlineESP(event.partialTicks);
                    break;
                case GLOW:
                    renderGlowESP(event.partialTicks);
                    break;
            }
        } finally {
            // Restaurar estado OpenGL
            restoreRenderState();
        }
    }

    /**
     * Renderiza ESP em modo Box (caixas 3D)
     */
    private void renderBoxESP(float partialTicks) {
        List<Entity> entities = mc.theWorld.loadedEntityList;

        for (Entity entity : entities) {
            if (!shouldRenderEntity(entity))
                continue;

            // Calcular posição interpolada
            double x = entity.lastTickPosX + (entity.posX - entity.lastTickPosX) * partialTicks;
            double y = entity.lastTickPosY + (entity.posY - entity.lastTickPosY) * partialTicks;
            double z = entity.lastTickPosZ + (entity.posZ - entity.lastTickPosZ) * partialTicks;

            // Ajustar para posição relativa da câmera
            double renderX = x - mc.getRenderManager().viewerPosX;
            double renderY = y - mc.getRenderManager().viewerPosY;
            double renderZ = z - mc.getRenderManager().viewerPosZ;

            // Calcular bounding box
            AxisAlignedBB boundingBox = entity.getEntityBoundingBox();
            double width = boundingBox.maxX - boundingBox.minX;
            double height = boundingBox.maxY - boundingBox.minY;

            // Expandir ligeiramente para melhor visualização
            double expand = 0.05;

            // Criar box renderizado
            AxisAlignedBB renderBox = new AxisAlignedBB(
                    renderX - width / 2 - expand,
                    renderY - expand,
                    renderZ - width / 2 - expand,
                    renderX + width / 2 + expand,
                    renderY + height + expand,
                    renderZ + width / 2 + expand);

            // Obter cor baseada no tipo de entidade
            Color entityColor = getEntityColor(entity);

            // Renderizar box preenchido
            renderFilledBox(renderBox, entityColor);

            // Renderizar outline se habilitado
            if (RatoAddonsConfigSimple.riftESPOutline) {
                renderBoxOutline(renderBox, OUTLINE_COLOR);
            }
        }
    }

    /**
     * Renderiza ESP em modo Outline (apenas contornos)
     */
    private void renderOutlineESP(float partialTicks) {
        List<Entity> entities = mc.theWorld.loadedEntityList;

        for (Entity entity : entities) {
            if (!shouldRenderEntity(entity))
                continue;

            // Calcular posição interpolada
            double x = entity.lastTickPosX + (entity.posX - entity.lastTickPosX) * partialTicks;
            double y = entity.lastTickPosY + (entity.posY - entity.lastTickPosY) * partialTicks;
            double z = entity.lastTickPosZ + (entity.posZ - entity.lastTickPosZ) * partialTicks;

            // Ajustar para posição relativa da câmera
            double renderX = x - mc.getRenderManager().viewerPosX;
            double renderY = y - mc.getRenderManager().viewerPosY;
            double renderZ = z - mc.getRenderManager().viewerPosZ;

            // Calcular bounding box
            AxisAlignedBB boundingBox = entity.getEntityBoundingBox();
            double width = boundingBox.maxX - boundingBox.minX;
            double height = boundingBox.maxY - boundingBox.minY;

            // Criar box renderizado
            AxisAlignedBB renderBox = new AxisAlignedBB(
                    renderX - width / 2,
                    renderY,
                    renderZ - width / 2,
                    renderX + width / 2,
                    renderY + height,
                    renderZ + width / 2);

            // Obter cor baseada no tipo de entidade
            Color entityColor = getEntityColor(entity);

            // Renderizar apenas outline
            renderBoxOutline(renderBox, entityColor);
        }
    }

    /**
     * Renderiza ESP em modo Glow (efeito de brilho)
     */
    private void renderGlowESP(float partialTicks) {
        // Implementação do glow effect será adicionada posteriormente
        // Por enquanto, usar box mode como fallback
        renderBoxESP(partialTicks);
    }

    /**
     * Verifica se a entidade deve ser renderizada
     */
    private boolean shouldRenderEntity(Entity entity) {
        if (entity == mc.thePlayer)
            return false;
        if (entity.isDead)
            return false;

        // Verificar tipos de entidade habilitados
        if (entity instanceof EntityZombie && RatoAddonsConfigSimple.riftESPZombies) {
            return true;
        }

        if (entity instanceof EntityPlayer && RatoAddonsConfigSimple.riftESPPlayers) {
            return true;
        }

        return false;
    }

    /**
     * Obtém a cor baseada no tipo de entidade
     */
    private Color getEntityColor(Entity entity) {
        if (entity instanceof EntityZombie) {
            // Converter OneColor para Color
            return new Color(
                    RatoAddonsConfigSimple.riftZombieESPColor.getRed(),
                    RatoAddonsConfigSimple.riftZombieESPColor.getGreen(),
                    RatoAddonsConfigSimple.riftZombieESPColor.getBlue(),
                    RatoAddonsConfigSimple.riftZombieESPColor.getAlpha());
        } else if (entity instanceof EntityPlayer) {
            // Converter OneColor para Color
            return new Color(
                    RatoAddonsConfigSimple.riftPlayerESPColor.getRed(),
                    RatoAddonsConfigSimple.riftPlayerESPColor.getGreen(),
                    RatoAddonsConfigSimple.riftPlayerESPColor.getBlue(),
                    RatoAddonsConfigSimple.riftPlayerESPColor.getAlpha());
        }

        return new Color(255, 255, 255, 150); // Branco padrão
    }

    /**
     * Configura o estado OpenGL para renderização
     */
    private void setupRenderState() {
        GlStateManager.pushMatrix();
        GlStateManager.enableBlend();
        GlStateManager.blendFunc(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA);
        GlStateManager.disableTexture2D();
        GlStateManager.disableLighting();
        GlStateManager.disableDepth();
        GlStateManager.depthMask(false);
        GL11.glEnable(GL11.GL_LINE_SMOOTH);
        GL11.glHint(GL11.GL_LINE_SMOOTH_HINT, GL11.GL_NICEST);
        GL11.glLineWidth(1.5f);
    }

    /**
     * Restaura o estado OpenGL
     */
    private void restoreRenderState() {
        GL11.glDisable(GL11.GL_LINE_SMOOTH);
        GlStateManager.depthMask(true);
        GlStateManager.enableDepth();
        GlStateManager.enableLighting();
        GlStateManager.enableTexture2D();
        GlStateManager.disableBlend();
        GlStateManager.popMatrix();
    }

    /**
     * Renderiza uma caixa preenchida
     */
    private void renderFilledBox(AxisAlignedBB box, Color color) {
        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer worldRenderer = tessellator.getWorldRenderer();

        GlStateManager.color(color.getRed() / 255f, color.getGreen() / 255f,
                color.getBlue() / 255f, color.getAlpha() / 255f);

        worldRenderer.begin(GL11.GL_QUADS, DefaultVertexFormats.POSITION);

        // Renderizar todas as faces da caixa
        addBoxVertices(worldRenderer, box);

        tessellator.draw();
    }

    /**
     * Renderiza o contorno de uma caixa
     */
    private void renderBoxOutline(AxisAlignedBB box, Color color) {
        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer worldRenderer = tessellator.getWorldRenderer();

        GlStateManager.color(color.getRed() / 255f, color.getGreen() / 255f,
                color.getBlue() / 255f, color.getAlpha() / 255f);

        worldRenderer.begin(GL11.GL_LINES, DefaultVertexFormats.POSITION);

        // Renderizar todas as arestas da caixa
        addBoxEdges(worldRenderer, box);

        tessellator.draw();
    }

    /**
     * Adiciona vértices para faces da caixa
     */
    private void addBoxVertices(WorldRenderer worldRenderer, AxisAlignedBB box) {
        double minX = box.minX, minY = box.minY, minZ = box.minZ;
        double maxX = box.maxX, maxY = box.maxY, maxZ = box.maxZ;

        // Face inferior (Y mínimo)
        worldRenderer.pos(minX, minY, minZ).endVertex();
        worldRenderer.pos(maxX, minY, minZ).endVertex();
        worldRenderer.pos(maxX, minY, maxZ).endVertex();
        worldRenderer.pos(minX, minY, maxZ).endVertex();

        // Face superior (Y máximo)
        worldRenderer.pos(minX, maxY, minZ).endVertex();
        worldRenderer.pos(minX, maxY, maxZ).endVertex();
        worldRenderer.pos(maxX, maxY, maxZ).endVertex();
        worldRenderer.pos(maxX, maxY, minZ).endVertex();

        // Face frontal (Z mínimo)
        worldRenderer.pos(minX, minY, minZ).endVertex();
        worldRenderer.pos(minX, maxY, minZ).endVertex();
        worldRenderer.pos(maxX, maxY, minZ).endVertex();
        worldRenderer.pos(maxX, minY, minZ).endVertex();

        // Face traseira (Z máximo)
        worldRenderer.pos(minX, minY, maxZ).endVertex();
        worldRenderer.pos(maxX, minY, maxZ).endVertex();
        worldRenderer.pos(maxX, maxY, maxZ).endVertex();
        worldRenderer.pos(minX, maxY, maxZ).endVertex();

        // Face esquerda (X mínimo)
        worldRenderer.pos(minX, minY, minZ).endVertex();
        worldRenderer.pos(minX, minY, maxZ).endVertex();
        worldRenderer.pos(minX, maxY, maxZ).endVertex();
        worldRenderer.pos(minX, maxY, minZ).endVertex();

        // Face direita (X máximo)
        worldRenderer.pos(maxX, minY, minZ).endVertex();
        worldRenderer.pos(maxX, maxY, minZ).endVertex();
        worldRenderer.pos(maxX, maxY, maxZ).endVertex();
        worldRenderer.pos(maxX, minY, maxZ).endVertex();
    }

    /**
     * Adiciona vértices para arestas da caixa
     */
    private void addBoxEdges(WorldRenderer worldRenderer, AxisAlignedBB box) {
        double minX = box.minX, minY = box.minY, minZ = box.minZ;
        double maxX = box.maxX, maxY = box.maxY, maxZ = box.maxZ;

        // Arestas verticais
        worldRenderer.pos(minX, minY, minZ).endVertex();
        worldRenderer.pos(minX, maxY, minZ).endVertex();

        worldRenderer.pos(maxX, minY, minZ).endVertex();
        worldRenderer.pos(maxX, maxY, minZ).endVertex();

        worldRenderer.pos(minX, minY, maxZ).endVertex();
        worldRenderer.pos(minX, maxY, maxZ).endVertex();

        worldRenderer.pos(maxX, minY, maxZ).endVertex();
        worldRenderer.pos(maxX, maxY, maxZ).endVertex();

        // Arestas horizontais inferiores
        worldRenderer.pos(minX, minY, minZ).endVertex();
        worldRenderer.pos(maxX, minY, minZ).endVertex();

        worldRenderer.pos(maxX, minY, minZ).endVertex();
        worldRenderer.pos(maxX, minY, maxZ).endVertex();

        worldRenderer.pos(maxX, minY, maxZ).endVertex();
        worldRenderer.pos(minX, minY, maxZ).endVertex();

        worldRenderer.pos(minX, minY, maxZ).endVertex();
        worldRenderer.pos(minX, minY, minZ).endVertex();

        // Arestas horizontais superiores
        worldRenderer.pos(minX, maxY, minZ).endVertex();
        worldRenderer.pos(maxX, maxY, minZ).endVertex();

        worldRenderer.pos(maxX, maxY, minZ).endVertex();
        worldRenderer.pos(maxX, maxY, maxZ).endVertex();

        worldRenderer.pos(maxX, maxY, maxZ).endVertex();
        worldRenderer.pos(minX, maxY, maxZ).endVertex();

        worldRenderer.pos(minX, maxY, maxZ).endVertex();
        worldRenderer.pos(minX, maxY, minZ).endVertex();
    }

}
