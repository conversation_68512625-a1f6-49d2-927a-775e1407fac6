package com.rato.addons.pathfinding.movement;

import net.minecraft.client.Minecraft;
import net.minecraft.client.settings.KeyBinding;
import net.minecraft.util.Vec3;
import net.minecraft.util.MathHelper;
import com.rato.addons.pathfinding.planning.PathfindingGrid3D;
import com.rato.addons.pathfinding.planning.AStar3DPathfinder;
import com.rato.addons.util.Logger;

import java.util.*;

/**
 * Sistema de execução de caminhos com movimento natural e detecção de obstáculos
 * Controla o movimento do jogador seguindo o caminho calculado
 */
public class PathExecutor {
    
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Estado da execução
    private boolean isExecuting = false;
    private List<Vec3> currentPath = new ArrayList<>();
    private List<PathfindingGrid3D.MovementType> movementTypes = new ArrayList<>();
    private int currentWaypointIndex = 0;
    private long executionStartTime = 0;
    
    // Configurações de movimento
    private static final double WAYPOINT_REACH_DISTANCE = 1.2;
    private static final double PRECISE_REACH_DISTANCE = 0.8;
    private static final double MAX_ROTATION_SPEED = 15.0; // graus por tick
    private static final double MOVEMENT_SMOOTHING = 0.3;
    private static final int STUCK_DETECTION_TICKS = 60; // 3 segundos
    private static final double STUCK_DISTANCE_THRESHOLD = 0.5;
    
    // Detecção de obstáculos
    private Vec3 lastPosition = null;
    private int stuckTicks = 0;
    private long lastProgressTime = 0;
    private boolean emergencyStop = false;

    // Controle de pulo
    private boolean isJumping = false;
    private boolean wasOnGround = true;
    private long lastJumpTime = 0;
    private static final long JUMP_COOLDOWN = 500; // 500ms entre pulos
    
    // Callbacks
    private Runnable onPathComplete = null;
    private Runnable onPathFailed = null;
    private Runnable onStuckDetected = null;
    
    /**
     * Estado da execução do caminho
     */
    public enum ExecutionState {
        IDLE("Idle"),
        MOVING("Moving to waypoint"),
        ROTATING("Adjusting rotation"),
        JUMPING("Jumping"),
        FALLING("Falling"),
        STUCK("Stuck - replanning"),
        COMPLETED("Path completed"),
        FAILED("Path execution failed");
        
        public final String displayName;
        
        ExecutionState(String displayName) {
            this.displayName = displayName;
        }
    }
    
    private ExecutionState currentState = ExecutionState.IDLE;
    
    /**
     * Inicia execução de um caminho
     */
    public boolean startPathExecution(AStar3DPathfinder.PathfindingResult pathResult) {
        if (pathResult == null || !pathResult.pathFound || pathResult.path.isEmpty()) {
            Logger.sendMessage("§cCaminho inválido para execução!");
            return false;
        }
        
        // Parar execução anterior
        stopExecution();
        
        // Configurar novo caminho
        this.currentPath = new ArrayList<>(pathResult.path);
        this.movementTypes = new ArrayList<>(pathResult.movementTypes);
        this.currentWaypointIndex = 0;
        this.executionStartTime = System.currentTimeMillis();
        this.isExecuting = true;
        this.emergencyStop = false;
        this.stuckTicks = 0;
        this.lastProgressTime = System.currentTimeMillis();
        
        // Inicializar posição
        if (mc.thePlayer != null) {
            this.lastPosition = mc.thePlayer.getPositionVector();
        }
        
        currentState = ExecutionState.MOVING;
        Logger.sendMessage("§aIniciando execução do caminho (" + currentPath.size() + " waypoints)");
        
        return true;
    }
    
    /**
     * Para a execução do caminho
     */
    public void stopExecution() {
        if (!isExecuting) return;
        
        isExecuting = false;
        currentState = ExecutionState.IDLE;
        
        // Parar movimento
        releaseMovementKeys();
        
        Logger.sendMessage("§7Execução do caminho interrompida");
    }
    
    /**
     * Atualiza a execução do caminho (chamado a cada tick)
     */
    public void update() {
        if (!isExecuting || mc.thePlayer == null || mc.theWorld == null) {
            return;
        }
        
        // Verificar parada de emergência
        if (emergencyStop) {
            handleEmergencyStop();
            return;
        }
        
        // Verificar se chegou ao final do caminho
        if (currentWaypointIndex >= currentPath.size()) {
            completePathExecution();
            return;
        }
        
        // Detectar se está preso
        if (detectStuck()) {
            handleStuckDetection();
            return;
        }
        
        // Executar movimento para o waypoint atual
        executeMovementToWaypoint();
    }
    
    /**
     * Executa movimento para o waypoint atual
     */
    private void executeMovementToWaypoint() {
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        Vec3 targetWaypoint = currentPath.get(currentWaypointIndex);
        
        double distanceToWaypoint = playerPos.distanceTo(targetWaypoint);
        
        // Verificar se chegou ao waypoint
        if (distanceToWaypoint <= WAYPOINT_REACH_DISTANCE) {
            advanceToNextWaypoint();
            return;
        }
        
        // Calcular direção e rotação necessária
        Vec3 direction = targetWaypoint.subtract(playerPos).normalize();
        float targetYaw = (float) Math.toDegrees(Math.atan2(-direction.xCoord, direction.zCoord));
        
        // Suavizar rotação
        float currentYaw = mc.thePlayer.rotationYaw;
        float yawDifference = MathHelper.wrapAngleTo180_float(targetYaw - currentYaw);
        
        // Aplicar rotação suave
        if (Math.abs(yawDifference) > 2.0f) {
            float rotationStep = (float) Math.min(Math.abs(yawDifference), MAX_ROTATION_SPEED);
            float newYaw = currentYaw + Math.signum(yawDifference) * rotationStep;
            mc.thePlayer.rotationYaw = newYaw;
            currentState = ExecutionState.ROTATING;
        } else {
            currentState = ExecutionState.MOVING;
        }
        
        // Determinar tipo de movimento necessário
        PathfindingGrid3D.MovementType movementType = getCurrentMovementType();
        executeMovementType(movementType, direction, distanceToWaypoint);
    }
    
    /**
     * Obtém o tipo de movimento atual
     */
    private PathfindingGrid3D.MovementType getCurrentMovementType() {
        if (currentWaypointIndex < movementTypes.size()) {
            return movementTypes.get(currentWaypointIndex);
        }
        return PathfindingGrid3D.MovementType.WALK;
    }
    
    /**
     * Executa o tipo de movimento específico
     */
    private void executeMovementType(PathfindingGrid3D.MovementType movementType, Vec3 direction, double distance) {
        // Liberar todas as teclas primeiro
        releaseMovementKeys();
        
        switch (movementType) {
            case WALK:
            case DIAGONAL:
                executeWalkMovement(direction);
                break;
                
            case JUMP:
                executeJumpMovement(direction);
                currentState = ExecutionState.JUMPING;
                break;
                
            case FALL:
                executeFallMovement(direction);
                currentState = ExecutionState.FALLING;
                break;
                
            case CLIMB:
                executeClimbMovement(direction);
                break;
                
            case SWIM:
                executeSwimMovement(direction);
                break;
                
            default:
                executeWalkMovement(direction);
                break;
        }
    }
    
    /**
     * Executa movimento de caminhada
     */
    private void executeWalkMovement(Vec3 direction) {
        // Determinar teclas de movimento baseado na direção
        if (direction.zCoord < -0.3) { // Norte (W)
            KeyBinding.setKeyBindState(mc.gameSettings.keyBindForward.getKeyCode(), true);
        }
        if (direction.zCoord > 0.3) { // Sul (S)
            KeyBinding.setKeyBindState(mc.gameSettings.keyBindBack.getKeyCode(), true);
        }
        if (direction.xCoord < -0.3) { // Oeste (A)
            KeyBinding.setKeyBindState(mc.gameSettings.keyBindLeft.getKeyCode(), true);
        }
        if (direction.xCoord > 0.3) { // Leste (D)
            KeyBinding.setKeyBindState(mc.gameSettings.keyBindRight.getKeyCode(), true);
        }
    }
    
    /**
     * Executa movimento de pulo
     */
    private void executeJumpMovement(Vec3 direction) {
        executeWalkMovement(direction); // Movimento horizontal

        // Controlar pulo de forma inteligente
        handleJumpControl();
    }
    
    /**
     * Executa movimento de queda
     */
    private void executeFallMovement(Vec3 direction) {
        executeWalkMovement(direction); // Movimento horizontal durante a queda
    }
    
    /**
     * Executa movimento de escalada
     */
    private void executeClimbMovement(Vec3 direction) {
        executeWalkMovement(direction);

        // Controlar pulo para escalada
        handleJumpControl();
    }
    
    /**
     * Executa movimento de natação
     */
    private void executeSwimMovement(Vec3 direction) {
        executeWalkMovement(direction);
        
        // Nadar para cima se necessário
        if (direction.yCoord > 0.1) {
            KeyBinding.setKeyBindState(mc.gameSettings.keyBindJump.getKeyCode(), true);
        }
    }
    
    /**
     * Avança para o próximo waypoint
     */
    private void advanceToNextWaypoint() {
        currentWaypointIndex++;
        lastProgressTime = System.currentTimeMillis();
        stuckTicks = 0;
        
        if (currentWaypointIndex < currentPath.size()) {
            Vec3 nextWaypoint = currentPath.get(currentWaypointIndex);
            Logger.sendMessage("§7Waypoint " + currentWaypointIndex + "/" + currentPath.size() + 
                             " - Próximo: " + String.format("%.1f, %.1f, %.1f", 
                             nextWaypoint.xCoord, nextWaypoint.yCoord, nextWaypoint.zCoord));
        }
    }
    
    /**
     * Detecta se o jogador está preso
     */
    private boolean detectStuck() {
        if (mc.thePlayer == null || lastPosition == null) {
            return false;
        }
        
        Vec3 currentPos = mc.thePlayer.getPositionVector();
        double distanceMoved = currentPos.distanceTo(lastPosition);
        
        // Verificar se houve progresso significativo
        if (distanceMoved < STUCK_DISTANCE_THRESHOLD) {
            stuckTicks++;
        } else {
            stuckTicks = 0;
            lastPosition = currentPos;
            lastProgressTime = System.currentTimeMillis();
        }
        
        // Verificar timeout de progresso
        long timeSinceProgress = System.currentTimeMillis() - lastProgressTime;
        
        return stuckTicks >= STUCK_DETECTION_TICKS || timeSinceProgress > 10000; // 10 segundos
    }
    
    /**
     * Lida com detecção de obstáculo
     */
    private void handleStuckDetection() {
        currentState = ExecutionState.STUCK;
        Logger.sendMessage("§cJogador preso detectado! Tentando contornar...");
        
        // Tentar movimento de emergência
        if (attemptEmergencyMovement()) {
            stuckTicks = 0;
            lastProgressTime = System.currentTimeMillis();
        } else {
            // Falha na recuperação
            failPathExecution("Jogador preso - não foi possível contornar");
        }
        
        if (onStuckDetected != null) {
            onStuckDetected.run();
        }
    }
    
    /**
     * Tenta movimento de emergência para sair de obstáculo
     */
    private boolean attemptEmergencyMovement() {
        // Usar controle inteligente de pulo
        handleJumpControl();

        // Tentar movimento para trás
        KeyBinding.setKeyBindState(mc.gameSettings.keyBindBack.getKeyCode(), true);

        // Aguardar alguns ticks
        return true; // Simplificado - pode ser melhorado
    }
    
    /**
     * Lida com parada de emergência
     */
    private void handleEmergencyStop() {
        releaseMovementKeys();
        failPathExecution("Parada de emergência ativada");
    }
    
    /**
     * Completa a execução do caminho com sucesso
     */
    private void completePathExecution() {
        isExecuting = false;
        currentState = ExecutionState.COMPLETED;
        releaseMovementKeys();
        
        long executionTime = System.currentTimeMillis() - executionStartTime;
        Logger.sendMessage("§aCaminho completado com sucesso! Tempo: " + (executionTime / 1000.0) + "s");
        
        if (onPathComplete != null) {
            onPathComplete.run();
        }
    }
    
    /**
     * Falha na execução do caminho
     */
    private void failPathExecution(String reason) {
        isExecuting = false;
        currentState = ExecutionState.FAILED;
        releaseMovementKeys();
        
        Logger.sendMessage("§cFalha na execução do caminho: " + reason);
        
        if (onPathFailed != null) {
            onPathFailed.run();
        }
    }
    
    /**
     * Controla o pulo de forma inteligente
     */
    private void handleJumpControl() {
        boolean currentlyOnGround = mc.thePlayer.onGround;
        long currentTime = System.currentTimeMillis();

        // Detectar quando o player sai do chão (completou o pulo)
        if (isJumping && wasOnGround && !currentlyOnGround) {
            // Player saiu do chão, liberar tecla de pulo
            KeyBinding.setKeyBindState(mc.gameSettings.keyBindJump.getKeyCode(), false);
            isJumping = false;
        }

        // Verificar se deve iniciar um novo pulo
        if (!isJumping && currentlyOnGround && (currentTime - lastJumpTime) > JUMP_COOLDOWN) {
            KeyBinding.setKeyBindState(mc.gameSettings.keyBindJump.getKeyCode(), true);
            isJumping = true;
            lastJumpTime = currentTime;
        }

        // Atualizar estado anterior
        wasOnGround = currentlyOnGround;
    }

    /**
     * Libera todas as teclas de movimento
     */
    private void releaseMovementKeys() {
        KeyBinding.setKeyBindState(mc.gameSettings.keyBindForward.getKeyCode(), false);
        KeyBinding.setKeyBindState(mc.gameSettings.keyBindBack.getKeyCode(), false);
        KeyBinding.setKeyBindState(mc.gameSettings.keyBindLeft.getKeyCode(), false);
        KeyBinding.setKeyBindState(mc.gameSettings.keyBindRight.getKeyCode(), false);
        KeyBinding.setKeyBindState(mc.gameSettings.keyBindJump.getKeyCode(), false);
        KeyBinding.setKeyBindState(mc.gameSettings.keyBindSneak.getKeyCode(), false);

        // Resetar estado de pulo
        isJumping = false;
        wasOnGround = true;
    }
    
    /**
     * Ativa parada de emergência
     */
    public void emergencyStop() {
        emergencyStop = true;
    }
    
    // Getters e Setters
    public boolean isExecuting() { return isExecuting; }
    public ExecutionState getCurrentState() { return currentState; }
    public int getCurrentWaypointIndex() { return currentWaypointIndex; }
    public int getTotalWaypoints() { return currentPath.size(); }
    public double getProgressPercentage() { 
        return currentPath.isEmpty() ? 0.0 : (double) currentWaypointIndex / currentPath.size() * 100.0; 
    }
    
    // Callbacks
    public void setOnPathComplete(Runnable callback) { this.onPathComplete = callback; }
    public void setOnPathFailed(Runnable callback) { this.onPathFailed = callback; }
    public void setOnStuckDetected(Runnable callback) { this.onStuckDetected = callback; }
}
