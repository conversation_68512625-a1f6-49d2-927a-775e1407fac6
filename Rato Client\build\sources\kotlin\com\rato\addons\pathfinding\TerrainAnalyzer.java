package com.rato.addons.pathfinding;

import net.minecraft.block.Block;
import net.minecraft.block.material.Material;
import net.minecraft.client.Minecraft;
import net.minecraft.init.Blocks;
import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;
import net.minecraft.world.World;

import java.util.ArrayList;
import java.util.List;

/**
 * Analisador de terreno para pathfinding inteligente
 * Detecta obstáculos, altura segura, e gera waypoints intermediários
 */
public class TerrainAnalyzer {
    
    private final Minecraft mc = Minecraft.getMinecraft();
    private static final int MAX_WAYPOINT_DISTANCE = 50; // Distância máxima entre waypoints
    private static final int SCAN_RADIUS = 3; // Raio de escaneamento
    
    /**
     * Analisa se um waypoint precisa de pontos intermediários
     */
    public List<Vec3> analyzeAndGenerateWaypoints(Vec3 start, Vec3 end) {
        List<Vec3> waypoints = new ArrayList<>();
        waypoints.add(start);
        
        double distance = start.distanceTo(end);
        
        if (distance <= MAX_WAYPOINT_DISTANCE) {
            // Distância curta - verificar apenas obstáculos diretos
            List<Vec3> intermediate = generateDirectPath(start, end);
            waypoints.addAll(intermediate);
        } else {
            // Distância longa - gerar múltiplos waypoints intermediários
            List<Vec3> intermediate = generateLongDistancePath(start, end);
            waypoints.addAll(intermediate);
        }
        
        waypoints.add(end);
        return waypoints;
    }
    
    /**
     * Gera caminho direto para distâncias curtas (CORRIGIDO PARA NODES FLUTUANDO)
     */
    private List<Vec3> generateDirectPath(Vec3 start, Vec3 end) {
        List<Vec3> waypoints = new ArrayList<>();

        // Verificar se há obstáculos no caminho direto
        if (hasObstacles(start, end)) {
            // Encontrar rota alternativa usando progressão gradual
            Vec3 intermediate = findSafeIntermediatePointProgressive(start, end);
            if (intermediate != null) {
                waypoints.add(intermediate);
            }
        } else {
            // Se não há obstáculos, verificar se precisa ajustar altura do destino
            double heightDiff = Math.abs(end.yCoord - start.yCoord);
            if (heightDiff > 3) {
                // Tentar adicionar waypoint intermediário apenas se for caminhável
                Vec3 intermediate = createHeightAdjustmentWaypoint(start, end);
                if (intermediate != null) {
                    waypoints.add(intermediate);
                }
                // Se intermediate for null, não adicionar waypoint (evita nodes flutuando)
            }
        }

        return waypoints;
    }

    /**
     * Cria waypoint intermediário apenas para ajustar altura gradualmente
     * CORREÇÃO: Só cria waypoint se encontrar posição caminhável
     */
    private Vec3 createHeightAdjustmentWaypoint(Vec3 start, Vec3 end) {
        double midX = (start.xCoord + end.xCoord) / 2;
        double midZ = (start.zCoord + end.zCoord) / 2;

        // Encontrar altura segura no ponto médio seguindo progressão
        double safeY = findSafeHeightWithProgression(midX, midZ, start, end, 0.5);

        // VERIFICAÇÃO ADICIONAL: Só retornar se a posição for realmente caminhável
        if (isWalkablePosition(midX, safeY, midZ)) {
            return new Vec3(midX, safeY, midZ);
        }

        // Se não for caminhável, não criar waypoint intermediário
        return null;
    }
    
    /**
     * Gera caminho para longas distâncias
     */
    private List<Vec3> generateLongDistancePath(Vec3 start, Vec3 end) {
        List<Vec3> waypoints = new ArrayList<>();

        // Calcular número de segmentos necessários
        double distance = start.distanceTo(end);
        int segments = (int) Math.ceil(distance / MAX_WAYPOINT_DISTANCE);

        Vec3 current = start;
        for (int i = 1; i < segments; i++) {
            // Calcular posição intermediária
            double progress = (double) i / segments;
            double x = start.xCoord + (end.xCoord - start.xCoord) * progress;
            double z = start.zCoord + (end.zCoord - start.zCoord) * progress;

            // Encontrar altura segura seguindo progressão gradual para o destino
            double y = findSafeHeightWithProgression(x, z, start, end, progress);

            Vec3 intermediate = new Vec3(x, y, z);

            // Verificar se este ponto é seguro
            if (isSafePosition(intermediate)) {
                waypoints.add(intermediate);
                current = intermediate;
            } else {
                // Encontrar posição alternativa próxima
                Vec3 alternative = findNearestSafePosition(intermediate);
                if (alternative != null) {
                    waypoints.add(alternative);
                    current = alternative;
                }
            }
        }
        
        return waypoints;
    }
    
    /**
     * Verifica se há obstáculos entre dois pontos (CORRIGIDO PARA NODES FLUTUANDO)
     */
    private boolean hasObstacles(Vec3 start, Vec3 end) {
        // Se há grande diferença de altura, não verificar obstáculos lineares no ar
        double heightDiff = Math.abs(end.yCoord - start.yCoord);
        if (heightDiff > 5) {
            // Para grandes diferenças de altura, verificar apenas obstáculos horizontais
            return hasHorizontalObstacles(start, end);
        }

        // Para pequenas diferenças de altura, usar raytrace normal
        double distance = start.distanceTo(end);
        int steps = (int) (distance * 2); // 2 verificações por bloco

        for (int i = 0; i <= steps; i++) {
            double progress = (double) i / steps;
            double x = start.xCoord + (end.xCoord - start.xCoord) * progress;
            double y = start.yCoord + (end.yCoord - start.yCoord) * progress;
            double z = start.zCoord + (end.zCoord - start.zCoord) * progress;

            if (isSolidBlock(x, y, z)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Verifica obstáculos apenas no plano horizontal (para grandes diferenças de altura)
     */
    private boolean hasHorizontalObstacles(Vec3 start, Vec3 end) {
        double horizontalDistance = Math.sqrt(
            Math.pow(end.xCoord - start.xCoord, 2) +
            Math.pow(end.zCoord - start.zCoord, 2)
        );

        int steps = (int) (horizontalDistance * 2);
        if (steps == 0) return false;

        // Usar altura do chão para verificação horizontal
        double checkY = Math.min(start.yCoord, end.yCoord);

        for (int i = 0; i <= steps; i++) {
            double progress = (double) i / steps;
            double x = start.xCoord + (end.xCoord - start.xCoord) * progress;
            double z = start.zCoord + (end.zCoord - start.zCoord) * progress;

            // Verificar se há obstáculos na altura do chão
            if (isSolidBlock(x, checkY, z) || isSolidBlock(x, checkY + 1, z)) {
                return true;
            }
        }

        return false;
    }
    
    /**
     * Encontra um ponto intermediário seguro
     */
    private Vec3 findSafeIntermediatePoint(Vec3 start, Vec3 end) {
        // Tentar pontos em diferentes alturas
        double midX = (start.xCoord + end.xCoord) / 2;
        double midZ = (start.zCoord + end.zCoord) / 2;

        // Tentar diferentes alturas
        for (int heightOffset = 5; heightOffset <= 20; heightOffset += 5) {
            double testY = Math.max(start.yCoord, end.yCoord) + heightOffset;
            Vec3 testPoint = new Vec3(midX, testY, midZ);

            if (isSafePosition(testPoint)) {
                return testPoint;
            }
        }

        return null;
    }

    /**
     * Encontra um ponto intermediário seguro seguindo progressão gradual (CORRIGE NODES FLUTUANDO)
     */
    private Vec3 findSafeIntermediatePointProgressive(Vec3 start, Vec3 end) {
        double midX = (start.xCoord + end.xCoord) / 2;
        double midZ = (start.zCoord + end.zCoord) / 2;

        // Calcular altura intermediária baseada na progressão linear
        double midY = (start.yCoord + end.yCoord) / 2;

        // Encontrar altura segura próxima da altura intermediária calculada
        double safeY = findSafeHeightWithProgression(midX, midZ, start, end, 0.5);

        Vec3 testPoint = new Vec3(midX, safeY, midZ);

        if (isSafePosition(testPoint)) {
            return testPoint;
        }

        // Fallback: tentar alturas próximas da altura intermediária
        for (int offset = 1; offset <= 5; offset++) {
            // Tentar para cima e para baixo
            double[] testHeights = {midY + offset, midY - offset};

            for (double testY : testHeights) {
                if (testY < 1 || testY > 255) continue;

                Vec3 fallbackPoint = new Vec3(midX, testY, midZ);
                if (isSafePosition(fallbackPoint)) {
                    return fallbackPoint;
                }
            }
        }

        return null;
    }
    
    /**
     * Encontra altura segura para uma coordenada X,Z seguindo o terreno (baseado no Mucifex/Stevebot)
     */
    private double findSafeHeight(double x, double z) {
        World world = mc.theWorld;
        if (world == null || mc.thePlayer == null) return 70; // Altura padrão

        // Começar da altura do player atual para seguir o terreno naturalmente
        int startY = (int) mc.thePlayer.posY;

        // Procurar o melhor chão em um range ao redor da altura atual
        int bestGroundY = findBestGroundInRange(x, z, startY, 15);

        if (bestGroundY != -1) {
            // Verificar se há espaço adequado para o player (3 blocos como no Stevebot)
            if (hasPlayerSpace(x, bestGroundY + 1, z)) {
                return bestGroundY + 1; // Uma posição acima do bloco sólido
            }
        }

        // Fallback: usar altura do player atual
        return mc.thePlayer.posY;
    }

    /**
     * Encontra altura segura seguindo progressão gradual em direção ao destino
     * CORREÇÃO DEFINITIVA PARA NODES FLUTUANDO - IMPLEMENTA WALKABILITY CORRETA
     */
    private double findSafeHeightWithProgression(double x, double z, Vec3 start, Vec3 end, double progress) {
        World world = mc.theWorld;
        if (world == null) return 70; // Altura padrão

        // Calcular altura alvo baseada na progressão linear entre start e end
        double targetY = start.yCoord + (end.yCoord - start.yCoord) * progress;
        int targetYInt = (int) Math.round(targetY);

        // CORREÇÃO PRINCIPAL: Encontrar posição CAMINHÁVEL real (não apenas chão)
        int walkableY = findWalkablePosition(x, z, targetYInt);

        if (walkableY != -1) {
            return walkableY; // Posição onde o player pode ficar em pé
        }

        // Se não encontrou posição caminhável, não criar waypoint no ar
        // Retornar altura do chão mais próximo encontrado
        int nearestGround = findNearestGroundLevel(x, z, targetYInt);
        if (nearestGround != -1) {
            return nearestGround + 1; // Uma posição acima do chão
        }

        // Último fallback: usar altura alvo calculada (mas isso pode causar nodes flutuando)
        return targetY;
    }

    /**
     * Encontra o melhor chão em um range de altura (inspirado no Mucifex)
     */
    private int findBestGroundInRange(double x, double z, int centerY, int range) {
        World world = mc.theWorld;
        int bestY = -1;
        int minHeightDiff = Integer.MAX_VALUE;

        // Procurar em camadas, priorizando altura próxima ao player
        for (int offset = 0; offset <= range; offset++) {
            // Verificar para baixo e para cima alternadamente
            int[] yLevels = {centerY - offset, centerY + offset};

            for (int y : yLevels) {
                if (y < 1 || y > 255) continue;

                BlockPos pos = new BlockPos(x, y, z);

                if (isValidGroundBlock(pos)) {
                    int heightDiff = Math.abs(y - centerY);

                    // Preferir o chão mais próximo da altura atual
                    if (heightDiff < minHeightDiff) {
                        minHeightDiff = heightDiff;
                        bestY = y;
                    }
                }
            }

            // Se encontrou um chão próximo, usar ele
            if (bestY != -1 && minHeightDiff <= 3) {
                break;
            }
        }

        return bestY;
    }

    /**
     * Encontra o melhor chão seguindo progressão gradual (CORRIGE NODES FLUTUANDO)
     * Prioriza chão próximo da altura alvo em vez da altura atual do player
     */
    private int findBestGroundInRangeProgressive(double x, double z, int targetY, int range) {
        World world = mc.theWorld;
        int bestY = -1;
        int minHeightDiff = Integer.MAX_VALUE;

        // Procurar em camadas, priorizando altura próxima ao ALVO (não ao player)
        for (int offset = 0; offset <= range; offset++) {
            // Verificar para baixo e para cima alternadamente a partir do alvo
            int[] yLevels = {targetY - offset, targetY + offset};

            for (int y : yLevels) {
                if (y < 1 || y > 255) continue;

                BlockPos pos = new BlockPos(x, y, z);

                if (isValidGroundBlock(pos)) {
                    int heightDiff = Math.abs(y - targetY);

                    // Preferir o chão mais próximo da altura ALVO
                    if (heightDiff < minHeightDiff) {
                        minHeightDiff = heightDiff;
                        bestY = y;
                    }
                }
            }

            // Se encontrou um chão próximo ao alvo, usar ele
            if (bestY != -1 && minHeightDiff <= 2) {
                break;
            }
        }

        return bestY;
    }

    /**
     * Encontra posição caminhável real (CORREÇÃO DEFINITIVA PARA NODES FLUTUANDO)
     * Implementa verificação de walkability como Mucifex/Stevebot
     */
    private int findWalkablePosition(double x, double z, int targetY) {
        World world = mc.theWorld;

        // Procurar em range limitado ao redor da altura alvo
        for (int offset = 0; offset <= 8; offset++) {
            // Verificar para baixo e para cima alternadamente
            int[] yLevels = {targetY - offset, targetY + offset};

            for (int y : yLevels) {
                if (y < 1 || y > 254) continue; // Deixar espaço para verificação acima

                if (isWalkablePosition(x, y, z)) {
                    return y; // Retorna a posição onde o player pode ficar
                }
            }
        }

        return -1; // Não encontrou posição caminhável
    }

    /**
     * Verifica se uma posição é caminhável (implementação correta de walkability)
     */
    private boolean isWalkablePosition(double x, double y, double z) {
        World world = mc.theWorld;

        BlockPos feetPos = new BlockPos(x, y, z);
        BlockPos headPos = new BlockPos(x, y + 1, z);
        BlockPos groundPos = new BlockPos(x, y - 1, z);

        Block feetBlock = world.getBlockState(feetPos).getBlock();
        Block headBlock = world.getBlockState(headPos).getBlock();
        Block groundBlock = world.getBlockState(groundPos).getBlock();

        // 1. Deve ter espaço para o player (2 blocos de altura)
        if (!canWalkThrough(feetBlock, feetPos) || !canWalkThrough(headBlock, headPos)) {
            return false;
        }

        // 2. Deve ter chão sólido embaixo
        if (!isValidGroundBlock(groundPos)) {
            return false;
        }

        return true;
    }

    /**
     * Encontra o nível de chão mais próximo (fallback quando não há posição caminhável)
     */
    private int findNearestGroundLevel(double x, double z, int targetY) {
        World world = mc.theWorld;

        // Procurar chão sólido em range maior
        for (int offset = 0; offset <= 15; offset++) {
            int[] yLevels = {targetY - offset, targetY + offset};

            for (int y : yLevels) {
                if (y < 1 || y > 255) continue;

                BlockPos pos = new BlockPos(x, y, z);
                if (isValidGroundBlock(pos)) {
                    return y; // Retorna Y do bloco sólido
                }
            }
        }

        return -1; // Não encontrou chão
    }

    /**
     * Verifica se há espaço adequado para o player (baseado no Stevebot)
     */
    private boolean hasPlayerSpace(double x, double y, double z) {
        World world = mc.theWorld;

        // Verificar 3 blocos de altura como no Stevebot
        for (int i = 0; i < 3; i++) {
            BlockPos checkPos = new BlockPos(x, y + i, z);
            Block block = world.getBlockState(checkPos).getBlock();

            if (!canWalkThrough(block, checkPos)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Verifica se pode caminhar através de um bloco (baseado no Stevebot)
     */
    private boolean canWalkThrough(Block block, BlockPos pos) {
        // Ar é sempre passável
        if (block == Blocks.air) {
            return true;
        }

        // Folhas são passáveis
        if (block == Blocks.leaves || block == Blocks.leaves2) {
            return true;
        }

        // Líquidos não são passáveis
        if (block == Blocks.water || block == Blocks.flowing_water ||
            block == Blocks.lava || block == Blocks.flowing_lava) {
            return false;
        }

        // Blocos perigosos não são passáveis
        if (block == Blocks.fire || block == Blocks.cactus) {
            return false;
        }

        // Usar verificação do Minecraft para outros blocos
        return !block.getMaterial().blocksMovement() || !block.isFullBlock();
    }

    /**
     * Encontra o nível do chão mais próximo da altura especificada
     */
    private int findGroundLevel(double x, double z, int startY) {
        World world = mc.theWorld;

        // Procurar para baixo primeiro (mais comum)
        for (int y = startY; y >= Math.max(1, startY - 20); y--) {
            BlockPos pos = new BlockPos(x, y, z);
            Block block = world.getBlockState(pos).getBlock();

            if (isValidGroundBlock(pos)) {
                return y;
            }
        }

        // Se não encontrou para baixo, procurar para cima (subidas)
        for (int y = startY + 1; y <= Math.min(255, startY + 10); y++) {
            BlockPos pos = new BlockPos(x, y, z);
            Block block = world.getBlockState(pos).getBlock();

            if (isValidGroundBlock(pos)) {
                return y;
            }
        }

        return -1; // Não encontrou chão válido
    }

    /**
     * Verifica se um bloco é válido como chão (baseado no Mucifex)
     */
    private boolean isValidGroundBlock(BlockPos pos) {
        World world = mc.theWorld;
        Block block = world.getBlockState(pos).getBlock();

        // Ar não é chão válido
        if (block == Blocks.air) {
            return false;
        }

        // Líquidos não são chão válido
        if (block == Blocks.water || block == Blocks.flowing_water ||
            block == Blocks.lava || block == Blocks.flowing_lava) {
            return false;
        }

        // Blocos perigosos não são chão válido
        if (block == Blocks.fire || block == Blocks.cactus) {
            return false;
        }

        // Verificar se é um bloco sólido que pode suportar o player
        return isBlockSolid(block, pos);
    }

    /**
     * Verifica se um bloco é sólido (baseado no Mucifex)
     */
    private boolean isBlockSolid(Block block, BlockPos pos) {
        // Fast path para ar
        if (block.getMaterial() == Material.air) {
            return false;
        }

        // Fast path para blocos sólidos completos
        if (block.getMaterial().blocksMovement() && !block.getMaterial().isLiquid()) {
            if (block.isFullCube()) {
                return true;
            }
        }

        // Verificar tipos específicos de blocos (como no Mucifex)
        return block.isBlockSolid(mc.theWorld, pos, null) ||
               block == Blocks.stone_slab ||
               block == Blocks.wooden_slab ||
               block == Blocks.stone_slab2 ||
               block == Blocks.glass ||
               block == Blocks.stained_glass ||
               block == Blocks.glass_pane ||
               block == Blocks.stained_glass_pane ||
               block == Blocks.oak_fence ||
               block == Blocks.nether_brick_fence ||
               block == Blocks.chest ||
               block == Blocks.ender_chest ||
               block == Blocks.stone_stairs ||
               block == Blocks.oak_stairs ||
               block == Blocks.cobblestone_wall;
    }
    
    /**
     * Verifica se uma posição é segura para o player
     */
    private boolean isSafePosition(Vec3 pos) {
        World world = mc.theWorld;
        if (world == null) return false;
        
        // Verificar se há espaço para o player (2 blocos de altura)
        for (int i = 0; i < 2; i++) {
            BlockPos checkPos = new BlockPos(pos.xCoord, pos.yCoord + i, pos.zCoord);
            Block block = world.getBlockState(checkPos).getBlock();
            
            if (block != Blocks.air && block.getMaterial() != Material.water) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Encontra posição segura mais próxima
     */
    private Vec3 findNearestSafePosition(Vec3 center) {
        // Buscar em espiral ao redor do centro
        for (int radius = 1; radius <= SCAN_RADIUS; radius++) {
            for (int x = -radius; x <= radius; x++) {
                for (int z = -radius; z <= radius; z++) {
                    if (Math.abs(x) == radius || Math.abs(z) == radius) {
                        double testX = center.xCoord + x;
                        double testZ = center.zCoord + z;
                        double testY = findSafeHeight(testX, testZ);
                        
                        Vec3 testPos = new Vec3(testX, testY, testZ);
                        if (isSafePosition(testPos)) {
                            return testPos;
                        }
                    }
                }
            }
        }
        
        return null;
    }
    
    /**
     * Verifica se um bloco é sólido
     */
    private boolean isSolidBlock(double x, double y, double z) {
        World world = mc.theWorld;
        if (world == null) return false;
        
        BlockPos pos = new BlockPos(x, y, z);
        Block block = world.getBlockState(pos).getBlock();
        
        return block != Blocks.air && 
               block.getMaterial() != Material.water &&
               block.getMaterial().isSolid();
    }
    
    /**
     * Calcula distância 3D entre dois pontos
     */
    public double getDistance3D(Vec3 a, Vec3 b) {
        return Math.sqrt(
            Math.pow(a.xCoord - b.xCoord, 2) +
            Math.pow(a.yCoord - b.yCoord, 2) +
            Math.pow(a.zCoord - b.zCoord, 2)
        );
    }
    
    /**
     * Verifica se um waypoint está muito longe do anterior
     */
    public boolean needsIntermediateWaypoints(Vec3 start, Vec3 end) {
        return getDistance3D(start, end) > MAX_WAYPOINT_DISTANCE;
    }
}
