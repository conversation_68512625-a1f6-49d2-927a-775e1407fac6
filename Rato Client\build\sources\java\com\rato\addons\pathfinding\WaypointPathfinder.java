package com.rato.addons.pathfinding;

import com.rato.addons.config.RatoAddonsConfigSimple;
import com.rato.addons.util.Logger;
import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.Tessellator;
import net.minecraft.client.renderer.WorldRenderer;
import net.minecraft.client.renderer.vertex.DefaultVertexFormats;
import net.minecraft.client.settings.KeyBinding;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;
import net.minecraft.init.Blocks;
import net.minecraft.block.Block;
import net.minecraftforge.client.event.RenderWorldLastEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.InputEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;
import org.lwjgl.opengl.GL11;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.Map;
import java.util.HashMap;

/**
 * Sistema de Pathfinding baseado em Waypoints com movimento de câmera realista
 * Renderiza "passos" bloco‑a‑bloco exatamente onde o player pisa.
 */
public class WaypointPathfinder {

    private static final Minecraft mc = Minecraft.getMinecraft();
    private static WaypointPathfinder instance;
    private final Random random = new Random();

    // Sistemas inteligentes
    private final TerrainAnalyzer terrainAnalyzer = new TerrainAnalyzer();
    private final NaturalCameraMovement cameraMovement = new NaturalCameraMovement();

    /* ====================== CONFIGURAÇÕES GERAIS ====================== */
    private static final double WAYPOINT_REACH_DISTANCE = 2.0;
    private static final float CAMERA_ROTATION_SPEED = 2.0f;
    private static final long HESITATION_CHANCE = 5000; // A cada 5 s há chance de hesitar

    /* ===== Densidade da linha renderizada (menor valor = linha mais lisa) */
    private static final double PATH_STEP = 0.35;

    /* ====================== ESTADO DO PATHFINDING ====================== */
    private boolean isActive = false;
    private List<Vec3> waypoints = new ArrayList<>();
    private int currentWaypointIndex = 0;
    private Vec3 currentTarget = null;

    /* Waypoints definidos manualmente (sempre renderizados quando inativo) */
    private final List<Vec3> definedWaypoints = new ArrayList<>();

    /* Lista densa de “passos” usada APENAS para renderização quando ativo */
    private final List<Vec3> pathSteps = new ArrayList<>();

    /* ====================== CÂMERA E COMPORTAMENTO HUMANO ====================== */
    private float targetYaw = 0;
    private float targetPitch = 0;
    private boolean shouldLookAtTarget = true;
    private long lastCameraUpdate = 0;

    private long lastMovementTime = 0;
    private long nextHesitationTime = 0;
    private boolean isHesitating = false;
    private float cameraVariation = 0;

    // Timeout para waypoint
    private long waypointStartTime = 0;
    private static final long WAYPOINT_TIMEOUT = 20000; // 20 segundos

    // Sistema de olhar natural
    private long lastLookAroundTime = 0;
    private float naturalYawOffset = 0;
    private long lookAroundDuration = 0;

    // SISTEMA DE STUCK DETECTION E RECOVERY
    private Vec3 lastPosition = null;
    private long lastMoveTime = 0;
    private static final long STUCK_TIMEOUT_MS = 2000; // 2 segundos
    private int stuckAttempts = 0;
    private static final int MAX_STUCK_ATTEMPTS = 3;
    private final Map<BlockPos, Long> penalizedNodes = new HashMap<>();
    private static final long PENALTY_DURATION = 30000; // 30 segundos

    /* -------------------------------------------------------------------------- */
    public static WaypointPathfinder getInstance() {
        if (instance == null) instance = new WaypointPathfinder();
        return instance;
    }

    /* ====================== MANEJO DE ENTRADAS ====================== */
    @SubscribeEvent
    public void onKeyInput(InputEvent.KeyInputEvent event) {
        if (!RatoAddonsConfigSimple.pathfindingEnabled) return;

        if (RatoAddonsConfigSimple.pathfindingToggle.isActive()) {
            if (isActive) {
                stopPathfinding();
            } else {
                startPathfindingWithDefinedWaypoints();
            }
        }
    }

    /* ====================== INÍCIO / PARADA ====================== */
    public void startPathfinding(List<Vec3> wps) {
        if (wps == null || wps.isEmpty()) {
            Logger.sendMessage("§cNenhum waypoint fornecido!");
            return;
        }

        // Análise inteligente e geração de waypoints intermediários
        List<Vec3> optimizedWaypoints = generateIntelligentPath(wps);

        this.waypoints = new ArrayList<>(optimizedWaypoints);
        this.currentWaypointIndex = 0;
        this.isActive = true;
        this.lastMovementTime = System.currentTimeMillis();
        this.nextHesitationTime = System.currentTimeMillis() + random.nextInt(10000) + 5000;

        Logger.sendMessage("§aPathfinding inteligente iniciado:");
        Logger.sendMessage("§7Waypoints originais: " + wps.size());
        Logger.sendMessage("§7Waypoints otimizados: " + waypoints.size());

        // Inicializar movimento natural da câmera
        if (mc.thePlayer != null) {
            cameraMovement.setInitialRotation(mc.thePlayer.rotationYaw, mc.thePlayer.rotationPitch);
        }

        setCurrentTarget(); // também reconstrói pathSteps
    }

    public void stopPathfinding() {
        this.isActive = false;
        this.waypoints.clear();
        this.currentWaypointIndex = 0;
        this.currentTarget = null;
        this.isHesitating = false;
        this.pathSteps.clear();
        stopMovement();
        Logger.sendMessage("§cPathfinding parado");
    }

    private void setCurrentTarget() {
        if (currentWaypointIndex >= waypoints.size()) {
            Logger.sendMessage("§aTodos os waypoints alcançados!");
            stopPathfinding();
            return;
        }

        currentTarget = waypoints.get(currentWaypointIndex);

        // Verificar se precisa antecipar mudança de direção para suavizar curvas
        if (shouldAnticipateDirectionChange()) {
            currentTarget = getAnticipatoryTarget();
            Logger.sendMessage("§7Antecipando curva para suavizar movimento");
        }

        waypointStartTime = System.currentTimeMillis(); // Iniciar timeout
        Logger.sendMessage("§6Movendo para waypoint " + (currentWaypointIndex + 1) + "/" + waypoints.size());
        calculateCameraRotation();
        rebuildPathSteps();
    }

    /**
     * Verifica se deve antecipar mudança de direção
     */
    private boolean shouldAnticipateDirectionChange() {
        if (currentWaypointIndex >= waypoints.size() - 1) return false;

        Vec3 playerPos = mc.thePlayer.getPositionVector();
        Vec3 currentWaypoint = waypoints.get(currentWaypointIndex);

        // Se está próximo do waypoint atual (8 blocos), verificar se há curva brusca
        double distanceToWaypoint = playerPos.distanceTo(currentWaypoint);
        return distanceToWaypoint < 8.0 && hasSharpDirectionChange();
    }

    /**
     * Calcula alvo antecipado para suavizar curvas
     */
    private Vec3 getAnticipatoryTarget() {
        if (currentWaypointIndex >= waypoints.size() - 1) {
            return waypoints.get(currentWaypointIndex);
        }

        Vec3 current = waypoints.get(currentWaypointIndex);
        Vec3 next = waypoints.get(currentWaypointIndex + 1);

        // Interpolar entre waypoint atual e próximo para suavizar
        double interpolation = 0.6; // 60% em direção ao próximo

        return new Vec3(
            current.xCoord + (next.xCoord - current.xCoord) * interpolation,
            current.yCoord + (next.yCoord - current.yCoord) * interpolation,
            current.zCoord + (next.zCoord - current.zCoord) * interpolation
        );
    }

    /* ====================== TICK / LÓGICA ====================== */
    @SubscribeEvent
    public void onClientTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.END) return;
        if (!isActive || mc.thePlayer == null || currentTarget == null) return;

        // STUCK DETECTION - Prioridade máxima
        if (checkStuckCondition()) {
            handleStuckRecovery();
            return;
        }

        // Verificar timeout do waypoint
        if (System.currentTimeMillis() - waypointStartTime > WAYPOINT_TIMEOUT) {
            Logger.sendMessage("§eTimeout no waypoint " + (currentWaypointIndex + 1) + ", pulando...");
            currentWaypointIndex++;
            setCurrentTarget();
            return;
        }

        if (hasReachedCurrentWaypoint()) {
            currentWaypointIndex++;
            setCurrentTarget();
            return;
        }

        executeMovement();
    }

    /* ====================== RENDER ====================== */
    @SubscribeEvent
    public void onRenderWorld(RenderWorldLastEvent event) {
        if (!RatoAddonsConfigSimple.pathfindingVisualization || mc.thePlayer == null) return;
        renderWaypoints(event.partialTicks);
    }

    private void renderWaypoints(float partialTicks) {
        EntityPlayer player = mc.thePlayer;
        double playerX = player.lastTickPosX + (player.posX - player.lastTickPosX) * partialTicks;
        double playerY = player.lastTickPosY + (player.posY - player.lastTickPosY) * partialTicks;
        double playerZ = player.lastTickPosZ + (player.posZ - player.lastTickPosZ) * partialTicks;

        GlStateManager.pushMatrix();
        GlStateManager.translate(-playerX, -playerY, -playerZ);
        GlStateManager.disableTexture2D();
        GlStateManager.enableBlend();
        GlStateManager.tryBlendFuncSeparate(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA, GL11.GL_ONE, GL11.GL_ZERO);
        GlStateManager.disableDepth(); // Desabilitar depth test para renderizar através de blocos (X-ray)
        GlStateManager.disableLighting();
        GlStateManager.disableCull(); // Desabilitar face culling para ver todas as faces

        if (isActive && currentTarget != null) {
            // visualização do próximo waypoint (quando ativo) - vermelho suave
            renderFullBlockOverlay(currentTarget, 200, 50, 50, 80);
            // renderizar linha até o próximo waypoint
            renderLineToCurrentTarget();
        } else if (!definedWaypoints.isEmpty()) {
            // visualização de waypoints definidos (quando inativo) - todos vermelhos suaves
            for (Vec3 wp : definedWaypoints) {
                renderFullBlockOverlay(wp, 180, 40, 40, 60); // Vermelho mais suave
            }
            renderWaypointConnections();
        }

        GlStateManager.enableDepth(); // Reabilitar depth test
        GlStateManager.enableTexture2D();
        GlStateManager.disableBlend();
        GlStateManager.enableLighting();
        GlStateManager.enableCull(); // Reabilitar face culling
        GlStateManager.popMatrix();
    }

    /* Overlay de cubo translúcido completo (todas as faces do bloco) */
    private void renderFullBlockOverlay(Vec3 pos, int red, int green, int blue, int alpha) {
        int blockX = (int) Math.floor(pos.xCoord);
        int blockY = (int) Math.floor(pos.yCoord);
        int blockZ = (int) Math.floor(pos.zCoord);

        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer buffer = tessellator.getWorldRenderer();

        double offset = 0.002; // evita z-fighting
        int outlineAlpha = Math.min(255, alpha + 80);

        // Renderizar todas as 6 faces do bloco com fill translúcido
        buffer.begin(GL11.GL_QUADS, DefaultVertexFormats.POSITION_COLOR);

        // Face superior (Y+) - ordem correta para face visível
        buffer.pos(blockX, blockY + 1 + offset, blockZ).color(red, green, blue, alpha).endVertex();
        buffer.pos(blockX, blockY + 1 + offset, blockZ + 1).color(red, green, blue, alpha).endVertex();
        buffer.pos(blockX + 1, blockY + 1 + offset, blockZ + 1).color(red, green, blue, alpha).endVertex();
        buffer.pos(blockX + 1, blockY + 1 + offset, blockZ).color(red, green, blue, alpha).endVertex();

        // Face inferior (Y-) - ordem correta para face visível
        buffer.pos(blockX, blockY - offset, blockZ).color(red, green, blue, alpha).endVertex();
        buffer.pos(blockX + 1, blockY - offset, blockZ).color(red, green, blue, alpha).endVertex();
        buffer.pos(blockX + 1, blockY - offset, blockZ + 1).color(red, green, blue, alpha).endVertex();
        buffer.pos(blockX, blockY - offset, blockZ + 1).color(red, green, blue, alpha).endVertex();

        // Face norte (Z-) - ordem correta para face visível
        buffer.pos(blockX, blockY, blockZ - offset).color(red, green, blue, alpha).endVertex();
        buffer.pos(blockX + 1, blockY, blockZ - offset).color(red, green, blue, alpha).endVertex();
        buffer.pos(blockX + 1, blockY + 1, blockZ - offset).color(red, green, blue, alpha).endVertex();
        buffer.pos(blockX, blockY + 1, blockZ - offset).color(red, green, blue, alpha).endVertex();

        // Face sul (Z+) - ordem correta para face visível
        buffer.pos(blockX, blockY, blockZ + 1 + offset).color(red, green, blue, alpha).endVertex();
        buffer.pos(blockX, blockY + 1, blockZ + 1 + offset).color(red, green, blue, alpha).endVertex();
        buffer.pos(blockX + 1, blockY + 1, blockZ + 1 + offset).color(red, green, blue, alpha).endVertex();
        buffer.pos(blockX + 1, blockY, blockZ + 1 + offset).color(red, green, blue, alpha).endVertex();

        // Face oeste (X-) - ordem correta para face visível
        buffer.pos(blockX - offset, blockY, blockZ).color(red, green, blue, alpha).endVertex();
        buffer.pos(blockX - offset, blockY + 1, blockZ).color(red, green, blue, alpha).endVertex();
        buffer.pos(blockX - offset, blockY + 1, blockZ + 1).color(red, green, blue, alpha).endVertex();
        buffer.pos(blockX - offset, blockY, blockZ + 1).color(red, green, blue, alpha).endVertex();

        // Face leste (X+) - ordem correta para face visível
        buffer.pos(blockX + 1 + offset, blockY, blockZ).color(red, green, blue, alpha).endVertex();
        buffer.pos(blockX + 1 + offset, blockY, blockZ + 1).color(red, green, blue, alpha).endVertex();
        buffer.pos(blockX + 1 + offset, blockY + 1, blockZ + 1).color(red, green, blue, alpha).endVertex();
        buffer.pos(blockX + 1 + offset, blockY + 1, blockZ).color(red, green, blue, alpha).endVertex();

        tessellator.draw();

        // Renderizar outline das bordas do bloco
        buffer.begin(GL11.GL_LINES, DefaultVertexFormats.POSITION_COLOR);
        double outlineOffset = 0.004;

        // Bordas verticais
        buffer.pos(blockX, blockY, blockZ).color(red, green, blue, outlineAlpha).endVertex();
        buffer.pos(blockX, blockY + 1, blockZ).color(red, green, blue, outlineAlpha).endVertex();

        buffer.pos(blockX + 1, blockY, blockZ).color(red, green, blue, outlineAlpha).endVertex();
        buffer.pos(blockX + 1, blockY + 1, blockZ).color(red, green, blue, outlineAlpha).endVertex();

        buffer.pos(blockX, blockY, blockZ + 1).color(red, green, blue, outlineAlpha).endVertex();
        buffer.pos(blockX, blockY + 1, blockZ + 1).color(red, green, blue, outlineAlpha).endVertex();

        buffer.pos(blockX + 1, blockY, blockZ + 1).color(red, green, blue, outlineAlpha).endVertex();
        buffer.pos(blockX + 1, blockY + 1, blockZ + 1).color(red, green, blue, outlineAlpha).endVertex();

        // Bordas horizontais (topo)
        buffer.pos(blockX, blockY + 1, blockZ).color(red, green, blue, outlineAlpha).endVertex();
        buffer.pos(blockX + 1, blockY + 1, blockZ).color(red, green, blue, outlineAlpha).endVertex();

        buffer.pos(blockX + 1, blockY + 1, blockZ).color(red, green, blue, outlineAlpha).endVertex();
        buffer.pos(blockX + 1, blockY + 1, blockZ + 1).color(red, green, blue, outlineAlpha).endVertex();

        buffer.pos(blockX + 1, blockY + 1, blockZ + 1).color(red, green, blue, outlineAlpha).endVertex();
        buffer.pos(blockX, blockY + 1, blockZ + 1).color(red, green, blue, outlineAlpha).endVertex();

        buffer.pos(blockX, blockY + 1, blockZ + 1).color(red, green, blue, outlineAlpha).endVertex();
        buffer.pos(blockX, blockY + 1, blockZ).color(red, green, blue, outlineAlpha).endVertex();

        // Bordas horizontais (base)
        buffer.pos(blockX, blockY, blockZ).color(red, green, blue, outlineAlpha).endVertex();
        buffer.pos(blockX + 1, blockY, blockZ).color(red, green, blue, outlineAlpha).endVertex();

        buffer.pos(blockX + 1, blockY, blockZ).color(red, green, blue, outlineAlpha).endVertex();
        buffer.pos(blockX + 1, blockY, blockZ + 1).color(red, green, blue, outlineAlpha).endVertex();

        buffer.pos(blockX + 1, blockY, blockZ + 1).color(red, green, blue, outlineAlpha).endVertex();
        buffer.pos(blockX, blockY, blockZ + 1).color(red, green, blue, outlineAlpha).endVertex();

        buffer.pos(blockX, blockY, blockZ + 1).color(red, green, blue, outlineAlpha).endVertex();
        buffer.pos(blockX, blockY, blockZ).color(red, green, blue, outlineAlpha).endVertex();

        tessellator.draw();
    }

    /* Conexões vermelhas entre waypoints definidos (quando inativo) - visível através de blocos */
    private void renderWaypointConnections() {
        if (definedWaypoints.size() < 2) return;

        // Configurar linha mais grossa e visível
        GL11.glLineWidth(3.0f);

        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer buffer = tessellator.getWorldRenderer();

        buffer.begin(GL11.GL_LINES, DefaultVertexFormats.POSITION_COLOR);
        for (int i = 0; i < definedWaypoints.size() - 1; i++) {
            Vec3 a = definedWaypoints.get(i);
            Vec3 b = definedWaypoints.get(i + 1);

            // Centro dos blocos
            double x1 = Math.floor(a.xCoord) + 0.5;
            double y1 = Math.floor(a.yCoord) + 0.5; // Centro do bloco
            double z1 = Math.floor(a.zCoord) + 0.5;
            double x2 = Math.floor(b.xCoord) + 0.5;
            double y2 = Math.floor(b.yCoord) + 0.5;
            double z2 = Math.floor(b.zCoord) + 0.5;

            // Linha vermelha suave (totalmente opaca para X-ray)
            buffer.pos(x1, y1, z1).color(200, 80, 80, 255).endVertex();
            buffer.pos(x2, y2, z2).color(200, 80, 80, 255).endVertex();
        }
        tessellator.draw();

        // Resetar largura da linha
        GL11.glLineWidth(1.0f);
    }

    /* Linha vermelha da posição do player até o próximo waypoint (quando ativo) */
    private void renderLineToCurrentTarget() {
        if (currentTarget == null || mc.thePlayer == null) return;

        // Configurar linha mais grossa e visível
        GL11.glLineWidth(3.0f);

        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer buffer = tessellator.getWorldRenderer();

        buffer.begin(GL11.GL_LINES, DefaultVertexFormats.POSITION_COLOR);

        // Posição do player
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        double playerX = playerPos.xCoord;
        double playerY = playerPos.yCoord;
        double playerZ = playerPos.zCoord;

        // Centro do bloco do próximo waypoint
        double targetX = Math.floor(currentTarget.xCoord) + 0.5;
        double targetY = Math.floor(currentTarget.yCoord) + 0.5;
        double targetZ = Math.floor(currentTarget.zCoord) + 0.5;

        // Linha vermelha do player até o próximo waypoint
        buffer.pos(playerX, playerY, playerZ).color(200, 80, 80, 255).endVertex();
        buffer.pos(targetX, targetY, targetZ).color(200, 80, 80, 255).endVertex();

        tessellator.draw();

        // Resetar largura da linha
        GL11.glLineWidth(1.0f);
    }

    /* ====================== LÓGICA DE HESITAÇÃO / CÂMERA ====================== */
    private void checkHesitation() {
        long now = System.currentTimeMillis();
        if (now > nextHesitationTime && !isHesitating) {
            if (random.nextFloat() < 0.3f) {
                isHesitating = true;
                stopMovement();
                long duration = 500 + random.nextInt(1500);
                new Thread(() -> {
                    try { Thread.sleep(duration); } catch (InterruptedException ignored) {}
                    isHesitating = false;
                }).start();
                Logger.sendMessage("§7Hesitando por " + duration + "ms...");
            }
            nextHesitationTime = now + 5000 + random.nextInt(10000);
        }
    }

    private void updateCameraMovement() {
        // COMPLETAMENTE DESABILITADO - causava movimento circular
        return;
    }

    /**
     * Aplica movimento natural aleatório muito sutil
     */
    private void applyNaturalRandomMovement() {
        if (mc.thePlayer == null) return;

        // Movimento horizontal muito sutil e aleatório
        float randomYaw = (random.nextFloat() - 0.5f) * 1.0f; // ±0.5 grau - ainda mais sutil
        mc.thePlayer.rotationYaw += randomYaw;

        // Manter pitch próximo de 0 (altura normal)
        float currentPitch = mc.thePlayer.rotationPitch;
        if (Math.abs(currentPitch) > 3.0f) {
            float pitchCorrection = -currentPitch * 0.05f; // Correção mais suave
            mc.thePlayer.rotationPitch += pitchCorrection;
        }
    }

    /**
     * Sistema de olhar ao redor DESABILITADO - causava movimento circular
     */
    private void updateNaturalLooking() {
        // DESABILITADO - qualquer offset de yaw causa movimento circular
        naturalYawOffset = 0;
        lookAroundDuration = 0;
    }

    /**
     * Mantém altura de visão normal de forma muito suave
     */
    private void maintainNormalViewHeight() {
        if (mc.thePlayer == null) return;

        // Apenas ajustar pitch se estiver muito extremo
        float currentPitch = mc.thePlayer.rotationPitch;

        if (Math.abs(currentPitch) > 20.0f) {
            // Correção extremamente suave
            float pitchStep = -currentPitch * 0.001f; // Extremamente gradual
            mc.thePlayer.rotationPitch += pitchStep;
        }

        // Nunca tocar no yaw - deixar pathfinding controlar completamente
    }

    /**
     * Detecta se há uma mudança brusca de direção
     */
    private boolean hasSharpDirectionChange() {
        if (currentWaypointIndex >= waypoints.size() - 1) return false;

        Vec3 current = waypoints.get(currentWaypointIndex);
        Vec3 next = waypoints.get(currentWaypointIndex + 1);
        Vec3 playerPos = mc.thePlayer.getPositionVector();

        // Calcular ângulo entre direção atual e próxima
        Vec3 currentDir = current.subtract(playerPos).normalize();
        Vec3 nextDir = next.subtract(current).normalize();

        double dotProduct = currentDir.dotProduct(nextDir);
        double angle = Math.toDegrees(Math.acos(Math.max(-1.0, Math.min(1.0, dotProduct))));

        // Se o ângulo for maior que 45 graus, é uma mudança brusca
        return angle > 45.0;
    }

    /**
     * Movimento de câmera suave para curvas bruscas
     */
    private void updateCameraForSharpTurn() {
        // Para curvas bruscas, olhar mais para frente na direção geral
        Vec3 playerPos = mc.thePlayer.getPositionVector();

        // Calcular direção média entre waypoints próximos
        Vec3 averageDirection = calculateAverageDirection();

        // Usar direção média em vez do waypoint específico
        Vec3 scaledDirection = new Vec3(
            averageDirection.xCoord * 20.0,
            averageDirection.yCoord * 20.0,
            averageDirection.zCoord * 20.0
        );
        Vec3 smoothTarget = playerPos.add(scaledDirection); // 20 blocos à frente

        cameraMovement.updateCameraMovement(smoothTarget);
    }

    /**
     * Calcula direção média para suavizar curvas
     */
    private Vec3 calculateAverageDirection() {
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        Vec3 totalDirection = new Vec3(0, 0, 0);
        int count = 0;

        // Considerar próximos 3 waypoints para direção média
        for (int i = currentWaypointIndex; i < Math.min(currentWaypointIndex + 3, waypoints.size()); i++) {
            Vec3 waypoint = waypoints.get(i);
            Vec3 direction = waypoint.subtract(playerPos).normalize();
            totalDirection = totalDirection.add(direction);
            count++;
        }

        if (count > 0) {
            double scale = 1.0 / count;
            Vec3 averageDir = new Vec3(
                totalDirection.xCoord * scale,
                totalDirection.yCoord * scale,
                totalDirection.zCoord * scale
            );
            return averageDir.normalize();
        }

        return currentTarget.subtract(playerPos).normalize();
    }

    private void calculateCameraRotation() {
        if (currentTarget == null || mc.thePlayer == null) return;
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        Vec3 dir = currentTarget.subtract(playerPos);
        targetYaw = (float) Math.toDegrees(Math.atan2(-dir.xCoord, dir.zCoord));
        double hDist = Math.sqrt(dir.xCoord * dir.xCoord + dir.zCoord * dir.zCoord);
        targetPitch = (float) -Math.toDegrees(Math.atan2(dir.yCoord, hDist));
        targetPitch = clamp(targetPitch, -90f, 90f);
    }

    /* ====================== MOVIMENTO DIRETO E EFICIENTE ====================== */
    private void executeMovement() {
        if (currentTarget == null || mc.thePlayer == null) return;

        Vec3 playerPos = mc.thePlayer.getPositionVector();
        Vec3 direction = currentTarget.subtract(playerPos);

        // Verificar se chegou ao waypoint
        double distance = direction.lengthVector();
        if (distance < 1.5) { // Distância ainda menor para chegar mais perto
            // Não parar movimento aqui - deixar setCurrentTarget() lidar com isso
            setCurrentTarget(); // Ir para próximo waypoint
            if (currentTarget == null) {
                stopMovement(); // Só parar se não há mais waypoints
                return;
            }
            // Recalcular direção para novo target
            direction = currentTarget.subtract(playerPos);
            distance = direction.lengthVector();
            direction = direction.normalize();
        }

        // Normalizar direção
        direction = direction.normalize();

        // Rotação DIRETA sem interferências
        float targetYaw = (float) Math.toDegrees(Math.atan2(-direction.xCoord, direction.zCoord));
        float currentYaw = mc.thePlayer.rotationYaw;
        float yawDiff = normalizeAngle(targetYaw - currentYaw);

        // Rotação mais agressiva para não ficar girando
        if (Math.abs(yawDiff) > 2.0f) {
            float rotationStep = yawDiff * 0.6f; // Mais agressivo
            mc.thePlayer.rotationYaw += rotationStep;
        }

        // Movimento SIMPLES - sempre para frente, usar strafe apenas se necessário
        boolean needsStrafe = Math.abs(yawDiff) > 45.0f;

        if (needsStrafe) {
            // Se precisa virar muito, usar strafe + forward
            boolean strafeLeft = yawDiff > 0;
            boolean strafeRight = yawDiff < 0;

            setKeyState(mc.gameSettings.keyBindForward, true);  // SEMPRE para frente
            setKeyState(mc.gameSettings.keyBindLeft, strafeLeft);
            setKeyState(mc.gameSettings.keyBindRight, strafeRight);
            setKeyState(mc.gameSettings.keyBindBack, false);
        } else {
            // Alinhado - apenas para frente
            setKeyState(mc.gameSettings.keyBindForward, true);  // SEMPRE para frente
            setKeyState(mc.gameSettings.keyBindLeft, false);
            setKeyState(mc.gameSettings.keyBindRight, false);
            setKeyState(mc.gameSettings.keyBindBack, false);
        }

        // SEMPRE sprint ativo
        setKeyState(mc.gameSettings.keyBindSprint, true);

        // Pular se necessário
        setKeyState(mc.gameSettings.keyBindJump, shouldJump(direction));
    }

    private boolean hasReachedCurrentWaypoint() {
        if (currentTarget == null || mc.thePlayer == null) return false;

        Vec3 playerPos = mc.thePlayer.getPositionVector();

        // Usar distância horizontal para evitar problemas com altura
        double deltaX = playerPos.xCoord - currentTarget.xCoord;
        double deltaZ = playerPos.zCoord - currentTarget.zCoord;
        double horizontalDistance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);

        return horizontalDistance < WAYPOINT_REACH_DISTANCE;
    }

    private boolean shouldJump(Vec3 dir) {
        if (mc.thePlayer == null || mc.theWorld == null) return false;

        Vec3 playerPos = mc.thePlayer.getPositionVector();

        // Verificar obstáculo à frente
        Vec3 frontPos = playerPos.addVector(dir.xCoord * 1.5, 0, dir.zCoord * 1.5);
        BlockPos frontBlock = new BlockPos(frontPos.xCoord, frontPos.yCoord, frontPos.zCoord);
        BlockPos frontBlockAbove = frontBlock.up();

        // Pular se há obstáculo sólido à frente
        if (!mc.theWorld.isAirBlock(frontBlock) && mc.theWorld.getBlockState(frontBlock).getBlock().isFullBlock()) {
            return true;
        }

        // Pular se precisa subir
        if (dir.yCoord > 0.3) {
            return true;
        }

        // Verificar se há água à frente (evitar entrar na água)
        Block frontBlockType = mc.theWorld.getBlockState(frontBlock).getBlock();
        if (frontBlockType == Blocks.water || frontBlockType == Blocks.flowing_water) {
            return true; // Tentar pular sobre a água
        }

        return false;
    }

    private void setKeyState(KeyBinding key, boolean pressed) {
        if (key != null) KeyBinding.setKeyBindState(key.getKeyCode(), pressed);
    }

    private void stopMovement() {
        if (mc.gameSettings == null) return;
        setKeyState(mc.gameSettings.keyBindForward, false);
        setKeyState(mc.gameSettings.keyBindBack, false);
        setKeyState(mc.gameSettings.keyBindLeft, false);
        setKeyState(mc.gameSettings.keyBindRight, false);
        setKeyState(mc.gameSettings.keyBindJump, false);
        setKeyState(mc.gameSettings.keyBindSprint, false);
    }

    /* ====================== PATH‑STEP BUILDER ====================== */
    private void rebuildPathSteps() {
        pathSteps.clear();
        if (mc.thePlayer == null) return;
        Vec3 prev = mc.thePlayer.getPositionVector();
        for (int i = currentWaypointIndex; i < waypoints.size(); i++) {
            Vec3 next = waypoints.get(i);
            double dist = prev.distanceTo(next);
            int steps = Math.max(1, (int) (dist / PATH_STEP));
            for (int s = 0; s <= steps; s++) {
                double t = s / (double) steps;
                double x = lerp(prev.xCoord, next.xCoord, t);
                double y = lerp(prev.yCoord, next.yCoord, t);
                double z = lerp(prev.zCoord, next.zCoord, t);
                int bx = (int) Math.floor(x);
                int by = (int) Math.floor(y - 0.01);
                int bz = (int) Math.floor(z);
                pathSteps.add(new Vec3(bx + 0.5, by + 1, bz + 0.5)); // centro do bloco
            }
            prev = next;
        }
    }

    private static double lerp(double a, double b, double t) { return a + (b - a) * t; }

    /* ====================== PATHFINDING INTELIGENTE ====================== */

    /**
     * Gera caminho inteligente com waypoints intermediários
     */
    private List<Vec3> generateIntelligentPath(List<Vec3> originalWaypoints) {
        List<Vec3> intelligentPath = new ArrayList<>();

        if (originalWaypoints.isEmpty()) return intelligentPath;

        // Adicionar primeiro waypoint
        intelligentPath.add(originalWaypoints.get(0));

        // Analisar cada segmento entre waypoints
        for (int i = 0; i < originalWaypoints.size() - 1; i++) {
            Vec3 start = originalWaypoints.get(i);
            Vec3 end = originalWaypoints.get(i + 1);

            // Verificar se precisa de waypoints intermediários
            if (terrainAnalyzer.needsIntermediateWaypoints(start, end)) {
                Logger.sendMessage("§7Analisando segmento " + (i + 1) + ": distância " +
                    String.format("%.1f", terrainAnalyzer.getDistance3D(start, end)) + " blocos");

                // Gerar waypoints intermediários
                List<Vec3> intermediateWaypoints = terrainAnalyzer.analyzeAndGenerateWaypoints(start, end);

                // Adicionar waypoints intermediários (exceto start e end)
                for (int j = 1; j < intermediateWaypoints.size() - 1; j++) {
                    intelligentPath.add(intermediateWaypoints.get(j));
                    Logger.sendMessage("§7+ Waypoint intermediário: " +
                        String.format("%.1f, %.1f, %.1f",
                            intermediateWaypoints.get(j).xCoord,
                            intermediateWaypoints.get(j).yCoord,
                            intermediateWaypoints.get(j).zCoord));
                }
            }

            // Adicionar próximo waypoint original
            intelligentPath.add(end);
        }

        return intelligentPath;
    }

    /* ====================== UTIL ====================== */
    private float normalizeAngle(float ang) {
        while (ang > 180) ang -= 360;
        while (ang < -180) ang += 360;
        return ang;
    }

    private float clamp(float v, float min, float max) { return Math.max(min, Math.min(max, v)); }


    /* ====================== APIs PÚBLICAS (COMANDOS) ====================== */
    public void addWaypointAtCurrentPosition() {
        if (mc.thePlayer == null) return;
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        int bx = (int) Math.floor(playerPos.xCoord);
        int by = (int) Math.floor(playerPos.yCoord - 1); // bloco abaixo dos pés
        int bz = (int) Math.floor(playerPos.zCoord);
        Vec3 wp = new Vec3(bx + 0.5, by, bz + 0.5);
        definedWaypoints.add(wp);
        Logger.sendMessage("§aWaypoint " + definedWaypoints.size() + " adicionado: " +
                String.format("%.1f, %.1f, %.1f", wp.xCoord, wp.yCoord, wp.zCoord));
    }

    public void startPathfindingWithDefinedWaypoints() {
        if (definedWaypoints.isEmpty()) {
            Logger.sendMessage("§cNenhum waypoint definido!");
            return;
        }
        startPathfinding(new ArrayList<>(definedWaypoints));
    }

    public void clearWaypoints() {
        definedWaypoints.clear();
        Logger.sendMessage("§cTodos os waypoints foram removidos");
    }

    /**
     * Obtém lista de waypoints definidos
     */
    public List<Vec3> getWaypoints() {
        return new ArrayList<>(definedWaypoints);
    }

    public void removeLastWaypoint() {
        if (definedWaypoints.isEmpty()) {
            Logger.sendMessage("§cNenhum waypoint para remover");
            return;
        }
        Vec3 removed = definedWaypoints.remove(definedWaypoints.size() - 1);
        Logger.sendMessage("§cWaypoint removido: " + String.format("%.1f, %.1f, %.1f", removed.xCoord, removed.yCoord, removed.zCoord));
    }

    public void listWaypoints() {
        if (definedWaypoints.isEmpty()) {
            Logger.sendMessage("§7Nenhum waypoint definido");
            return;
        }
        Logger.sendMessage("§6Waypoints definidos:");
        for (int i = 0; i < definedWaypoints.size(); i++) {
            Vec3 wp = definedWaypoints.get(i);
            Logger.sendMessage("§7" + (i + 1) + ". " + String.format("%.1f, %.1f, %.1f", wp.xCoord, wp.yCoord, wp.zCoord));
        }
    }

    /* ====================== STUCK DETECTION E RECOVERY ====================== */

    /**
     * Verifica se o player está preso
     */
    private boolean checkStuckCondition() {
        if (mc.thePlayer == null) return false;

        Vec3 currentPosition = mc.thePlayer.getPositionVector();
        long currentTime = System.currentTimeMillis();

        // Inicializar na primeira execução
        if (lastPosition == null) {
            lastPosition = currentPosition;
            lastMoveTime = currentTime;
            return false;
        }

        // Verificar se houve movimento significativo
        double distanceMoved = lastPosition.distanceTo(currentPosition);

        if (distanceMoved < 0.1) { // Movimento insignificante
            if (currentTime - lastMoveTime > STUCK_TIMEOUT_MS) {
                return true; // PRESO!
            }
        } else {
            // Houve movimento - resetar timer
            lastPosition = currentPosition;
            lastMoveTime = currentTime;
        }

        return false;
    }

    /**
     * Lida com recuperação quando preso
     */
    private void handleStuckRecovery() {
        stuckAttempts++;

        Logger.sendMessage("§c[EMERGENCY STOP] Player preso! Tentativa " + stuckAttempts + "/" + MAX_STUCK_ATTEMPTS);

        if (stuckAttempts >= MAX_STUCK_ATTEMPTS) {
            Logger.sendMessage("§cMuitas tentativas de recuperação - parando pathfinding");
            stopPathfinding();
            return;
        }

        // Penalizar área atual
        penalizeCurrentArea();

        // Tentar replanejamento
        triggerReplanning();
    }

    /**
     * Penaliza a área onde o player ficou preso
     */
    private void penalizeCurrentArea() {
        if (mc.thePlayer == null || currentTarget == null) return;

        Vec3 playerPos = mc.thePlayer.getPositionVector();
        BlockPos stuckPos = new BlockPos(playerPos);
        long currentTime = System.currentTimeMillis();

        // Penalizar área em um raio de 3 blocos
        for (int x = -3; x <= 3; x++) {
            for (int z = -3; z <= 3; z++) {
                for (int y = -1; y <= 1; y++) {
                    BlockPos penaltyPos = stuckPos.add(x, y, z);
                    penalizedNodes.put(penaltyPos, currentTime + PENALTY_DURATION);
                }
            }
        }

        Logger.sendMessage("§7Área penalizada: raio 3 blocos por 30 segundos");
    }

    /**
     * Força replanejamento da rota
     */
    private void triggerReplanning() {
        if (mc.thePlayer == null) return;

        Logger.sendMessage("§eReplanejando rota...");

        // Limpar penalidades expiradas
        cleanupExpiredPenalties();

        // Pular waypoint atual se estiver muito próximo
        if (currentTarget != null) {
            Vec3 playerPos = mc.thePlayer.getPositionVector();
            double distanceToTarget = playerPos.distanceTo(currentTarget);

            if (distanceToTarget < 5.0) {
                Logger.sendMessage("§7Pulando waypoint problemático");
                currentWaypointIndex++;
                setCurrentTarget();
                return;
            }
        }

        // Gerar nova rota evitando áreas penalizadas
        List<Vec3> originalWaypoints = new ArrayList<>(waypoints);
        Vec3 playerPos = mc.thePlayer.getPositionVector();

        // Criar nova lista começando da posição atual
        List<Vec3> newWaypoints = new ArrayList<>();
        newWaypoints.add(playerPos);

        // Adicionar waypoints restantes
        for (int i = currentWaypointIndex; i < originalWaypoints.size(); i++) {
            Vec3 waypoint = originalWaypoints.get(i);

            // Verificar se o waypoint não está penalizado
            if (!isPositionPenalized(waypoint)) {
                newWaypoints.add(waypoint);
            } else {
                // Encontrar posição alternativa próxima
                Vec3 alternative = findAlternativePosition(waypoint);
                if (alternative != null) {
                    newWaypoints.add(alternative);
                    Logger.sendMessage("§7Waypoint alternativo: " +
                        String.format("%.1f, %.1f, %.1f", alternative.xCoord, alternative.yCoord, alternative.zCoord));
                }
            }
        }

        // Reiniciar pathfinding com nova rota
        if (newWaypoints.size() > 1) {
            this.waypoints = newWaypoints;
            this.currentWaypointIndex = 0;
            this.stuckAttempts = 0; // Reset tentativas
            setCurrentTarget();
            Logger.sendMessage("§aRota replanejada com " + newWaypoints.size() + " waypoints");
        } else {
            Logger.sendMessage("§cNão foi possível replanejar - parando pathfinding");
            stopPathfinding();
        }
    }

    /**
     * Limpa penalidades expiradas
     */
    private void cleanupExpiredPenalties() {
        long currentTime = System.currentTimeMillis();
        penalizedNodes.entrySet().removeIf(entry -> entry.getValue() < currentTime);
    }

    /**
     * Verifica se uma posição está penalizada
     */
    private boolean isPositionPenalized(Vec3 position) {
        BlockPos blockPos = new BlockPos(position);
        Long penaltyTime = penalizedNodes.get(blockPos);

        if (penaltyTime == null) return false;

        return System.currentTimeMillis() < penaltyTime;
    }

    /**
     * Encontra posição alternativa próxima que não esteja penalizada
     */
    private Vec3 findAlternativePosition(Vec3 original) {
        for (int radius = 5; radius <= 15; radius += 5) {
            for (int x = -radius; x <= radius; x += 5) {
                for (int z = -radius; z <= radius; z += 5) {
                    Vec3 alternative = new Vec3(
                        original.xCoord + x,
                        original.yCoord,
                        original.zCoord + z
                    );

                    if (!isPositionPenalized(alternative)) {
                        return alternative;
                    }
                }
            }
        }

        return null;
    }

    /* ====================== GETTERS PARA DEBUG ====================== */
    public boolean isActive() { return isActive; }
    public int getCurrentWaypointIndex() { return currentWaypointIndex; }
    public Vec3 getCurrentTarget() { return currentTarget; }
    public boolean isHesitating() { return isHesitating; }
    public boolean isStuck() { return stuckAttempts > 0; }
    public int getStuckAttempts() { return stuckAttempts; }
}
