package com.rato.addons.imgui;

import com.rato.addons.gui.modern.AnimationSystem;
import com.rato.addons.gui.modern.ModernRenderer;
import com.rato.addons.gui.modern.TTFRenderer;
import com.rato.addons.util.Logger;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.ScaledResolution;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.Tessellator;
import net.minecraft.client.renderer.WorldRenderer;
import net.minecraft.client.renderer.vertex.DefaultVertexFormats;
import net.minecraftforge.client.event.RenderGameOverlayEvent;
import net.minecraftforge.client.event.MouseEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import org.lwjgl.input.Mouse;
import org.lwjgl.opengl.GL11;

import java.awt.*;
import java.util.HashMap;
import java.util.Map;

/**
 * Rato ImGui Professional Click GUI - Sistema Completo
 * Interface moderna com sidebar, animações e fontes personalizadas
 */
public class RatoImGuiRenderer {
    private static RatoImGuiRenderer instance;
    private static boolean initialized = false;
    private static boolean visible = false;

    // Minecraft instance
    private final Minecraft mc = Minecraft.getMinecraft();

    // Modern Font System
    private TTFRenderer customFont;
    private TTFRenderer titleFont;
    private TTFRenderer headerFont;

    // Animation System
    private float animationProgress = 0.0f;
    private long lastAnimationTime = 0;
    private static final float ANIMATION_SPEED = 0.08f;

    // Sidebar System
    private String selectedCategory = "Combat";
    private final String[] categories = { "Combat", "Movement", "Visual", "Player", "World", "Misc" };
    private final Map<String, Integer> categoryColors = new HashMap<>();

    // Layout Constants - PUBLIC para acesso do mouse handler
    public static final int SIDEBAR_WIDTH = 120;
    public static final int MAIN_PANEL_WIDTH = 400;
    public static final int WINDOW_HEIGHT = 350;
    private static final int CORNER_RADIUS = 12;

    // Feature states
    private boolean aimbotEnabled = false;
    private boolean killAuraEnabled = false;

    private boolean criticalsEnabled = false;
    private boolean speedEnabled = false;
    private boolean flyEnabled = false;
    private boolean bunnyHopEnabled = false;
    private boolean espEnabled = false;
    private boolean nametagsEnabled = false;
    private boolean tracersEnabled = false;
    private boolean riftAutofarmEnabled = false;
    private boolean autoClickEnabled = false;
    private boolean noFallEnabled = false;

    public static RatoImGuiRenderer getInstance() {
        if (instance == null) {
            instance = new RatoImGuiRenderer();
        }
        return instance;
    }

    public static void initialize() {
        if (!initialized) {
            try {
                Logger.sendLog("🔧 Inicializando RatoImGuiRenderer Professional...");
                instance = getInstance();
                instance.initializeFonts();
                instance.initializeCategoryColors();

                // Sistema de overlay implementado diretamente
                Logger.sendLog("🖱️ Sistema de overlay ativo!");

                initialized = true;
                Logger.sendLog("✅ RatoImGuiRenderer Professional inicializado!");
            } catch (Exception e) {
                Logger.sendLog("❌ Erro ao inicializar RatoImGuiRenderer: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    /**
     * Initialize modern TTF fonts
     */
    private void initializeFonts() {
        try {
            // Initialize modern TTF fonts
            customFont = TTFRenderer.getInterFont(14);
            titleFont = TTFRenderer.getInterFont(18);
            headerFont = TTFRenderer.getInterFont(24);

            Logger.sendLog("✅ Modern TTF font system initialized!");
        } catch (Exception e) {
            Logger.sendLog("❌ Error initializing fonts: " + e.getMessage());
            // Fallback to basic fonts if TTF fails
            customFont = null;
            titleFont = null;
            headerFont = null;
        }
    }

    /**
     * Convert integer color to Color object
     */
    private Color intToColor(int color) {
        int alpha = (color >> 24) & 0xFF;
        int red = (color >> 16) & 0xFF;
        int green = (color >> 8) & 0xFF;
        int blue = color & 0xFF;
        return new Color(red, green, blue, alpha);
    }

    /**
     * Initialize category colors
     */
    private void initializeCategoryColors() {
        categoryColors.put("Combat", 0xFF4CAF50); // Verde
        categoryColors.put("Movement", 0xFF2196F3); // Azul
        categoryColors.put("Visual", 0xFFFF9800); // Laranja
        categoryColors.put("Player", 0xFF9C27B0); // Roxo
        categoryColors.put("World", 0xFF795548); // Marrom
        categoryColors.put("Misc", 0xFF607D8B); // Cinza azulado
        Logger.sendLog("✅ Sistema de cores das categorias inicializado!");
    }

    public static void toggle() {
        visible = !visible;
        Minecraft mc = Minecraft.getMinecraft();

        if (visible) {
            Logger.sendLog("🔓 Interface Professional ABERTA - Sistema ativo!");
            // Reset animation
            if (instance != null) {
                instance.animationProgress = 0.0f;
                instance.lastAnimationTime = 0;
            }

            // Release mouse cursor for menu interaction
            Mouse.setGrabbed(false);
            mc.inGameHasFocus = false;
            mc.mouseHelper.ungrabMouseCursor();

        } else {
            Logger.sendLog("🔒 Interface Professional FECHADA - Voltando ao jogo");

            // Immediately restore game control
            Mouse.setGrabbed(true);
            mc.inGameHasFocus = true;
            mc.mouseHelper.grabMouseCursor();

            // Ensure proper focus restoration
            if (mc.currentScreen == null) {
                mc.setIngameFocus();
            }
        }
    }

    public static boolean isVisible() {
        return visible;
    }

    /**
     * Métodos para acesso do mouse handler
     */
    public String getSelectedCategory() {
        return selectedCategory;
    }

    public void setSelectedCategory(String category) {
        this.selectedCategory = category;
        Logger.sendLog("📂 Categoria selecionada: " + category);
    }

    public void toggleAimbot() {
        aimbotEnabled = !aimbotEnabled;
        Logger.sendLog("⚔️ Aimbot: " + (aimbotEnabled ? "ATIVADO" : "DESATIVADO"));
    }

    public void toggleKillAura() {
        killAuraEnabled = !killAuraEnabled;
        Logger.sendLog("⚔️ KillAura: " + (killAuraEnabled ? "ATIVADO" : "DESATIVADO"));
    }

    @SubscribeEvent
    public void onRenderOverlay(RenderGameOverlayEvent.Post event) {
        if (event.type != RenderGameOverlayEvent.ElementType.ALL || !visible) {
            return;
        }

        try {
            updateAnimations();
            renderProfessionalInterface();

            // Cursor control is handled in toggle() method
        } catch (Exception e) {
            Logger.sendLog("❌ ERRO CRÍTICO ao renderizar interface: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @SubscribeEvent
    public void onMouseEvent(MouseEvent event) {
        // Só processar se o menu estiver visível
        if (!visible) {
            return;
        }

        // Só processar cliques do botão esquerdo
        if (event.button != 0 || !event.buttonstate) {
            return;
        }

        try {
            // Cancelar o evento para que não interfira com o jogo
            event.setCanceled(true);

            // Processar o clique no menu
            processOverlayClick();

        } catch (Exception e) {
            Logger.sendLog("❌ Erro no mouse overlay: " + e.getMessage());
        }
    }

    /**
     * Processar cliques do overlay
     */
    private void processOverlayClick() {
        try {
            ScaledResolution sr = new ScaledResolution(mc);
            int mouseX = Mouse.getX() * sr.getScaledWidth() / mc.displayWidth;
            int mouseY = sr.getScaledHeight() - Mouse.getY() * sr.getScaledHeight() / mc.displayHeight - 1;

            Logger.sendLog("🖱️ Overlay click at: " + mouseX + ", " + mouseY);

            // Calcular posição da janela
            int totalWidth = SIDEBAR_WIDTH + MAIN_PANEL_WIDTH;
            int windowX = (sr.getScaledWidth() - totalWidth) / 2;
            int windowY = (sr.getScaledHeight() - WINDOW_HEIGHT) / 2;

            // Verificar se o clique está dentro da janela
            if (mouseX >= windowX && mouseX <= windowX + totalWidth &&
                    mouseY >= windowY && mouseY <= windowY + WINDOW_HEIGHT) {

                Logger.sendLog("✅ Click inside overlay window");

                // Processar clique na sidebar
                if (mouseX >= windowX && mouseX <= windowX + SIDEBAR_WIDTH) {
                    handleOverlaySidebarClick(mouseX - windowX, mouseY - windowY);
                }

                // Processar clique no main panel
                if (mouseX >= windowX + SIDEBAR_WIDTH && mouseX <= windowX + totalWidth) {
                    handleOverlayMainPanelClick(mouseX - windowX - SIDEBAR_WIDTH, mouseY - windowY);
                }
            }

        } catch (Exception e) {
            Logger.sendLog("❌ Erro ao processar clique do overlay: " + e.getMessage());
        }
    }

    private void handleOverlaySidebarClick(int relativeX, int relativeY) {
        Logger.sendLog("🎯 Sidebar overlay click at: " + relativeX + ", " + relativeY);

        // Verificar cliques nas categorias
        int currentY = 50; // Posição inicial das categorias

        for (String category : categories) {
            if (relativeY >= currentY && relativeY <= currentY + 25) {
                Logger.sendLog("📂 Category selected: " + category);
                selectedCategory = category;
                break;
            }
            currentY += 30;
        }
    }

    private void handleOverlayMainPanelClick(int relativeX, int relativeY) {
        Logger.sendLog("🎯 Main panel overlay click at: " + relativeX + ", " + relativeY);

        // Verificar cliques nos botões de features
        int currentY = 55; // Posição inicial dos botões

        if ("Combat".equals(selectedCategory)) {
            // Verificar cliques nos botões de combat
            if (relativeY >= currentY && relativeY <= currentY + 25) {
                Logger.sendLog("⚔️ Aimbot toggled");
                aimbotEnabled = !aimbotEnabled;
            }
            currentY += 37;

            if (relativeY >= currentY && relativeY <= currentY + 25) {
                Logger.sendLog("⚔️ KillAura toggled");
                killAuraEnabled = !killAuraEnabled;
            }
        }
        // Adicionar mais categorias conforme necessário
    }

    /**
     * Update animation system
     */
    private void updateAnimations() {
        long currentTime = System.currentTimeMillis();

        if (visible && animationProgress < 1.0f) {
            if (lastAnimationTime == 0) {
                lastAnimationTime = currentTime;
            }

            float deltaTime = (currentTime - lastAnimationTime) / 1000.0f;
            animationProgress = Math.min(1.0f, animationProgress + (ANIMATION_SPEED * deltaTime * 60));
            lastAnimationTime = currentTime;
        } else if (!visible && animationProgress > 0.0f) {
            if (lastAnimationTime == 0) {
                lastAnimationTime = currentTime;
            }

            float deltaTime = (currentTime - lastAnimationTime) / 1000.0f;
            animationProgress = Math.max(0.0f, animationProgress - (ANIMATION_SPEED * deltaTime * 60));
            lastAnimationTime = currentTime;
        }
    }

    private void renderFallbackInterface() {
        try {
            ScaledResolution sr = new ScaledResolution(mc);

            // Usar coordenadas escaladas do Minecraft
            int screenWidth = sr.getScaledWidth();
            int screenHeight = sr.getScaledHeight();

            // Calcular tamanho da janela baseado na resolução
            int windowWidth = Math.min(400, screenWidth - 40);
            int windowHeight = Math.min(300, screenHeight - 40);
            int windowX = (screenWidth - windowWidth) / 2;
            int windowY = (screenHeight - windowHeight) / 2;

            // Configurar OpenGL para renderização 2D usando o sistema do Minecraft
            GL11.glPushMatrix();
            GL11.glDisable(GL11.GL_DEPTH_TEST);
            GL11.glEnable(GL11.GL_BLEND);
            GL11.glBlendFunc(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA);

            // Fundo da janela com gradiente
            drawGradientRect(windowX, windowY, windowX + windowWidth, windowY + windowHeight, 0xE0000000, 0xE0111111);

            // Borda externa com glow effect
            drawRect(windowX - 2, windowY - 2, windowX + windowWidth + 2, windowY - 1, 0xFF00AAFF); // Top glow
            drawRect(windowX - 2, windowY + windowHeight + 1, windowX + windowWidth + 2, windowY + windowHeight + 2,
                    0xFF00AAFF); // Bottom glow
            drawRect(windowX - 2, windowY - 1, windowX - 1, windowY + windowHeight + 1, 0xFF00AAFF); // Left glow
            drawRect(windowX + windowWidth + 1, windowY - 1, windowX + windowWidth + 2, windowY + windowHeight + 1,
                    0xFF00AAFF); // Right glow

            // Borda principal
            drawRect(windowX - 1, windowY - 1, windowX + windowWidth + 1, windowY, 0xFF333333); // Top
            drawRect(windowX - 1, windowY + windowHeight, windowX + windowWidth + 1, windowY + windowHeight + 1,
                    0xFF333333); // Bottom
            drawRect(windowX - 1, windowY, windowX, windowY + windowHeight, 0xFF333333); // Left
            drawRect(windowX + windowWidth, windowY, windowX + windowWidth + 1, windowY + windowHeight, 0xFF333333); // Right

            // Header com gradiente
            drawGradientRect(windowX, windowY, windowX + windowWidth, windowY + 25, 0xFF1A1A1A, 0xFF0D0D0D);

            // Título com shadow
            mc.fontRendererObj.drawString("§0RATO CLIENT", windowX + 6, windowY + 9, 0x000000); // Shadow
            mc.fontRendererObj.drawString("§b§lRATO CLIENT", windowX + 5, windowY + 8, 0x00AAFF);

            // Linha separadora com gradiente
            drawGradientRect(windowX + 5, windowY + 25, windowX + windowWidth - 5, windowY + 26, 0xFF00AAFF,
                    0xFF0066CC);

            // Conteúdo do menu
            int currentY = windowY + 30;
            int leftColumn = windowX + 10;
            int rightColumn = windowX + windowWidth / 2 + 10;

            // Combat Section (Coluna Esquerda)
            mc.fontRendererObj.drawString("§6§lCombat:", leftColumn, currentY, 0xFFAA00);
            currentY += 15;

            renderModuleButton("Aimbot", aimbotEnabled, leftColumn + 5, currentY);
            currentY += 12;

            renderModuleButton("KillAura", killAuraEnabled, leftColumn + 5, currentY);
            currentY += 12;

            renderModuleButton("Criticals", criticalsEnabled, leftColumn + 5, currentY);
            currentY += 20;

            // Movement Section (Coluna Esquerda)
            mc.fontRendererObj.drawString("§a§lMovement:", leftColumn, currentY, 0x55FF55);
            currentY += 15;

            renderModuleButton("Speed", speedEnabled, leftColumn + 5, currentY);
            currentY += 12;

            renderModuleButton("Fly", flyEnabled, leftColumn + 5, currentY);
            currentY += 12;

            renderModuleButton("Bunny Hop", bunnyHopEnabled, leftColumn + 5, currentY);

            // Visual Section (Coluna Direita)
            currentY = windowY + 35;
            mc.fontRendererObj.drawString("§d§lVisual:", rightColumn, currentY, 0xFF55FF);
            currentY += 15;

            renderModuleButton("ESP", espEnabled, rightColumn + 5, currentY);
            currentY += 12;

            renderModuleButton("Nametags", nametagsEnabled, rightColumn + 5, currentY);
            currentY += 12;

            renderModuleButton("Tracers", tracersEnabled, rightColumn + 5, currentY);
            currentY += 20;

            // Rift Section (Coluna Direita)
            mc.fontRendererObj.drawString("§e§lRift:", rightColumn, currentY, 0xFFFF55);
            currentY += 15;

            renderModuleButton("Autofarm", riftAutofarmEnabled, rightColumn + 5, currentY);
            currentY += 20;

            // Misc Section (Coluna Direita)
            mc.fontRendererObj.drawString("§7§lMisc:", rightColumn, currentY, 0xAAAAAA);
            currentY += 15;

            renderModuleButton("Auto Click", autoClickEnabled, rightColumn + 5, currentY);
            currentY += 12;

            renderModuleButton("No Fall", noFallEnabled, rightColumn + 5, currentY);

            // Footer com instruções
            drawGradientRect(windowX, windowY + windowHeight - 30, windowX + windowWidth, windowY + windowHeight,
                    0xFF0D0D0D, 0xFF1A1A1A);
            mc.fontRendererObj.drawString("§7§oINSERT §8- §7Fechar Menu", windowX + 8, windowY + windowHeight - 22,
                    0x888888);
            mc.fontRendererObj.drawString("§7§oClique §8- §7Ativar/Desativar", windowX + 8, windowY + windowHeight - 12,
                    0x888888);

            // Processar cliques do mouse
            processMouseClicks(windowX, windowY, windowWidth, windowHeight);

            GL11.glPopMatrix();

        } catch (Exception e) {
            Logger.sendLog("❌ Erro ao renderizar interface: " + e.getMessage());
        }
    }

    /**
     * Render the professional interface with sidebar and animations
     */
    private void renderProfessionalInterface() {
        // Proteção contra stack overflow
        boolean matrixPushed = false;
        boolean textureDisabled = false;
        boolean blendEnabled = false;

        try {
            ScaledResolution sr = new ScaledResolution(mc);
            int screenWidth = sr.getScaledWidth();
            int screenHeight = sr.getScaledHeight();

            // Calculate window dimensions with animation
            int totalWidth = SIDEBAR_WIDTH + MAIN_PANEL_WIDTH;
            int windowX = (screenWidth - totalWidth) / 2;
            int windowY = (screenHeight - WINDOW_HEIGHT) / 2;

            // Apply animation scaling
            float scale = easeInOutCubic(animationProgress);

            // Salvar estado OpenGL com proteção
            GL11.glPushMatrix();
            matrixPushed = true;

            GL11.glTranslatef(windowX + totalWidth / 2f, windowY + WINDOW_HEIGHT / 2f, 0);
            GL11.glScalef(scale, scale, 1.0f);
            GL11.glTranslatef(-(totalWidth / 2f), -(WINDOW_HEIGHT / 2f), 0);

            // Setup OpenGL for 2D rendering com proteção
            GL11.glDisable(GL11.GL_TEXTURE_2D);
            textureDisabled = true;

            GL11.glEnable(GL11.GL_BLEND);
            blendEnabled = true;

            GL11.glBlendFunc(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA);

            // Update animations
            AnimationSystem.updateAll();

            // Render main window background with SOLID rounded corners
            GL11.glDisable(GL11.GL_TEXTURE_2D);
            GL11.glEnable(GL11.GL_BLEND);
            GL11.glBlendFunc(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA);

            // FORÇA O FUNDO SÓLIDO - MÉTODO DEFINITIVO com raio menor
            drawSolidRoundedBackground(0, 0, totalWidth, WINDOW_HEIGHT, 8, 0xE0000000);

            // Border opcional com raio menor
            drawRoundedBorder(0, 0, totalWidth, WINDOW_HEIGHT, 8, 1, 0xFF444444);

            GL11.glDisable(GL11.GL_BLEND);
            GL11.glEnable(GL11.GL_TEXTURE_2D);

            // Render sidebar
            renderSidebar(0, 0);

            // Render main panel
            renderMainPanel(SIDEBAR_WIDTH, 0);

            // Mouse clicks são processados pelo RatoImGuiMouseHandler

        } catch (Exception e) {
            Logger.sendLog("❌ Erro na renderização profissional: " + e.getMessage());
            e.printStackTrace(); // Para debug detalhado
        } finally {
            // Restaurar estado OpenGL de forma segura
            try {
                if (textureDisabled) {
                    GL11.glEnable(GL11.GL_TEXTURE_2D);
                }
                if (blendEnabled) {
                    GL11.glDisable(GL11.GL_BLEND);
                }
                if (matrixPushed) {
                    GL11.glPopMatrix();
                }
            } catch (Exception e) {
                Logger.sendLog("❌ Erro ao restaurar estado OpenGL: " + e.getMessage());
            }
        }
    }

    /**
     * Easing function for smooth animations
     */
    private float easeInOutCubic(float t) {
        return t < 0.5f ? 4 * t * t * t : 1 - (float) Math.pow(-2 * t + 2, 3) / 2;
    }

    /**
     * Draw a simple rectangle using OpenGL
     */
    private void drawSimpleRect(int x, int y, int width, int height, int color) {
        float alpha = (float) (color >> 24 & 255) / 255.0F;
        float red = (float) (color >> 16 & 255) / 255.0F;
        float green = (float) (color >> 8 & 255) / 255.0F;
        float blue = (float) (color & 255) / 255.0F;

        GL11.glColor4f(red, green, blue, alpha);
        GL11.glBegin(GL11.GL_QUADS);
        GL11.glVertex2f(x, y);
        GL11.glVertex2f(x + width, y);
        GL11.glVertex2f(x + width, y + height);
        GL11.glVertex2f(x, y + height);
        GL11.glEnd();
    }

    /**
     * Draw a rounded rectangle border
     */
    private void drawRoundedRectBorder(int x, int y, int width, int height, int radius, int borderWidth, int color) {
        // Draw outer rounded rect
        drawRoundedRect(x, y, width, height, radius, color);
        // Draw inner rounded rect with background color to create border effect
        drawRoundedRect(x + borderWidth, y + borderWidth, width - 2 * borderWidth, height - 2 * borderWidth,
                radius - borderWidth, 0xE0000000);
    }

    /**
     * Draw a rounded rectangle using OpenGL with proper corners - NEW VERSION
     */
    private void drawRoundedRectangle(int x, int y, int width, int height, int radius, int color) {
        float alpha = (float) (color >> 24 & 255) / 255.0F;
        float red = (float) (color >> 16 & 255) / 255.0F;
        float green = (float) (color >> 8 & 255) / 255.0F;
        float blue = (float) (color & 255) / 255.0F;

        GL11.glColor4f(red, green, blue, alpha);
        GL11.glEnable(GL11.GL_POLYGON_SMOOTH);

        // Draw main rectangle (without corners)
        GL11.glBegin(GL11.GL_QUADS);
        GL11.glVertex2f(x + radius, y);
        GL11.glVertex2f(x + width - radius, y);
        GL11.glVertex2f(x + width - radius, y + height);
        GL11.glVertex2f(x + radius, y + height);
        GL11.glEnd();

        // Draw left rectangle
        GL11.glBegin(GL11.GL_QUADS);
        GL11.glVertex2f(x, y + radius);
        GL11.glVertex2f(x + radius, y + radius);
        GL11.glVertex2f(x + radius, y + height - radius);
        GL11.glVertex2f(x, y + height - radius);
        GL11.glEnd();

        // Draw right rectangle
        GL11.glBegin(GL11.GL_QUADS);
        GL11.glVertex2f(x + width - radius, y + radius);
        GL11.glVertex2f(x + width, y + radius);
        GL11.glVertex2f(x + width, y + height - radius);
        GL11.glVertex2f(x + width - radius, y + height - radius);
        GL11.glEnd();

        // Draw corners as circles
        drawCircle(x + radius, y + radius, radius); // Top-left
        drawCircle(x + width - radius, y + radius, radius); // Top-right
        drawCircle(x + radius, y + height - radius, radius); // Bottom-left
        drawCircle(x + width - radius, y + height - radius, radius); // Bottom-right

        GL11.glDisable(GL11.GL_POLYGON_SMOOTH);
    }

    /**
     * Draw a circle for rounded corners
     */
    private void drawCircle(float centerX, float centerY, float radius) {
        GL11.glBegin(GL11.GL_TRIANGLE_FAN);
        GL11.glVertex2f(centerX, centerY);
        for (int i = 0; i <= 20; i++) {
            double angle = 2.0 * Math.PI * i / 20;
            GL11.glVertex2f(centerX + (float) Math.cos(angle) * radius, centerY + (float) Math.sin(angle) * radius);
        }
        GL11.glEnd();
    }

    /**
     * Draw a rounded rectangle border - NEW VERSION
     */
    private void drawRoundedRectangleBorder(int x, int y, int width, int height, int radius, int borderWidth,
            int color) {
        // Draw outer rounded rectangle
        drawRoundedRectangle(x, y, width, height, radius, color);
        // Draw inner rounded rectangle with transparent color to create border effect
        drawRoundedRectangle(x + borderWidth, y + borderWidth, width - 2 * borderWidth, height - 2 * borderWidth,
                Math.max(0, radius - borderWidth), 0x00000000);
    }

    /**
     * MÉTODO DEFINITIVO - Desenha fundo sólido com bordas arredondadas GARANTIDAS
     */
    private void drawSolidRoundedBackground(int x, int y, int width, int height, int radius, int color) {
        float alpha = (float) (color >> 24 & 255) / 255.0F;
        float red = (float) (color >> 16 & 255) / 255.0F;
        float green = (float) (color >> 8 & 255) / 255.0F;
        float blue = (float) (color & 255) / 255.0F;

        GL11.glColor4f(red, green, blue, alpha);
        GL11.glEnable(GL11.GL_POLYGON_SMOOTH);

        if (radius <= 0) {
            // Se não há raio, desenha retângulo normal
            GL11.glBegin(GL11.GL_QUADS);
            GL11.glVertex2f(x, y);
            GL11.glVertex2f(x + width, y);
            GL11.glVertex2f(x + width, y + height);
            GL11.glVertex2f(x, y + height);
            GL11.glEnd();
        } else {
            // Desenha retângulo com bordas arredondadas usando múltiplos quads e círculos

            // 1. Retângulo central (sem tocar os cantos)
            GL11.glBegin(GL11.GL_QUADS);
            GL11.glVertex2f(x + radius, y + radius);
            GL11.glVertex2f(x + width - radius, y + radius);
            GL11.glVertex2f(x + width - radius, y + height - radius);
            GL11.glVertex2f(x + radius, y + height - radius);
            GL11.glEnd();

            // 2. Retângulo superior
            GL11.glBegin(GL11.GL_QUADS);
            GL11.glVertex2f(x + radius, y);
            GL11.glVertex2f(x + width - radius, y);
            GL11.glVertex2f(x + width - radius, y + radius);
            GL11.glVertex2f(x + radius, y + radius);
            GL11.glEnd();

            // 3. Retângulo inferior
            GL11.glBegin(GL11.GL_QUADS);
            GL11.glVertex2f(x + radius, y + height - radius);
            GL11.glVertex2f(x + width - radius, y + height - radius);
            GL11.glVertex2f(x + width - radius, y + height);
            GL11.glVertex2f(x + radius, y + height);
            GL11.glEnd();

            // 4. Retângulo esquerdo
            GL11.glBegin(GL11.GL_QUADS);
            GL11.glVertex2f(x, y + radius);
            GL11.glVertex2f(x + radius, y + radius);
            GL11.glVertex2f(x + radius, y + height - radius);
            GL11.glVertex2f(x, y + height - radius);
            GL11.glEnd();

            // 5. Retângulo direito
            GL11.glBegin(GL11.GL_QUADS);
            GL11.glVertex2f(x + width - radius, y + radius);
            GL11.glVertex2f(x + width, y + radius);
            GL11.glVertex2f(x + width, y + height - radius);
            GL11.glVertex2f(x + width - radius, y + height - radius);
            GL11.glEnd();

            // 6. Círculos nos cantos
            drawFilledQuarterCircle(x + radius, y + radius, radius, 180, 270); // Superior esquerdo
            drawFilledQuarterCircle(x + width - radius, y + radius, radius, 270, 360); // Superior direito
            drawFilledQuarterCircle(x + radius, y + height - radius, radius, 90, 180); // Inferior esquerdo
            drawFilledQuarterCircle(x + width - radius, y + height - radius, radius, 0, 90); // Inferior direito
        }

        GL11.glDisable(GL11.GL_POLYGON_SMOOTH);
    }

    /**
     * Desenha um quarto de círculo PREENCHIDO para cantos arredondados
     */
    private void drawFilledQuarterCircle(float centerX, float centerY, float radius, int startAngle, int endAngle) {
        GL11.glBegin(GL11.GL_TRIANGLE_FAN);
        GL11.glVertex2f(centerX, centerY);
        for (int i = startAngle; i <= endAngle; i += 3) {
            double angle = Math.toRadians(i);
            GL11.glVertex2f(centerX + (float) Math.cos(angle) * radius, centerY + (float) Math.sin(angle) * radius);
        }
        // Garantir que o último ponto seja exato
        double endAngleRad = Math.toRadians(endAngle);
        GL11.glVertex2f(centerX + (float) Math.cos(endAngleRad) * radius, centerY + (float) Math.sin(endAngleRad) * radius);
        GL11.glEnd();
    }

    /**
     * Desenha um quarto de círculo para cantos arredondados
     */
    private void drawQuarterCircle(float centerX, float centerY, float radius, int startAngle, int endAngle) {
        GL11.glBegin(GL11.GL_TRIANGLE_FAN);
        GL11.glVertex2f(centerX, centerY);
        for (int i = startAngle; i <= endAngle; i += 5) {
            double angle = Math.toRadians(i);
            GL11.glVertex2f(centerX + (float) Math.cos(angle) * radius, centerY + (float) Math.sin(angle) * radius);
        }
        GL11.glEnd();
    }

    /**
     * Desenha círculo sólido GARANTIDO
     */
    private void drawSolidCircle(float centerX, float centerY, float radius) {
        GL11.glBegin(GL11.GL_TRIANGLE_FAN);
        GL11.glVertex2f(centerX, centerY);
        for (int i = 0; i <= 32; i++) {
            double angle = 2.0 * Math.PI * i / 32;
            GL11.glVertex2f(centerX + (float) Math.cos(angle) * radius, centerY + (float) Math.sin(angle) * radius);
        }
        GL11.glEnd();
    }

    /**
     * Desenha borda arredondada simples
     */
    private void drawRoundedBorder(int x, int y, int width, int height, int radius, int borderWidth, int color) {
        float alpha = (float) (color >> 24 & 255) / 255.0F;
        float red = (float) (color >> 16 & 255) / 255.0F;
        float green = (float) (color >> 8 & 255) / 255.0F;
        float blue = (float) (color & 255) / 255.0F;

        GL11.glColor4f(red, green, blue, alpha);
        GL11.glLineWidth(borderWidth);

        // Desenha linhas das bordas
        GL11.glBegin(GL11.GL_LINE_LOOP);
        // Canto superior esquerdo
        for (int i = 180; i <= 270; i += 5) {
            double angle = Math.toRadians(i);
            GL11.glVertex2f(x + radius + (float) Math.cos(angle) * radius,
                    y + radius + (float) Math.sin(angle) * radius);
        }
        // Linha superior
        GL11.glVertex2f(x + width - radius, y);
        // Canto superior direito
        for (int i = 270; i <= 360; i += 5) {
            double angle = Math.toRadians(i);
            GL11.glVertex2f(x + width - radius + (float) Math.cos(angle) * radius,
                    y + radius + (float) Math.sin(angle) * radius);
        }
        // Linha direita
        GL11.glVertex2f(x + width, y + height - radius);
        // Canto inferior direito
        for (int i = 0; i <= 90; i += 5) {
            double angle = Math.toRadians(i);
            GL11.glVertex2f(x + width - radius + (float) Math.cos(angle) * radius,
                    y + height - radius + (float) Math.sin(angle) * radius);
        }
        // Linha inferior
        GL11.glVertex2f(x + radius, y + height);
        // Canto inferior esquerdo
        for (int i = 90; i <= 180; i += 5) {
            double angle = Math.toRadians(i);
            GL11.glVertex2f(x + radius + (float) Math.cos(angle) * radius,
                    y + height - radius + (float) Math.sin(angle) * radius);
        }
        // Linha esquerda
        GL11.glVertex2f(x, y + radius);
        GL11.glEnd();
    }

    /**
     * Draw a rounded rectangle using OpenGL - IMPROVED VERSION
     */
    private void drawRoundedRect(int x, int y, int width, int height, int radius, int color) {
        try {
            // Validação de parâmetros
            if (width <= 0 || height <= 0 || radius < 0) {
                return;
            }

            // Limitar radius para evitar problemas
            radius = Math.min(radius, Math.min(width / 2, height / 2));

            float alpha = (float) (color >> 24 & 255) / 255.0F;
            float red = (float) (color >> 16 & 255) / 255.0F;
            float green = (float) (color >> 8 & 255) / 255.0F;
            float blue = (float) (color & 255) / 255.0F;

            // Estado OpenGL seguro
            GL11.glColor4f(red, green, blue, alpha);

            // Se radius é muito pequeno, usar retângulo simples
            if (radius <= 2) {
                GL11.glBegin(GL11.GL_QUADS);
                GL11.glVertex2f(x, y);
                GL11.glVertex2f(x + width, y);
                GL11.glVertex2f(x + width, y + height);
                GL11.glVertex2f(x, y + height);
                GL11.glEnd();
                return;
            }

            // Desenhar retângulo central (sem cantos)
            GL11.glBegin(GL11.GL_QUADS);
            // Centro horizontal
            GL11.glVertex2f(x + radius, y);
            GL11.glVertex2f(x + width - radius, y);
            GL11.glVertex2f(x + width - radius, y + height);
            GL11.glVertex2f(x + radius, y + height);
            GL11.glEnd();

            GL11.glBegin(GL11.GL_QUADS);
            // Centro vertical esquerdo
            GL11.glVertex2f(x, y + radius);
            GL11.glVertex2f(x + radius, y + radius);
            GL11.glVertex2f(x + radius, y + height - radius);
            GL11.glVertex2f(x, y + height - radius);
            GL11.glEnd();

            GL11.glBegin(GL11.GL_QUADS);
            // Centro vertical direito
            GL11.glVertex2f(x + width - radius, y + radius);
            GL11.glVertex2f(x + width, y + radius);
            GL11.glVertex2f(x + width, y + height - radius);
            GL11.glVertex2f(x + width - radius, y + height - radius);
            GL11.glEnd();

            // Desenhar cantos arredondados com mais segmentos para suavidade
            int segments = 8;

            // Top-left corner
            GL11.glBegin(GL11.GL_TRIANGLE_FAN);
            GL11.glVertex2f(x + radius, y + radius); // Centro
            for (int i = 0; i <= segments; i++) {
                double angle = Math.PI + (Math.PI / 2) * i / segments;
                GL11.glVertex2f((float) (x + radius + Math.cos(angle) * radius),
                        (float) (y + radius + Math.sin(angle) * radius));
            }
            GL11.glEnd();

            // Top-right corner
            GL11.glBegin(GL11.GL_TRIANGLE_FAN);
            GL11.glVertex2f(x + width - radius, y + radius); // Centro
            for (int i = 0; i <= segments; i++) {
                double angle = 3 * Math.PI / 2 + (Math.PI / 2) * i / segments;
                GL11.glVertex2f((float) (x + width - radius + Math.cos(angle) * radius),
                        (float) (y + radius + Math.sin(angle) * radius));
            }
            GL11.glEnd();

            // Bottom-right corner
            GL11.glBegin(GL11.GL_TRIANGLE_FAN);
            GL11.glVertex2f(x + width - radius, y + height - radius); // Centro
            for (int i = 0; i <= segments; i++) {
                double angle = 0 + (Math.PI / 2) * i / segments;
                GL11.glVertex2f((float) (x + width - radius + Math.cos(angle) * radius),
                        (float) (y + height - radius + Math.sin(angle) * radius));
            }
            GL11.glEnd();

            // Bottom-left corner
            GL11.glBegin(GL11.GL_TRIANGLE_FAN);
            GL11.glVertex2f(x + radius, y + height - radius); // Centro
            for (int i = 0; i <= segments; i++) {
                double angle = Math.PI / 2 + (Math.PI / 2) * i / segments;
                GL11.glVertex2f((float) (x + radius + Math.cos(angle) * radius),
                        (float) (y + height - radius + Math.sin(angle) * radius));
            }
            GL11.glEnd();

        } catch (Exception e) {
            Logger.sendLog("❌ Erro em drawRoundedRect: " + e.getMessage());
            // Fallback para retângulo simples
            drawRect(x, y, x + width, y + height, color);
        }
    }

    /**
     * Render the sidebar with categories
     */
    private void renderSidebar(int x, int y) {
        try {
            // Sidebar background with SOLID rounded corners
            GL11.glDisable(GL11.GL_TEXTURE_2D);
            GL11.glEnable(GL11.GL_BLEND);
            GL11.glBlendFunc(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA);

            drawSolidRoundedBackground(x, y, SIDEBAR_WIDTH, WINDOW_HEIGHT, 12, 0xE0111111);

            GL11.glDisable(GL11.GL_BLEND);
            GL11.glEnable(GL11.GL_TEXTURE_2D);

            // Sidebar header com fontes melhoradas
            GL11.glEnable(GL11.GL_TEXTURE_2D);

            // Desenhar sombra do texto primeiro
            mc.fontRendererObj.drawString("§l§8RATO", x + 11, y + 16, 0x000000);
            mc.fontRendererObj.drawString("§8CLIENT", x + 11, y + 26, 0x000000);

            // Desenhar texto principal
            mc.fontRendererObj.drawString("§l§bRATO", x + 10, y + 15, 0x00AAFF);
            mc.fontRendererObj.drawString("§7CLIENT", x + 10, y + 25, 0xAAAAAA);

            GL11.glDisable(GL11.GL_TEXTURE_2D);

            // Category buttons
            int currentY = y + 50;
            for (String category : categories) {
                boolean isSelected = category.equals(selectedCategory);
                Integer categoryColor = categoryColors.get(category);
                int buttonColor = isSelected && categoryColor != null ? categoryColor : 0x40333333;
                int textColor = isSelected ? 0xFFFFFF : 0xAAAAAA;

                // Desativar textura para botão
                GL11.glDisable(GL11.GL_TEXTURE_2D);
                GL11.glEnable(GL11.GL_BLEND);
                GL11.glBlendFunc(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA);

                drawSolidRoundedBackground(x + 5, currentY, SIDEBAR_WIDTH - 10, 25, 8, buttonColor);

                GL11.glDisable(GL11.GL_BLEND);
                GL11.glEnable(GL11.GL_TEXTURE_2D);

                // Button text com sombra melhorada
                GL11.glEnable(GL11.GL_TEXTURE_2D);

                // Desenhar sombra do texto
                String shadowText = isSelected ? "§8" + category : "§8" + category;
                mc.fontRendererObj.drawString(shadowText, x + 13, currentY + 9, 0x000000);

                // Desenhar texto principal
                String mainText = isSelected ? "§l" + category : category;
                mc.fontRendererObj.drawString(mainText, x + 12, currentY + 8, textColor);

                GL11.glDisable(GL11.GL_TEXTURE_2D);
                currentY += 30;
            }

            // Footer info com sombra
            GL11.glEnable(GL11.GL_TEXTURE_2D);

            // Sombra do footer
            mc.fontRendererObj.drawString("§8v1.0", x + 11, y + WINDOW_HEIGHT - 19, 0x000000);

            // Texto principal do footer
            mc.fontRendererObj.drawString("§7v1.0", x + 10, y + WINDOW_HEIGHT - 20, 0x666666);

            GL11.glDisable(GL11.GL_TEXTURE_2D);

        } catch (Exception e) {
            Logger.sendLog("❌ Erro ao renderizar sidebar moderna: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Render the main panel with features
     */
    private void renderMainPanel(int x, int y) {
        try {
            // Main panel background with SOLID rounded corners
            GL11.glDisable(GL11.GL_TEXTURE_2D);
            GL11.glEnable(GL11.GL_BLEND);
            GL11.glBlendFunc(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA);

            drawSolidRoundedBackground(x, y, MAIN_PANEL_WIDTH, WINDOW_HEIGHT, 12, 0xE0222222);

            GL11.glDisable(GL11.GL_BLEND);
            GL11.glEnable(GL11.GL_TEXTURE_2D);

            // Header com fontes melhoradas e sombra
            Integer categoryColorInt = categoryColors.get(selectedCategory);
            int colorInt = categoryColorInt != null ? categoryColorInt : 0x00AAFF;

            GL11.glEnable(GL11.GL_TEXTURE_2D);

            // Sombra do título
            mc.fontRendererObj.drawString("§l§8" + selectedCategory, x + 16, y + 16, 0x000000);

            // Título principal
            mc.fontRendererObj.drawString("§l" + selectedCategory, x + 15, y + 15, colorInt);

            // Sombra da descrição
            mc.fontRendererObj.drawString("§8Configure your " + selectedCategory.toLowerCase() + " settings",
                    x + 16, y + 31, 0x000000);

            // Descrição principal
            mc.fontRendererObj.drawString("§7Configure your " + selectedCategory.toLowerCase() + " settings",
                    x + 15, y + 30, 0xAAAAAA);

            GL11.glDisable(GL11.GL_TEXTURE_2D);

            // Separator line
            drawSimpleRect(x + 15, y + 45, MAIN_PANEL_WIDTH - 30, 1, 0xFF333333);

            // Render features based on selected category
            renderCategoryFeatures(x, y + 100);

        } catch (Exception e) {
            Logger.sendLog("❌ Erro ao renderizar main panel: " + e.getMessage());
            e.printStackTrace(); // Para debug detalhado
        }
    }

    /**
     * Render features for the selected category
     */
    private void renderCategoryFeatures(int x, int y) {
        int currentY = y;

        switch (selectedCategory) {
            case "Combat":
                currentY = renderCombatFeatures(x, currentY);
                break;
            case "Movement":
                currentY = renderMovementFeatures(x, currentY);
                break;
            case "Visual":
                currentY = renderVisualFeatures(x, currentY);
                break;
            case "Player":
                currentY = renderPlayerFeatures(x, currentY);
                break;
            case "World":
                currentY = renderWorldFeatures(x, currentY);
                break;
            case "Misc":
                currentY = renderMiscFeatures(x, currentY);
                break;
        }
    }

    /**
     * Render combat features
     */
    private int renderCombatFeatures(int x, int y) {
        int currentY = y;
        currentY += renderProfessionalButton("Aimbot", aimbotEnabled, x + 15, currentY);
        currentY += renderProfessionalButton("Kill Aura", killAuraEnabled, x + 15, currentY);
        currentY += renderProfessionalButton("Criticals", criticalsEnabled, x + 15, currentY);
        return currentY;
    }

    /**
     * Render movement features
     */
    private int renderMovementFeatures(int x, int y) {
        int currentY = y;
        currentY += renderProfessionalButton("Speed", speedEnabled, x + 15, currentY);
        currentY += renderProfessionalButton("Fly", flyEnabled, x + 15, currentY);
        currentY += renderProfessionalButton("Bunny Hop", bunnyHopEnabled, x + 15, currentY);
        return currentY;
    }

    /**
     * Render visual features
     */
    private int renderVisualFeatures(int x, int y) {
        int currentY = y;
        currentY += renderProfessionalButton("ESP", espEnabled, x + 15, currentY);
        currentY += renderProfessionalButton("Nametags", nametagsEnabled, x + 15, currentY);
        currentY += renderProfessionalButton("Tracers", tracersEnabled, x + 15, currentY);
        return currentY;
    }

    /**
     * Render player features
     */
    private int renderPlayerFeatures(int x, int y) {
        int currentY = y;
        currentY += renderProfessionalButton("Auto Click", autoClickEnabled, x + 15, currentY);
        currentY += renderProfessionalButton("No Fall", noFallEnabled, x + 15, currentY);
        return currentY;
    }

    /**
     * Render world features
     */
    private int renderWorldFeatures(int x, int y) {
        int currentY = y;
        // Add world-specific features here
        if (customFont != null) {
            customFont.drawString("World features coming soon...", x + 15, currentY, new Color(136, 136, 136), false,
                    false);
        }
        return currentY + 20;
    }

    /**
     * Render misc features
     */
    private int renderMiscFeatures(int x, int y) {
        int currentY = y;
        currentY += renderProfessionalButton("Rift Autofarm", riftAutofarmEnabled, x + 15, currentY);
        return currentY;
    }

    /**
     * Render a professional button with hover effects
     */
    private int renderProfessionalButton(String name, boolean enabled, int x, int y) {
        int buttonWidth = MAIN_PANEL_WIDTH - 30;
        int buttonHeight = 25;

        // Check if mouse is hovering over button - IMPROVED VERSION
        boolean isHovered = false;
        try {
            ScaledResolution sr = new ScaledResolution(mc);
            int mouseX = Mouse.getX() * sr.getScaledWidth() / mc.displayWidth;
            int mouseY = sr.getScaledHeight() - Mouse.getY() * sr.getScaledHeight() / mc.displayHeight - 1;
            isHovered = mouseX >= x && mouseX <= x + buttonWidth && mouseY >= y && mouseY <= y + buttonHeight;
        } catch (Exception e) {
            // Se houver erro, assumir que não está hovering
            isHovered = false;
        }

        // Button colors based on state
        int backgroundColor;
        int borderColor;
        int textColor;

        if (enabled) {
            backgroundColor = isHovered ? 0xFF4CAF50 : 0xE04CAF50;
            borderColor = 0xFF66BB6A;
            textColor = 0xFFFFFF;
        } else {
            backgroundColor = isHovered ? 0xFF555555 : 0xE0333333;
            borderColor = 0xFF666666;
            textColor = 0xAAAAAA;
        }

        // Draw button background
        drawRoundedRect(x, y, buttonWidth, buttonHeight, 6, backgroundColor);

        // Draw button border
        GL11.glDisable(GL11.GL_TEXTURE_2D);
        GL11.glEnable(GL11.GL_BLEND);
        GL11.glBlendFunc(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA);

        // Border effect
        drawRoundedRect(x - 1, y - 1, buttonWidth + 2, buttonHeight + 2, 7, borderColor);
        drawRoundedRect(x, y, buttonWidth, buttonHeight, 6, backgroundColor);

        GL11.glDisable(GL11.GL_BLEND);
        GL11.glEnable(GL11.GL_TEXTURE_2D);

        // Draw button text
        String displayText = name + (enabled ? " [ON]" : " [OFF]");
        if (customFont != null) {
            customFont.drawString(displayText, x + 8, y + 8, intToColor(textColor), false, false);
        }

        return buttonHeight + 8; // Return height + spacing
    }

    /**
     * Process advanced mouse clicks with proper coordinate handling
     */
    private void processAdvancedMouseClicks(int windowX, int windowY, int windowWidth, int windowHeight) {
        // Verificar se o mouse foi clicado (apenas uma vez por clique)
        if (!Mouse.isButtonDown(0) || !isMouseClicked()) {
            return;
        }

        // Get mouse coordinates usando ScaledResolution
        ScaledResolution sr = new ScaledResolution(mc);
        int mouseX = Mouse.getX() * sr.getScaledWidth() / mc.displayWidth;
        int mouseY = sr.getScaledHeight() - Mouse.getY() * sr.getScaledHeight() / mc.displayHeight - 1;

        // Debug log
        Logger.sendLog("🖱️ Mouse click detected at: " + mouseX + ", " + mouseY + " | Window: " + windowX + ", "
                + windowY + " | Size: " + windowWidth + "x" + windowHeight);

        // Check if click is within window bounds
        if (mouseX < windowX || mouseX > windowX + windowWidth || mouseY < windowY || mouseY > windowY + windowHeight) {
            Logger.sendLog("❌ Click outside window bounds");
            return;
        }

        Logger.sendLog("✅ Click inside window bounds");

        // Handle sidebar clicks (category selection)
        if (mouseX >= windowX && mouseX <= windowX + SIDEBAR_WIDTH) {
            handleSidebarClick(mouseX - windowX, mouseY - windowY);
        }

        // Handle main panel clicks (feature toggles)
        if (mouseX >= windowX + SIDEBAR_WIDTH && mouseX <= windowX + windowWidth) {
            handleMainPanelClick(mouseX - windowX - SIDEBAR_WIDTH, mouseY - windowY);
        }
    }

    /**
     * Handle sidebar category clicks
     */
    private void handleSidebarClick(int relativeX, int relativeY) {
        int categoryY = 50;

        for (String category : categories) {
            if (relativeY >= categoryY && relativeY <= categoryY + 25) {
                if (!category.equals(selectedCategory)) {
                    selectedCategory = category;
                    Logger.sendLog("🔄 Categoria selecionada: " + category);
                }
                return;
            }
            categoryY += 30;
        }
    }

    /**
     * Handle main panel feature clicks
     */
    private void handleMainPanelClick(int relativeX, int relativeY) {
        // Calculate which button was clicked based on Y position
        int buttonStartY = 55;
        int buttonHeight = 25;
        int buttonSpacing = 8;
        int totalButtonHeight = buttonHeight + buttonSpacing;

        if (relativeY < buttonStartY) {
            return;
        }

        int buttonIndex = (relativeY - buttonStartY) / totalButtonHeight;

        // Toggle the appropriate feature based on category and button index
        switch (selectedCategory) {
            case "Combat":
                toggleCombatFeature(buttonIndex);
                break;
            case "Movement":
                toggleMovementFeature(buttonIndex);
                break;
            case "Visual":
                toggleVisualFeature(buttonIndex);
                break;
            case "Player":
                togglePlayerFeature(buttonIndex);
                break;
            case "Misc":
                toggleMiscFeature(buttonIndex);
                break;
        }
    }

    /**
     * Toggle combat features
     */
    private void toggleCombatFeature(int index) {
        switch (index) {
            case 0:
                aimbotEnabled = !aimbotEnabled;
                Logger.sendLog("🎯 Aimbot: " + (aimbotEnabled ? "§aON" : "§cOFF"));
                break;
            case 1:
                killAuraEnabled = !killAuraEnabled;
                Logger.sendLog("⚔️ Kill Aura: " + (killAuraEnabled ? "§aON" : "§cOFF"));
                break;
            case 2:
                criticalsEnabled = !criticalsEnabled;
                Logger.sendLog("💥 Criticals: " + (criticalsEnabled ? "§aON" : "§cOFF"));
                break;
        }
    }

    /**
     * Toggle movement features
     */
    private void toggleMovementFeature(int index) {
        switch (index) {
            case 0:
                speedEnabled = !speedEnabled;
                Logger.sendLog("💨 Speed: " + (speedEnabled ? "§aON" : "§cOFF"));
                break;
            case 1:
                flyEnabled = !flyEnabled;
                Logger.sendLog("🚁 Fly: " + (flyEnabled ? "§aON" : "§cOFF"));
                break;
            case 2:
                bunnyHopEnabled = !bunnyHopEnabled;
                Logger.sendLog("🐰 Bunny Hop: " + (bunnyHopEnabled ? "§aON" : "§cOFF"));
                break;
        }
    }

    /**
     * Toggle visual features
     */
    private void toggleVisualFeature(int index) {
        switch (index) {
            case 0:
                espEnabled = !espEnabled;
                Logger.sendLog("👁️ ESP: " + (espEnabled ? "§aON" : "§cOFF"));
                break;
            case 1:
                nametagsEnabled = !nametagsEnabled;
                Logger.sendLog("🏷️ Nametags: " + (nametagsEnabled ? "§aON" : "§cOFF"));
                break;
            case 2:
                tracersEnabled = !tracersEnabled;
                Logger.sendLog("📍 Tracers: " + (tracersEnabled ? "§aON" : "§cOFF"));
                break;
        }
    }

    /**
     * Toggle player features
     */
    private void togglePlayerFeature(int index) {
        switch (index) {
            case 0:
                autoClickEnabled = !autoClickEnabled;
                Logger.sendLog("🖱️ Auto Click: " + (autoClickEnabled ? "§aON" : "§cOFF"));
                break;
            case 1:
                noFallEnabled = !noFallEnabled;
                Logger.sendLog("🛡️ No Fall: " + (noFallEnabled ? "§aON" : "§cOFF"));
                break;
        }
    }

    /**
     * Toggle misc features
     */
    private void toggleMiscFeature(int index) {
        switch (index) {
            case 0:
                riftAutofarmEnabled = !riftAutofarmEnabled;
                Logger.sendLog("🌾 Rift Autofarm: " + (riftAutofarmEnabled ? "§aON" : "§cOFF"));
                break;
        }
    }

    // Método helper para desenhar retângulos
    private void drawRect(int left, int top, int right, int bottom, int color) {
        if (left < right) {
            int temp = left;
            left = right;
            right = temp;
        }

        if (top < bottom) {
            int temp = top;
            top = bottom;
            bottom = temp;
        }

        float alpha = (float) (color >> 24 & 255) / 255.0F;
        float red = (float) (color >> 16 & 255) / 255.0F;
        float green = (float) (color >> 8 & 255) / 255.0F;
        float blue = (float) (color & 255) / 255.0F;

        GL11.glDisable(GL11.GL_TEXTURE_2D);
        GL11.glColor4f(red, green, blue, alpha);
        GL11.glBegin(GL11.GL_QUADS);
        GL11.glVertex2f((float) left, (float) bottom);
        GL11.glVertex2f((float) right, (float) bottom);
        GL11.glVertex2f((float) right, (float) top);
        GL11.glVertex2f((float) left, (float) top);
        GL11.glEnd();
        GL11.glEnable(GL11.GL_TEXTURE_2D);
    }

    private void drawGradientRect(int left, int top, int right, int bottom, int startColor, int endColor) {
        GL11.glDisable(GL11.GL_TEXTURE_2D);
        GL11.glEnable(GL11.GL_BLEND);
        GL11.glDisable(GL11.GL_ALPHA_TEST);
        GL11.glBlendFunc(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA);
        GL11.glShadeModel(GL11.GL_SMOOTH);

        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer worldrenderer = tessellator.getWorldRenderer();
        worldrenderer.begin(7, DefaultVertexFormats.POSITION_COLOR);

        float startAlpha = (float) (startColor >> 24 & 255) / 255.0F;
        float startRed = (float) (startColor >> 16 & 255) / 255.0F;
        float startGreen = (float) (startColor >> 8 & 255) / 255.0F;
        float startBlue = (float) (startColor & 255) / 255.0F;

        float endAlpha = (float) (endColor >> 24 & 255) / 255.0F;
        float endRed = (float) (endColor >> 16 & 255) / 255.0F;
        float endGreen = (float) (endColor >> 8 & 255) / 255.0F;
        float endBlue = (float) (endColor & 255) / 255.0F;

        worldrenderer.pos((double) right, (double) top, 0.0D).color(startRed, startGreen, startBlue, startAlpha)
                .endVertex();
        worldrenderer.pos((double) left, (double) top, 0.0D).color(startRed, startGreen, startBlue, startAlpha)
                .endVertex();
        worldrenderer.pos((double) left, (double) bottom, 0.0D).color(endRed, endGreen, endBlue, endAlpha).endVertex();
        worldrenderer.pos((double) right, (double) bottom, 0.0D).color(endRed, endGreen, endBlue, endAlpha).endVertex();

        tessellator.draw();
        GL11.glShadeModel(GL11.GL_FLAT);
        GL11.glDisable(GL11.GL_BLEND);
        GL11.glEnable(GL11.GL_ALPHA_TEST);
        GL11.glEnable(GL11.GL_TEXTURE_2D);
    }

    private void renderModuleButton(String name, boolean enabled, int x, int y) {
        ScaledResolution sr = new ScaledResolution(mc);
        int mouseX = Mouse.getX() * sr.getScaledWidth() / mc.displayWidth;
        int mouseY = sr.getScaledHeight() - Mouse.getY() * sr.getScaledHeight() / mc.displayHeight;

        boolean isHovered = mouseX >= x && mouseX <= x + 150 && mouseY >= y && mouseY <= y + 9;

        // Background do botão
        if (enabled) {
            drawRect(x - 2, y - 1, x + 150, y + 10, isHovered ? 0x8800FF00 : 0x6600AA00);
        } else {
            drawRect(x - 2, y - 1, x + 150, y + 10, isHovered ? 0x88444444 : 0x66222222);
        }

        // Borda do botão
        int borderColor = enabled ? (isHovered ? 0xFF00FF00 : 0xFF00AA00) : (isHovered ? 0xFF666666 : 0xFF333333);
        drawRect(x - 2, y - 1, x + 150, y, borderColor); // Top
        drawRect(x - 2, y + 9, x + 150, y + 10, borderColor); // Bottom
        drawRect(x - 2, y, x - 1, y + 9, borderColor); // Left
        drawRect(x + 149, y, x + 150, y + 9, borderColor); // Right

        // Checkbox
        String checkbox = enabled ? "§a✓" : "§c✗";
        mc.fontRendererObj.drawString(checkbox, x, y, 0xFFFFFF);

        // Nome do módulo
        String displayName = enabled ? "§f" + name : "§7" + name;
        mc.fontRendererObj.drawString(displayName, x + 12, y, enabled ? 0xFFFFFF : 0x888888);
    }

    private boolean lastMouseState = false;
    private long lastClickTime = 0;
    private static final long CLICK_COOLDOWN = 150; // 150ms cooldown entre cliques

    /**
     * Detectar clique único do mouse
     */
    private boolean isMouseClicked() {
        boolean currentMouseState = Mouse.isButtonDown(0);
        long currentTime = System.currentTimeMillis();

        // Se o mouse foi pressionado agora e não estava pressionado antes
        if (currentMouseState && !lastMouseState && (currentTime - lastClickTime) > CLICK_COOLDOWN) {
            lastMouseState = currentMouseState;
            lastClickTime = currentTime;
            return true;
        }

        lastMouseState = currentMouseState;
        return false;
    }

    private void processMouseClicks(int windowX, int windowY, int windowWidth, int windowHeight) {
        boolean currentMouseState = Mouse.isButtonDown(0);
        long currentTime = System.currentTimeMillis();

        // Detectar clique único (mouse pressionado agora, mas não estava antes)
        if (currentMouseState && !lastMouseState && (currentTime - lastClickTime) > CLICK_COOLDOWN) {
            ScaledResolution sr = new ScaledResolution(mc);
            int mouseX = Mouse.getX() * sr.getScaledWidth() / mc.displayWidth;
            int mouseY = sr.getScaledHeight() - Mouse.getY() * sr.getScaledHeight() / mc.displayHeight;

            // Verificar se clique está dentro da janela
            if (mouseX >= windowX && mouseX <= windowX + windowWidth &&
                    mouseY >= windowY && mouseY <= windowY + windowHeight) {

                handleOptionClick(mouseX, mouseY, windowX, windowY, windowWidth, windowHeight);
                lastClickTime = currentTime;
            }
        }

        lastMouseState = currentMouseState;
    }

    private void handleOptionClick(int mouseX, int mouseY, int windowX, int windowY, int windowWidth,
            int windowHeight) {
        int leftColumn = windowX + 15;
        int rightColumn = windowX + windowWidth / 2 + 15;
        int currentY = windowY + 42; // Primeira opção Combat

        // Combat options (Coluna Esquerda)
        if (isMouseOverOption(mouseX, mouseY, leftColumn, currentY)) {
            aimbotEnabled = !aimbotEnabled;
            Logger.sendLog("🎯 Aimbot: " + (aimbotEnabled ? "§aATIVADO" : "§cDESATIVADO"));
            return;
        }
        currentY += 10;

        if (isMouseOverOption(mouseX, mouseY, leftColumn, currentY)) {
            killAuraEnabled = !killAuraEnabled;
            Logger.sendLog("⚔️ KillAura: " + (killAuraEnabled ? "§aATIVADO" : "§cDESATIVADO"));
            return;
        }
        currentY += 10;

        if (isMouseOverOption(mouseX, mouseY, leftColumn, currentY)) {
            criticalsEnabled = !criticalsEnabled;
            Logger.sendLog("💥 Criticals: " + (criticalsEnabled ? "§aATIVADO" : "§cDESATIVADO"));
            return;
        }
        currentY += 27; // Pular para Movement

        // Movement options (Coluna Esquerda)
        if (isMouseOverOption(mouseX, mouseY, leftColumn, currentY)) {
            speedEnabled = !speedEnabled;
            Logger.sendLog("💨 Speed: " + (speedEnabled ? "§aATIVADO" : "§cDESATIVADO"));
            return;
        }
        currentY += 10;

        if (isMouseOverOption(mouseX, mouseY, leftColumn, currentY)) {
            flyEnabled = !flyEnabled;
            Logger.sendLog("🚁 Fly: " + (flyEnabled ? "§aATIVADO" : "§cDESATIVADO"));
            return;
        }
        currentY += 10;

        if (isMouseOverOption(mouseX, mouseY, leftColumn, currentY)) {
            bunnyHopEnabled = !bunnyHopEnabled;
            Logger.sendLog("🐰 Bunny Hop: " + (bunnyHopEnabled ? "§aATIVADO" : "§cDESATIVADO"));
            return;
        }

        // Visual options (Coluna Direita)
        currentY = windowY + 42;
        if (isMouseOverOption(mouseX, mouseY, rightColumn, currentY)) {
            espEnabled = !espEnabled;
            Logger.sendLog("👁️ ESP: " + (espEnabled ? "§aATIVADO" : "§cDESATIVADO"));
            return;
        }
        currentY += 10;

        if (isMouseOverOption(mouseX, mouseY, rightColumn, currentY)) {
            nametagsEnabled = !nametagsEnabled;
            Logger.sendLog("🏷️ Nametags: " + (nametagsEnabled ? "§aATIVADO" : "§cDESATIVADO"));
            return;
        }
        currentY += 10;

        if (isMouseOverOption(mouseX, mouseY, rightColumn, currentY)) {
            tracersEnabled = !tracersEnabled;
            Logger.sendLog("📍 Tracers: " + (tracersEnabled ? "§aATIVADO" : "§cDESATIVADO"));
            return;
        }
        currentY += 27; // Pular para Rift

        // Rift options (Coluna Direita)
        if (isMouseOverOption(mouseX, mouseY, rightColumn, currentY)) {
            riftAutofarmEnabled = !riftAutofarmEnabled;
            Logger.sendLog("🌾 Rift Autofarm: " + (riftAutofarmEnabled ? "§aATIVADO" : "§cDESATIVADO"));
            return;
        }
        currentY += 27; // Pular para Misc

        // Misc options (Coluna Direita)
        if (isMouseOverOption(mouseX, mouseY, rightColumn, currentY)) {
            autoClickEnabled = !autoClickEnabled;
            Logger.sendLog("🖱️ Auto Click: " + (autoClickEnabled ? "§aATIVADO" : "§cDESATIVADO"));
            return;
        }
        currentY += 10;

        if (isMouseOverOption(mouseX, mouseY, rightColumn, currentY)) {
            noFallEnabled = !noFallEnabled;
            Logger.sendLog("🪂 No Fall: " + (noFallEnabled ? "§aATIVADO" : "§cDESATIVADO"));
            return;
        }
    }

    private boolean isMouseOverOption(int mouseX, int mouseY, int optionX, int optionY) {
        return mouseX >= optionX && mouseX <= optionX + 150 &&
                mouseY >= optionY && mouseY <= optionY + 9;
    }

    // Getters for feature states
    public boolean isAimbotEnabled() {
        return aimbotEnabled;
    }

    public boolean isKillAuraEnabled() {
        return killAuraEnabled;
    }

    public boolean isCriticalsEnabled() {
        return criticalsEnabled;
    }

    public boolean isSpeedEnabled() {
        return speedEnabled;
    }

    public boolean isFlyEnabled() {
        return flyEnabled;
    }

    public boolean isBunnyHopEnabled() {
        return bunnyHopEnabled;
    }

    public boolean isEspEnabled() {
        return espEnabled;
    }

    public boolean isNametagsEnabled() {
        return nametagsEnabled;
    }

    public boolean isTracersEnabled() {
        return tracersEnabled;
    }

    public boolean isRiftAutofarmEnabled() {
        return riftAutofarmEnabled;
    }

    public boolean isAutoClickEnabled() {
        return autoClickEnabled;
    }

    public boolean isNoFallEnabled() {
        return noFallEnabled;
    }

    public static void cleanup() {
        if (initialized && instance != null) {
            try {
                Logger.sendLog("🧹 Limpando RatoImGuiRenderer Professional...");
                initialized = false;
                instance = null;
                Logger.sendLog("✅ RatoImGuiRenderer Professional limpo!");
            } catch (Exception e) {
                Logger.sendLog("❌ Erro ao limpar RatoImGuiRenderer: " + e.getMessage());
            }
        }
    }
}
