package com.rato.addons.visuals;

import com.rato.addons.config.RatoAddonsConfigSimple;
import com.rato.addons.visuals.esp.ESPRenderer;
import com.rato.addons.visuals.nametags.NametagRenderer;
import com.rato.addons.visuals.glow.GlowRenderer;
import net.minecraft.client.Minecraft;
import net.minecraftforge.client.event.RenderWorldLastEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;

/**
 * Sistema de Visuals completo baseado no LiquidBounce
 * Gerencia ESP, Nametags, Glow e outros efeitos visuais
 */
public class VisualsManager {

    private static final Minecraft mc = Minecraft.getMinecraft();

    // Renderizadores especializados
    private final ESPRenderer espRenderer;
    private final NametagRenderer nametagRenderer;
    private final GlowRenderer glowRenderer;

    public VisualsManager() {
        this.espRenderer = new ESPRenderer();
        this.nametagRenderer = new NametagRenderer();
        this.glowRenderer = new GlowRenderer();
    }

    /**
     * Evento principal de renderização do mundo
     */
    @SubscribeEvent
    public void onRenderWorld(RenderWorldLastEvent event) {
        if (mc.theWorld == null || mc.thePlayer == null)
            return;

        // Renderizar ESP se habilitado
        if (RatoAddonsConfigSimple.visualsESPEnabled) {
            espRenderer.render(event.partialTicks);
        }

        // Renderizar Nametags se habilitado
        if (RatoAddonsConfigSimple.visualsNametagsEnabled) {
            nametagRenderer.render(event.partialTicks);
        }

        // Renderizar Glow se habilitado
        if (RatoAddonsConfigSimple.visualsGlowEnabled) {
            glowRenderer.render(event.partialTicks);
        }
    }

    /**
     * Obtém o renderizador ESP
     */
    public ESPRenderer getESPRenderer() {
        return espRenderer;
    }

    /**
     * Obtém o renderizador de Nametags
     */
    public NametagRenderer getNametagRenderer() {
        return nametagRenderer;
    }

    /**
     * Obtém o renderizador de Glow
     */
    public GlowRenderer getGlowRenderer() {
        return glowRenderer;
    }
}
