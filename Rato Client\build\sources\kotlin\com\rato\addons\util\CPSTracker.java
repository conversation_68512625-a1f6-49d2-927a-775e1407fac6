package com.rato.addons.util;

import java.util.ArrayList;
import java.util.List;

public class CPSTracker {
    private static CPSTracker instance;
    private final List<Long> clicks = new ArrayList<>();
    private final List<Long> rightClicks = new ArrayList<>();
    
    public static CPSTracker getInstance() {
        if (instance == null) {
            instance = new CPSTracker();
        }
        return instance;
    }
    
    public void addLeftClick() {
        clicks.add(System.currentTimeMillis());
        cleanOldClicks(clicks);
    }
    
    public void addRightClick() {
        rightClicks.add(System.currentTimeMillis());
        cleanOldClicks(rightClicks);
    }
    
    private void cleanOldClicks(List<Long> clickList) {
        long currentTime = System.currentTimeMillis();
        clickList.removeIf(clickTime -> currentTime - clickTime > 1000);
    }
    
    public int getLeftCPS() {
        cleanOldClicks(clicks);
        return clicks.size();
    }
    
    public int getRightCPS() {
        cleanOldClicks(rightClicks);
        return rightClicks.size();
    }
    
    public int getTotalCPS() {
        return getLeftCPS() + getRightCPS();
    }
}
