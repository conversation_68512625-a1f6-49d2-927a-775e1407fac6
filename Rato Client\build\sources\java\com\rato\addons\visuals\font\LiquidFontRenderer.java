package com.rato.addons.visuals.font;

import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.FontRenderer;
import net.minecraft.client.renderer.GlStateManager;
import org.lwjgl.opengl.GL11;

import java.awt.*;

/**
 * Sistema de fonte customizada baseado no LiquidBounce
 * Versão simplificada para Minecraft 1.8.9
 */
public class LiquidFontRenderer {

    // Fontes padrão melhoradas (estilo LiquidBounce)
    public static final LiquidFontRenderer DEFAULT = new LiquidFontRenderer("Segoe UI", Font.PLAIN, 16);
    public static final LiquidFontRenderer BOLD = new LiquidFontRenderer("Segoe UI", Font.BOLD, 16);
    public static final LiquidFontRenderer LARGE = new LiquidFontRenderer("Segoe UI", Font.PLAIN, 20);

    private final Font font;
    private final int fontHeight;

    public LiquidFontRenderer(String fontName, int style, int size) {
        this.font = new Font(fontName, style, size);
        this.fontHeight = size + 2; // Aproximação
    }

    /**
     * Desenha uma string com a fonte customizada (estilo LiquidBounce) com suporte
     * a cores do Minecraft
     */
    public float drawString(String text, float x, float y, int color, boolean shadow) {
        if (text == null || text.isEmpty())
            return x;

        // Configurar OpenGL para renderização suave
        GlStateManager.pushMatrix();
        GlStateManager.enableBlend();
        GlStateManager.blendFunc(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA);

        // Usar FontRenderer padrão do Minecraft com melhorias
        FontRenderer mcFont = Minecraft.getMinecraft().fontRendererObj;

        float result = x;

        if (shadow) {
            // Sombra com transparência (estilo LiquidBounce)
            int shadowColor = (color & 0xFF000000) != 0 ? ((color >> 24 & 0xFF) / 4 << 24) : 0x40000000;
            result = mcFont.drawString(text, (int) (x + 1), (int) (y + 1), shadowColor, false);
        }

        // Texto principal com suporte a cores do Minecraft
        result = mcFont.drawString(text, (int) x, (int) y, color, false);

        GlStateManager.disableBlend();
        GlStateManager.popMatrix();

        return result;
    }

    /**
     * Desenha uma string sem sombra
     */
    public float drawString(String text, float x, float y, int color) {
        return drawString(text, x, y, color, false);
    }

    /**
     * Obtém a largura de uma string
     */
    public int getStringWidth(String text) {
        if (text == null || text.isEmpty())
            return 0;
        return Minecraft.getMinecraft().fontRendererObj.getStringWidth(text);
    }

    /**
     * Obtém a altura da fonte
     */
    public int getHeight() {
        return fontHeight;
    }

    /**
     * Desenha texto centralizado (estilo LiquidBounce)
     */
    public void drawCenteredString(String text, float x, float y, int color, boolean shadow) {
        float width = getStringWidth(text);
        drawString(text, x - width / 2f, y, color, shadow);
    }

    /**
     * Desenha texto com escala (estilo LiquidBounce)
     */
    public void drawStringWithScale(String text, float x, float y, int color, boolean shadow, float scale) {
        GlStateManager.pushMatrix();
        GlStateManager.scale(scale, scale, 1.0f);

        drawString(text, x / scale, y / scale, color, shadow);

        GlStateManager.popMatrix();
    }

    /**
     * Desenha texto com outline (estilo LiquidBounce)
     */
    public void drawStringWithOutline(String text, float x, float y, int color, int outlineColor) {
        // Desenhar outline em 8 direções
        drawString(text, x - 1, y - 1, outlineColor, false);
        drawString(text, x, y - 1, outlineColor, false);
        drawString(text, x + 1, y - 1, outlineColor, false);
        drawString(text, x - 1, y, outlineColor, false);
        drawString(text, x + 1, y, outlineColor, false);
        drawString(text, x - 1, y + 1, outlineColor, false);
        drawString(text, x, y + 1, outlineColor, false);
        drawString(text, x + 1, y + 1, outlineColor, false);

        // Desenhar texto principal
        drawString(text, x, y, color, false);
    }
}
