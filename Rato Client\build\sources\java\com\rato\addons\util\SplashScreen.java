package com.rato.addons.util;

import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.Gui;
import net.minecraft.client.gui.ScaledResolution;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.util.ResourceLocation;
import org.lwjgl.opengl.GL11;

public class SplashScreen {
    
    private static final ResourceLocation RAT_LOGO = new ResourceLocation("ratoaddons", "textures/rat_loading.png");
    private static final Minecraft mc = Minecraft.getMinecraft();
    
    public static void drawSplashScreen() {
        if (mc.getTextureManager() == null) return;
        
        try {
            ScaledResolution sr = new ScaledResolution(mc);
            int width = sr.getScaledWidth();
            int height = sr.getScaledHeight();
            
            // Clear screen
            GlStateManager.clear(GL11.GL_COLOR_BUFFER_BIT);
            
            // Set background color (dark theme)
            GlStateManager.clearColor(0.1f, 0.1f, 0.1f, 1.0f);
            
            // Enable blending for transparency
            GlStateManager.enableBlend();
            GlStateManager.blendFunc(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA);
            
            // Bind and draw the rat image
            mc.getTextureManager().bindTexture(RAT_LOGO);
            
            // Calculate image position (centered)
            int imageSize = 128;
            int x = (width - imageSize) / 2;
            int y = (height - imageSize) / 2 - 20;
            
            // Draw the rat image
            Gui.drawModalRectWithCustomSizedTexture(x, y, 0, 0, imageSize, imageSize, imageSize, imageSize);
            
            // Draw loading text
            String loadingText = "§6§lRatoAddons §r§7Loading...";
            int textWidth = mc.fontRendererObj.getStringWidth(loadingText);
            int textX = (width - textWidth) / 2;
            int textY = y + imageSize + 20;
            
            mc.fontRendererObj.drawStringWithShadow(loadingText, textX, textY, 0xFFFFFF);
            
            // Draw version
            String version = "§7v1.0.0";
            int versionWidth = mc.fontRendererObj.getStringWidth(version);
            int versionX = (width - versionWidth) / 2;
            int versionY = textY + 15;
            
            mc.fontRendererObj.drawStringWithShadow(version, versionX, versionY, 0x888888);
            
            GlStateManager.disableBlend();
            
        } catch (Exception e) {
            // Fallback to text-only splash
            drawTextOnlySplash();
        }
    }
    
    private static void drawTextOnlySplash() {
        ScaledResolution sr = new ScaledResolution(mc);
        int width = sr.getScaledWidth();
        int height = sr.getScaledHeight();
        
        String title = "§6§lRatoAddons";
        String subtitle = "§7Loading your custom features...";
        
        int titleWidth = mc.fontRendererObj.getStringWidth(title);
        int subtitleWidth = mc.fontRendererObj.getStringWidth(subtitle);
        
        int titleX = (width - titleWidth) / 2;
        int subtitleX = (width - subtitleWidth) / 2;
        int centerY = height / 2;
        
        mc.fontRendererObj.drawStringWithShadow(title, titleX, centerY - 10, 0xFFFFFF);
        mc.fontRendererObj.drawStringWithShadow(subtitle, subtitleX, centerY + 5, 0x888888);
    }
}
