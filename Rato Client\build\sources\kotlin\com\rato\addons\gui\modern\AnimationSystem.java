package com.rato.addons.gui.modern;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Advanced Animation System with Easing Functions
 * Provides smooth, professional animations for UI elements
 */
public class AnimationSystem {
    private static final Map<String, Animation> animations = new ConcurrentHashMap<>();
    
    /**
     * Animation class to track individual animations
     */
    public static class Animation {
        private final String id;
        private final float startValue;
        private final float endValue;
        private final long duration;
        private final EasingType easingType;
        private final long startTime;
        private boolean completed = false;
        private float currentValue;
        
        public Animation(String id, float startValue, float endValue, long duration, EasingType easingType) {
            this.id = id;
            this.startValue = startValue;
            this.endValue = endValue;
            this.duration = duration;
            this.easingType = easingType;
            this.startTime = System.currentTimeMillis();
            this.currentValue = startValue;
        }
        
        public void update() {
            if (completed) return;
            
            long elapsed = System.currentTimeMillis() - startTime;
            float progress = Math.min(1.0f, (float) elapsed / duration);
            
            if (progress >= 1.0f) {
                currentValue = endValue;
                completed = true;
            } else {
                float easedProgress = applyEasing(progress, easingType);
                currentValue = startValue + (endValue - startValue) * easedProgress;
            }
        }
        
        public float getValue() {
            return currentValue;
        }
        
        public boolean isCompleted() {
            return completed;
        }
        
        public String getId() {
            return id;
        }
    }
    
    /**
     * Easing types for different animation feels
     */
    public enum EasingType {
        LINEAR,
        EASE_IN_QUAD, EASE_OUT_QUAD, EASE_IN_OUT_QUAD,
        EASE_IN_CUBIC, EASE_OUT_CUBIC, EASE_IN_OUT_CUBIC,
        EASE_IN_QUART, EASE_OUT_QUART, EASE_IN_OUT_QUART,
        EASE_IN_QUINT, EASE_OUT_QUINT, EASE_IN_OUT_QUINT,
        EASE_IN_SINE, EASE_OUT_SINE, EASE_IN_OUT_SINE,
        EASE_IN_EXPO, EASE_OUT_EXPO, EASE_IN_OUT_EXPO,
        EASE_IN_CIRC, EASE_OUT_CIRC, EASE_IN_OUT_CIRC,
        EASE_IN_BACK, EASE_OUT_BACK, EASE_IN_OUT_BACK,
        EASE_IN_ELASTIC, EASE_OUT_ELASTIC, EASE_IN_OUT_ELASTIC,
        EASE_IN_BOUNCE, EASE_OUT_BOUNCE, EASE_IN_OUT_BOUNCE
    }
    
    /**
     * Start a new animation
     */
    public static void animate(String id, float startValue, float endValue, long duration, EasingType easingType) {
        animations.put(id, new Animation(id, startValue, endValue, duration, easingType));
    }
    
    /**
     * Start animation with default easing (ease out cubic)
     */
    public static void animate(String id, float startValue, float endValue, long duration) {
        animate(id, startValue, endValue, duration, EasingType.EASE_OUT_CUBIC);
    }
    
    /**
     * Get current animation value
     */
    public static float getValue(String id) {
        Animation animation = animations.get(id);
        return animation != null ? animation.getValue() : 0.0f;
    }
    
    /**
     * Get current animation value with fallback
     */
    public static float getValue(String id, float fallback) {
        Animation animation = animations.get(id);
        return animation != null ? animation.getValue() : fallback;
    }
    
    /**
     * Check if animation is completed
     */
    public static boolean isCompleted(String id) {
        Animation animation = animations.get(id);
        return animation == null || animation.isCompleted();
    }
    
    /**
     * Check if animation exists and is running
     */
    public static boolean isRunning(String id) {
        Animation animation = animations.get(id);
        return animation != null && !animation.isCompleted();
    }
    
    /**
     * Stop an animation
     */
    public static void stop(String id) {
        animations.remove(id);
    }
    
    /**
     * Update all animations (call this every frame)
     */
    public static void updateAll() {
        animations.values().removeIf(animation -> {
            animation.update();
            return animation.isCompleted();
        });
    }
    
    /**
     * Apply easing function to progress value
     */
    private static float applyEasing(float t, EasingType type) {
        switch (type) {
            case LINEAR:
                return t;
                
            // Quadratic
            case EASE_IN_QUAD:
                return t * t;
            case EASE_OUT_QUAD:
                return t * (2 - t);
            case EASE_IN_OUT_QUAD:
                return t < 0.5f ? 2 * t * t : -1 + (4 - 2 * t) * t;
                
            // Cubic
            case EASE_IN_CUBIC:
                return t * t * t;
            case EASE_OUT_CUBIC:
                return (--t) * t * t + 1;
            case EASE_IN_OUT_CUBIC:
                return t < 0.5f ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
                
            // Quartic
            case EASE_IN_QUART:
                return t * t * t * t;
            case EASE_OUT_QUART:
                return 1 - (--t) * t * t * t;
            case EASE_IN_OUT_QUART:
                return t < 0.5f ? 8 * t * t * t * t : 1 - 8 * (--t) * t * t * t;
                
            // Quintic
            case EASE_IN_QUINT:
                return t * t * t * t * t;
            case EASE_OUT_QUINT:
                return 1 + (--t) * t * t * t * t;
            case EASE_IN_OUT_QUINT:
                return t < 0.5f ? 16 * t * t * t * t * t : 1 + 16 * (--t) * t * t * t * t;
                
            // Sine
            case EASE_IN_SINE:
                return 1 - (float) Math.cos(t * Math.PI / 2);
            case EASE_OUT_SINE:
                return (float) Math.sin(t * Math.PI / 2);
            case EASE_IN_OUT_SINE:
                return -(float) (Math.cos(Math.PI * t) - 1) / 2;
                
            // Exponential
            case EASE_IN_EXPO:
                return t == 0 ? 0 : (float) Math.pow(2, 10 * (t - 1));
            case EASE_OUT_EXPO:
                return t == 1 ? 1 : 1 - (float) Math.pow(2, -10 * t);
            case EASE_IN_OUT_EXPO:
                if (t == 0) return 0;
                if (t == 1) return 1;
                if (t < 0.5f) return (float) Math.pow(2, 20 * t - 10) / 2;
                return (2 - (float) Math.pow(2, -20 * t + 10)) / 2;
                
            // Circular
            case EASE_IN_CIRC:
                return 1 - (float) Math.sqrt(1 - t * t);
            case EASE_OUT_CIRC:
                return (float) Math.sqrt(1 - (--t) * t);
            case EASE_IN_OUT_CIRC:
                return t < 0.5f
                    ? (1 - (float) Math.sqrt(1 - 4 * t * t)) / 2
                    : ((float) Math.sqrt(1 - (-2 * t + 2) * (-2 * t + 2)) + 1) / 2;
                
            // Back
            case EASE_IN_BACK:
                float c1 = 1.70158f;
                float c3 = c1 + 1;
                return c3 * t * t * t - c1 * t * t;
            case EASE_OUT_BACK:
                c1 = 1.70158f;
                c3 = c1 + 1;
                return 1 + c3 * (float) Math.pow(t - 1, 3) + c1 * (float) Math.pow(t - 1, 2);
            case EASE_IN_OUT_BACK:
                c1 = 1.70158f;
                float c2 = c1 * 1.525f;
                return t < 0.5f
                    ? ((float) Math.pow(2 * t, 2) * ((c2 + 1) * 2 * t - c2)) / 2
                    : ((float) Math.pow(2 * t - 2, 2) * ((c2 + 1) * (t * 2 - 2) + c2) + 2) / 2;
                
            // Elastic
            case EASE_IN_ELASTIC:
                c1 = (2 * (float) Math.PI) / 3;
                return t == 0 ? 0 : t == 1 ? 1 : -(float) Math.pow(2, 10 * t - 10) * (float) Math.sin((t * 10 - 10.75f) * c1);
            case EASE_OUT_ELASTIC:
                c1 = (2 * (float) Math.PI) / 3;
                return t == 0 ? 0 : t == 1 ? 1 : (float) Math.pow(2, -10 * t) * (float) Math.sin((t * 10 - 0.75f) * c1) + 1;
            case EASE_IN_OUT_ELASTIC:
                c1 = (2 * (float) Math.PI) / 4.5f;
                return t == 0 ? 0 : t == 1 ? 1 : t < 0.5f
                    ? -((float) Math.pow(2, 20 * t - 10) * (float) Math.sin((20 * t - 11.125f) * c1)) / 2
                    : ((float) Math.pow(2, -20 * t + 10) * (float) Math.sin((20 * t - 11.125f) * c1)) / 2 + 1;
                
            // Bounce
            case EASE_IN_BOUNCE:
                return 1 - bounceOut(1 - t);
            case EASE_OUT_BOUNCE:
                return bounceOut(t);
            case EASE_IN_OUT_BOUNCE:
                return t < 0.5f
                    ? (1 - bounceOut(1 - 2 * t)) / 2
                    : (1 + bounceOut(2 * t - 1)) / 2;
                
            default:
                return t;
        }
    }
    
    /**
     * Helper function for bounce easing
     */
    private static float bounceOut(float t) {
        float n1 = 7.5625f;
        float d1 = 2.75f;
        
        if (t < 1 / d1) {
            return n1 * t * t;
        } else if (t < 2 / d1) {
            return n1 * (t -= 1.5f / d1) * t + 0.75f;
        } else if (t < 2.5 / d1) {
            return n1 * (t -= 2.25f / d1) * t + 0.9375f;
        } else {
            return n1 * (t -= 2.625f / d1) * t + 0.984375f;
        }
    }
    
    /**
     * Convenience methods for common animations
     */
    public static void fadeIn(String id, long duration) {
        animate(id, 0.0f, 1.0f, duration, EasingType.EASE_OUT_CUBIC);
    }
    
    public static void fadeOut(String id, long duration) {
        animate(id, 1.0f, 0.0f, duration, EasingType.EASE_IN_CUBIC);
    }
    
    public static void slideIn(String id, float distance, long duration) {
        animate(id, -distance, 0.0f, duration, EasingType.EASE_OUT_BACK);
    }
    
    public static void slideOut(String id, float distance, long duration) {
        animate(id, 0.0f, distance, duration, EasingType.EASE_IN_BACK);
    }
    
    public static void scaleIn(String id, long duration) {
        animate(id, 0.0f, 1.0f, duration, EasingType.EASE_OUT_ELASTIC);
    }
    
    public static void scaleOut(String id, long duration) {
        animate(id, 1.0f, 0.0f, duration, EasingType.EASE_IN_ELASTIC);
    }
    
    /**
     * Clear all animations
     */
    public static void clearAll() {
        animations.clear();
    }
    
    /**
     * Get animation count (for debugging)
     */
    public static int getAnimationCount() {
        return animations.size();
    }
}
