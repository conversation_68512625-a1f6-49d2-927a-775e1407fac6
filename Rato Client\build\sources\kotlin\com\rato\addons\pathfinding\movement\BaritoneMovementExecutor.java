package com.rato.addons.pathfinding.movement;

import com.rato.addons.config.RatoAddonsConfigSimple;
import com.rato.addons.pathfinding.BaritoneStylePathfinder;
import com.rato.addons.util.Logger;
import net.minecraft.block.Block;
import net.minecraft.client.Minecraft;
import net.minecraft.client.settings.KeyBinding;
import net.minecraft.util.BlockPos;
import net.minecraft.util.MathHelper;
import net.minecraft.util.Vec3;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;

import java.util.List;

/**
 * Executor de movimento baseado no Baritone - Versão Corrigida
 * Movimento suave como o Baritone: apenas FORWARD + rotação suave
 */
public class BaritoneMovementExecutor {

    private final Minecraft mc = Minecraft.getMinecraft();

    // Estado do movimento
    private List<BaritoneStylePathfinder.BaritoneNode> currentPath;
    private int currentNodeIndex = 0;
    private boolean isExecuting = false;
    private boolean registered = false;

    // Configurações baseadas no Baritone (otimizadas para movimento fluido)
    private static final double NODE_ARRIVAL_THRESHOLD = 1.5; // Mais tolerante
    private static final double DYNAMIC_SKIP_DISTANCE = 3.0; // Pular nodes se muito próximo
    private static final double SPRINT_THRESHOLD = 1.2; // Distância mínima para sprint
    private static final double FINAL_DESTINATION_THRESHOLD = 2.0; // Distância para considerar chegada final

    // Estado atual
    private BlockPos currentTarget;
    private Vec3 lastPlayerPos;
    private int stuckTicks = 0;
    private static final int MAX_STUCK_TICKS = 40; // 2 segundos

    // Controlador de câmera simples e eficaz
    private SimpleCameraController cameraController;

    // Controle de câmera durante combate
    private boolean combatModeActive = false;

    // Controle de pulo melhorado
    private boolean shouldJump = false;
    private int jumpCooldown = 0;
    private int jumpAttempts = 0;
    private static final int JUMP_COOLDOWN_TICKS = 3; // Cooldown reduzido para 3 ticks
    private static final int MAX_JUMP_ATTEMPTS = 5; // Mais tentativas
    private boolean isCurrentlyJumping = false;

    /**
     * Inicia execução do caminho
     */
    public void startExecution(List<BaritoneStylePathfinder.BaritoneNode> path) {
        if (path == null || path.isEmpty()) {
            Logger.sendMessage("§cCaminho vazio!");
            return;
        }

        stopExecution(); // Parar execução anterior

        this.currentPath = path;
        this.currentNodeIndex = 0;
        this.isExecuting = true;
        this.stuckTicks = 0;
        this.lastPlayerPos = mc.thePlayer.getPositionVector();

        // Inicializar controlador de câmera simples com detecção de velocidade
        this.cameraController = new SimpleCameraController();
        this.cameraController.enable();

        // Log de debug para velocidade se necessário
        if (RatoAddonsConfigSimple.pathfindingAutoSpeedDetection) {
            Logger.sendMessage("§7Sistema de detecção de velocidade ativado");
        }
        this.shouldJump = false;
        this.jumpCooldown = 0;

        if (!registered) {
            MinecraftForge.EVENT_BUS.register(this);
            registered = true;
        }

        Logger.sendMessage("§a[BaritoneMovement] Iniciando execução com " + path.size() + " nodes");
        updateCurrentTarget();
    }

    /**
     * Para execução
     */
    public void stopExecution() {
        if (!isExecuting)
            return;

        isExecuting = false;
        currentPath = null;
        currentNodeIndex = 0;
        currentTarget = null;

        // Parar todos os inputs
        releaseAllKeys();

        // Desativar controlador de câmera
        if (cameraController != null) {
            cameraController.disable();
        }

        if (registered) {
            MinecraftForge.EVENT_BUS.unregister(this);
            registered = false;
        }

        Logger.sendMessage("§c[BaritoneMovement] Execução parada");
    }

    /**
     * Tick principal - baseado no Baritone
     */
    @SubscribeEvent
    public void onClientTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.START || !isExecuting || mc.thePlayer == null) {
            return;
        }

        try {
            Vec3 playerPos = mc.thePlayer.getPositionVector();

            // Verificar se chegou ao final
            if (currentNodeIndex >= currentPath.size()) {
                Logger.sendMessage("§a[BaritoneMovement] Caminho completado!");
                completeExecution();
                return;
            }

            // Verificar se chegou muito próximo do destino final
            if (isNearFinalDestination(playerPos)) {
                Logger.sendMessage("§a[BaritoneMovement] Destino alcançado!");
                completeExecution();
                return;
            }

            // Verificar se chegou no node atual ou pode pular dinamicamente
            if (checkNodeCompletion(playerPos)) {
                advanceToNextNode();
                return;
            }

            // Atualizar rotação ultra-suave
            updateCameraRotation(playerPos);

            // Executar movimento estilo Baritone
            executeBaritoneMovement(playerPos);

            // Verificar travamento
            checkStuckDetection(playerPos);

        } catch (Exception e) {
            Logger.sendMessage("§c[BaritoneMovement] Erro: " + e.getMessage());
            stopExecution();
        }
    }

    /**
     * Verifica se completou o node atual ou pode pular dinamicamente
     */
    private boolean checkNodeCompletion(Vec3 playerPos) {
        if (currentTarget == null)
            return false;

        Vec3 targetCenter = new Vec3(currentTarget.getX() + 0.5, currentTarget.getY(), currentTarget.getZ() + 0.5);
        double horizontalDistance = Math.sqrt(
                Math.pow(targetCenter.xCoord - playerPos.xCoord, 2) +
                        Math.pow(targetCenter.zCoord - playerPos.zCoord, 2));
        double verticalDistance = Math.abs(targetCenter.yCoord - playerPos.yCoord);

        // Verificar tipo do node atual para ajustar threshold
        BaritoneStylePathfinder.BaritoneNode currentNode = null;
        if (currentNodeIndex < currentPath.size()) {
            currentNode = currentPath.get(currentNodeIndex);
        }

        // Para nodes ASCEND, SER MUITO MAIS RESTRITIVO - só avançar depois de pular
        if (currentNode != null && currentNode.movementType == MovementType.ASCEND) {
            // Para ASCEND: só considerar que chegou se está MUITO próximo E já pulou
            if (horizontalDistance <= 0.8 && verticalDistance <= 1.0) {
                Logger.sendMessage("§a[DEBUG] §7Node ASCEND completado - distH=" +
                        String.format("%.2f", horizontalDistance) + " distV="
                        + String.format("%.2f", verticalDistance));
                return true; // Só avançar se realmente chegou
            } else {
                // Ainda não chegou no node ASCEND
                return false;
            }
        } else {
            // Para outros tipos: verificação normal
            if (horizontalDistance <= NODE_ARRIVAL_THRESHOLD && verticalDistance <= 2.0) {
                return true;
            }
        }

        // Verificar se passou do node (overshoot como no Baritone)
        if (hasOvershot(playerPos, targetCenter)) {
            return true;
        }

        // DESABILITAR PULO DINÂMICO COMPLETAMENTE QUANDO HÁ NODES ASCEND
        // Verificar se o node atual ou próximos são ASCEND
        if (currentNodeIndex < currentPath.size()) {
            BaritoneStylePathfinder.BaritoneNode checkNode = currentPath.get(currentNodeIndex);
            if (checkNode != null && checkNode.movementType == MovementType.ASCEND) {
                // Se o node atual é ASCEND, NÃO fazer pulo dinâmico
                Logger.sendMessage("§c[DEBUG] §7Node atual é ASCEND - DESABILITANDO pulo dinâmico");
                return false; // Não fazer pulo dinâmico
            }
        }

        // Verificar se há nodes ASCEND próximos
        if (hasAscendNodeInRange(currentNodeIndex, Math.min(currentNodeIndex + 5, currentPath.size()))) {
            Logger.sendMessage("§c[DEBUG] §7Há nodes ASCEND próximos - DESABILITANDO pulo dinâmico");
            return false; // Não fazer pulo dinâmico se há ASCEND próximo
        }

        // Só fazer pulo dinâmico se NÃO há nodes ASCEND próximos
        try {
            for (int i = currentNodeIndex + 1; i < Math.min(currentNodeIndex + 3, currentPath.size()); i++) {
                if (i >= currentPath.size())
                    break; // Verificação extra de segurança

                BaritoneStylePathfinder.BaritoneNode futureNode = currentPath.get(i);
                if (futureNode == null || futureNode.position == null)
                    continue; // Verificação de null

                Vec3 futureTarget = new Vec3(futureNode.position.getX() + 0.5, futureNode.position.getY(),
                        futureNode.position.getZ() + 0.5);
                double distanceToFuture = playerPos.distanceTo(futureTarget);

                if (distanceToFuture <= DYNAMIC_SKIP_DISTANCE) {
                    Logger.sendMessage("§7[BaritoneMovement] Pulando nodes dinamicamente para " + (i + 1));
                    currentNodeIndex = i;
                    updateCurrentTarget();
                    return false;
                }
            }
        } catch (Exception e) {
            // Se houver erro no pulo dinâmico, continuar normalmente
            Logger.sendMessage("§e[BaritoneMovement] Erro no pulo dinâmico: " + e.getMessage());
        }

        return false;
    }

    /**
     * Verifica se há nodes ASCEND no range especificado
     */
    private boolean hasAscendNodeInRange(int startIndex, int endIndex) {
        try {
            for (int i = startIndex; i <= endIndex && i < currentPath.size(); i++) {
                BaritoneStylePathfinder.BaritoneNode node = currentPath.get(i);
                if (node != null && node.movementType == MovementType.ASCEND) {
                    return true; // Há um node ASCEND no range
                }
            }
        } catch (Exception e) {
            // Em caso de erro, assumir que há ASCEND para ser conservativo
            return true;
        }
        return false;
    }

    /**
     * Verifica se passou do target (overshoot)
     */
    private boolean hasOvershot(Vec3 playerPos, Vec3 targetPos) {
        // Se há um próximo node, verificar se está mais próximo dele que do atual
        if (currentNodeIndex + 1 < currentPath.size()) {
            BaritoneStylePathfinder.BaritoneNode nextNode = currentPath.get(currentNodeIndex + 1);
            Vec3 nextTarget = new Vec3(nextNode.position.getX() + 0.5, nextNode.position.getY(),
                    nextNode.position.getZ() + 0.5);

            double distanceToCurrent = playerPos.distanceTo(targetPos);
            double distanceToNext = playerPos.distanceTo(nextTarget);

            // Se está mais próximo do próximo node, passou do atual
            return distanceToNext < distanceToCurrent && distanceToCurrent > NODE_ARRIVAL_THRESHOLD;
        }

        return false;
    }

    /**
     * Avança para o próximo node
     */
    private void advanceToNextNode() {
        currentNodeIndex++;
        if (currentNodeIndex < currentPath.size()) {
            updateCurrentTarget();
            BaritoneStylePathfinder.BaritoneNode node = currentPath.get(currentNodeIndex);

            // Log especial para nodes ASCEND
            if (node.movementType == MovementType.ASCEND) {
                Logger.sendMessage("§6[ASCEND DETECTADO] §7Node " + (currentNodeIndex + 1) + "/" + currentPath.size() +
                        " (" + node.movementType.getName() + ") - DEVE PULAR AQUI!");
            } else {
                Logger.sendMessage("§7[BaritoneMovement] Node " + (currentNodeIndex + 1) + "/" + currentPath.size() +
                        " (" + node.movementType.getName() + ")");
            }
        }
    }

    /**
     * Atualiza o target atual
     */
    private void updateCurrentTarget() {
        try {
            if (currentPath != null && !currentPath.isEmpty() &&
                    currentNodeIndex >= 0 && currentNodeIndex < currentPath.size()) {

                BaritoneStylePathfinder.BaritoneNode node = currentPath.get(currentNodeIndex);
                if (node != null && node.position != null) {
                    currentTarget = node.position;
                } else {
                    Logger.sendMessage("§e[BaritoneMovement] Node ou posição nula no índice " + currentNodeIndex);
                    currentTarget = null;
                }
            } else {
                currentTarget = null;
            }
        } catch (Exception e) {
            Logger.sendMessage("§c[BaritoneMovement] Erro ao atualizar target: " + e.getMessage());
            currentTarget = null;
        }
    }

    /**
     * Executa movimento estilo Baritone - apenas FORWARD (rotação feita pelo
     * SmoothLookBehavior)
     */
    private void executeBaritoneMovement(Vec3 playerPos) {
        if (currentTarget == null) {
            // Se não há target, parar movimento
            releaseAllKeys();
            return;
        }

        Vec3 targetCenter = new Vec3(currentTarget.getX() + 0.5, currentTarget.getY(), currentTarget.getZ() + 0.5);
        double distanceToTarget = playerPos.distanceTo(targetCenter);

        // Só parar se extremamente próximo (jogador real não para cedo)
        if (distanceToTarget <= 0.3) {
            setKeyPressed(mc.gameSettings.keyBindForward, false);
            setKeyPressed(mc.gameSettings.keyBindSprint, false);
            return;
        }

        // Calcular yaw necessário para o target
        float targetYaw = calculateYawToTarget(playerPos, targetCenter);

        // SEMPRE ANDAR - jogador real não para para virar
        setKeyPressed(mc.gameSettings.keyBindForward, true);

        // Sprint agressivo - priorizar velocidade
        boolean shouldSprint = distanceToTarget > 0.5 && // Threshold muito baixo
                !mc.thePlayer.isInWater() &&
                mc.thePlayer.onGround;
        setKeyPressed(mc.gameSettings.keyBindSprint, shouldSprint);

        // Controle de pulo inteligente
        handleJumpLogic(true); // Sempre pode pular se necessário

        // Reduzir cooldown de pulo
        if (jumpCooldown > 0) {
            jumpCooldown--;
        }
    }

    /**
     * Controla lógica de pulo inteligente (sem pulo infinito)
     */
    private void handleJumpLogic(boolean canMove) {
        if (currentNodeIndex >= currentPath.size() || currentPath.isEmpty())
            return;

        try {
            BaritoneStylePathfinder.BaritoneNode currentNode = currentPath.get(currentNodeIndex);
            if (currentNode == null)
                return;

            Vec3 playerPos = mc.thePlayer.getPositionVector();

            // LÓGICA MELHORADA: Detectar quando precisa pular
            boolean needsJump = shouldJumpNow(currentNode, playerPos);

            if (needsJump) {
                // Executar pulo de forma mais agressiva
                if (mc.thePlayer.onGround && jumpCooldown <= 0) {
                    // Pulo principal
                    setKeyPressed(mc.gameSettings.keyBindJump, true);
                    jumpCooldown = JUMP_COOLDOWN_TICKS;
                    jumpAttempts++;
                    isCurrentlyJumping = true;

                    Logger.sendMessage("§a[PULO EXECUTADO!] §7Tipo: " + currentNode.movementType.getName() +
                            " §7em §f"
                            + String.format("%.1f,%.1f,%.1f", playerPos.xCoord, playerPos.yCoord, playerPos.zCoord) +
                            " §7-> §f" + String.format("%d,%d,%d",
                                    currentTarget.getX(), currentTarget.getY(), currentTarget.getZ()));
                } else if (jumpCooldown <= 0) {
                    // Continuar pressionando pulo mesmo se não está no chão (para pulos
                    // consecutivos)
                    setKeyPressed(mc.gameSettings.keyBindJump, true);

                    if (jumpAttempts == 0) {
                        Logger.sendMessage("§e[TENTANDO PULAR] §7onGround=" + mc.thePlayer.onGround +
                                " cooldown=" + jumpCooldown + " tipo=" + currentNode.movementType.getName());
                        jumpAttempts = 1;
                    }
                }

                // Se tentou muitas vezes, pular para próximo node
                if (jumpAttempts >= MAX_JUMP_ATTEMPTS) {
                    Logger.sendMessage("§c[BaritoneMovement] Muitas tentativas de pulo, avançando node");
                    advanceToNextNode();
                    jumpAttempts = 0;
                    isCurrentlyJumping = false;
                }
            } else {
                // Não precisa pular - liberar tecla
                setKeyPressed(mc.gameSettings.keyBindJump, false);
                shouldJump = false;
                jumpAttempts = 0;
                isCurrentlyJumping = false;
            }

        } catch (Exception e) {
            // Se houver erro na lógica de pulo, continuar sem pular
            setKeyPressed(mc.gameSettings.keyBindJump, false);
            isCurrentlyJumping = false;
            Logger.sendMessage("§e[BaritoneMovement] Erro na lógica de pulo: " + e.getMessage());
        }
    }

    /**
     * Determina se deve pular agora baseado no node atual e posição
     */
    private boolean shouldJumpNow(BaritoneStylePathfinder.BaritoneNode currentNode, Vec3 playerPos) {
        // VERIFICAÇÃO CRÍTICA: Se há slabs envolvidos, NÃO pular
        if (currentTarget != null) {
            BlockPos playerBlock = new BlockPos(playerPos);
            BlockPos targetBlock = new BlockPos(currentTarget);

            Block playerGroundBlock = mc.theWorld.getBlockState(playerBlock.down()).getBlock();
            Block targetGroundBlock = mc.theWorld.getBlockState(targetBlock.down()).getBlock();
            boolean hasSlabs = (playerGroundBlock instanceof net.minecraft.block.BlockSlab) ||
                    (targetGroundBlock instanceof net.minecraft.block.BlockSlab);

            if (hasSlabs) {
                Logger.sendMessage("§7[DEBUG] Slabs detectadas - CANCELANDO pulo");
                return false;
            }
        }

        // 1. Se é node ASCEND, verificar se realmente precisa pular
        if (currentNode.movementType == MovementType.ASCEND) {
            // Mesmo para ASCEND, verificar se não é movimento natural com slabs
            if (currentTarget != null) {
                double heightDiff = currentTarget.getY() - playerPos.yCoord;
                if (heightDiff <= 0.8) { // Movimento natural
                    Logger.sendMessage("§7[DEBUG] ASCEND com altura baixa - movimento natural");
                    return false;
                }
            }
            return true;
        }

        // 2. Verificar se há obstáculo na frente que requer pulo (mas não para slabs)
        if (hasObstacleAhead(playerPos)) {
            return true;
        }

        // 3. Verificar se o target está acima e precisa pular para alcançar
        if (currentTarget != null) {
            double heightDiff = currentTarget.getY() - playerPos.yCoord;
            if (heightDiff > 0.8) { // Aumentado de 0.5 para 0.8 para ser mais restritivo
                return true;
            }
        }

        return false;
    }

    /**
     * Verifica se há obstáculo na frente que requer pulo
     */
    private boolean hasObstacleAhead(Vec3 playerPos) {
        if (currentTarget == null || mc.theWorld == null)
            return false;

        // VERIFICAÇÃO CRÍTICA: Se há slabs envolvidos, NÃO considerar obstáculos
        BlockPos playerBlock = new BlockPos(playerPos);
        BlockPos targetBlock = new BlockPos(currentTarget);

        Block playerGroundBlock = mc.theWorld.getBlockState(playerBlock.down()).getBlock();
        Block targetGroundBlock = mc.theWorld.getBlockState(targetBlock.down()).getBlock();
        boolean hasSlabs = (playerGroundBlock instanceof net.minecraft.block.BlockSlab) ||
                (targetGroundBlock instanceof net.minecraft.block.BlockSlab);

        if (hasSlabs) {
            Logger.sendMessage("§7[DEBUG] Slabs detectadas - ignorando obstáculos à frente");
            return false;
        }

        // Calcular direção para o target
        Vec3 direction = new Vec3(
                currentTarget.getX() + 0.5 - playerPos.xCoord,
                0,
                currentTarget.getZ() + 0.5 - playerPos.zCoord).normalize();

        // Verificar bloco na frente do jogador
        BlockPos frontPos = new BlockPos(
                playerPos.xCoord + direction.xCoord,
                playerPos.yCoord,
                playerPos.zCoord + direction.zCoord);

        Block frontBlock = mc.theWorld.getBlockState(frontPos).getBlock();

        // Se o bloco à frente é uma slab, NÃO é obstáculo
        if (frontBlock instanceof net.minecraft.block.BlockSlab) {
            Logger.sendMessage("§7[DEBUG] Slab à frente - NÃO é obstáculo");
            return false;
        }

        // Se há bloco sólido na altura do jogador, precisa pular
        return !MovementType.canWalkThrough(frontPos, mc);
    }

    /**
     * Verifica se realmente precisa pular para o node atual
     */
    private boolean needsJumpForCurrentNode() {
        if (currentTarget == null || mc.theWorld == null)
            return false;

        // Se o node atual é ASCEND, SEMPRE precisa pular
        if (currentNodeIndex < currentPath.size()) {
            BaritoneStylePathfinder.BaritoneNode currentNode = currentPath.get(currentNodeIndex);
            if (currentNode != null && currentNode.movementType == MovementType.ASCEND) {
                Vec3 playerPos = mc.thePlayer.getPositionVector();
                Logger.sendMessage("§6[Debug] §7Node ASCEND detectado em §f" +
                        String.format("%d,%d,%d", currentTarget.getX(), currentTarget.getY(), currentTarget.getZ()) +
                        " §7- Player em §f"
                        + String.format("%.1f,%.1f,%.1f", playerPos.xCoord, playerPos.yCoord, playerPos.zCoord));
                return true; // Node ASCEND sempre requer pulo
            }
        }

        Vec3 playerPos = mc.thePlayer.getPositionVector();
        BlockPos playerBlock = new BlockPos(playerPos);
        BlockPos targetBlock = new BlockPos(currentTarget);

        // Verificar se há slabs envolvidos
        Block playerGroundBlock = mc.theWorld.getBlockState(playerBlock.down()).getBlock();
        Block targetGroundBlock = mc.theWorld.getBlockState(targetBlock.down()).getBlock();
        boolean hasSlabs = (playerGroundBlock instanceof net.minecraft.block.BlockSlab) ||
                (targetGroundBlock instanceof net.minecraft.block.BlockSlab);

        // 1. Verificar se target está acima (movimento vertical)
        double playerY = playerPos.yCoord;
        double targetY = currentTarget.getY();

        // Para slabs, ser mais permissivo com a diferença de altura
        double heightThreshold = hasSlabs ? 0.8 : 0.5;
        boolean targetAbove = targetY > playerY + heightThreshold;

        // 2. Verificar se há obstáculo no caminho horizontal que requer pulo (mas não
        // para slabs)
        boolean hasObstacle = !hasSlabs && hasObstacleRequiringJump(playerBlock, targetBlock);

        // 3. Verificar se é movimento para subir bloco/slab (mas não para slabs)
        boolean needsStepUp = !hasSlabs && needsStepUpMovement(playerBlock, targetBlock);

        return targetAbove || hasObstacle || needsStepUp;
    }

    /**
     * Verifica se há obstáculo no caminho que requer pulo
     */
    private boolean hasObstacleRequiringJump(BlockPos from, BlockPos to) {
        if (mc.theWorld == null)
            return false;

        // Calcular direção do movimento
        int dx = to.getX() - from.getX();
        int dz = to.getZ() - from.getZ();

        // Normalizar para obter direção unitária
        int stepX = dx == 0 ? 0 : (dx > 0 ? 1 : -1);
        int stepZ = dz == 0 ? 0 : (dz > 0 ? 1 : -1);

        // Verificar posição imediatamente à frente
        BlockPos checkPos = from.add(stepX, 0, stepZ);
        Block obstacleBlock = mc.theWorld.getBlockState(checkPos).getBlock();

        // Se há bloco sólido no caminho
        if (isBlockSolid(obstacleBlock)) {
            // Verificar se pode pular por cima
            Block aboveObstacle = mc.theWorld.getBlockState(checkPos.up()).getBlock();
            Block aboveAbove = mc.theWorld.getBlockState(checkPos.up(2)).getBlock();

            // Precisa pular se há espaço acima do obstáculo
            return isBlockPassable(aboveObstacle) && isBlockPassable(aboveAbove);
        }

        return false;
    }

    /**
     * Verifica se precisa de movimento de subida (step up)
     */
    private boolean needsStepUpMovement(BlockPos from, BlockPos to) {
        if (mc.theWorld == null)
            return false;

        // Verificar se destino está 1 bloco acima
        int yDiff = to.getY() - from.getY();
        if (yDiff != 1)
            return false;

        // Verificar se há chão sólido no destino
        Block toGround = mc.theWorld.getBlockState(to.down()).getBlock();
        Block fromGround = mc.theWorld.getBlockState(from.down()).getBlock();

        return isBlockSolid(toGround) && isBlockSolid(fromGround);
    }

    /**
     * Verifica se bloco é sólido
     */
    private boolean isBlockSolid(Block block) {
        return block != null &&
                block != net.minecraft.init.Blocks.air &&
                block.getMaterial().isSolid() &&
                block.isFullBlock();
    }

    /**
     * Verifica se bloco é passável
     */
    private boolean isBlockPassable(Block block) {
        return block == null ||
                block == net.minecraft.init.Blocks.air ||
                !block.getMaterial().isSolid() ||
                block.getMaterial().isReplaceable();
    }

    /**
     * Detecção de travamento
     */
    private void checkStuckDetection(Vec3 currentPos) {
        if (lastPlayerPos != null) {
            double movement = currentPos.distanceTo(lastPlayerPos);

            if (movement < 0.1) {
                stuckTicks++;
                if (stuckTicks >= MAX_STUCK_TICKS) {
                    Logger.sendMessage("§e[BaritoneMovement] Travamento detectado, tentando contornar...");
                    handleStuckSituation();
                    stuckTicks = 0;
                }
            } else {
                stuckTicks = 0;
            }
        }

        lastPlayerPos = currentPos;
    }

    /**
     * Lida com travamento
     */
    private void handleStuckSituation() {
        // Pular para tentar sair
        if (mc.thePlayer.onGround) {
            setKeyPressed(mc.gameSettings.keyBindJump, true);
        }

        // Tentar pular para o próximo node
        if (currentNodeIndex + 1 < currentPath.size()) {
            currentNodeIndex++;
            updateCurrentTarget();
            Logger.sendMessage("§7[BaritoneMovement] Pulando para próximo node devido a travamento");
        }
    }

    /**
     * Define estado de uma tecla (método melhorado)
     */
    private void setKeyPressed(KeyBinding key, boolean pressed) {
        try {
            // Forçar atualização do estado
            KeyBinding.setKeyBindState(key.getKeyCode(), pressed);

            // Se estamos pressionando, simular o tick
            if (pressed) {
                key.onTick(key.getKeyCode());

                // Para pulo, garantir que seja registrado
                if (key == mc.gameSettings.keyBindJump) {
                    // Debug para pulo
                    Logger.sendMessage("§7[DEBUG] Pulo pressionado - onGround: " + mc.thePlayer.onGround);
                }
            } else {
                // Liberar tecla - não precisamos acessar o campo privado
                // O KeyBinding.setKeyBindState já cuida disso
            }
        } catch (Exception e) {
            Logger.sendMessage("§c[BaritoneMovement] Erro ao definir tecla: " + e.getMessage());
        }
    }

    /**
     * Libera todas as teclas (método melhorado para garantir parada)
     */
    private void releaseAllKeys() {
        // Liberar todas as teclas de movimento
        KeyBinding.setKeyBindState(mc.gameSettings.keyBindForward.getKeyCode(), false);
        KeyBinding.setKeyBindState(mc.gameSettings.keyBindBack.getKeyCode(), false);
        KeyBinding.setKeyBindState(mc.gameSettings.keyBindLeft.getKeyCode(), false);
        KeyBinding.setKeyBindState(mc.gameSettings.keyBindRight.getKeyCode(), false);
        KeyBinding.setKeyBindState(mc.gameSettings.keyBindJump.getKeyCode(), false);
        KeyBinding.setKeyBindState(mc.gameSettings.keyBindSprint.getKeyCode(), false);
        KeyBinding.setKeyBindState(mc.gameSettings.keyBindSneak.getKeyCode(), false);

        // Aguardar um tick para garantir que as teclas sejam liberadas
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            // Ignore
        }
    }

    /**
     * Verifica se chegou próximo do destino final
     */
    private boolean isNearFinalDestination(Vec3 playerPos) {
        if (currentPath == null || currentPath.isEmpty())
            return false;

        // Pegar o último node do caminho
        BaritoneStylePathfinder.BaritoneNode finalNode = currentPath.get(currentPath.size() - 1);
        Vec3 finalDestination = new Vec3(finalNode.position.getX() + 0.5, finalNode.position.getY(),
                finalNode.position.getZ() + 0.5);

        double distanceToFinal = playerPos.distanceTo(finalDestination);
        return distanceToFinal <= FINAL_DESTINATION_THRESHOLD;
    }

    /**
     * Completa a execução e limpa tudo
     */
    private void completeExecution() {
        // Parar todos os inputs imediatamente (múltiplas vezes para garantir)
        for (int i = 0; i < 3; i++) {
            releaseAllKeys();
        }

        // Marcar como não executando
        isExecuting = false;

        // Limpar dados do caminho
        currentPath = null;
        currentNodeIndex = 0;
        currentTarget = null;
        stuckTicks = 0;
        lastPlayerPos = null;

        // Notificar o MovementManager para limpar renderização
        notifyExecutionComplete();

        // Desregistrar eventos
        if (registered) {
            MinecraftForge.EVENT_BUS.unregister(this);
            registered = false;
        }

        // Aguardar um tick e liberar teclas novamente (garantia extra)
        new Thread(() -> {
            try {
                Thread.sleep(50); // 50ms = ~1 tick
                releaseAllKeys();
            } catch (InterruptedException e) {
                // Ignore
            }
        }).start();

        Logger.sendMessage("§a[BaritoneMovement] Execução completada e limpa!");
    }

    /**
     * Notifica que a execução foi completada (para limpar renderização)
     */
    private void notifyExecutionComplete() {
        // Usar o MovementManager para limpar a renderização
        try {
            com.rato.addons.pathfinding.movement.MovementManager.getInstance().clearRenderingAfterCompletion();
        } catch (Exception e) {
            // Fallback silencioso
        }
    }

    /**
     * Atualiza rotação da câmera com lookahead (olha mais à frente)
     * DESABILITADO durante combate para evitar conflito com aimbot
     */
    private void updateCameraRotation(Vec3 playerPos) {
        if (currentTarget == null || cameraController == null)
            return;

        // NÃO CONTROLAR CÂMERA DURANTE COMBATE
        if (combatModeActive) {
            return; // Deixar o aimbot controlar a câmera
        }

        // Calcular ponto de lookahead (olhar mais à frente no caminho)
        Vec3 lookaheadTarget = calculateLookaheadTarget(playerPos);
        float targetYaw = calculateYawToTarget(playerPos, lookaheadTarget);

        // Definir target no controlador de câmera
        cameraController.setTargetYaw(targetYaw);
    }

    /**
     * Calcula ponto de lookahead para câmera mais natural
     */
    private Vec3 calculateLookaheadTarget(Vec3 playerPos) {
        if (currentPath == null || currentNodeIndex >= currentPath.size()) {
            return new Vec3(currentTarget.getX() + 0.5, currentTarget.getY(), currentTarget.getZ() + 0.5);
        }

        // Distância de lookahead baseada na velocidade
        double lookaheadDistance = mc.thePlayer.isSprinting() ? 8.0 : 5.0;

        // Procurar node mais à frente dentro da distância de lookahead
        Vec3 bestTarget = new Vec3(currentTarget.getX() + 0.5, currentTarget.getY(), currentTarget.getZ() + 0.5);
        double totalDistance = 0.0;

        for (int i = currentNodeIndex; i < Math.min(currentNodeIndex + 10, currentPath.size()); i++) {
            BaritoneStylePathfinder.BaritoneNode node = currentPath.get(i);
            Vec3 nodePos = new Vec3(node.position.getX() + 0.5, node.position.getY(), node.position.getZ() + 0.5);

            if (i > currentNodeIndex) {
                BaritoneStylePathfinder.BaritoneNode prevNode = currentPath.get(i - 1);
                Vec3 prevPos = new Vec3(prevNode.position.getX() + 0.5, prevNode.position.getY(),
                        prevNode.position.getZ() + 0.5);
                totalDistance += nodePos.distanceTo(prevPos);
            }

            // Se chegou na distância de lookahead, usar este node
            if (totalDistance >= lookaheadDistance) {
                bestTarget = nodePos;
                break;
            }

            // Sempre atualizar o melhor target
            bestTarget = nodePos;
        }

        // Interpolar entre posição atual e target de lookahead para suavidade
        double distanceToPlayer = playerPos.distanceTo(bestTarget);
        if (distanceToPlayer < 3.0) {
            // Se muito próximo, olhar mais à frente
            return bestTarget;
        } else {
            // Interpolar para movimento mais suave
            double t = Math.min(0.7, 3.0 / distanceToPlayer);
            Vec3 currentTargetPos = new Vec3(currentTarget.getX() + 0.5, currentTarget.getY(),
                    currentTarget.getZ() + 0.5);
            return interpolateVec3(currentTargetPos, bestTarget, t);
        }
    }

    /**
     * Interpola entre dois Vec3
     */
    private Vec3 interpolateVec3(Vec3 start, Vec3 end, double t) {
        return new Vec3(
                start.xCoord + (end.xCoord - start.xCoord) * t,
                start.yCoord + (end.yCoord - start.yCoord) * t,
                start.zCoord + (end.zCoord - start.zCoord) * t);
    }

    /**
     * Calcula yaw para o target
     */
    private float calculateYawToTarget(Vec3 playerPos, Vec3 targetPos) {
        double deltaX = targetPos.xCoord - playerPos.xCoord;
        double deltaZ = targetPos.zCoord - playerPos.zCoord;

        float yaw = (float) (Math.atan2(deltaZ, deltaX) * 180.0 / Math.PI) - 90.0f;

        // Normalizar
        while (yaw > 180.0f)
            yaw -= 360.0f;
        while (yaw < -180.0f)
            yaw += 360.0f;

        return yaw;
    }

    /**
     * Verifica se está executando
     */
    public boolean isExecuting() {
        return isExecuting;
    }

    /**
     * Ativa modo combate - desabilita controle de câmera do pathfinding
     */
    public void setCombatMode(boolean active) {
        this.combatModeActive = active;
        if (active) {
            Logger.sendMessage("§c[BaritoneMovement] Modo combate ATIVO - câmera do pathfinding DESABILITADA");
        } else {
            Logger.sendMessage("§a[BaritoneMovement] Modo combate INATIVO - câmera do pathfinding HABILITADA");
        }
    }

    /**
     * Verifica se está em modo combate
     */
    public boolean isCombatMode() {
        return combatModeActive;
    }

    /**
     * Obtém informações de debug sobre o sistema de câmera
     */
    public String getCameraDebugInfo() {
        if (cameraController == null) {
            return "§cSistema de câmera não inicializado";
        }
        return cameraController.getSpeedDebugInfo();
    }
}
