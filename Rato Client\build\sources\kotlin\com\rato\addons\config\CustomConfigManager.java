package com.rato.addons.config;

import com.google.gson.*;
import net.minecraft.client.Minecraft;
import org.lwjgl.input.Keyboard;

import java.io.*;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * Sistema de configuração customizado para substituir OneConfig
 * Gerencia todas as configurações do mod usando JSON
 */
public class CustomConfigManager {
    private static final File CONFIG_FILE = new File(Minecraft.getMinecraft().mcDataDir, "config/ratoaddons.json");
    private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();

    // Configurações organizadas por categoria
    public static Map<String, Object> combatConfig = new HashMap<>();
    public static Map<String, Object> visualsConfig = new HashMap<>();
    public static Map<String, Object> movementConfig = new HashMap<>();
    public static Map<String, Object> playerConfig = new HashMap<>();
    public static Map<String, Object> worldConfig = new HashMap<>();
    public static Map<String, Object> miscConfig = new HashMap<>();

    // Keybinds
    public static int guiKey = Keyboard.KEY_RSHIFT;

    static {
        initializeDefaults();
    }

    private static void initializeDefaults() {
        // Combat defaults
        combatConfig.put("aimbot_enabled", false);
        combatConfig.put("aimbot_fov", 90.0f);
        combatConfig.put("aimbot_range", 4.5f);
        combatConfig.put("aimbot_smoothness", 0.15f);
        combatConfig.put("aimbot_speed", 1.0f);
        combatConfig.put("aimbot_prediction", true);
        combatConfig.put("aimbot_raytrace", true);

        // Visuals defaults
        visualsConfig.put("player_esp", false);
        visualsConfig.put("mob_esp", false);
        visualsConfig.put("esp_range", 50.0f);
        visualsConfig.put("esp_show_names", true);
        visualsConfig.put("esp_2d", false);
        visualsConfig.put("esp_3d", true);
        visualsConfig.put("esp_show_health", true);
        visualsConfig.put("player_esp_color", 0xFF00FF00); // Verde
        visualsConfig.put("mob_esp_color", 0xFFFF0000); // Vermelho

        // Movement defaults
        movementConfig.put("pathfinding_enabled", false);
        movementConfig.put("risk_tolerance", 50);
        movementConfig.put("human_movement", true);
        movementConfig.put("allow_block_breaking", false);
        movementConfig.put("allow_risky_jumps", false);
        movementConfig.put("path_visualization", true);
        movementConfig.put("movement_speed", 5);
        movementConfig.put("camera_rotation_speed", 1.0f);
        movementConfig.put("camera_smoothness", 0.05f);
        movementConfig.put("natural_movement", true);
        movementConfig.put("jump_penalty", 3.0f);

        // Player defaults
        playerConfig.put("freecam_enabled", false);
        playerConfig.put("freecam_speed", 5);
        playerConfig.put("inventory_pip", false);
        playerConfig.put("pip_position", 0); // Top Right
        playerConfig.put("pip_border_color", 0xFF39FF14); // Verde neon

        // World defaults
        worldConfig.put("mining_helper", false);
        worldConfig.put("mining_speed", 50);
        worldConfig.put("auto_farm", false);
        worldConfig.put("crop_type", 0); // Wheat
        worldConfig.put("foraging_enabled", false);
        worldConfig.put("foraging_area", 0); // Park
        worldConfig.put("tree_scan_radius", 50);
        worldConfig.put("tree_break_delay", 500);

        // Misc defaults
        miscConfig.put("emergency_stop_all", false);
        miscConfig.put("staff_check_response", false);
        miscConfig.put("manual_pause", false);
        miscConfig.put("debug_mode", false);

        // Rift defaults
        miscConfig.put("rift_autofarm", false);
        miscConfig.put("rift_area_color", 0x6400FFFF); // Ciano transparente
        miscConfig.put("rift_zombie_esp_color", 0x96FF5555); // Vermelho
        miscConfig.put("rift_player_esp_color", 0x9655FF55); // Verde
    }

    public static void loadConfig() {
        if (!CONFIG_FILE.exists()) {
            saveConfig();
            return;
        }

        try (FileReader reader = new FileReader(CONFIG_FILE)) {
            JsonParser parser = new JsonParser();
            JsonObject config = parser.parse(reader).getAsJsonObject();

            // Carregar cada categoria
            loadCategory(config, "combat", combatConfig);
            loadCategory(config, "visuals", visualsConfig);
            loadCategory(config, "movement", movementConfig);
            loadCategory(config, "player", playerConfig);
            loadCategory(config, "world", worldConfig);
            loadCategory(config, "misc", miscConfig);

            // Carregar keybinds
            if (config.has("gui_key")) {
                guiKey = config.get("gui_key").getAsInt();
            }

        } catch (Exception e) {
            System.err.println("Erro ao carregar configuração: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void loadCategory(JsonObject config, String categoryName, Map<String, Object> categoryMap) {
        if (config.has(categoryName)) {
            JsonObject categoryObj = config.getAsJsonObject(categoryName);
            for (Map.Entry<String, JsonElement> entry : categoryObj.entrySet()) {
                String key = entry.getKey();
                JsonElement value = entry.getValue();

                if (value.isJsonPrimitive()) {
                    JsonPrimitive primitive = value.getAsJsonPrimitive();
                    if (primitive.isBoolean()) {
                        categoryMap.put(key, primitive.getAsBoolean());
                    } else if (primitive.isNumber()) {
                        // Detectar se é float ou int
                        if (key.contains("speed") || key.contains("smoothness") || key.contains("penalty") ||
                                key.contains("tolerance") || key.contains("range") && !key.equals("esp_range")) {
                            categoryMap.put(key, primitive.getAsFloat());
                        } else {
                            categoryMap.put(key, primitive.getAsInt());
                        }
                    } else if (primitive.isString()) {
                        categoryMap.put(key, primitive.getAsString());
                    }
                }
            }
        }
    }

    public static void saveConfig() {
        try {
            CONFIG_FILE.getParentFile().mkdirs();

            JsonObject config = new JsonObject();

            // Salvar cada categoria
            config.add("combat", GSON.toJsonTree(combatConfig));
            config.add("visuals", GSON.toJsonTree(visualsConfig));
            config.add("movement", GSON.toJsonTree(movementConfig));
            config.add("player", GSON.toJsonTree(playerConfig));
            config.add("world", GSON.toJsonTree(worldConfig));
            config.add("misc", GSON.toJsonTree(miscConfig));

            // Salvar keybinds
            config.addProperty("gui_key", guiKey);

            try (FileWriter writer = new FileWriter(CONFIG_FILE)) {
                GSON.toJson(config, writer);
            }

        } catch (Exception e) {
            System.err.println("Erro ao salvar configuração: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // Métodos de conveniência para acessar configurações
    public static boolean getBoolean(String category, String key) {
        Map<String, Object> categoryMap = getCategoryMap(category);
        Object value = categoryMap.get(key);
        return value instanceof Boolean ? (Boolean) value : false;
    }

    public static int getInt(String category, String key) {
        Map<String, Object> categoryMap = getCategoryMap(category);
        Object value = categoryMap.get(key);
        return value instanceof Number ? ((Number) value).intValue() : 0;
    }

    public static float getFloat(String category, String key) {
        Map<String, Object> categoryMap = getCategoryMap(category);
        Object value = categoryMap.get(key);
        return value instanceof Number ? ((Number) value).floatValue() : 0.0f;
    }

    public static String getString(String category, String key) {
        Map<String, Object> categoryMap = getCategoryMap(category);
        Object value = categoryMap.get(key);
        return value instanceof String ? (String) value : "";
    }

    public static void setBoolean(String category, String key, boolean value) {
        getCategoryMap(category).put(key, value);
    }

    public static void setInt(String category, String key, int value) {
        getCategoryMap(category).put(key, value);
    }

    public static void setFloat(String category, String key, float value) {
        getCategoryMap(category).put(key, value);
    }

    public static void setString(String category, String key, String value) {
        getCategoryMap(category).put(key, value);
    }

    private static Map<String, Object> getCategoryMap(String category) {
        switch (category.toLowerCase()) {
            case "combat":
                return combatConfig;
            case "visuals":
                return visualsConfig;
            case "movement":
                return movementConfig;
            case "player":
                return playerConfig;
            case "world":
                return worldConfig;
            case "misc":
                return miscConfig;
            default:
                return miscConfig;
        }
    }

    /**
     * Método de compatibilidade para sincronizar com o sistema antigo
     * Usado durante a transição do OneConfig
     */
    public static void syncWithOldConfig() {
        try {
            // Tentar carregar configurações do sistema antigo se existir
            Class<?> oldConfigClass = Class.forName("com.rato.addons.config.RatoAddonsConfig");

            // Se chegou aqui, o sistema antigo ainda existe - copiar valores
            Field[] fields = oldConfigClass.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                String fieldName = field.getName();

                // Mapear campos do sistema antigo para o novo
                if (fieldName.contains("aimbot") || fieldName.contains("combat")) {
                    // Configurações de combate
                } else if (fieldName.contains("esp") || fieldName.contains("visual")) {
                    // Configurações visuais
                }
                // ... outros mapeamentos
            }

        } catch (ClassNotFoundException e) {
            // Sistema antigo não existe mais - usar configurações padrão
            System.out.println("Sistema antigo de configuração não encontrado - usando configurações padrão");
        } catch (Exception e) {
            System.err.println("Erro ao sincronizar com configuração antiga: " + e.getMessage());
        }
    }
}
