---- Minecraft Crash Report ----
// Don't be sad, have a hug! <3

Time: 06/07/25 10:20
Description: There was a severe problem during mod loading that has caused the game to fail

net.minecraftforge.fml.common.LoaderException: java.lang.NoSuchMethodError: java.nio.ByteBuffer.flip()Ljava/nio/ByteBuffer;
	at net.minecraftforge.fml.common.LoadController.transition(LoadController.java:162)
	at net.minecraftforge.fml.common.Loader.initializeMods(Loader.java:739)
	at net.minecraftforge.fml.client.FMLClientHandler.finishMinecraftLoading(FMLClientHandler.java:310)
	at net.minecraft.client.Minecraft.startGame(Minecraft.java:495)
	at net.minecraft.client.Minecraft.run(Minecraft.java:329)
	at net.minecraft.client.main.Main.main(Main.java:124)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at net.minecraft.launchwrapper.Launch.launch(Launch.java:135)
	at net.minecraft.launchwrapper.Launch.main(Launch.java:28)
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86)
Caused by: java.lang.NoSuchMethodError: java.nio.ByteBuffer.flip()Ljava/nio/ByteBuffer;
	at com.rato.addons.gui.modern.TTFRenderer.generateTexture(TTFRenderer.java:212)
	at com.rato.addons.gui.modern.TTFRenderer.<init>(TTFRenderer.java:114)
	at com.rato.addons.gui.modern.TTFRenderer.lambda$getFont$0(TTFRenderer.java:57)
	at java.util.HashMap.computeIfAbsent(HashMap.java:1128)
	at com.rato.addons.gui.modern.TTFRenderer.getFont(TTFRenderer.java:57)
	at com.rato.addons.gui.modern.TTFRenderer.getInterFont(TTFRenderer.java:64)
	at com.rato.addons.imgui.RatoImGuiRenderer.initializeFonts(RatoImGuiRenderer.java:104)
	at com.rato.addons.imgui.RatoImGuiRenderer.initialize(RatoImGuiRenderer.java:83)
	at com.rato.addons.RatoAddons.initializeImGui(RatoAddons.java:82)
	at com.rato.addons.RatoAddons.init(RatoAddons.java:59)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at net.minecraftforge.fml.common.FMLModContainer.handleModStateEvent(FMLModContainer.java:560)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.google.common.eventbus.EventSubscriber.handleEvent(EventSubscriber.java:74)
	at com.google.common.eventbus.SynchronizedEventSubscriber.handleEvent(SynchronizedEventSubscriber.java:47)
	at com.google.common.eventbus.EventBus.dispatch(EventBus.java:322)
	at com.google.common.eventbus.EventBus.dispatchQueuedEvents(EventBus.java:304)
	at com.google.common.eventbus.EventBus.post(EventBus.java:275)
	at net.minecraftforge.fml.common.LoadController.sendEventToModContainer(LoadController.java:211)
	at net.minecraftforge.fml.common.LoadController.propogateStateMessage(LoadController.java:189)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.google.common.eventbus.EventSubscriber.handleEvent(EventSubscriber.java:74)
	at com.google.common.eventbus.SynchronizedEventSubscriber.handleEvent(SynchronizedEventSubscriber.java:47)
	at com.google.common.eventbus.EventBus.dispatch(EventBus.java:322)
	at com.google.common.eventbus.EventBus.dispatchQueuedEvents(EventBus.java:304)
	at com.google.common.eventbus.EventBus.post(EventBus.java:275)
	at net.minecraftforge.fml.common.LoadController.distributeStateMessage(LoadController.java:118)
	at net.minecraftforge.fml.common.Loader.initializeMods(Loader.java:737)
	... 11 more


A detailed walkthrough of the error, its code path and all known details is as follows:
---------------------------------------------------------------------------------------

-- System Details --
Details:
	Minecraft Version: 1.8.9
	Operating System: Windows 11 (amd64) version 10.0
	Java Version: 1.8.0_333, Oracle Corporation
	Java VM Version: Java HotSpot(TM) 64-Bit Server VM (mixed mode), Oracle Corporation
	Memory: 1640491888 bytes (1564 MB) / 2323644416 bytes (2216 MB) up to 15250489344 bytes (14544 MB)
	JVM Flags: 0 total; 
	IntCache: cache: 0, tcache: 0, allocated: 0, tallocated: 0
	FML: MCP 9.19 Powered by Forge 11.15.1.2318 4 mods loaded, 4 mods active
	States: 'U' = Unloaded 'L' = Loaded 'C' = Constructed 'H' = Pre-initialized 'I' = Initialized 'J' = Post-initialized 'A' = Available 'D' = Disabled 'E' = Errored
	UCHI	mcp{9.19} [Minecraft Coder Pack] (minecraft.jar) 
	UCHI	FML{8.0.99.99} [Forge Mod Loader] (forge-mapped.jar) 
	UCHI	Forge{11.15.1.2318} [Minecraft Forge] (forge-mapped.jar) 
	UCHE	ratoaddons{1.0} [ratoaddons] (main) 
	Loaded coremods (and transformers): 
DevAuthLoadingPlugin (DevAuth-forge-legacy-1.2.0.jar)
  me.djtheredstoner.devauth.forge.legacy.ClassTransformer
	GL info: ' Vendor: 'NVIDIA Corporation' Version: '4.6.0 NVIDIA 560.94' Renderer: 'NVIDIA GeForce RTX 3060/PCIe/SSE2'