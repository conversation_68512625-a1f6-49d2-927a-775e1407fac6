package com.rato.addons.pathfinding.analysis;

import net.minecraft.client.Minecraft;
import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;
import net.minecraft.block.Block;
import net.minecraft.init.Blocks;

import java.util.*;

/**
 * Sistema avançado de detecção de árvores em 3D
 * Identifica árvores válidas, acessíveis e otimiza pontos de quebra
 */
public class TreeDetector3D {
    
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Configurações de detecção
    private static final int MIN_TREE_HEIGHT = 3;
    private static final int MAX_TREE_HEIGHT = 20;
    private static final double MAX_BREAK_DISTANCE = 4.5;
    private static final int MIN_LOG_DENSITY = 3; // Mínimo de logs para considerar árvore
    
    /**
     * Resultado da análise de uma árvore
     */
    public static class TreeAnalysis {
        public final BlockPos basePosition;
        public final List<BlockPos> logBlocks;
        public final List<BlockPos> leafBlocks;
        public final BlockPos optimalBreakPoint;
        public final Vec3 optimalStandingPosition;
        public final double accessibility;
        public final double priority;
        public final boolean isValid;
        public final TreeType treeType;
        
        public TreeAnalysis(BlockPos basePosition, List<BlockPos> logBlocks, List<BlockPos> leafBlocks,
                           BlockPos optimalBreakPoint, Vec3 optimalStandingPosition, 
                           double accessibility, double priority, boolean isValid, TreeType treeType) {
            this.basePosition = basePosition;
            this.logBlocks = new ArrayList<>(logBlocks);
            this.leafBlocks = new ArrayList<>(leafBlocks);
            this.optimalBreakPoint = optimalBreakPoint;
            this.optimalStandingPosition = optimalStandingPosition;
            this.accessibility = accessibility;
            this.priority = priority;
            this.isValid = isValid;
            this.treeType = treeType;
        }
    }
    
    /**
     * Tipos de árvore detectados
     */
    public enum TreeType {
        OAK("Oak", Blocks.log, 0),
        BIRCH("Birch", Blocks.log, 2),
        SPRUCE("Spruce", Blocks.log, 1),
        JUNGLE("Jungle", Blocks.log, 3),
        ACACIA("Acacia", Blocks.log2, 0),
        DARK_OAK("Dark Oak", Blocks.log2, 1),
        UNKNOWN("Unknown", null, -1);
        
        public final String displayName;
        public final Block logBlock;
        public final int metadata;
        
        TreeType(String displayName, Block logBlock, int metadata) {
            this.displayName = displayName;
            this.logBlock = logBlock;
            this.metadata = metadata;
        }
    }
    
    /**
     * Detecta todas as árvores válidas em um grid 3D
     */
    public List<TreeAnalysis> detectTrees(Grid3DMapper.MappedGrid3D grid, Vec3 playerPosition) {
        if (grid == null || grid.treeLogPositions.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<TreeAnalysis> detectedTrees = new ArrayList<>();
        Set<BlockPos> processedLogs = new HashSet<>();
        
        // Processar cada posição de log encontrada
        for (BlockPos logPos : grid.treeLogPositions) {
            if (processedLogs.contains(logPos)) continue;
            
            // Analisar cluster de logs (árvore completa)
            TreeCluster cluster = analyzeTreeCluster(logPos, grid, processedLogs);
            if (cluster != null && cluster.isValidTree()) {
                
                // Encontrar ponto ótimo de quebra e posição
                OptimalBreakingResult breakingResult = findOptimalBreaking(cluster, grid, playerPosition);
                if (breakingResult != null && breakingResult.isValid) {
                    
                    // Calcular métricas de prioridade
                    double accessibility = calculateAccessibility(cluster, grid, playerPosition);
                    double priority = calculateTreePriority(cluster, breakingResult, playerPosition);
                    
                    TreeAnalysis analysis = new TreeAnalysis(
                        cluster.basePosition,
                        cluster.logBlocks,
                        cluster.leafBlocks,
                        breakingResult.breakPoint,
                        breakingResult.standingPosition,
                        accessibility,
                        priority,
                        true,
                        determineTreeType(cluster.logBlocks.get(0))
                    );
                    
                    detectedTrees.add(analysis);
                }
                
                // Marcar todos os logs desta árvore como processados
                processedLogs.addAll(cluster.logBlocks);
            }
        }
        
        // Ordenar por prioridade (maior primeiro)
        detectedTrees.sort((a, b) -> Double.compare(b.priority, a.priority));
        
        return detectedTrees;
    }
    
    /**
     * Analisa um cluster de logs para formar uma árvore
     */
    private TreeCluster analyzeTreeCluster(BlockPos startLog, Grid3DMapper.MappedGrid3D grid, Set<BlockPos> processedLogs) {
        Set<BlockPos> visited = new HashSet<>();
        List<BlockPos> logBlocks = new ArrayList<>();
        List<BlockPos> leafBlocks = new ArrayList<>();
        Queue<BlockPos> toCheck = new LinkedList<>();
        
        toCheck.add(startLog);
        
        // BFS para encontrar todos os logs conectados
        while (!toCheck.isEmpty()) {
            BlockPos current = toCheck.poll();
            if (visited.contains(current)) continue;
            
            visited.add(current);
            Grid3DMapper.GridCell cell = grid.getCell(current);
            
            if (cell != null && cell.type == Grid3DMapper.CellType.TREE_LOG) {
                logBlocks.add(current);
                
                // Verificar vizinhos (incluindo diagonais e verticais)
                for (int dx = -1; dx <= 1; dx++) {
                    for (int dy = -1; dy <= 1; dy++) {
                        for (int dz = -1; dz <= 1; dz++) {
                            if (dx == 0 && dy == 0 && dz == 0) continue;
                            
                            BlockPos neighbor = current.add(dx, dy, dz);
                            if (!visited.contains(neighbor)) {
                                Grid3DMapper.GridCell neighborCell = grid.getCell(neighbor);
                                if (neighborCell != null) {
                                    if (neighborCell.type == Grid3DMapper.CellType.TREE_LOG) {
                                        toCheck.add(neighbor);
                                    } else if (neighborCell.type == Grid3DMapper.CellType.TREE_LEAVES) {
                                        leafBlocks.add(neighbor);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        // Validar se é uma árvore válida
        if (logBlocks.size() < MIN_LOG_DENSITY) {
            return null;
        }
        
        // Encontrar posição base (log mais baixo)
        BlockPos basePosition = logBlocks.stream()
            .min(Comparator.comparingInt(BlockPos::getY))
            .orElse(startLog);
        
        return new TreeCluster(basePosition, logBlocks, leafBlocks);
    }
    
    /**
     * Cluster de uma árvore
     */
    private static class TreeCluster {
        final BlockPos basePosition;
        final List<BlockPos> logBlocks;
        final List<BlockPos> leafBlocks;
        
        TreeCluster(BlockPos basePosition, List<BlockPos> logBlocks, List<BlockPos> leafBlocks) {
            this.basePosition = basePosition;
            this.logBlocks = logBlocks;
            this.leafBlocks = leafBlocks;
        }
        
        boolean isValidTree() {
            if (logBlocks.size() < MIN_LOG_DENSITY) return false;
            
            int minY = logBlocks.stream().mapToInt(BlockPos::getY).min().orElse(0);
            int maxY = logBlocks.stream().mapToInt(BlockPos::getY).max().orElse(0);
            int height = maxY - minY + 1;
            
            return height >= MIN_TREE_HEIGHT && height <= MAX_TREE_HEIGHT;
        }
    }
    
    /**
     * Resultado da análise de quebra ótima
     */
    private static class OptimalBreakingResult {
        final BlockPos breakPoint;
        final Vec3 standingPosition;
        final double distance;
        final boolean hasLineOfSight;
        final boolean isValid;
        
        OptimalBreakingResult(BlockPos breakPoint, Vec3 standingPosition, double distance, 
                             boolean hasLineOfSight, boolean isValid) {
            this.breakPoint = breakPoint;
            this.standingPosition = standingPosition;
            this.distance = distance;
            this.hasLineOfSight = hasLineOfSight;
            this.isValid = isValid;
        }
    }
    
    /**
     * Encontra o ponto ótimo para quebrar a árvore
     */
    private OptimalBreakingResult findOptimalBreaking(TreeCluster cluster, Grid3DMapper.MappedGrid3D grid, Vec3 playerPos) {
        // Priorizar logs da base (mais eficiente para Treecapitator)
        List<BlockPos> baseLogs = cluster.logBlocks.stream()
            .filter(pos -> pos.getY() <= cluster.basePosition.getY() + 2)
            .sorted(Comparator.comparingInt(BlockPos::getY))
            .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
        
        for (BlockPos breakCandidate : baseLogs) {
            // Encontrar posição ótima para ficar
            Vec3 standingPos = findOptimalStandingPosition(breakCandidate, grid, playerPos);
            if (standingPos != null) {
                double distance = playerPos.distanceTo(standingPos);
                boolean hasLineOfSight = checkBreakingLineOfSight(standingPos, breakCandidate);
                
                if (distance <= MAX_BREAK_DISTANCE && hasLineOfSight) {
                    return new OptimalBreakingResult(breakCandidate, standingPos, distance, true, true);
                }
            }
        }
        
        return null; // Nenhuma posição válida encontrada
    }
    
    /**
     * Encontra posição ótima para o jogador ficar
     */
    private Vec3 findOptimalStandingPosition(BlockPos breakPoint, Grid3DMapper.MappedGrid3D grid, Vec3 playerPos) {
        Vec3 breakCenter = new Vec3(breakPoint.getX() + 0.5, breakPoint.getY() + 0.5, breakPoint.getZ() + 0.5);
        
        // Testar posições em círculo ao redor do ponto de quebra
        for (double radius = 2.0; radius <= MAX_BREAK_DISTANCE; radius += 0.5) {
            for (double angle = 0; angle < 2 * Math.PI; angle += Math.PI / 8) {
                double x = breakPoint.getX() + radius * Math.cos(angle);
                double z = breakPoint.getZ() + radius * Math.sin(angle);
                
                BlockPos candidatePos = new BlockPos(x, breakPoint.getY(), z);
                
                // Verificar se é uma posição caminhável
                if (grid.isWalkable(candidatePos)) {
                    Vec3 standingCenter = new Vec3(candidatePos.getX() + 0.5, candidatePos.getY() + 1.6, candidatePos.getZ() + 0.5);
                    
                    // Verificar linha de visão para o ponto de quebra
                    if (checkBreakingLineOfSight(standingCenter, breakPoint)) {
                        return standingCenter;
                    }
                }
            }
        }
        
        return null;
    }
    
    /**
     * Verifica linha de visão para quebra
     */
    private boolean checkBreakingLineOfSight(Vec3 standingPos, BlockPos breakPoint) {
        Vec3 breakCenter = new Vec3(breakPoint.getX() + 0.5, breakPoint.getY() + 0.5, breakPoint.getZ() + 0.5);
        
        double distance = standingPos.distanceTo(breakCenter);
        int steps = Math.max(1, (int) Math.ceil(distance * 2));
        
        for (int i = 0; i <= steps; i++) {
            double t = (double) i / steps;
            Vec3 direction = breakCenter.subtract(standingPos);
            Vec3 scaledDirection = new Vec3(direction.xCoord * t, direction.yCoord * t, direction.zCoord * t);
            Vec3 point = standingPos.add(scaledDirection);
            BlockPos checkPos = new BlockPos(point);
            
            Block block = mc.theWorld.getBlockState(checkPos).getBlock();
            if (block.isFullBlock() && block != Blocks.leaves && !isLogBlock(block)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Calcula acessibilidade da árvore
     */
    private double calculateAccessibility(TreeCluster cluster, Grid3DMapper.MappedGrid3D grid, Vec3 playerPos) {
        double totalAccessibility = 0.0;
        int validPositions = 0;
        
        // Verificar quantas posições ao redor da árvore são acessíveis
        for (int radius = 2; radius <= 5; radius++) {
            for (double angle = 0; angle < 2 * Math.PI; angle += Math.PI / 4) {
                double x = cluster.basePosition.getX() + radius * Math.cos(angle);
                double z = cluster.basePosition.getZ() + radius * Math.sin(angle);
                BlockPos testPos = new BlockPos(x, cluster.basePosition.getY(), z);
                
                if (grid.isWalkable(testPos)) {
                    validPositions++;
                    double distance = playerPos.distanceTo(new Vec3(testPos));
                    totalAccessibility += 1.0 / (1.0 + distance * 0.1); // Closer = better
                }
            }
        }
        
        return validPositions > 0 ? totalAccessibility / validPositions : 0.0;
    }
    
    /**
     * Calcula prioridade da árvore
     */
    private double calculateTreePriority(TreeCluster cluster, OptimalBreakingResult breaking, Vec3 playerPos) {
        double distanceFactor = 1.0 / (1.0 + breaking.distance * 0.2);
        double sizeFactor = Math.min(cluster.logBlocks.size() / 10.0, 1.0);
        double accessibilityBonus = breaking.hasLineOfSight ? 1.2 : 1.0;
        
        return distanceFactor * sizeFactor * accessibilityBonus;
    }
    
    /**
     * Determina o tipo da árvore
     */
    private TreeType determineTreeType(BlockPos logPos) {
        Block block = mc.theWorld.getBlockState(logPos).getBlock();
        int metadata = mc.theWorld.getBlockState(logPos).getBlock().getMetaFromState(mc.theWorld.getBlockState(logPos));
        
        for (TreeType type : TreeType.values()) {
            if (type.logBlock == block && (type.metadata == -1 || type.metadata == metadata)) {
                return type;
            }
        }
        
        return TreeType.UNKNOWN;
    }
    
    /**
     * Verifica se um bloco é um log de árvore
     */
    private boolean isLogBlock(Block block) {
        String blockName = Block.blockRegistry.getNameForObject(block).toString();
        return blockName.contains("log") || 
               blockName.contains("wood") ||
               block == Blocks.log || 
               block == Blocks.log2;
    }
}
