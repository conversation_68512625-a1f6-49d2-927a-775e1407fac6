package com.rato.addons.features;

import com.rato.addons.util.Logger;
import net.minecraft.client.Minecraft;
import net.minecraft.item.ItemStack;
import net.minecraft.item.ItemFishingRod;
import net.minecraft.util.ChatComponentText;

/**
 * Sistema de troca automática de pets para otimização de XP e velocidade
 * Implementa troca inteligente baseada no contexto da ação
 */
public class AutoPetSwap {
    
    private static AutoPetSwap instance;
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Estados do sistema
    private boolean isEnabled = false;
    private PetType currentOptimalPet = PetType.SPEED;
    private long lastSwapTime = 0;
    private static final long SWAP_COOLDOWN = 500; // 500ms entre trocas
    
    // Tipos de pets e suas funções
    public enum PetType {
        SPEED("Ocelot", "Velocidade de movimento", "§aVelocidade"),
        XP("Monkey", "Bônus de XP Foraging", "§6XP Boost"),
        COMBAT("Tiger", "Dano e defesa", "§cCombate");
        
        private final String petName;
        private final String description;
        private final String displayName;
        
        PetType(String petName, String description, String displayName) {
            this.petName = petName;
            this.description = description;
            this.displayName = displayName;
        }
        
        public String getPetName() { return petName; }
        public String getDescription() { return description; }
        public String getDisplayName() { return displayName; }
    }
    
    // Contextos de ação
    public enum ActionContext {
        MOVING("Movendo-se", PetType.SPEED),
        BREAKING("Quebrando árvore", PetType.XP),
        COLLECTING("Coletando itens", PetType.XP),
        COMBAT("Em combate", PetType.COMBAT),
        IDLE("Parado", PetType.SPEED);
        
        private final String description;
        private final PetType optimalPet;
        
        ActionContext(String description, PetType optimalPet) {
            this.description = description;
            this.optimalPet = optimalPet;
        }
        
        public String getDescription() { return description; }
        public PetType getOptimalPet() { return optimalPet; }
    }
    
    public static AutoPetSwap getInstance() {
        if (instance == null) {
            instance = new AutoPetSwap();
        }
        return instance;
    }
    
    /**
     * Atualiza o sistema de troca automática
     */
    public void update() {
        if (!isEnabled || mc.thePlayer == null) return;
        
        // Determinar contexto atual
        ActionContext currentContext = determineCurrentContext();
        PetType optimalPet = currentContext.getOptimalPet();
        
        // Verificar se precisa trocar pet
        if (shouldSwapPet(optimalPet)) {
            performPetSwap(optimalPet, currentContext);
        }
    }
    
    /**
     * Determina o contexto atual da ação
     */
    private ActionContext determineCurrentContext() {
        // Verificar se está quebrando bloco
        if (mc.gameSettings.keyBindAttack.isKeyDown()) {
            return ActionContext.BREAKING;
        }
        
        // Verificar se está se movendo
        if (isPlayerMoving()) {
            return ActionContext.MOVING;
        }
        
        // Verificar se há itens próximos (coletando)
        if (hasNearbyItems()) {
            return ActionContext.COLLECTING;
        }
        
        // Verificar se há mobs hostis próximos
        if (hasNearbyHostileMobs()) {
            return ActionContext.COMBAT;
        }
        
        return ActionContext.IDLE;
    }
    
    /**
     * Verifica se o player está se movendo
     */
    private boolean isPlayerMoving() {
        return mc.gameSettings.keyBindForward.isKeyDown() ||
               mc.gameSettings.keyBindBack.isKeyDown() ||
               mc.gameSettings.keyBindLeft.isKeyDown() ||
               mc.gameSettings.keyBindRight.isKeyDown() ||
               mc.gameSettings.keyBindJump.isKeyDown();
    }
    
    /**
     * Verifica se há itens próximos para coletar
     */
    private boolean hasNearbyItems() {
        if (mc.theWorld == null) return false;
        
        return mc.theWorld.getEntitiesWithinAABB(
            net.minecraft.entity.item.EntityItem.class,
            mc.thePlayer.getEntityBoundingBox().expand(5.0, 2.0, 5.0)
        ).size() > 0;
    }
    
    /**
     * Verifica se há mobs hostis próximos
     */
    private boolean hasNearbyHostileMobs() {
        if (mc.theWorld == null) return false;
        
        return mc.theWorld.getEntitiesWithinAABB(
            net.minecraft.entity.monster.EntityMob.class,
            mc.thePlayer.getEntityBoundingBox().expand(10.0, 5.0, 10.0)
        ).size() > 0;
    }
    
    /**
     * Verifica se deve trocar de pet
     */
    private boolean shouldSwapPet(PetType optimalPet) {
        // Verificar cooldown
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastSwapTime < SWAP_COOLDOWN) {
            return false;
        }
        
        // Verificar se já está com o pet ótimo
        if (currentOptimalPet == optimalPet) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Executa a troca de pet
     */
    private void performPetSwap(PetType targetPet, ActionContext context) {
        if (mc.thePlayer == null) return;
        
        // Método de troca usando vara de pescar
        boolean swapSuccess = swapUsingFishingRod(targetPet);
        
        if (swapSuccess) {
            currentOptimalPet = targetPet;
            lastSwapTime = System.currentTimeMillis();
            
            Logger.sendMessage("§aPet trocado: " + targetPet.getDisplayName() + 
                " §7(" + context.getDescription() + ")");
        }
    }
    
    /**
     * Executa troca de pet usando mecânica da vara de pescar
     */
    private boolean swapUsingFishingRod(PetType targetPet) {
        try {
            // Salvar item atual
            ItemStack currentItem = mc.thePlayer.getHeldItem();
            int originalSlot = mc.thePlayer.inventory.currentItem;
            
            // Encontrar vara de pescar no inventário
            int fishingRodSlot = findFishingRodSlot();
            if (fishingRodSlot == -1) {
                Logger.sendMessage("§cVara de pescar não encontrada para troca de pet");
                return false;
            }
            
            // Sequência de troca
            // 1. Trocar para vara de pescar
            mc.thePlayer.inventory.currentItem = fishingRodSlot;
            Thread.sleep(50);
            
            // 2. Usar vara de pescar (simula abertura do menu de pets)
            mc.playerController.sendUseItem(mc.thePlayer, mc.theWorld, mc.thePlayer.getHeldItem());
            Thread.sleep(100);
            
            // 3. Simular comando de troca de pet
            String petCommand = "/pets";
            mc.thePlayer.sendChatMessage(petCommand);
            Thread.sleep(50);
            
            // 4. Voltar para item original
            mc.thePlayer.inventory.currentItem = originalSlot;
            
            return true;
            
        } catch (Exception e) {
            Logger.sendMessage("§cErro na troca de pet: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Encontra slot da vara de pescar no inventário
     */
    private int findFishingRodSlot() {
        for (int i = 0; i < 9; i++) { // Apenas hotbar
            ItemStack stack = mc.thePlayer.inventory.getStackInSlot(i);
            if (stack != null && stack.getItem() instanceof ItemFishingRod) {
                return i;
            }
        }
        return -1;
    }
    
    /**
     * Força troca para pet específico
     */
    public void forcePetSwap(PetType petType, String reason) {
        if (!isEnabled) return;
        
        Logger.sendMessage("§6Troca forçada: " + petType.getDisplayName() + " §7(" + reason + ")");
        
        ActionContext context = ActionContext.IDLE;
        performPetSwap(petType, context);
    }
    
    /**
     * Otimização para quebra de árvore
     */
    public void optimizeForTreeBreaking() {
        if (!isEnabled) return;
        forcePetSwap(PetType.XP, "Quebra de árvore");
    }
    
    /**
     * Otimização para movimento
     */
    public void optimizeForMovement() {
        if (!isEnabled) return;
        forcePetSwap(PetType.SPEED, "Movimento");
    }
    
    /**
     * Obtém estatísticas do sistema
     */
    public String getSwapStats() {
        return "§6Pet Atual: " + currentOptimalPet.getDisplayName() + "\n" +
               "§7Última troca: " + (System.currentTimeMillis() - lastSwapTime) + "ms atrás\n" +
               "§7Status: " + (isEnabled ? "§aAtivo" : "§cInativo");
    }
    
    /**
     * Detecta troca de pet pelo chat
     */
    public void onChatMessage(String message) {
        // Detectar mensagens de troca de pet do servidor
        if (message.contains("You summoned your") && message.contains("Pet!")) {
            // Extrair nome do pet da mensagem
            for (PetType petType : PetType.values()) {
                if (message.toLowerCase().contains(petType.getPetName().toLowerCase())) {
                    currentOptimalPet = petType;
                    Logger.sendMessage("§7Pet detectado: " + petType.getDisplayName());
                    break;
                }
            }
        }
    }
    
    // Getters e Setters
    public boolean isEnabled() { return isEnabled; }
    public void setEnabled(boolean enabled) { 
        this.isEnabled = enabled;
        Logger.sendMessage("§6Auto Pet Swap: " + (enabled ? "§aAtivado" : "§cDesativado"));
    }
    
    public PetType getCurrentOptimalPet() { return currentOptimalPet; }
    public long getLastSwapTime() { return lastSwapTime; }
}
