package com.rato.addons.failsafe;

import com.rato.addons.config.RatoAddonsConfigSimple;
import com.rato.addons.util.Logger;
import net.minecraft.network.Packet;
import net.minecraft.network.play.server.S08PacketPlayerPosLook;
import net.minecraft.util.BlockPos;

public class RotationFailsafe extends Failsafe {

    private static RotationFailsafe instance;

    public static RotationFailsafe getInstance() {
        if (instance == null) {
            instance = new RotationFailsafe();
        }
        return instance;
    }

    private RotationCheckState rotationCheckState = RotationCheckState.NONE;
    private BlockPos positionBeforeReacting = null;
    private Rotation rotationBeforeReacting = null;
    private String randomMessage;
    private String randomContinueMessage;

    // Sistema de detecção profissional
    private long rotationCheckStartTime = 0;
    private boolean isWaitingForPlayerResponse = false;
    private boolean playerRespondedNaturally = false;
    private boolean farmingWasPaused = false;
    private static final long RESPONSE_WINDOW = 3000; // 3 segundos para resposta

    @Override
    public int getPriority() {
        return 4; // Alta prioridade
    }

    @Override
    public FailsafeManager.EmergencyType getType() {
        return FailsafeManager.EmergencyType.ROTATION_CHECK;
    }

    @Override
    public boolean shouldSendNotification() {
        return false; // Não enviar Discord para rotation
    }

    @Override
    public boolean shouldPlaySound() {
        return RatoAddonsConfigSimple.soundAlert;
    }
    
    @Override
    public void onReceivedPacketDetection(Packet packet) {
        if (!isEnabled()) return;
        if (!(packet instanceof S08PacketPlayerPosLook)) return;

        S08PacketPlayerPosLook posLookPacket = (S08PacketPlayerPosLook) packet;
        double packetYaw = posLookPacket.getYaw();
        double packetPitch = posLookPacket.getPitch();
        double playerYaw = mc.thePlayer.rotationYaw;
        double playerPitch = mc.thePlayer.rotationPitch;
        double yawDiff = Math.abs(packetYaw - playerYaw);
        double pitchDiff = Math.abs(packetPitch - playerPitch);

        if (yawDiff == 360 && pitchDiff == 0) return;

        if (shouldTriggerCheck(packetYaw, packetPitch)) {
            // DETECÇÃO INSTANTÂNEA - mostrar mensagem como no exemplo
            Logger.sendMessage("§c§lRotation Failsafe §7» §fPossible check detected!");

            // Incrementar contador de detecções
            com.rato.addons.features.StaffCheckDetection.incrementDetectedChecks();

            FailsafeManager.getInstance().possibleDetection(this);
        }
    }
    
    @Override
    public void onWorldChangeDetection() {
        // Detecção instantânea - não precisa de monitoramento
    }

    private void evaluateRotation() {
        if (rotationBeforeReacting == null) {
            Logger.sendMessage("§7[DEBUG] Original rotation is null. Ignoring");
            return;
        }

        // Verificar se ainda há diferença significativa na rotação atual
        double currentYaw = mc.thePlayer.rotationYaw;
        double currentPitch = mc.thePlayer.rotationPitch;
        double originalYaw = rotationBeforeReacting.getYaw();
        double originalPitch = rotationBeforeReacting.getPitch();

        double yawDiffFromOriginal = Math.abs(currentYaw - originalYaw) % 360;
        double pitchDiffFromOriginal = Math.abs(currentPitch - originalPitch) % 360;

        Logger.sendMessage("§7[DEBUG] Evaluating: Current vs Original - Yaw diff: " + String.format("%.1f", yawDiffFromOriginal) + "°, Pitch diff: " + String.format("%.1f", pitchDiffFromOriginal) + "°");

        // Se ainda há diferença significativa da posição original, é staff check
        if (yawDiffFromOriginal >= RatoAddonsConfigSimple.rotationThreshold || pitchDiffFromOriginal >= RatoAddonsConfigSimple.rotationThreshold) {
            Logger.sendMessage("§7[DEBUG] Still rotated from original position - triggering failsafe");
            FailsafeManager.getInstance().possibleDetection(this);
        } else {
            Logger.sendMessage("§c§lWARNING: §eRotation check was triggered but admin rotated you back. DO NOT REACT!");
            if (shouldSendNotification()) {
                Logger.sendMessage("§7[WEBHOOK] Rotation check detected but player was rotated back");
            }
        }
        rotationBeforeReacting = null;
    }
    
    // Método removido - detecção é instantânea agora

    private boolean shouldTriggerCheck(double newYaw, double newPitch) {
        double yawDiff = Math.abs(newYaw - mc.thePlayer.rotationYaw) % 360;
        double pitchDiff = Math.abs(newPitch - mc.thePlayer.rotationPitch) % 360;
        double threshold = RatoAddonsConfigSimple.rotationThreshold;

        return (yawDiff >= threshold || pitchDiff >= threshold);
    }
    
    @Override
    public void duringFailsafeTrigger() {
        // Failsafe simples - apenas 1 rotação
        performSimpleRotation();
        endOfFailsafeTrigger();
    }

    private void performSimpleRotation() {
        if (mc.thePlayer != null) {
            // Rotação simples e rápida
            float yawChange = (float) ((Math.random() - 0.5) * 60); // -30 a +30 graus
            float pitchChange = (float) ((Math.random() - 0.5) * 20); // -10 a +10 graus

            mc.thePlayer.rotationYaw += yawChange;
            mc.thePlayer.rotationPitch += pitchChange;

            // Limitar pitch
            if (mc.thePlayer.rotationPitch > 90.0f) {
                mc.thePlayer.rotationPitch = 90.0f;
            }
            if (mc.thePlayer.rotationPitch < -90.0f) {
                mc.thePlayer.rotationPitch = -90.0f;
            }
        }
    }
    
    private void performNaturalLookAround() {
        // Movimento natural de "olhar ao redor" - simula gravação de movimento
        if (mc.thePlayer != null) {
            // Movimento mais amplo e natural
            float yawChange = (float) ((Math.random() - 0.5) * 60); // -30 a +30 graus
            float pitchChange = (float) ((Math.random() - 0.5) * 30); // -15 a +15 graus

            mc.thePlayer.rotationYaw += yawChange;
            mc.thePlayer.rotationPitch += pitchChange;

            // Limitar pitch
            if (mc.thePlayer.rotationPitch > 90.0f) {
                mc.thePlayer.rotationPitch = 90.0f;
            }
            if (mc.thePlayer.rotationPitch < -90.0f) {
                mc.thePlayer.rotationPitch = -90.0f;
            }
        }
    }

    private void performRotationToOriginal() {
        // Rotacionar de volta para posição original com variação humana
        if (mc.thePlayer != null && rotationBeforeReacting != null) {
            float targetYaw = (float) (rotationBeforeReacting.getYaw() + (Math.random() * 30 - 15));
            float targetPitch = (float) (Math.random() * 30 + 30);

            mc.thePlayer.rotationYaw = targetYaw;
            mc.thePlayer.rotationPitch = targetPitch;
        }
    }

    private String getRandomMessage() {
        String[] messages = {
            "hey", "hello", "hi", "what's up", "sup", "yo", "hey there",
            "how's it going", "what's good", "hola", "wassup"
        };
        return messages[(int) (Math.random() * messages.length)];
    }

    private String getRandomContinueMessage() {
        String[] messages = {
            "ok", "alright", "cool", "nice", "good", "thanks", "ty",
            "sounds good", "perfect", "awesome", "great"
        };
        return messages[(int) (Math.random() * messages.length)];
    }
    
    @Override
    public void endOfFailsafeTrigger() {
        FailsafeManager.getInstance().stopFailsafes();
        FailsafeManager.getInstance().scheduleDelay(1000);
    }

    @Override
    public void resetStates() {
        rotationCheckState = RotationCheckState.NONE;
        rotationBeforeReacting = null;
        positionBeforeReacting = null;
        randomMessage = null;
        randomContinueMessage = null;
    }

    // Classe auxiliar para rotação
    private static class Rotation {
        private final float yaw;
        private final float pitch;

        public Rotation(float yaw, float pitch) {
            this.yaw = yaw;
            this.pitch = pitch;
        }

        public float getYaw() {
            return yaw;
        }

        public float getPitch() {
            return pitch;
        }
    }

    private enum RotationCheckState {
        NONE,
        WAIT_BEFORE_START,
        LOOK_AROUND,
        WAIT_BEFORE_SENDING_MESSAGE_1,
        SEND_MESSAGE,
        ROTATE_TO_POS_BEFORE,
        LOOK_AROUND_2,
        WAIT_BEFORE_SENDING_MESSAGE_2,
        SEND_MESSAGE_2,
        ROTATE_TO_POS_BEFORE_2,
        GO_BACK_START,
        GO_BACK_END,
        WARP_GARDEN,
        END
    }
}
