package com.rato.addons.commands;

import com.rato.addons.pathfinding.TreeMarkerSystem;
import com.rato.addons.util.Logger;
import net.minecraft.command.CommandBase;
import net.minecraft.command.CommandException;
import net.minecraft.command.ICommandSender;
import net.minecraft.util.BlockPos;

import java.util.Arrays;
import java.util.List;

/**
 * Comandos para gerenciar o sistema de marcação de árvores
 */
public class TreeMarkerCommands extends CommandBase {
    
    @Override
    public String getCommandName() {
        return "trees";
    }
    
    @Override
    public String getCommandUsage(ICommandSender sender) {
        return "/trees <mark|list|clear|region|stats|help>";
    }
    
    @Override
    public int getRequiredPermissionLevel() {
        return 0;
    }
    
    @Override
    public void processCommand(ICommandSender sender, String[] args) throws CommandException {
        if (args.length == 0) {
            showHelp();
            return;
        }
        
        TreeMarkerSystem treeSystem = TreeMarkerSystem.getInstance();
        
        switch (args[0].toLowerCase()) {
            case "mark":
                handleMarkCommand(treeSystem);
                break;
                
            case "list":
                handleListCommand(treeSystem);
                break;
                
            case "clear":
                handleClearCommand(treeSystem);
                break;
                
            case "region":
                handleRegionCommand(treeSystem, args);
                break;
                
            case "stats":
                handleStatsCommand(treeSystem);
                break;
                
            case "help":
            default:
                showHelp();
                break;
        }
    }
    
    /**
     * Comando para marcar árvore
     */
    private void handleMarkCommand(TreeMarkerSystem treeSystem) {
        if (treeSystem.markTreeAtCrosshair()) {
            Logger.sendMessage("§a✓ Árvore marcada com sucesso!");
        } else {
            Logger.sendMessage("§cFalha ao marcar árvore. Mire em um bloco de madeira.");
        }
    }
    
    /**
     * Comando para listar árvores marcadas
     */
    private void handleListCommand(TreeMarkerSystem treeSystem) {
        List<TreeMarkerSystem.MarkedTree> trees = treeSystem.getMarkedTrees();
        
        if (trees.isEmpty()) {
            Logger.sendMessage("§7Nenhuma árvore marcada na região atual.");
            return;
        }
        
        Logger.sendMessage("§6=== Árvores Marcadas ===");
        Logger.sendMessage("§7Região: " + treeSystem.getCurrentRegion());
        
        for (int i = 0; i < trees.size(); i++) {
            TreeMarkerSystem.MarkedTree tree = trees.get(i);
            String position = String.format("%.0f, %.0f, %.0f", 
                tree.optimalBreakingPosition.xCoord,
                tree.optimalBreakingPosition.yCoord, 
                tree.optimalBreakingPosition.zCoord);
            
            String distance = String.format("%.1f", tree.distanceFromPlayer);
            
            Logger.sendMessage(String.format("§7%d. §f%s §7(§a%sm§7)", 
                i + 1, position, distance));
        }
        
        Logger.sendMessage("§7Total: " + trees.size() + " árvores");
    }
    
    /**
     * Comando para limpar árvores marcadas
     */
    private void handleClearCommand(TreeMarkerSystem treeSystem) {
        treeSystem.clearMarkedTrees();
        Logger.sendMessage("§a✓ Todas as árvores marcadas foram removidas!");
    }
    
    /**
     * Comando para gerenciar regiões
     */
    private void handleRegionCommand(TreeMarkerSystem treeSystem, String[] args) {
        if (args.length < 2) {
            Logger.sendMessage("§7Região atual: " + treeSystem.getCurrentRegion());
            Logger.sendMessage("§7Uso: /trees region <nome>");
            return;
        }
        
        String newRegion = args[1];
        treeSystem.setCurrentRegion(newRegion);
        Logger.sendMessage("§a✓ Região alterada para: " + newRegion);
    }
    
    /**
     * Comando para mostrar estatísticas
     */
    private void handleStatsCommand(TreeMarkerSystem treeSystem) {
        Logger.sendMessage("§6=== Estatísticas do Sistema ===");
        Logger.sendMessage(treeSystem.getStats());
        
        TreeMarkerSystem.MarkedTree nearest = treeSystem.getNearestMarkedTree();
        if (nearest != null) {
            String pos = String.format("%.0f, %.0f, %.0f", 
                nearest.optimalBreakingPosition.xCoord,
                nearest.optimalBreakingPosition.yCoord,
                nearest.optimalBreakingPosition.zCoord);
            Logger.sendMessage("§7Árvore mais próxima: " + pos);
        } else {
            Logger.sendMessage("§7Nenhuma árvore marcada");
        }
    }
    
    /**
     * Mostra ajuda dos comandos
     */
    private void showHelp() {
        Logger.sendMessage("§6=== Sistema de Marcação de Árvores ===");
        Logger.sendMessage("§f/trees mark §7- Marca a árvore que você está mirando");
        Logger.sendMessage("§f/trees list §7- Lista todas as árvores marcadas");
        Logger.sendMessage("§f/trees clear §7- Remove todas as árvores marcadas");
        Logger.sendMessage("§f/trees region <nome> §7- Altera a região atual");
        Logger.sendMessage("§f/trees stats §7- Mostra estatísticas do sistema");
        Logger.sendMessage("§f/trees help §7- Mostra esta ajuda");
        Logger.sendMessage("");
        Logger.sendMessage("§7Como usar:");
        Logger.sendMessage("§71. Mire em um bloco de madeira de uma árvore");
        Logger.sendMessage("§72. Digite §f/trees mark §7para marcar a árvore");
        Logger.sendMessage("§73. O sistema encontrará a melhor posição para quebrar");
        Logger.sendMessage("§74. Use o foraging macro normalmente");
    }
    
    @Override
    public List<String> addTabCompletionOptions(ICommandSender sender, String[] args, BlockPos pos) {
        if (args.length == 1) {
            return Arrays.asList("mark", "list", "clear", "region", "stats", "help");
        }
        
        if (args.length == 2 && args[0].equalsIgnoreCase("region")) {
            return Arrays.asList("park", "hub", "forest", "jungle", "custom");
        }
        
        return null;
    }
}
