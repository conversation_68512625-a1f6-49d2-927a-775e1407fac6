package com.rato.addons.mixin.network;

import com.rato.addons.features.Freecam;
import net.minecraft.network.NetworkManager;
import net.minecraft.network.Packet;
import net.minecraft.network.play.client.C03PacketPlayer;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(NetworkManager.class)
public class MixinNetworkManager {

    @Inject(method = "sendPacket(Lnet/minecraft/network/Packet;)V", at = @At("HEAD"), cancellable = true)
    private void onSendPacket(Packet packet, CallbackInfo ci) {
        // Cancelar packets de movimento quando freecam está ativo
        if (Freecam.getInstance().isFreecamActive() && packet instanceof C03PacketPlayer) {
            // Simplesmente cancelar o packet - não enviar nada
            ci.cancel();
        }
    }
}
