package com.rato.addons.util;

import com.rato.addons.config.CustomConfigManager;
import net.minecraft.client.Minecraft;
import net.minecraft.util.ChatComponentText;
import net.minecraft.util.EnumChatFormatting;
import net.minecraft.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

public abstract class Logger {

    protected static final Minecraft mc = Minecraft.getMinecraft();
    private static final Map<String, String> lastMessages = new HashMap<>();

    public static void addMessage(String text) {
        if (mc.thePlayer == null || mc.theWorld == null) {
            System.out.println("RatoAddons " + StringUtils.stripControlCodes(text));
        } else {
            mc.thePlayer.addChatMessage(new ChatComponentText(text));
        }
    }

    public static void sendMessage(final String message) {
        addMessage(formatPrefix("§6Rato Client", message));
    }

    public static void sendWarning(final String message) {
        addMessage("§c§l[WARNING] §8» §e" + message);
    }

    public static void sendError(final String message) {
        addMessage("§l§4§kZ§r§l§4[RatoAddons]§kH§r §8» §c" + message);
    }

    public static void sendNote(final String message) {
        sendMessage(message);
    }

    public static void sendLog(final String message) {
        if (isDuplicate("debug", message))
            return;

        boolean debugMode = CustomConfigManager.getBoolean("misc", "debug_mode");
        if (debugMode && mc.thePlayer != null) {
            addMessage("§l§2[RatoAddons] §8» §7" + message);
        } else {
            System.out.println("[RatoAddons] " + message);
        }
    }

    public static void sendNotification(String title, String message, Long duration) {
        if (isDuplicate("notification", message))
            return;
        // Substituir OneConfig Notifications por chat message simples
        sendMessage(title + ": " + message);
    }

    private static boolean isDuplicate(String type, String message) {
        if (lastMessages.containsKey(type) && lastMessages.get(type).equals(message)) {
            return true;
        }
        lastMessages.put(type, message);
        return false;
    }

    private static String formatPrefix(String prefix, String message) {
        return EnumChatFormatting.RED + "[" + EnumChatFormatting.GOLD + prefix + EnumChatFormatting.RED + "] §8» §e"
                + message;
    }

    public abstract String getName();

    protected void log(String message) {
        sendLog(formatMessage(message));
    }

    protected void send(String message) {
        sendMessage(formatMessage(message));
    }

    protected void error(String message) {
        sendError(formatMessage(message));
    }

    protected void warn(String message) {
        sendWarning(formatMessage(message));
    }

    protected void note(String message) {
        sendNote(formatMessage(message));
    }

    protected String formatMessage(String message) {
        return "[" + getName() + "] " + message;
    }
}
