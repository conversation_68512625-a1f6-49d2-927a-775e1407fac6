package com.rato.addons.pathfinding.planning;

import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;
import com.rato.addons.pathfinding.analysis.Grid3DMapper;

import java.util.*;

/**
 * Grid 3D otimizado para pathfinding
 * Conecta nós vizinhos e calcula custos de movimento
 */
public class PathfindingGrid3D {
    
    // Direções de movimento possíveis (26 direções em 3D)
    private static final int[][] MOVEMENT_DIRECTIONS = {
        // Movimento horizontal (8 direções)
        {1, 0, 0}, {-1, 0, 0}, {0, 0, 1}, {0, 0, -1},
        {1, 0, 1}, {1, 0, -1}, {-1, 0, 1}, {-1, 0, -1},
        
        // Movimento para cima (9 direções)
        {0, 1, 0}, {1, 1, 0}, {-1, 1, 0}, {0, 1, 1}, {0, 1, -1},
        {1, 1, 1}, {1, 1, -1}, {-1, 1, 1}, {-1, 1, -1},
        
        // Movimento para baixo (9 direções)
        {0, -1, 0}, {1, -1, 0}, {-1, -1, 0}, {0, -1, 1}, {0, -1, -1},
        {1, -1, 1}, {1, -1, -1}, {-1, -1, 1}, {-1, -1, -1}
    };
    
    // Custos de movimento
    private static final double COST_HORIZONTAL = 1.0;
    private static final double COST_DIAGONAL = 1.414; // sqrt(2)
    private static final double COST_VERTICAL = 1.2;
    private static final double COST_DIAGONAL_3D = 1.732; // sqrt(3)
    private static final double COST_JUMP = 1.8;
    private static final double COST_FALL = 1.1;
    
    /**
     * Nó do grid de pathfinding
     */
    public static class PathfindingNode {
        public final BlockPos position;
        public final Grid3DMapper.GridCell gridCell;
        public final List<Connection> connections;
        public final boolean isWalkable;
        public final double baseCost;
        
        public PathfindingNode(BlockPos position, Grid3DMapper.GridCell gridCell) {
            this.position = position;
            this.gridCell = gridCell;
            this.connections = new ArrayList<>();
            this.isWalkable = gridCell != null && gridCell.isWalkable;
            this.baseCost = gridCell != null ? gridCell.movementCost : Double.MAX_VALUE;
        }
        
        public void addConnection(PathfindingNode neighbor, double cost, MovementType type) {
            connections.add(new Connection(neighbor, cost, type));
        }
        
        public List<Connection> getValidConnections() {
            return connections.stream()
                .filter(conn -> conn.neighbor.isWalkable && conn.cost < Double.MAX_VALUE)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
        }
    }
    
    /**
     * Conexão entre nós
     */
    public static class Connection {
        public final PathfindingNode neighbor;
        public final double cost;
        public final MovementType type;
        
        public Connection(PathfindingNode neighbor, double cost, MovementType type) {
            this.neighbor = neighbor;
            this.cost = cost;
            this.type = type;
        }
    }
    
    /**
     * Tipos de movimento
     */
    public enum MovementType {
        WALK,           // Movimento horizontal
        DIAGONAL,       // Movimento diagonal horizontal
        JUMP,           // Pulo para cima
        FALL,           // Queda
        CLIMB,          // Subir (escadas, etc)
        SWIM,           // Nadar
        DIAGONAL_3D     // Movimento diagonal em 3D
    }
    
    /**
     * Grid de pathfinding construído
     */
    public static class BuiltPathfindingGrid {
        public final Map<BlockPos, PathfindingNode> nodes;
        public final List<PathfindingNode> walkableNodes;
        public final BlockPos center;
        public final int radius;
        public final long buildTime;
        
        public BuiltPathfindingGrid(Map<BlockPos, PathfindingNode> nodes, BlockPos center, int radius) {
            this.nodes = nodes;
            this.center = center;
            this.radius = radius;
            this.buildTime = System.currentTimeMillis();
            
            // Pré-calcular nós caminháveis
            this.walkableNodes = nodes.values().stream()
                .filter(node -> node.isWalkable)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
        }
        
        public PathfindingNode getNode(BlockPos pos) {
            return nodes.get(pos);
        }
        
        public List<PathfindingNode> getNeighbors(BlockPos pos) {
            PathfindingNode node = getNode(pos);
            if (node == null) return new ArrayList<>();
            
            return node.getValidConnections().stream()
                .map(conn -> conn.neighbor)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
        }
        
        public double getMovementCost(BlockPos from, BlockPos to) {
            PathfindingNode fromNode = getNode(from);
            if (fromNode == null) return Double.MAX_VALUE;
            
            return fromNode.connections.stream()
                .filter(conn -> conn.neighbor.position.equals(to))
                .mapToDouble(conn -> conn.cost)
                .findFirst()
                .orElse(Double.MAX_VALUE);
        }
    }
    
    /**
     * Constrói o grid de pathfinding a partir do grid 3D mapeado
     */
    public BuiltPathfindingGrid buildPathfindingGrid(Grid3DMapper.MappedGrid3D mappedGrid) {
        if (mappedGrid == null) return null;
        
        Map<BlockPos, PathfindingNode> nodes = new HashMap<>();
        
        // Fase 1: Criar todos os nós
        for (Map.Entry<BlockPos, Grid3DMapper.GridCell> entry : mappedGrid.cells.entrySet()) {
            BlockPos pos = entry.getKey();
            Grid3DMapper.GridCell cell = entry.getValue();
            
            PathfindingNode node = new PathfindingNode(pos, cell);
            nodes.put(pos, node);
        }
        
        // Fase 2: Conectar nós vizinhos
        for (PathfindingNode node : nodes.values()) {
            if (!node.isWalkable) continue;
            
            connectNeighbors(node, nodes, mappedGrid);
        }
        
        return new BuiltPathfindingGrid(nodes, mappedGrid.center, mappedGrid.radius);
    }
    
    /**
     * Conecta um nó aos seus vizinhos válidos
     */
    private void connectNeighbors(PathfindingNode node, Map<BlockPos, PathfindingNode> allNodes, 
                                 Grid3DMapper.MappedGrid3D mappedGrid) {
        
        for (int[] direction : MOVEMENT_DIRECTIONS) {
            BlockPos neighborPos = node.position.add(direction[0], direction[1], direction[2]);
            PathfindingNode neighbor = allNodes.get(neighborPos);
            
            if (neighbor == null || !neighbor.isWalkable) continue;
            
            // Analisar movimento
            MovementAnalysis movement = analyzeMovement(node.position, neighborPos, mappedGrid);
            if (movement.isValid) {
                node.addConnection(neighbor, movement.totalCost, movement.type);
            }
        }
    }
    
    /**
     * Resultado da análise de movimento
     */
    private static class MovementAnalysis {
        boolean isValid = false;
        double totalCost = Double.MAX_VALUE;
        MovementType type = MovementType.WALK;
    }
    
    /**
     * Analisa movimento entre duas posições
     */
    private MovementAnalysis analyzeMovement(BlockPos from, BlockPos to, Grid3DMapper.MappedGrid3D grid) {
        MovementAnalysis result = new MovementAnalysis();
        
        int dx = to.getX() - from.getX();
        int dy = to.getY() - from.getY();
        int dz = to.getZ() - from.getZ();
        
        // Verificar se o movimento é válido
        if (!isMovementValid(from, to, grid)) {
            return result;
        }
        
        // Calcular custo base do movimento
        double baseCost = calculateBaseCost(dx, dy, dz);
        
        // Determinar tipo de movimento
        MovementType movementType = determineMovementType(dx, dy, dz, grid, from, to);
        
        // Aplicar modificadores de custo
        double modifiedCost = applyMovementModifiers(baseCost, movementType, from, to, grid);
        
        result.isValid = true;
        result.totalCost = modifiedCost;
        result.type = movementType;
        
        return result;
    }
    
    /**
     * Verifica se um movimento é fisicamente válido
     */
    private boolean isMovementValid(BlockPos from, BlockPos to, Grid3DMapper.MappedGrid3D grid) {
        int dx = Math.abs(to.getX() - from.getX());
        int dy = to.getY() - from.getY();
        int dz = Math.abs(to.getZ() - from.getZ());
        
        // Movimento muito extremo
        if (dx > 1 || dz > 1 || Math.abs(dy) > 1) {
            return false;
        }
        
        // Verificar se há obstáculos no caminho
        if (dy > 0) { // Movimento para cima
            return canJumpTo(from, to, grid);
        } else if (dy < 0) { // Movimento para baixo
            return canFallTo(from, to, grid);
        } else { // Movimento horizontal
            return canWalkTo(from, to, grid);
        }
    }
    
    /**
     * Verifica se pode pular para uma posição
     */
    private boolean canJumpTo(BlockPos from, BlockPos to, Grid3DMapper.MappedGrid3D grid) {
        // Verificar se há espaço para pular (2 blocos acima da posição atual)
        BlockPos jumpSpace = from.up(2);
        Grid3DMapper.GridCell jumpCell = grid.getCell(jumpSpace);
        
        return jumpCell != null && 
               (jumpCell.type == Grid3DMapper.CellType.AIR || 
                jumpCell.type == Grid3DMapper.CellType.TREE_LEAVES);
    }
    
    /**
     * Verifica se pode cair para uma posição
     */
    private boolean canFallTo(BlockPos from, BlockPos to, Grid3DMapper.MappedGrid3D grid) {
        // Verificar se não há obstáculos no caminho da queda
        int steps = Math.abs(to.getY() - from.getY());
        
        for (int i = 1; i <= steps; i++) {
            BlockPos checkPos = from.add(0, -i, 0);
            Grid3DMapper.GridCell cell = grid.getCell(checkPos);
            
            if (cell != null && cell.type == Grid3DMapper.CellType.SOLID) {
                return false; // Obstáculo no caminho
            }
        }
        
        return true;
    }
    
    /**
     * Verifica se pode andar para uma posição
     */
    private boolean canWalkTo(BlockPos from, BlockPos to, Grid3DMapper.MappedGrid3D grid) {
        // Movimento horizontal simples - já verificado pela validação de walkable
        return true;
    }
    
    /**
     * Calcula custo base do movimento
     */
    private double calculateBaseCost(int dx, int dy, int dz) {
        boolean isDiagonal = (Math.abs(dx) + Math.abs(dz)) == 2;
        boolean hasVertical = dy != 0;
        
        if (hasVertical && isDiagonal) {
            return COST_DIAGONAL_3D; // Movimento diagonal em 3D
        } else if (hasVertical) {
            return COST_VERTICAL; // Movimento vertical
        } else if (isDiagonal) {
            return COST_DIAGONAL; // Movimento diagonal horizontal
        } else {
            return COST_HORIZONTAL; // Movimento horizontal
        }
    }
    
    /**
     * Determina o tipo de movimento
     */
    private MovementType determineMovementType(int dx, int dy, int dz, Grid3DMapper.MappedGrid3D grid, 
                                              BlockPos from, BlockPos to) {
        if (dy > 0) {
            Grid3DMapper.GridCell fromCell = grid.getCell(from);
            if (fromCell != null && fromCell.type == Grid3DMapper.CellType.CLIMBABLE) {
                return MovementType.CLIMB;
            } else {
                return MovementType.JUMP;
            }
        } else if (dy < 0) {
            return MovementType.FALL;
        } else {
            Grid3DMapper.GridCell toCell = grid.getCell(to);
            if (toCell != null && toCell.type == Grid3DMapper.CellType.WATER) {
                return MovementType.SWIM;
            } else if (Math.abs(dx) + Math.abs(dz) == 2) {
                return MovementType.DIAGONAL;
            } else {
                return MovementType.WALK;
            }
        }
    }
    
    /**
     * Aplica modificadores de custo baseados no tipo de movimento e terreno
     */
    private double applyMovementModifiers(double baseCost, MovementType type, BlockPos from, BlockPos to, 
                                        Grid3DMapper.MappedGrid3D grid) {
        double modifiedCost = baseCost;
        
        // Modificadores por tipo de movimento
        switch (type) {
            case JUMP:
                modifiedCost *= COST_JUMP;
                break;
            case FALL:
                modifiedCost *= COST_FALL;
                break;
            case SWIM:
                modifiedCost *= 2.5; // Nadar é mais lento
                break;
            case CLIMB:
                modifiedCost *= 1.5; // Subir é mais custoso
                break;
        }
        
        // Modificadores por tipo de terreno de destino
        Grid3DMapper.GridCell toCell = grid.getCell(to);
        if (toCell != null) {
            modifiedCost *= toCell.movementCost;
        }
        
        return modifiedCost;
    }
    
    /**
     * Otimiza o grid removendo nós desnecessários
     */
    public BuiltPathfindingGrid optimizeGrid(BuiltPathfindingGrid grid) {
        if (grid == null) return null;
        
        Map<BlockPos, PathfindingNode> optimizedNodes = new HashMap<>();
        
        // Manter apenas nós que são úteis para pathfinding
        for (PathfindingNode node : grid.nodes.values()) {
            if (shouldKeepNode(node)) {
                optimizedNodes.put(node.position, node);
            }
        }
        
        return new BuiltPathfindingGrid(optimizedNodes, grid.center, grid.radius);
    }
    
    /**
     * Determina se um nó deve ser mantido no grid otimizado
     */
    private boolean shouldKeepNode(PathfindingNode node) {
        // Manter nós caminháveis
        if (node.isWalkable) return true;
        
        // Manter nós que são pontos de conexão importantes
        if (node.connections.size() > 2) return true;
        
        // Remover nós isolados ou desnecessários
        return false;
    }
}
