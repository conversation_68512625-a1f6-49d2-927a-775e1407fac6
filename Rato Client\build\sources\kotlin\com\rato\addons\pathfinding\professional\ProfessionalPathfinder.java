package com.rato.addons.pathfinding.professional;

import net.minecraft.block.Block;
import net.minecraft.block.material.Material;
import net.minecraft.client.Minecraft;
import net.minecraft.init.Blocks;
import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;
import net.minecraft.world.World;

import java.util.*;
import java.util.function.Consumer;

/**
 * Sistema de pathfinding profissional usando algoritmo A* otimizado
 */
public class ProfessionalPathfinder {

    private final Minecraft mc = Minecraft.getMinecraft();
    private final TerrainAnalyzer terrainAnalyzer = new TerrainAnalyzer();
    
    // Configurações do algoritmo
    private static final int MAX_SEARCH_NODES = 15000;
    private static final double GOAL_TOLERANCE = 1.0;
    private static final double HEURISTIC_WEIGHT = 1.1; // Peso da heurística (> 1 = mais rápido, menos ótimo)
    private static final double TERRAIN_ANALYSIS_RADIUS = 3.0; // Raio para análise de terreno
    
    // Direções de movimento otimizadas para pathfinding preciso
    private static final int[][] MOVEMENT_DIRECTIONS = {
        // Movimento horizontal básico (prioridade alta)
        {1, 0, 0}, {-1, 0, 0}, {0, 0, 1}, {0, 0, -1},
        // Movimento diagonal (prioridade média)
        {1, 0, 1}, {1, 0, -1}, {-1, 0, 1}, {-1, 0, -1},
        // Movimento vertical simples (pulo/queda de 1 bloco)
        {0, 1, 0}, {0, -1, 0},
        // Movimento com pulo horizontal
        {1, 1, 0}, {-1, 1, 0}, {0, 1, 1}, {0, 1, -1},
        // Movimento diagonal com pulo (usar com cuidado)
        {1, 1, 1}, {1, 1, -1}, {-1, 1, 1}, {-1, 1, -1}
    };
    
    // Estado do pathfinding
    private volatile boolean cancelled = false;
    private int nodesExplored = 0;
    
    // Callbacks
    private Consumer<List<PathNode>> onPathFound;
    private Runnable onPathNotFound;
    private Runnable onStuckDetected;
    
    /**
     * Encontra um caminho entre duas posições usando A*
     */
    public List<PathNode> findPath(Vec3 start, Vec3 goal) {
        if (mc.theWorld == null) return null;

        cancelled = false;
        nodesExplored = 0;

        // Ajustar posições para grid
        Vec3 adjustedStart = adjustPositionToGrid(start);
        Vec3 adjustedGoal = adjustPositionToGrid(goal);

        // Pré-processamento: verificar se o objetivo é alcançável
        if (!isPositionValid(new BlockPos(adjustedGoal))) {
            // Tentar encontrar posição válida próxima ao objetivo
            adjustedGoal = findNearestValidPosition(adjustedGoal);
            if (adjustedGoal == null) return null;
        }
        
        // Estruturas de dados do A*
        PriorityQueue<PathNode> openSet = new PriorityQueue<>();
        Set<BlockPos> closedSet = new HashSet<>();
        Map<BlockPos, PathNode> allNodes = new HashMap<>();
        
        // Nó inicial
        PathNode startNode = new PathNode(adjustedStart, 0, calculateHeuristic(adjustedStart, adjustedGoal), null);
        openSet.add(startNode);
        allNodes.put(startNode.blockPos, startNode);
        
        PathNode bestNode = startNode; // Melhor nó encontrado até agora
        
        while (!openSet.isEmpty() && nodesExplored < MAX_SEARCH_NODES && !cancelled) {
            PathNode current = openSet.poll();
            nodesExplored++;
            
            // Atualizar melhor nó se este estiver mais próximo do objetivo
            if (current.distanceTo(adjustedGoal) < bestNode.distanceTo(adjustedGoal)) {
                bestNode = current;
            }
            
            // Verificar se chegou ao destino
            if (current.isNear(adjustedGoal, GOAL_TOLERANCE)) {
                List<PathNode> path = reconstructPath(current);
                if (onPathFound != null) {
                    onPathFound.accept(path);
                }
                return path;
            }
            
            closedSet.add(current.blockPos);
            
            // Explorar vizinhos
            for (int[] direction : MOVEMENT_DIRECTIONS) {
                if (cancelled) break;
                
                Vec3 neighborPos = current.position.addVector(direction[0], direction[1], direction[2]);
                BlockPos neighborBlockPos = new BlockPos(neighborPos);
                
                if (closedSet.contains(neighborBlockPos)) continue;
                
                // Analisar movimento
                MovementAnalysis movement = analyzeMovement(current.position, neighborPos);
                if (!movement.isValid) continue;
                
                double tentativeGCost = current.gCost + movement.cost;
                
                PathNode existingNode = allNodes.get(neighborBlockPos);
                if (existingNode != null && tentativeGCost >= existingNode.gCost) {
                    continue; // Caminho pior
                }
                
                // Criar ou atualizar nó
                double hCost = calculateHeuristic(neighborPos, adjustedGoal);
                PathNode neighborNode;
                
                if (existingNode != null) {
                    existingNode.updateCosts(tentativeGCost, hCost, current);
                    existingNode.movementType = movement.movementType;
                    neighborNode = existingNode;
                } else {
                    neighborNode = new PathNode(neighborPos, tentativeGCost, hCost, current, movement.movementType);
                    allNodes.put(neighborBlockPos, neighborNode);
                }
                
                // Adicionar metadados do ambiente
                addEnvironmentMetadata(neighborNode);
                
                if (!openSet.contains(neighborNode)) {
                    openSet.add(neighborNode);
                }
            }
        }
        
        // Se não encontrou caminho completo, retornar caminho parcial para o melhor nó
        if (bestNode != startNode && bestNode.distanceTo(adjustedGoal) < start.distanceTo(adjustedGoal)) {
            List<PathNode> partialPath = reconstructPath(bestNode);
            if (onPathFound != null) {
                onPathFound.accept(partialPath);
            }
            return partialPath;
        }
        
        // Nenhum caminho encontrado
        if (onPathNotFound != null) {
            onPathNotFound.run();
        }
        return null;
    }
    
    /**
     * Analisa um movimento entre duas posições
     */
    private MovementAnalysis analyzeMovement(Vec3 from, Vec3 to) {
        MovementAnalysis result = new MovementAnalysis();
        
        BlockPos fromPos = new BlockPos(from);
        BlockPos toPos = new BlockPos(to);
        
        // Verificar se a posição de destino é válida
        if (!isPositionValid(toPos)) {
            result.isValid = false;
            return result;
        }
        
        // Calcular diferenças
        int dx = toPos.getX() - fromPos.getX();
        int dy = toPos.getY() - fromPos.getY();
        int dz = toPos.getZ() - fromPos.getZ();
        
        // Usar análise avançada de terreno para determinar movimento
        TerrainAnalyzer.TerrainInfo fromTerrain = terrainAnalyzer.analyzePosition(fromPos);
        TerrainAnalyzer.TerrainInfo toTerrain = terrainAnalyzer.analyzePosition(toPos);

        // Verificar se o movimento é possível baseado na análise de terreno
        Vec3 targetVec = new Vec3(toPos.getX() + 0.5, toPos.getY(), toPos.getZ() + 0.5);

        // Procurar opção de movimento adequada no terreno de origem
        TerrainAnalyzer.PathOption bestOption = null;
        double bestDistance = Double.MAX_VALUE;

        for (TerrainAnalyzer.PathOption option : fromTerrain.pathOptions) {
            double distance = option.position.distanceTo(targetVec);
            if (distance < bestDistance && distance < 2.0) { // Dentro de 2 blocos
                bestOption = option;
                bestDistance = distance;
            }
        }

        if (bestOption != null) {
            // Usar a opção encontrada pela análise de terreno
            result.movementType = bestOption.movementType;
            result.cost = bestOption.cost * toTerrain.movementCost;
        } else {
            // Análise inteligente de movimento
            if (dy == 0) {
                // Movimento horizontal - verificar se precisa pular
                if (needsJumpForHorizontalMovement(fromPos, toPos)) {
                    if (canJump(fromPos, toPos)) {
                        result.movementType = PathNode.MovementType.JUMP;
                        result.cost = 1.5 * toTerrain.movementCost;
                    } else {
                        result.isValid = false;
                        return result;
                    }
                } else if (toTerrain.hasWater) {
                    result.movementType = PathNode.MovementType.SWIM;
                    result.cost = 1.8 * toTerrain.movementCost;
                } else if (Math.abs(dx) + Math.abs(dz) == 1) {
                    result.movementType = PathNode.MovementType.WALK;
                    result.cost = 1.0 * toTerrain.movementCost;
                } else {
                    result.movementType = PathNode.MovementType.DIAGONAL;
                    result.cost = 1.414 * toTerrain.movementCost;
                }
            } else if (dy == 1) {
                // Movimento para cima
                if (canJump(fromPos, toPos)) {
                    if (toTerrain.hasWater) {
                        result.movementType = PathNode.MovementType.SWIM;
                        result.cost = 2.0 * toTerrain.movementCost;
                    } else {
                        result.movementType = PathNode.MovementType.JUMP;
                        result.cost = 1.5 * toTerrain.movementCost;
                    }
                } else {
                    result.isValid = false;
                    return result;
                }
            } else if (dy == -1) {
                // Movimento para baixo
                if (canFall(fromPos, toPos)) {
                    if (toTerrain.hasWater) {
                        result.movementType = PathNode.MovementType.SWIM;
                        result.cost = 1.3 * toTerrain.movementCost;
                    } else {
                        result.movementType = PathNode.MovementType.FALL;
                        result.cost = 1.2 * toTerrain.movementCost;
                    }
                } else {
                    result.isValid = false;
                    return result;
                }
            } else if (dy >= 2) {
                // Movimento vertical alto
                if (canClimb(fromPos, toPos)) {
                    result.movementType = PathNode.MovementType.CLIMB;
                    result.cost = (2.5 + (dy - 2) * 0.5) * toTerrain.movementCost;
                } else {
                    result.isValid = false;
                    return result;
                }
            } else if (dy <= -2) {
                // Queda alta
                if (canFallSafely(fromPos, toPos)) {
                    result.movementType = PathNode.MovementType.FALL;
                    result.cost = (1.5 + Math.abs(dy) * 0.2) * toTerrain.movementCost;
                } else {
                    result.isValid = false;
                    return result;
                }
            } else {
                result.isValid = false;
                return result;
            }
        }
        
        // Verificar colisões no caminho
        if (!isPathClear(from, to)) {
            result.isValid = false;
            return result;
        }
        
        result.isValid = true;
        return result;
    }
    
    /**
     * Verifica se uma posição é válida para movimento usando análise avançada de terreno
     */
    private boolean isPositionValid(BlockPos pos) {
        World world = mc.theWorld;
        if (world == null) return false;

        // Verificar se está dentro dos limites do mundo
        if (pos.getY() < 0 || pos.getY() > 255) return false;

        // Usar análise avançada de terreno
        TerrainAnalyzer.TerrainInfo terrainInfo = terrainAnalyzer.analyzePosition(pos);

        // Verificar se é caminhável
        if (!terrainInfo.isWalkable) return false;

        // Evitar áreas muito perigosas
        if (terrainInfo.isDangerous && terrainInfo.type == TerrainAnalyzer.TerrainType.LAVA) {
            return false;
        }

        // Evitar áreas void
        if (terrainInfo.type == TerrainAnalyzer.TerrainType.VOID_AREA) {
            return false;
        }

        // Verificar se há opções de movimento disponíveis
        return !terrainInfo.pathOptions.isEmpty() || terrainInfo.movementCost < 50.0;
    }
    
    /**
     * Verifica se um bloco é passável
     */
    private boolean isBlockPassable(Block block) {
        if (block == Blocks.air) return true;
        if (block == Blocks.water || block == Blocks.flowing_water) return true;
        if (block == Blocks.lava || block == Blocks.flowing_lava) return true;
        
        Material material = block.getMaterial();
        return !material.isSolid() || material == Material.plants;
    }
    
    /**
     * Verifica se um bloco é sólido (pode pisar)
     */
    private boolean isBlockSolid(Block block) {
        if (block == Blocks.air) return false;
        if (block == Blocks.water || block == Blocks.flowing_water) return false;
        if (block == Blocks.lava || block == Blocks.flowing_lava) return false;
        
        return block.getMaterial().isSolid();
    }
    
    /**
     * Verifica se um bloco é líquido
     */
    private boolean isBlockLiquid(Block block) {
        return block == Blocks.water || block == Blocks.flowing_water ||
               block == Blocks.lava || block == Blocks.flowing_lava;
    }
    
    /**
     * Verifica se pode pular de uma posição para outra
     */
    private boolean canJump(BlockPos from, BlockPos to) {
        World world = mc.theWorld;
        if (world == null) return false;

        // Verificar se há espaço para pular (2 blocos acima da posição atual)
        Block aboveFrom = world.getBlockState(from.up(2)).getBlock();
        if (!isBlockPassable(aboveFrom)) return false;

        // Verificar se o destino é válido para pousar
        if (!isValidStandingPosition(to)) return false;

        // Verificar se a distância horizontal é razoável para um pulo
        int dx = Math.abs(to.getX() - from.getX());
        int dz = Math.abs(to.getZ() - from.getZ());
        int horizontalDistance = dx + dz;

        if (horizontalDistance > 2) return false; // Muito longe para pular

        // Verificar se a diferença de altura é apropriada para pulo
        int dy = to.getY() - from.getY();
        if (dy < 0 || dy > 2) return false; // Só pode pular para cima ou mesmo nível, máximo 2 blocos

        // Verificar se há obstáculo no caminho que requer pulo
        if (dy == 0) {
            // Pulo horizontal - verificar se há obstáculo
            BlockPos midPos = new BlockPos(
                (from.getX() + to.getX()) / 2,
                from.getY(),
                (from.getZ() + to.getZ()) / 2
            );
            Block obstacleBlock = world.getBlockState(midPos).getBlock();
            return isBlockSolid(obstacleBlock); // Só precisa pular se há obstáculo
        }

        return true; // Pulo vertical válido
    }
    
    /**
     * Verifica se pode cair de uma posição para outra
     */
    private boolean canFall(BlockPos from, BlockPos to) {
        // Queda simples - apenas verificar se o destino é válido
        return isPositionValid(to);
    }

    /**
     * Verifica se pode escalar de uma posição para outra
     */
    private boolean canClimb(BlockPos from, BlockPos to) {
        World world = mc.theWorld;
        if (world == null) return false;

        int heightDiff = to.getY() - from.getY();
        if (heightDiff <= 1) return canJump(from, to);

        // Verificar se há blocos para escalar (como escadas, vinhas, etc.)
        for (int i = 1; i <= heightDiff; i++) {
            BlockPos checkPos = from.up(i);
            Block block = world.getBlockState(checkPos).getBlock();

            // Verificar se há espaço para o player
            if (!isBlockPassable(block)) return false;

            // Verificar se há algo para escalar nas proximidades
            boolean hasClimbableNearby = false;
            for (int dx = -1; dx <= 1; dx++) {
                for (int dz = -1; dz <= 1; dz++) {
                    Block nearbyBlock = world.getBlockState(checkPos.add(dx, 0, dz)).getBlock();
                    if (isBlockSolid(nearbyBlock)) {
                        hasClimbableNearby = true;
                        break;
                    }
                }
                if (hasClimbableNearby) break;
            }

            if (!hasClimbableNearby) return false;
        }

        return isPositionValid(to);
    }

    /**
     * Verifica se pode cair com segurança
     */
    private boolean canFallSafely(BlockPos from, BlockPos to) {
        World world = mc.theWorld;
        if (world == null) return false;

        int fallHeight = from.getY() - to.getY();

        // Quedas muito altas são perigosas
        if (fallHeight > 10) return false;

        // Verificar se há água para amortecer a queda
        for (int i = 1; i <= fallHeight; i++) {
            BlockPos checkPos = from.down(i);
            Block block = world.getBlockState(checkPos).getBlock();

            if (isBlockLiquid(block)) {
                return true; // Água amortece a queda
            }
        }

        // Quedas de até 3 blocos são seguras
        if (fallHeight <= 3) return isPositionValid(to);

        // Quedas maiores precisam de verificação especial
        return fallHeight <= 6 && isPositionValid(to);
    }

    /**
     * Verifica se precisa pular para movimento horizontal
     */
    private boolean needsJumpForHorizontalMovement(BlockPos from, BlockPos to) {
        World world = mc.theWorld;
        if (world == null) return false;

        // Verificar se há obstáculo no caminho direto
        int dx = to.getX() - from.getX();
        int dz = to.getZ() - from.getZ();

        // Normalizar direção
        int stepX = dx == 0 ? 0 : (dx > 0 ? 1 : -1);
        int stepZ = dz == 0 ? 0 : (dz > 0 ? 1 : -1);

        // Verificar posição intermediária
        BlockPos checkPos = from.add(stepX, 0, stepZ);
        Block obstacleBlock = world.getBlockState(checkPos).getBlock();

        // Se há bloco sólido no caminho, precisa pular
        if (isBlockSolid(obstacleBlock)) {
            // Verificar se pode pular por cima
            Block aboveObstacle = world.getBlockState(checkPos.up()).getBlock();
            return isBlockPassable(aboveObstacle);
        }

        // Verificar se o destino está um bloco acima
        Block toGround = world.getBlockState(to.down()).getBlock();
        Block fromGround = world.getBlockState(from.down()).getBlock();

        if (isBlockSolid(toGround) && isBlockSolid(fromGround)) {
            return to.getY() > from.getY(); // Precisa pular para subir
        }

        return false;
    }
    
    /**
     * Verifica se o caminho entre duas posições está livre
     */
    private boolean isPathClear(Vec3 from, Vec3 to) {
        // Verificação simples - pode ser melhorada com raycasting
        return true;
    }
    
    /**
     * Calcula heurística (distância estimada até o objetivo)
     */
    private double calculateHeuristic(Vec3 from, Vec3 to) {
        // Distância euclidiana base
        double distance = from.distanceTo(to);

        // Adicionar penalidade por diferença de altura
        double heightDiff = Math.abs(to.yCoord - from.yCoord);
        double heightPenalty = heightDiff * 0.5; // Penalidade por subida/descida

        // Adicionar análise de terreno se disponível
        double terrainPenalty = analyzeTerrainDifficulty(from, to);

        return (distance + heightPenalty + terrainPenalty) * HEURISTIC_WEIGHT;
    }

    /**
     * Analisa dificuldade do terreno entre duas posições usando TerrainAnalyzer
     */
    private double analyzeTerrainDifficulty(Vec3 from, Vec3 to) {
        double penalty = 0;

        // Verificar alguns pontos no caminho direto usando análise avançada
        Vec3 direction = to.subtract(from);
        double distance = from.distanceTo(to);
        int samples = Math.min((int) distance, 7); // Mais amostras para melhor precisão

        for (int i = 1; i <= samples; i++) {
            double t = (double) i / (samples + 1);
            Vec3 samplePos = from.addVector(
                direction.xCoord * t,
                direction.yCoord * t,
                direction.zCoord * t
            );

            BlockPos blockPos = new BlockPos(samplePos);
            TerrainAnalyzer.TerrainInfo terrainInfo = terrainAnalyzer.analyzePosition(blockPos);

            // Penalidade baseada no tipo de terreno
            switch (terrainInfo.type) {
                case LAVA:
                    penalty += 20.0; // Muito perigoso
                    break;
                case CLIFF:
                    penalty += 8.0;
                    break;
                case STEEP_HILL:
                    penalty += 4.0;
                    break;
                case WATER_DEEP:
                    penalty += 3.0;
                    break;
                case ROUGH_TERRAIN:
                    penalty += 2.0;
                    break;
                case MOUNTAIN:
                    penalty += 3.0;
                    break;
                case UNDERGROUND:
                    penalty += 1.5;
                    break;
                case VOID_AREA:
                    penalty += 50.0; // Evitar completamente
                    break;
                case WATER_SHALLOW:
                    penalty += 1.0;
                    break;
                case FOREST:
                    penalty += 0.5;
                    break;
                default:
                    penalty += 0.1;
                    break;
            }

            // Penalidade adicional por perigo
            if (terrainInfo.isDangerous) {
                penalty += 5.0;
            }

            // Penalidade por falta de opções de movimento
            if (terrainInfo.pathOptions.isEmpty()) {
                penalty += 2.0;
            }

            // Bonificação para terrenos fáceis
            if (terrainInfo.type == TerrainAnalyzer.TerrainType.FLAT_GROUND ||
                terrainInfo.type == TerrainAnalyzer.TerrainType.BRIDGE_AREA) {
                penalty -= 0.5;
            }
        }

        return Math.max(0, penalty);
    }
    
    /**
     * Ajusta posição para o grid de pathfinding com detecção de chão
     */
    private Vec3 adjustPositionToGrid(Vec3 pos) {
        World world = mc.theWorld;
        if (world == null) {
            return new Vec3(
                Math.floor(pos.xCoord) + 0.5,
                Math.floor(pos.yCoord),
                Math.floor(pos.zCoord) + 0.5
            );
        }

        BlockPos blockPos = new BlockPos(pos);

        // Encontrar o nível correto do chão
        int groundLevel = findCorrectGroundLevel(blockPos);

        return new Vec3(
            Math.floor(pos.xCoord) + 0.5,
            groundLevel,
            Math.floor(pos.zCoord) + 0.5
        );
    }

    /**
     * Encontra o nível correto do chão para pathfinding seguindo o terreno
     */
    private int findCorrectGroundLevel(BlockPos pos) {
        World world = mc.theWorld;
        if (world == null || mc.thePlayer == null) return pos.getY();

        // Usar altura do player como referência para seguir terreno
        int playerY = (int) mc.thePlayer.posY;
        int searchRange = 20;

        // Encontrar o chão mais próximo da altura do player
        int bestGroundY = findNearestGroundLevel(pos, playerY, searchRange);

        if (bestGroundY != -1 && isValidStandingPosition(new BlockPos(pos.getX(), bestGroundY, pos.getZ()))) {
            return bestGroundY;
        }

        // Fallback: verificar posição atual
        if (isValidStandingPosition(pos)) {
            return pos.getY();
        }

        // Procurar para baixo (limitado)
        for (int i = 1; i <= 8; i++) {
            BlockPos checkPos = pos.down(i);
            if (isValidStandingPosition(checkPos)) {
                return checkPos.getY();
            }
        }

        // Procurar para cima (limitado)
        for (int i = 1; i <= 5; i++) {
            BlockPos checkPos = pos.up(i);
            if (isValidStandingPosition(checkPos)) {
                return checkPos.getY();
            }
        }

        return pos.getY(); // Fallback
    }

    /**
     * Encontra o nível de chão mais próximo da altura de referência
     */
    private int findNearestGroundLevel(BlockPos pos, int referenceY, int searchRange) {
        World world = mc.theWorld;
        int bestY = -1;
        int minHeightDiff = Integer.MAX_VALUE;

        // Procurar em um range ao redor da altura de referência
        for (int y = referenceY - searchRange; y <= referenceY + searchRange; y++) {
            if (y < 1 || y > 255) continue;

            BlockPos checkPos = new BlockPos(pos.getX(), y, pos.getZ());
            Block block = world.getBlockState(checkPos).getBlock();

            // Verificar se é um bloco sólido válido para chão
            if (block != Blocks.air &&
                block != Blocks.water &&
                block != Blocks.flowing_water &&
                block != Blocks.lava &&
                block != Blocks.flowing_lava &&
                block.getMaterial().blocksMovement() &&
                block.isFullBlock()) {

                int heightDiff = Math.abs(y - referenceY);

                // Preferir o chão mais próximo da altura atual
                if (heightDiff < minHeightDiff) {
                    minHeightDiff = heightDiff;
                    bestY = y;
                }
            }
        }

        return bestY;
    }

    /**
     * Verifica se é uma posição válida para o player ficar em pé
     */
    private boolean isValidStandingPosition(BlockPos pos) {
        World world = mc.theWorld;

        Block feetBlock = world.getBlockState(pos).getBlock();
        Block headBlock = world.getBlockState(pos.up()).getBlock();
        Block groundBlock = world.getBlockState(pos.down()).getBlock();

        // Deve ter espaço para o player (2 blocos)
        if (!isBlockPassable(feetBlock) || !isBlockPassable(headBlock)) {
            return false;
        }

        // Deve ter chão sólido ou ser água
        return isBlockSolid(groundBlock) || isBlockLiquid(feetBlock);
    }

    /**
     * Encontra a posição válida mais próxima de uma posição alvo
     */
    private Vec3 findNearestValidPosition(Vec3 target) {
        BlockPos targetPos = new BlockPos(target);

        // Verificar em espiral ao redor da posição alvo
        for (int radius = 1; radius <= 5; radius++) {
            for (int dx = -radius; dx <= radius; dx++) {
                for (int dz = -radius; dz <= radius; dz++) {
                    for (int dy = -2; dy <= 2; dy++) {
                        BlockPos checkPos = targetPos.add(dx, dy, dz);
                        if (isPositionValid(checkPos)) {
                            return new Vec3(
                                checkPos.getX() + 0.5,
                                checkPos.getY(),
                                checkPos.getZ() + 0.5
                            );
                        }
                    }
                }
            }
        }

        return null; // Nenhuma posição válida encontrada
    }
    
    /**
     * Adiciona metadados do ambiente ao nó usando análise avançada
     */
    private void addEnvironmentMetadata(PathNode node) {
        TerrainAnalyzer.TerrainInfo terrainInfo = terrainAnalyzer.analyzePosition(node.blockPos);

        // Aplicar informações da análise de terreno
        node.isWater = terrainInfo.hasWater;
        node.requiresJump = terrainInfo.requiresJump;
        node.isDangerous = terrainInfo.isDangerous;

        // Ajustar custo de movimento baseado no terreno
        node.movementCost = terrainInfo.movementCost;

        // Verificar se é caminhável
        node.isWalkable = terrainInfo.isWalkable;

        // Adicionar informações específicas do tipo de terreno
        switch (terrainInfo.type) {
            case LAVA:
                node.isDangerous = true;
                node.movementCost *= 10.0; // Muito custoso
                break;
            case WATER_DEEP:
                node.isWater = true;
                node.movementCost *= 2.0;
                break;
            case CLIFF:
            case STEEP_HILL:
                node.requiresJump = true;
                node.movementCost *= 1.5;
                break;
            case VOID_AREA:
                node.isWalkable = false;
                node.movementCost = 100.0;
                break;
            case FLAT_GROUND:
            case BRIDGE_AREA:
                node.movementCost *= 0.8; // Mais fácil
                break;
        }
    }
    
    /**
     * Reconstrói o caminho a partir do nó final
     */
    private List<PathNode> reconstructPath(PathNode endNode) {
        List<PathNode> path = new ArrayList<>();
        PathNode current = endNode;
        
        while (current != null) {
            path.add(0, current);
            current = current.parent;
        }
        
        return path;
    }
    
    /**
     * Cancela o pathfinding atual
     */
    public void cancel() {
        cancelled = true;
    }

    /**
     * Limpa o cache de análise de terreno
     */
    public void clearTerrainCache() {
        terrainAnalyzer.clearCache();
    }

    // Getters
    public int getNodesExplored() { return nodesExplored; }
    public TerrainAnalyzer getTerrainAnalyzer() { return terrainAnalyzer; }

    // Setters para callbacks
    public void setOnPathFound(Consumer<List<PathNode>> callback) { this.onPathFound = callback; }
    public void setOnPathNotFound(Runnable callback) { this.onPathNotFound = callback; }
    public void setOnStuckDetected(Runnable callback) { this.onStuckDetected = callback; }
    
    /**
     * Classe para análise de movimento
     */
    private static class MovementAnalysis {
        boolean isValid = false;
        double cost = 1.0;
        PathNode.MovementType movementType = PathNode.MovementType.WALK;
    }
}
