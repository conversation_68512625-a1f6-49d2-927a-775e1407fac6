package com.rato.addons.pathfinding.analysis;

import net.minecraft.client.Minecraft;
import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;
import net.minecraft.block.Block;
import net.minecraft.init.Blocks;
import net.minecraft.block.material.Material;

import java.util.*;

/**
 * Sistema de mapeamento 3D do entorno do jogador
 * Cria um grid tridimensional representando o terreno e obstáculos
 */
public class Grid3DMapper {
    
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Configurações do mapeamento
    private static final int DEFAULT_SCAN_RADIUS = 15;
    private static final int DEFAULT_SCAN_HEIGHT = 10;
    private static final int PLAYER_HEIGHT = 2; // Altura necessária para o jogador passar
    
    /**
     * Tipos de célula no grid 3D
     */
    public enum CellType {
        SOLID,          // Bloco sólido - não passável
        AIR,            // Ar - passável
        WALKABLE,       // Posição onde o jogador pode ficar
        DANGEROUS,      // Lava, cacto, etc - evitar
        WATER,          // Água - movimento mais lento
        CLIMBABLE,      // Escadas, vinhas - pode subir
        TREE_LOG,       // Bloco de madeira - alvo potencial
        TREE_LEAVES     // Folhas - passável mas indica árvore
    }
    
    /**
     * Célula do grid 3D
     */
    public static class GridCell {
        public final BlockPos position;
        public final CellType type;
        public final Block block;
        public final double movementCost;
        public final boolean isWalkable;
        public final boolean hasLineOfSight;
        
        public GridCell(BlockPos position, CellType type, Block block, double movementCost, 
                       boolean isWalkable, boolean hasLineOfSight) {
            this.position = position;
            this.type = type;
            this.block = block;
            this.movementCost = movementCost;
            this.isWalkable = isWalkable;
            this.hasLineOfSight = hasLineOfSight;
        }
    }
    
    /**
     * Grid 3D mapeado
     */
    public static class MappedGrid3D {
        public final BlockPos center;
        public final int radius;
        public final int height;
        public final Map<BlockPos, GridCell> cells;
        public final List<BlockPos> walkablePositions;
        public final List<BlockPos> treeLogPositions;
        public final long timestamp;
        
        public MappedGrid3D(BlockPos center, int radius, int height, Map<BlockPos, GridCell> cells) {
            this.center = center;
            this.radius = radius;
            this.height = height;
            this.cells = cells;
            this.timestamp = System.currentTimeMillis();
            
            // Pré-calcular listas para performance
            this.walkablePositions = new ArrayList<>();
            this.treeLogPositions = new ArrayList<>();
            
            for (GridCell cell : cells.values()) {
                if (cell.isWalkable) {
                    walkablePositions.add(cell.position);
                }
                if (cell.type == CellType.TREE_LOG) {
                    treeLogPositions.add(cell.position);
                }
            }
        }
        
        public GridCell getCell(BlockPos pos) {
            return cells.get(pos);
        }
        
        public boolean isWalkable(BlockPos pos) {
            GridCell cell = getCell(pos);
            return cell != null && cell.isWalkable;
        }
        
        public double getMovementCost(BlockPos pos) {
            GridCell cell = getCell(pos);
            return cell != null ? cell.movementCost : Double.MAX_VALUE;
        }
    }
    
    /**
     * Mapeia o entorno do jogador em um grid 3D
     */
    public MappedGrid3D mapSurroundings(Vec3 center) {
        return mapSurroundings(center, DEFAULT_SCAN_RADIUS, DEFAULT_SCAN_HEIGHT);
    }
    
    /**
     * Mapeia o entorno com raio e altura customizados
     */
    public MappedGrid3D mapSurroundings(Vec3 center, int radius, int height) {
        if (mc.theWorld == null) return null;
        
        BlockPos centerPos = new BlockPos(center);
        Map<BlockPos, GridCell> cells = new HashMap<>();
        
        // Escanear cubo ao redor do centro
        for (int x = -radius; x <= radius; x++) {
            for (int z = -radius; z <= radius; z++) {
                for (int y = -height; y <= height; y++) {
                    BlockPos scanPos = centerPos.add(x, y, z);
                    GridCell cell = analyzePosition(scanPos, centerPos);
                    if (cell != null) {
                        cells.put(scanPos, cell);
                    }
                }
            }
        }
        
        return new MappedGrid3D(centerPos, radius, height, cells);
    }
    
    /**
     * Analisa uma posição específica e determina o tipo de célula
     */
    private GridCell analyzePosition(BlockPos pos, BlockPos playerCenter) {
        Block block = mc.theWorld.getBlockState(pos).getBlock();
        Material material = block.getMaterial();
        
        // Determinar tipo da célula
        CellType cellType = determineCellType(block, material);
        
        // Calcular custo de movimento
        double movementCost = calculateMovementCost(cellType, material);
        
        // Verificar se é uma posição onde o jogador pode ficar
        boolean isWalkable = isWalkablePosition(pos);
        
        // Verificar linha de visão do centro do jogador
        boolean hasLineOfSight = checkLineOfSight(playerCenter, pos);
        
        return new GridCell(pos, cellType, block, movementCost, isWalkable, hasLineOfSight);
    }
    
    /**
     * Determina o tipo de célula baseado no bloco
     */
    private CellType determineCellType(Block block, Material material) {
        // Ar e blocos não-sólidos
        if (block == Blocks.air || !block.isFullBlock()) {
            return CellType.AIR;
        }
        
        // Blocos perigosos
        if (block == Blocks.lava || block == Blocks.flowing_lava || 
            block == Blocks.cactus || block == Blocks.fire) {
            return CellType.DANGEROUS;
        }
        
        // Água
        if (material == Material.water) {
            return CellType.WATER;
        }
        
        // Blocos escaláveis
        if (block == Blocks.ladder || block == Blocks.vine) {
            return CellType.CLIMBABLE;
        }
        
        // Blocos de madeira (logs)
        if (isLogBlock(block)) {
            return CellType.TREE_LOG;
        }
        
        // Folhas
        if (material == Material.leaves) {
            return CellType.TREE_LEAVES;
        }
        
        // Blocos sólidos padrão
        return CellType.SOLID;
    }
    
    /**
     * Calcula o custo de movimento para um tipo de célula
     */
    private double calculateMovementCost(CellType cellType, Material material) {
        switch (cellType) {
            case AIR:
            case TREE_LEAVES:
                return 1.0;
            case WATER:
                return 2.5; // Movimento mais lento na água
            case CLIMBABLE:
                return 1.8; // Subir é mais custoso
            case DANGEROUS:
                return 50.0; // Evitar ao máximo
            case SOLID:
            case TREE_LOG:
                return Double.MAX_VALUE; // Não passável
            default:
                return 1.0;
        }
    }
    
    /**
     * Verifica se uma posição é caminhável (jogador pode ficar lá)
     */
    private boolean isWalkablePosition(BlockPos pos) {
        // Verificar se há espaço para o jogador (2 blocos de altura)
        Block blockAtPos = mc.theWorld.getBlockState(pos).getBlock();
        Block blockAbove = mc.theWorld.getBlockState(pos.up()).getBlock();
        Block blockBelow = mc.theWorld.getBlockState(pos.down()).getBlock();

        // Deve ter ar nos 2 blocos de altura (considerar folhas como passáveis)
        boolean hasSpace = (blockAtPos == Blocks.air || blockAtPos == Blocks.leaves || !blockAtPos.isFullBlock()) &&
                          (blockAbove == Blocks.air || blockAbove == Blocks.leaves || !blockAbove.isFullBlock());

        // Chão sólido embaixo (excluir todos os líquidos)
        boolean hasSolidGround = blockBelow.isFullBlock() &&
                               blockBelow != Blocks.water &&
                               blockBelow != Blocks.flowing_water &&
                               blockBelow != Blocks.lava &&
                               blockBelow != Blocks.flowing_lava &&
                               blockBelow.getMaterial().blocksMovement();

        return hasSpace && hasSolidGround;
    }
    
    /**
     * Verifica linha de visão entre duas posições
     */
    private boolean checkLineOfSight(BlockPos from, BlockPos to) {
        // Implementação simplificada - pode ser otimizada
        Vec3 fromVec = new Vec3(from.getX() + 0.5, from.getY() + 1.6, from.getZ() + 0.5);
        Vec3 toVec = new Vec3(to.getX() + 0.5, to.getY() + 0.5, to.getZ() + 0.5);
        
        double distance = fromVec.distanceTo(toVec);
        int steps = Math.max(1, (int) Math.ceil(distance));
        
        for (int i = 0; i <= steps; i++) {
            double t = (double) i / steps;
            Vec3 direction = toVec.subtract(fromVec);
            Vec3 scaledDirection = new Vec3(direction.xCoord * t, direction.yCoord * t, direction.zCoord * t);
            Vec3 point = fromVec.add(scaledDirection);
            BlockPos checkPos = new BlockPos(point);
            
            Block block = mc.theWorld.getBlockState(checkPos).getBlock();
            if (block.isFullBlock() && block != Blocks.leaves) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Verifica se um bloco é um log de árvore
     */
    private boolean isLogBlock(Block block) {
        String blockName = Block.blockRegistry.getNameForObject(block).toString();
        return blockName.contains("log") || 
               blockName.contains("wood") ||
               block == Blocks.log || 
               block == Blocks.log2;
    }
    
    /**
     * Atualiza uma região específica do grid (para mudanças dinâmicas)
     */
    public void updateGridRegion(MappedGrid3D grid, BlockPos changedPos, int updateRadius) {
        if (grid == null || mc.theWorld == null) return;
        
        // Atualizar células ao redor da posição modificada
        for (int x = -updateRadius; x <= updateRadius; x++) {
            for (int z = -updateRadius; z <= updateRadius; z++) {
                for (int y = -updateRadius; y <= updateRadius; y++) {
                    BlockPos updatePos = changedPos.add(x, y, z);
                    
                    // Verificar se está dentro do grid original
                    if (grid.cells.containsKey(updatePos)) {
                        GridCell newCell = analyzePosition(updatePos, grid.center);
                        if (newCell != null) {
                            grid.cells.put(updatePos, newCell);
                        }
                    }
                }
            }
        }
    }
}
