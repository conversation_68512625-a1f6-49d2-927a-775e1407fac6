package com.rato.addons.commands;

import com.rato.addons.pathfinding.SimplePathfindingSystem;
import com.rato.addons.util.Logger;
import net.minecraft.client.Minecraft;
import net.minecraft.command.CommandBase;
import net.minecraft.command.CommandException;
import net.minecraft.command.ICommandSender;
import net.minecraft.util.Vec3;
import net.minecraft.util.ChatComponentText;

/**
 * Comando simplificado para testar pathfinding
 * Uso: /simplepath [tree|pos x y z|stop|stats]
 */
public class SimplePathCommand extends CommandBase {
    
    private static SimplePathfindingSystem pathSystem = new SimplePathfindingSystem();
    private final Minecraft mc = Minecraft.getMinecraft();
    
    @Override
    public String getCommandName() {
        return "simplepath";
    }
    
    @Override
    public String getCommandUsage(ICommandSender sender) {
        return "/simplepath [tree|pos x y z|stop|stats|help]";
    }
    
    @Override
    public int getRequiredPermissionLevel() {
        return 0;
    }
    
    @Override
    public void processCommand(ICommandSender sender, String[] args) throws CommandException {
        if (args.length == 0) {
            showHelp();
            return;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "tree":
            case "arvore":
                pathToTree();
                break;
                
            case "pos":
            case "position":
                if (args.length >= 4) {
                    try {
                        double x = Double.parseDouble(args[1]);
                        double y = Double.parseDouble(args[2]);
                        double z = Double.parseDouble(args[3]);
                        pathToPosition(x, y, z);
                    } catch (NumberFormatException e) {
                        Logger.sendMessage("§cCoordenadas inválidas! Use números.");
                    }
                } else {
                    Logger.sendMessage("§cUso: /simplepath pos <x> <y> <z>");
                }
                break;
                
            case "stop":
            case "parar":
                stopPath();
                break;
                
            case "stats":
            case "status":
                showStats();
                break;
                
            case "help":
            case "ajuda":
                showHelp();
                break;
                
            case "forward":
            case "frente":
                pathForward();
                break;
                
            case "test":
            case "teste":
                runTest();
                break;
                
            default:
                Logger.sendMessage("§cComando inválido! Use /simplepath help");
                break;
        }
    }
    
    /**
     * Inicia pathfinding para árvore mais próxima
     */
    private void pathToTree() {
        if (mc.thePlayer == null) {
            Logger.sendMessage("§cJogador não disponível!");
            return;
        }
        
        Logger.sendMessage("§7Procurando árvore mais próxima...");
        
        boolean success = pathSystem.startTreePathfinding();
        
        if (success) {
            Logger.sendMessage("§aPathfinding para árvore iniciado!");
        } else {
            Logger.sendMessage("§cFalha ao iniciar pathfinding para árvore!");
        }
    }
    
    /**
     * Inicia pathfinding para posição específica
     */
    private void pathToPosition(double x, double y, double z) {
        if (mc.thePlayer == null) {
            Logger.sendMessage("§cJogador não disponível!");
            return;
        }
        
        Vec3 target = new Vec3(x, y, z);
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        double distance = playerPos.distanceTo(target);
        
        Logger.sendMessage("§7Iniciando pathfinding para: " + 
                         String.format("%.1f, %.1f, %.1f", x, y, z));
        Logger.sendMessage("§7Distância: " + String.format("%.1f", distance) + " blocos");
        
        boolean success = pathSystem.startPathfinding(target);
        
        if (success) {
            Logger.sendMessage("§aPathfinding iniciado!");
        } else {
            Logger.sendMessage("§cFalha ao iniciar pathfinding!");
        }
    }
    
    /**
     * Para o pathfinding
     */
    private void stopPath() {
        if (pathSystem.isActive()) {
            pathSystem.stopPathfinding();
            Logger.sendMessage("§aPathfinding parado!");
        } else {
            Logger.sendMessage("§7Pathfinding não está ativo.");
        }
    }
    
    /**
     * Mostra estatísticas
     */
    private void showStats() {
        Logger.sendMessage("§6=== PATHFINDING STATS ===");
        Logger.sendMessage(pathSystem.getStats());
        
        if (mc.thePlayer != null) {
            Vec3 pos = mc.thePlayer.getPositionVector();
            Logger.sendMessage("§7Posição atual: " + 
                             String.format("%.1f, %.1f, %.1f", pos.xCoord, pos.yCoord, pos.zCoord));
        }
    }
    
    /**
     * Pathfinding para frente
     */
    private void pathForward() {
        if (mc.thePlayer == null) {
            Logger.sendMessage("§cJogador não disponível!");
            return;
        }
        
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        float yaw = mc.thePlayer.rotationYaw;
        
        // Calcular posição 10 blocos à frente
        double radians = Math.toRadians(yaw);
        double x = playerPos.xCoord - Math.sin(radians) * 10;
        double z = playerPos.zCoord + Math.cos(radians) * 10;
        
        Vec3 target = new Vec3(x, playerPos.yCoord, z);
        
        Logger.sendMessage("§7Pathfinding 10 blocos à frente...");
        
        boolean success = pathSystem.startPathfinding(target);
        
        if (success) {
            Logger.sendMessage("§aPathfinding iniciado!");
        } else {
            Logger.sendMessage("§cFalha ao iniciar pathfinding!");
        }
    }
    
    /**
     * Executa teste básico
     */
    private void runTest() {
        if (mc.thePlayer == null) {
            Logger.sendMessage("§cJogador não disponível!");
            return;
        }
        
        Logger.sendMessage("§6=== TESTE DE PATHFINDING ===");
        
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        Logger.sendMessage("§7Posição do jogador: " + 
                         String.format("%.1f, %.1f, %.1f", playerPos.xCoord, playerPos.yCoord, playerPos.zCoord));
        
        // Teste 1: Pathfinding curto
        Vec3 shortTarget = new Vec3(playerPos.xCoord + 5, playerPos.yCoord, playerPos.zCoord);
        Logger.sendMessage("§7Teste 1: Pathfinding curto (5 blocos)");
        
        boolean success = pathSystem.startPathfinding(shortTarget);
        Logger.sendMessage(success ? "§a✓ Sucesso" : "§c✗ Falha");
        
        if (success) {
            // Aguardar um pouco e parar
            new Thread(() -> {
                try {
                    Thread.sleep(3000); // 3 segundos
                    pathSystem.stopPathfinding();
                    Logger.sendMessage("§7Teste concluído!");
                } catch (InterruptedException e) {
                    // Ignorar
                }
            }).start();
        }
    }
    
    /**
     * Mostra ajuda
     */
    private void showHelp() {
        Logger.sendMessage("§6=== SIMPLE PATHFINDING ===");
        Logger.sendMessage("§7/simplepath tree - Ir para árvore mais próxima");
        Logger.sendMessage("§7/simplepath pos <x> <y> <z> - Ir para posição");
        Logger.sendMessage("§7/simplepath forward - Ir 10 blocos à frente");
        Logger.sendMessage("§7/simplepath stop - Parar pathfinding");
        Logger.sendMessage("§7/simplepath stats - Ver estatísticas");
        Logger.sendMessage("§7/simplepath test - Executar teste");
        Logger.sendMessage("§7/simplepath help - Mostrar ajuda");
        Logger.sendMessage("");
        Logger.sendMessage("§eExemplos:");
        Logger.sendMessage("§7/simplepath tree");
        Logger.sendMessage("§7/simplepath pos 100 64 200");
        Logger.sendMessage("§7/simplepath forward");
    }
    
    /**
     * Atualiza o sistema (chamado externamente)
     */
    public static void updateSystem() {
        if (pathSystem != null) {
            pathSystem.update();
        }
    }
    
    /**
     * Para o sistema (chamado externamente)
     */
    public static void stopSystem() {
        if (pathSystem != null) {
            pathSystem.stopPathfinding();
        }
    }
    
    /**
     * Obtém o sistema
     */
    public static SimplePathfindingSystem getSystem() {
        return pathSystem;
    }
}
