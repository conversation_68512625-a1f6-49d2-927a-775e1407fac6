package com.rato.addons.features;

import com.rato.addons.util.Logger;
import net.minecraft.client.Minecraft;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.InputEvent;
import org.lwjgl.input.Keyboard;

/**
 * Sistema de parada de emergência universal
 * Tecla B para parar todos os scripts/macros ativos
 */
public class EmergencyStop {
    
    private static EmergencyStop instance;
    private final Minecraft mc = Minecraft.getMinecraft();
    
    public static EmergencyStop getInstance() {
        if (instance == null) {
            instance = new EmergencyStop();
        }
        return instance;
    }
    
    @SubscribeEvent
    public void onKeyInput(InputEvent.KeyInputEvent event) {
        if (!Keyboard.getEventKeyState()) return;
        
        // Tecla B: Parada de emergência universal
        if (Keyboard.isKeyDown(Keyboard.KEY_B)) {
            performEmergencyStop();
        }
    }
    
    /**
     * Executa parada de emergência de todos os sistemas
     */
    public void performEmergencyStop() {
        Logger.sendMessage("§c§l[EMERGENCY STOP] §fParando todos os scripts...");
        
        boolean anythingStopped = false;
        
        // Parar Foraging Macro
        if (ForagingMacro.getInstance().isActive()) {
            ForagingMacro.getInstance().stopForaging();
            anythingStopped = true;
        }
        
        // Parar Pathfinding
        if (com.rato.addons.pathfinding.WaypointPathfinder.getInstance().isActive()) {
            com.rato.addons.pathfinding.WaypointPathfinder.getInstance().stopPathfinding();
            anythingStopped = true;
        }
        
        // Parar Freecam se ativo
        if (Freecam.getInstance().isFreecamActive()) {
            Freecam.getInstance().toggle();
            anythingStopped = true;
        }
        
        // Parar qualquer movimento de teclas
        stopAllMovement();
        
        if (anythingStopped) {
            Logger.sendMessage("§a§l[EMERGENCY STOP] §fTodos os scripts foram parados!");
        } else {
            Logger.sendMessage("§7§l[EMERGENCY STOP] §fNenhum script estava ativo.");
        }
        
        // Som de confirmação
        if (mc.thePlayer != null) {
            mc.thePlayer.playSound("random.orb", 1.0f, 1.0f);
        }
    }
    
    /**
     * Para todos os movimentos de teclas
     */
    private void stopAllMovement() {
        if (mc.gameSettings == null) return;
        
        // Soltar todas as teclas de movimento
        setKeyState(mc.gameSettings.keyBindForward, false);
        setKeyState(mc.gameSettings.keyBindBack, false);
        setKeyState(mc.gameSettings.keyBindLeft, false);
        setKeyState(mc.gameSettings.keyBindRight, false);
        setKeyState(mc.gameSettings.keyBindJump, false);
        setKeyState(mc.gameSettings.keyBindSneak, false);
        setKeyState(mc.gameSettings.keyBindSprint, false);
    }
    
    /**
     * Define estado de uma tecla
     */
    private void setKeyState(net.minecraft.client.settings.KeyBinding keyBinding, boolean pressed) {
        if (keyBinding == null) return;
        
        try {
            keyBinding.setKeyBindState(keyBinding.getKeyCode(), pressed);
        } catch (Exception e) {
            // Ignorar erros de key binding
        }
    }
    
    /**
     * Verifica se algum script está ativo
     */
    public boolean isAnyScriptActive() {
        return ForagingMacro.getInstance().isActive() ||
               com.rato.addons.pathfinding.WaypointPathfinder.getInstance().isActive() ||
               Freecam.getInstance().isFreecamActive();
    }
    
    /**
     * Obtém status de todos os scripts
     */
    public String getScriptsStatus() {
        StringBuilder status = new StringBuilder();
        status.append("§6=== SCRIPTS STATUS ===\n");
        
        status.append("§7Foraging: ").append(ForagingMacro.getInstance().isActive() ? "§aAtivo" : "§cInativo").append("\n");
        status.append("§7Pathfinding: ").append(com.rato.addons.pathfinding.WaypointPathfinder.getInstance().isActive() ? "§aAtivo" : "§cInativo").append("\n");
        status.append("§7Freecam: ").append(Freecam.getInstance().isFreecamActive() ? "§aAtivo" : "§cInativo").append("\n");
        
        status.append("§7Pressione §c§lB §7para parada de emergência");
        
        return status.toString();
    }
}
