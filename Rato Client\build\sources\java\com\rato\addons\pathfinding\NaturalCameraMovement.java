package com.rato.addons.pathfinding;

import com.rato.addons.config.RatoAddonsConfigSimple;
import net.minecraft.client.Minecraft;
import net.minecraft.util.MathHelper;
import net.minecraft.util.Vec3;

import java.util.Random;

/**
 * Sistema de movimento natural de câmera para pathfinding
 * Simula movimentos humanos realistas
 */
public class NaturalCameraMovement {
    
    private final Minecraft mc = Minecraft.getMinecraft();
    private final Random random = new Random();
    
    // Configurações de movimento (agora configuráveis)
    private static final float BASE_SENSITIVITY = 0.05f; // Muito reduzido
    private static final boolean ENABLE_HUMAN_VARIATIONS = false; // Desabilitar variações

    // Configurações dinâmicas baseadas no config e velocidade
    private float getMaxRotationSpeed() {
        // Se detecção automática estiver ativa, adaptar baseado na velocidade
        if (RatoAddonsConfigSimple.pathfindingAutoSpeedDetection) {
            float playerSpeed = getCurrentPlayerSpeed();
            if (playerSpeed > 2.0f) { // Speed II+
                return RatoAddonsConfigSimple.pathfindingRotationSpeed * 0.5f; // Muito mais lento
            } else if (playerSpeed > 1.0f) { // Speed I
                return RatoAddonsConfigSimple.pathfindingRotationSpeed * 0.8f; // Mais lento
            }
        }
        return RatoAddonsConfigSimple.pathfindingRotationSpeed * 2.0f; // Multiplicador para range adequado
    }

    private float getInterpolationSpeed() {
        // Se detecção automática estiver ativa, adaptar baseado na velocidade
        if (RatoAddonsConfigSimple.pathfindingAutoSpeedDetection) {
            float playerSpeed = getCurrentPlayerSpeed();
            if (playerSpeed > 2.0f) { // Speed II+
                return RatoAddonsConfigSimple.pathfindingHighSpeedSmoothness;
            } else if (playerSpeed > 1.0f) { // Speed I
                return RatoAddonsConfigSimple.pathfindingSmoothness * 0.5f; // Mais suave
            }
        }
        return RatoAddonsConfigSimple.pathfindingSmoothness;
    }

    /**
     * Calcula velocidade atual do jogador
     */
    private float getCurrentPlayerSpeed() {
        if (mc.thePlayer == null) return 0.0f;

        double motionX = mc.thePlayer.motionX;
        double motionZ = mc.thePlayer.motionZ;
        return (float) Math.sqrt(motionX * motionX + motionZ * motionZ) * 20.0f; // Converter para blocos/segundo
    }

    private static final float MIN_ROTATION_THRESHOLD = 0.1f; // Threshold mínimo para rotação
    
    // Estado do movimento
    private float targetYaw = 0;
    private float targetPitch = 0;
    private float currentYaw = 0;
    private float currentPitch = 0;
    
    // Variações humanas
    private long lastMovementTime = 0;
    private float microMovementTimer = 0;
    private float breathingOffset = 0;
    
    /**
     * Atualiza movimento da câmera - DESABILITADO durante pathfinding para evitar interferência
     */
    public void updateCameraMovement(Vec3 targetPosition) {
        // SISTEMA DESABILITADO - deixar pathfinding controlar completamente
        // Qualquer interferência aqui causa movimento circular
        return;
    }

    /**
     * Decide se deve olhar para o alvo ou fazer movimento natural
     */
    private boolean shouldLookAtTarget() {
        // Quase nunca interferir - deixar pathfinding controlar
        return random.nextFloat() < 0.001f; // Apenas 0.1% do tempo olha para waypoint
    }

    /**
     * Movimento de escaneamento natural quando não está focado no waypoint
     */
    private void performNaturalScanning() {
        microMovementTimer += 0.005f; // Muito mais lento

        // Movimento de escaneamento muito sutil - altura normal
        float scanYaw = (float) Math.sin(microMovementTimer * 0.2f) * 2.0f; // Reduzido ainda mais

        // CORRIGIDO: Definir targetYaw em vez de somar
        targetYaw = currentYaw + scanYaw; // Movimento relativo à posição atual
        targetPitch = 0.0f; // Manter pitch neutro (altura normal)

        // Normalizar targetYaw para evitar valores extremos
        targetYaw = normalizeAngle(targetYaw);
    }
    
    /**
     * Calcula rotação alvo de forma muito suave
     */
    private void calculateSmoothTargetRotation(Vec3 direction) {
        // Calcular yaw (rotação horizontal)
        float newTargetYaw = (float) Math.toDegrees(Math.atan2(-direction.xCoord, direction.zCoord));

        // Calcular pitch (rotação vertical) - manter próximo de 0 para altura normal
        double horizontalDistance = Math.sqrt(direction.xCoord * direction.xCoord + direction.zCoord * direction.zCoord);
        float newTargetPitch = (float) -Math.toDegrees(Math.atan2(direction.yCoord, horizontalDistance));

        // Limitar pitch para altura mais natural
        newTargetPitch = MathHelper.clamp_float(newTargetPitch, -15.0f, 15.0f); // Muito limitado

        // Interpolar suavemente para o novo alvo em vez de definir diretamente
        float yawDiff = normalizeAngle(newTargetYaw - targetYaw);
        float pitchDiff = newTargetPitch - targetPitch;

        // Aplicar apenas uma pequena fração da diferença
        targetYaw += yawDiff * 0.1f; // Muito gradual
        targetPitch += pitchDiff * 0.05f; // Ainda mais gradual para pitch

        // Normalizar
        targetYaw = normalizeAngle(targetYaw);
        targetPitch = MathHelper.clamp_float(targetPitch, -15.0f, 15.0f);
    }
    
    /**
     * Aplica rotação super suave usando interpolação linear configurável
     */
    private void applySuperSmoothRotation() {
        // Calcular diferença angular
        float yawDiff = normalizeAngle(targetYaw - currentYaw);
        float pitchDiff = targetPitch - currentPitch;

        // Interpolação linear configurável (lerp)
        float yawLerpFactor = getInterpolationSpeed(); // Configurável
        float pitchLerpFactor = getInterpolationSpeed() * 0.5f; // Ainda mais gradual para pitch

        // Aplicar apenas se a diferença for significativa
        if (Math.abs(yawDiff) > MIN_ROTATION_THRESHOLD) {
            currentYaw += yawDiff * yawLerpFactor;
        }

        if (Math.abs(pitchDiff) > MIN_ROTATION_THRESHOLD) {
            currentPitch += pitchDiff * pitchLerpFactor;
        }

        // Normalizar ângulos
        currentYaw = normalizeAngle(currentYaw);
        currentPitch = MathHelper.clamp_float(currentPitch, -15.0f, 15.0f); // Altura natural
    }
    
    /**
     * Variações humanas desabilitadas para eliminar shake
     */
    private void addHumanVariations() {
        // Completamente desabilitado para eliminar qualquer shake
        // O movimento natural será feito apenas pelo escaneamento sutil
    }
    
    /**
     * Hesitação desabilitada para eliminar shake
     */
    private void addHesitation() {
        // Desabilitado para eliminar qualquer movimento brusco
    }
    
    /**
     * Normaliza ângulo para -180 a 180 graus
     */
    private float normalizeAngle(float angle) {
        while (angle > 180.0f) angle -= 360.0f;
        while (angle < -180.0f) angle += 360.0f;
        return angle;
    }
    
    /**
     * Verifica se a câmera está próxima do alvo
     */
    public boolean isLookingAtTarget(Vec3 targetPosition, float tolerance) {
        if (mc.thePlayer == null) return false;
        
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        Vec3 direction = targetPosition.subtract(playerPos).normalize();
        
        float requiredYaw = (float) Math.toDegrees(Math.atan2(-direction.xCoord, direction.zCoord));
        double horizontalDistance = Math.sqrt(direction.xCoord * direction.xCoord + direction.zCoord * direction.zCoord);
        float requiredPitch = (float) -Math.toDegrees(Math.atan2(direction.yCoord, horizontalDistance));
        
        float yawDiff = Math.abs(normalizeAngle(requiredYaw - currentYaw));
        float pitchDiff = Math.abs(requiredPitch - currentPitch);
        
        return yawDiff < tolerance && pitchDiff < tolerance;
    }
    
    /**
     * Define rotação inicial
     */
    public void setInitialRotation(float yaw, float pitch) {
        this.currentYaw = yaw;
        this.currentPitch = pitch;
        this.targetYaw = yaw;
        this.targetPitch = pitch;
    }
    
    /**
     * Obtém rotação atual
     */
    public float getCurrentYaw() {
        return currentYaw;
    }
    
    public float getCurrentPitch() {
        return currentPitch;
    }
    
    /**
     * Movimento de escaneamento (olhar ao redor)
     */
    public void performScanMovement() {
        // Movimento de escaneamento natural
        float scanRange = 15.0f; // Reduzido
        float scanSpeed = 0.5f; // Mais lento

        float scanYaw = (float) Math.sin(microMovementTimer * scanSpeed) * scanRange;
        float scanPitch = 0.0f; // Sem movimento vertical

        // CORRIGIDO: Definir em vez de somar
        targetYaw = currentYaw + scanYaw * 0.05f; // Muito reduzido
        targetPitch = 0.0f; // Altura normal

        // Normalizar
        targetYaw = normalizeAngle(targetYaw);
    }
    
    /**
     * Calcula tempo estimado para olhar para o alvo
     */
    public float getTimeToTarget(Vec3 targetPosition) {
        if (mc.thePlayer == null) return 0;
        
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        Vec3 direction = targetPosition.subtract(playerPos).normalize();
        
        float requiredYaw = (float) Math.toDegrees(Math.atan2(-direction.xCoord, direction.zCoord));
        double horizontalDistance = Math.sqrt(direction.xCoord * direction.xCoord + direction.zCoord * direction.zCoord);
        float requiredPitch = (float) -Math.toDegrees(Math.atan2(direction.yCoord, horizontalDistance));
        
        float yawDiff = Math.abs(normalizeAngle(requiredYaw - currentYaw));
        float pitchDiff = Math.abs(requiredPitch - currentPitch);
        
        float maxDiff = Math.max(yawDiff, pitchDiff);
        return maxDiff / getMaxRotationSpeed(); // Tempo em segundos
    }
}
