package com.rato.addons.mixin.client;

import com.rato.addons.util.CPSTracker;
import net.minecraft.client.Minecraft;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(Minecraft.class)
public class MixinMinecraft {

    @Inject(method = "clickMouse", at = @At("HEAD"))
    private void onLeftClick(CallbackInfo ci) {
        CPSTracker.getInstance().addLeftClick();
    }

    @Inject(method = "rightClickMouse", at = @At("HEAD"))
    private void onRightClick(CallbackInfo ci) {
        CPSTracker.getInstance().addRightClick();
    }
}
