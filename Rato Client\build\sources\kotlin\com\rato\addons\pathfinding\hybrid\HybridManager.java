package com.rato.addons.pathfinding.hybrid;

import com.rato.addons.pathfinding.enhanced.EnhancedLookManager;
import com.rato.addons.pathfinding.enhanced.EnhancedWalker;
import com.rato.addons.pathfinding.professional.MucifexPathRenderer;
import com.rato.addons.pathfinding.professional.PathNode;
import com.rato.addons.util.Logger;
import net.minecraft.client.Minecraft;
import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Gerenciador do sistema híbrido Mucifex + Stevebot
 * Combina as melhores características de ambos os sistemas
 */
public class HybridManager {
    
    private static HybridManager instance;
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Componentes do sistema
    private final HybridPathfinder pathfinder;
    private final EnhancedWalker walker;
    private final EnhancedLookManager lookManager;
    private final MucifexPathRenderer pathRenderer;
    private final ExecutorService pathfindingExecutor;
    
    // Estado do sistema
    private boolean isActive = false;
    private boolean isCalculating = false;
    private boolean debugMode = false;
    private boolean renderingEnabled = true;
    
    // Configurações
    private float movementSpeed = 1.0f;
    private float rotationSmoothness = 0.9f;
    
    // Estatísticas
    private long pathfindingStartTime;
    private int pathCalculations = 0;
    private Vec3 currentDestination;
    private List<PathNode> currentPath;
    
    // Controle de recálculo
    private long lastRecalculation = 0;
    private static final long RECALCULATION_COOLDOWN = 3000; // 3 segundos
    
    private HybridManager() {
        pathfinder = new HybridPathfinder();
        walker = EnhancedWalker.getInstance();
        lookManager = EnhancedLookManager.getInstance();
        pathRenderer = new MucifexPathRenderer();
        pathfindingExecutor = Executors.newSingleThreadExecutor(r -> {
            Thread t = new Thread(r, "Hybrid-Pathfinding");
            t.setDaemon(true);
            return t;
        });
        
        setupCallbacks();
        MinecraftForge.EVENT_BUS.register(this);
    }
    
    public static HybridManager getInstance() {
        if (instance == null) {
            instance = new HybridManager();
        }
        return instance;
    }
    
    /**
     * Configura callbacks do sistema
     */
    private void setupCallbacks() {
        walker.setOnDestinationReached(() -> {
            // Verificar se realmente chegou ao destino final
            if (currentDestination != null) {
                Vec3 playerPos = getPlayerPosition();
                double distanceToFinalDestination = playerPos.distanceTo(currentDestination);

                if (distanceToFinalDestination <= 2.0) {
                    Logger.sendMessage("§a[Hybrid] Destino final alcançado! Distância: " + String.format("%.2f", distanceToFinalDestination));
                    stopPathfinding();
                } else {
                    Logger.sendMessage("§c[Hybrid] Chegou ao fim do caminho mas não ao destino final!");
                    Logger.sendMessage("§c[Hybrid] Distância ao destino: " + String.format("%.2f", distanceToFinalDestination));
                    Logger.sendMessage("§e[Hybrid] Tentando pathfinding adicional para o destino final...");

                    // Tentar pathfinding adicional para chegar ao destino exato
                    tryAdditionalPathfinding();
                }
            } else {
                Logger.sendMessage("§a[Hybrid] Destino alcançado!");
                stopPathfinding();
            }
        });
        
        walker.setOnMovementFailed(() -> {
            if (debugMode) {
                Logger.sendMessage("§e[Hybrid] Falha no movimento, recalculando...");
            }
            scheduleRecalculation();
        });
    }
    
    /**
     * Inicia pathfinding para destino específico
     */
    public boolean startPathfinding(Vec3 destination) {
        if (mc.thePlayer == null || mc.theWorld == null) {
            return false;
        }
        
        if (isActive) {
            stopPathfinding();
        }
        
        currentDestination = destination;
        pathfindingStartTime = System.currentTimeMillis();
        pathCalculations = 0;
        
        Vec3 playerPos = getPlayerPosition();
        double distance = playerPos.distanceTo(destination);
        
        Logger.sendMessage("§6[Hybrid] Iniciando sistema híbrido Mucifex + Stevebot...");
        Logger.sendMessage("§7De: " + formatVec3(playerPos));
        Logger.sendMessage("§7Para: " + formatVec3(destination));
        Logger.sendMessage("§7Distância: " + String.format("%.1f", distance) + " blocos");

        if (debugMode) {
            Logger.sendMessage("§7[Hybrid] Debug ativado - informações detalhadas serão exibidas");
        }
        
        // Configurar componentes
        walker.setDebugMode(debugMode);
        walker.setSprintEnabled(movementSpeed > 1.0f);
        lookManager.setRotationSpeed(200.0 / rotationSmoothness);
        
        // Iniciar cálculo assíncrono
        calculatePathAsync(playerPos, destination);
        
        isActive = true;
        Logger.sendMessage("§a[Hybrid] Sistema híbrido iniciado! Calculando caminho...");
        
        return true;
    }
    
    /**
     * Calcula caminho de forma assíncrona
     */
    private void calculatePathAsync(Vec3 start, Vec3 goal) {
        if (isCalculating) return;
        
        isCalculating = true;
        pathCalculations++;
        
        CompletableFuture.supplyAsync(() -> {
            try {
                if (debugMode) {
                    Logger.sendMessage("§7[Hybrid] Calculando caminho híbrido... (" + pathCalculations + ")");
                }
                
                long startTime = System.currentTimeMillis();
                List<PathNode> path = pathfinder.findPath(start, goal);
                long calculationTime = System.currentTimeMillis() - startTime;
                
                if (debugMode && path != null) {
                    Logger.sendMessage("§a[Hybrid] Caminho híbrido calculado em " + calculationTime + "ms: " + 
                        path.size() + " nós");
                    
                    // Debug dos tipos de movimento
                    long jumpNodes = path.stream().filter(n -> n.movementType == PathNode.MovementType.JUMP).count();
                    long fallNodes = path.stream().filter(n -> n.movementType == PathNode.MovementType.FALL).count();
                    long diagonalNodes = path.stream().filter(n -> n.movementType == PathNode.MovementType.DIAGONAL).count();
                    
                    if (jumpNodes > 0 || fallNodes > 0 || diagonalNodes > 0) {
                        Logger.sendMessage("§7[Hybrid] Pulos: " + jumpNodes + ", Quedas: " + fallNodes + ", Diagonais: " + diagonalNodes);
                    }
                }
                
                return path;
                
            } catch (Exception e) {
                Logger.sendMessage("§c[Hybrid] Erro no cálculo: " + e.getMessage());
                if (debugMode) {
                    e.printStackTrace();
                }
                return null;
            }
        }, pathfindingExecutor).thenAccept(this::onPathCalculated);
    }
    
    /**
     * Callback quando caminho é calculado
     */
    private void onPathCalculated(List<PathNode> path) {
        isCalculating = false;
        
        if (!isActive) return;
        
        if (path != null && !path.isEmpty()) {
            currentPath = path;
            
            // Configurar renderização
            if (renderingEnabled) {
                pathRenderer.setPath(path);
                pathRenderer.setEnabled(true);
            }
            
            // Iniciar movimento
            walker.startWalking(path);
            
            if (debugMode) {
                Logger.sendMessage("§a[Hybrid] Iniciando movimento híbrido!");
            }
            
        } else {
            Logger.sendMessage("§c[Hybrid] Não foi possível encontrar um caminho!");
            stopPathfinding();
        }
    }
    
    /**
     * Agenda recálculo do caminho
     */
    private void scheduleRecalculation() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastRecalculation < RECALCULATION_COOLDOWN) {
            return; // Cooldown ativo
        }
        
        lastRecalculation = currentTime;
        
        if (isActive && currentDestination != null && !isCalculating) {
            Vec3 currentPos = getPlayerPosition();
            
            if (debugMode) {
                Logger.sendMessage("§7[Hybrid] Recalculando caminho híbrido...");
            }
            
            calculatePathAsync(currentPos, currentDestination);
        }
    }
    
    /**
     * Tenta pathfinding adicional para chegar ao destino exato
     */
    private void tryAdditionalPathfinding() {
        if (currentDestination == null) return;

        Vec3 playerPos = getPlayerPosition();
        Logger.sendMessage("§e[Hybrid] Calculando caminho adicional para destino final...");

        // Tentar pathfinding direto com muitas iterações
        List<PathNode> additionalPath = pathfinder.findPath(playerPos, currentDestination);

        if (additionalPath != null && !additionalPath.isEmpty()) {
            Logger.sendMessage("§a[Hybrid] Caminho adicional encontrado com " + additionalPath.size() + " nós!");
            walker.startWalking(additionalPath);
        } else {
            Logger.sendMessage("§c[Hybrid] Não foi possível encontrar caminho adicional");
            Logger.sendMessage("§a[Hybrid] Aceitando posição atual como destino final");
            stopPathfinding();
        }
    }

    /**
     * Para o pathfinding atual
     */
    public void stopPathfinding() {
        isActive = false;
        isCalculating = false;
        
        walker.stopWalking();
        lookManager.cancel();
        
        if (pathRenderer != null) {
            pathRenderer.setEnabled(false);
        }
        
        currentPath = null;
        currentDestination = null;
        
        if (debugMode) {
            Logger.sendMessage("§7[Hybrid] Sistema híbrido parado");
        }
    }
    
    /**
     * Atualização principal (chamada a cada tick)
     */
    @SubscribeEvent
    public void onTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.START || !isActive) {
            return;
        }
        
        if (mc.thePlayer == null || mc.theWorld == null) {
            stopPathfinding();
            return;
        }
        
        // Verificar se precisa recalcular periodicamente
        if (shouldPeriodicRecalculation()) {
            scheduleRecalculation();
        }
    }
    
    /**
     * Verifica se deve fazer recálculo periódico
     */
    private boolean shouldPeriodicRecalculation() {
        if (currentDestination == null || isCalculating) return false;
        
        long timeSinceStart = System.currentTimeMillis() - pathfindingStartTime;
        long timeSinceLastRecalc = System.currentTimeMillis() - lastRecalculation;
        
        // Recalcular a cada 20 segundos se estiver ativo há mais de 30 segundos
        return timeSinceStart > 30000 && timeSinceLastRecalc > 20000;
    }
    
    /**
     * Obtém posição correta do player
     */
    private Vec3 getPlayerPosition() {
        if (mc.thePlayer == null) return new Vec3(0, 0, 0);

        // Usar posição atual do player (não Math.floor para melhor precisão)
        double x = mc.thePlayer.posX;
        double y = mc.thePlayer.posY;
        double z = mc.thePlayer.posZ;

        Vec3 pos = new Vec3(x, y, z);

        if (debugMode) {
            Logger.sendMessage("§7[Hybrid] Posição do player: " + String.format("%.2f, %.2f, %.2f", x, y, z));
        }

        return pos;
    }
    
    /**
     * Verifica se um bloco é sólido
     */
    private boolean isBlockSolid(BlockPos pos) {
        if (mc.theWorld == null) return false;

        try {
            return mc.theWorld.getBlockState(pos).getBlock().getMaterial().blocksMovement();
        } catch (Exception e) {
            return false;
        }
    }

    // Getters e Setters
    public boolean isActive() { return isActive; }
    public boolean isCalculating() { return isCalculating; }
    public void setDebugMode(boolean debug) {
        this.debugMode = debug;
        if (walker != null) walker.setDebugMode(debug);
    }
    public boolean isDebugMode() { return debugMode; }

    public void setRenderingEnabled(boolean enabled) {
        this.renderingEnabled = enabled;
        if (pathRenderer != null) {
            pathRenderer.setEnabled(enabled && isActive);
        }
    }
    public boolean isRenderingEnabled() { return renderingEnabled; }

    public void setMovementSpeed(float speed) {
        this.movementSpeed = speed;
        if (walker != null) walker.setSprintEnabled(speed > 1.0f);
    }
    public float getMovementSpeed() { return movementSpeed; }

    public void setRotationSmoothness(float smoothness) {
        this.rotationSmoothness = smoothness;
        if (lookManager != null) lookManager.setRotationSpeed(200.0 / smoothness);
    }
    public float getRotationSmoothness() { return rotationSmoothness; }

    /**
     * Retorna status detalhado do sistema
     */
    public String getDetailedStatus() {
        if (!isActive) return "Inativo";

        long elapsed = System.currentTimeMillis() - pathfindingStartTime;
        StringBuilder status = new StringBuilder();

        status.append("Híbrido ativo há ").append(elapsed / 1000).append("s");
        status.append(", Cálculos: ").append(pathCalculations);

        if (currentPath != null) {
            status.append(", Nós: ").append(currentPath.size());
            float progress = walker.getProgress();
            status.append(" (").append(String.format("%.1f", progress * 100)).append("%)");
        }

        if (isCalculating) {
            status.append(", Calculando");
        } else if (walker.isActive()) {
            status.append(", Movendo");
        }

        return status.toString();
    }

    /**
     * Retorna informações de debug
     */
    public String getDebugInfo() {
        StringBuilder info = new StringBuilder();
        info.append("§7Status Híbrido: ").append(isActive ? "§aAtivo" : "§cInativo").append("\n");
        info.append("§7Destino: ").append(currentDestination != null ? formatVec3(currentDestination) : "§cNenhum").append("\n");
        info.append("§7Cálculos realizados: §f").append(pathCalculations).append("\n");
        info.append("§7Nós no caminho: §f").append(currentPath != null ? currentPath.size() : 0).append("\n");
        info.append("§7Calculando: ").append(isCalculating ? "§aSim" : "§cNão").append("\n");
        info.append("§7Walker: ").append(walker.isActive() ? "§aAtivo" : "§cInativo").append("\n");
        info.append("§7LookManager: ").append(lookManager.isActive() ? "§aAtivo" : "§cInativo").append("\n");
        info.append("§7Velocidade: §f").append(movementSpeed).append("\n");
        info.append("§7Suavidade: §f").append(rotationSmoothness).append("\n");
        info.append("§7Renderização: ").append(renderingEnabled ? "§aAtiva" : "§cInativa");

        if (walker.isActive()) {
            info.append("\n§7Walker Info: §f").append(walker.getDebugInfo());
        }

        if (lookManager.isActive()) {
            info.append("\n§7Look Info: §f").append(lookManager.getDebugInfo());
        }

        return info.toString();
    }

    /**
     * Obtém estatísticas de performance
     */
    public String getPerformanceStats() {
        if (!isActive) return "Sistema híbrido inativo";

        long elapsed = Math.max(1, System.currentTimeMillis() - pathfindingStartTime);
        double calculationsPerSecond = pathCalculations / (elapsed / 1000.0);

        StringBuilder stats = new StringBuilder();
        stats.append("Híbrido - Cálculos/s: ").append(String.format("%.2f", calculationsPerSecond));

        if (currentPath != null) {
            double nodesPerSecond = currentPath.size() / (elapsed / 1000.0);
            stats.append(", Nós/s: ").append(String.format("%.2f", nodesPerSecond));
        }

        return stats.toString();
    }

    /**
     * Força recálculo imediato
     */
    public void forceRecalculation() {
        if (isActive && currentDestination != null) {
            lastRecalculation = 0; // Reset cooldown
            scheduleRecalculation();
        }
    }

    /**
     * Limpa recursos
     */
    public void shutdown() {
        stopPathfinding();
        pathfindingExecutor.shutdown();
        walker.shutdown();
        lookManager.shutdown();
    }

    private String formatVec3(Vec3 vec) {
        return String.format("%.1f, %.1f, %.1f", vec.xCoord, vec.yCoord, vec.zCoord);
    }
}
