package com.rato.addons;

import com.rato.addons.config.RatoAddonsConfigSimple;
import com.rato.addons.config.CustomConfigManager;
import com.rato.addons.imgui.RatoImGuiRenderer;
import com.rato.addons.imgui.RatoImGuiKeyHandler;
import com.rato.addons.gui.GuiEventHandler;
import com.rato.addons.event.PacketHandler;
import com.rato.addons.failsafe.FailsafeManager;
import com.rato.addons.features.AdvancedStaffChecks;
import com.rato.addons.features.EmergencyStop;
import com.rato.addons.features.ESP;
import com.rato.addons.features.ForagingCommands;
import com.rato.addons.features.ForagingMacro;
import com.rato.addons.features.ForagingOverlay;
import com.rato.addons.features.Freecam;
import com.rato.addons.features.InventoryPIP;
import com.rato.addons.features.StaffCheckDetection;
import com.rato.addons.pathfinding.WaypointPathfinder;
import com.rato.addons.pathfinding.WaypointCommands;
import com.rato.addons.commands.TreeMarkerCommands;
import com.rato.addons.commands.PathfindingTestCommand;
import com.rato.addons.commands.SimplePathCommand;
import com.rato.addons.commands.RatoClientCommand;

import com.rato.addons.render.ModernESP;

import com.rato.addons.util.Logger;
import net.minecraft.client.Minecraft;
import net.minecraftforge.client.ClientCommandHandler;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.common.event.FMLInitializationEvent;
import net.minecraftforge.fml.common.event.FMLPostInitializationEvent;
import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
import org.lwjgl.opengl.Display;

@Mod(modid = RatoAddons.modid, useMetadata = true)
public class RatoAddons {

    public static final String modid = "ratoaddons";
    private static final Minecraft mc = Minecraft.getMinecraft();
    public static RatoAddonsConfigSimple config;
    @Mod.Instance
    public static RatoAddons instance;
    public final String VERSION = "1.0.0";

    @Mod.EventHandler
    public void preInit(FMLPreInitializationEvent event) {
        Logger.sendLog("RatoAddons PreInit - Basic setup");
    }

    @Mod.EventHandler
    public void init(FMLInitializationEvent event) {
        initializeFields();
        registerEventHandlers();

        // Inicializar sistema ImGui
        initializeImGui();

        mc.gameSettings.gammaSetting = 1000;
        mc.gameSettings.pauseOnLostFocus = false;

        Display.setTitle("RatoAddons 〔v" + VERSION + "〕" + mc.getSession().getUsername());

        Logger.sendLog("RatoAddons initialized");
    }

    @Mod.EventHandler
    public void postInit(FMLPostInitializationEvent event) {
        Logger.sendLog("RatoAddons PostInit");
    }

    /**
     * Inicializa o sistema ImGui nativo baseado no BloxxHaxx
     */
    private void initializeImGui() {
        try {
            Logger.sendLog("🎨 Inicializando sistema ImGui LWJGL...");

            // Inicializar sistema ImGui LWJGL
            RatoImGuiRenderer.initialize();

            // Inicializar handler de teclas
            RatoImGuiKeyHandler.initialize();

            Logger.sendLog("✅ Sistema ImGui LWJGL inicializado com sucesso!");
            Logger.sendLog("🔑 Pressione INSERT para abrir interface ImGui LWJGL");
            Logger.sendLog("🚨 Pressione END para panic stop");

        } catch (Exception e) {
            Logger.sendLog("❌ Erro ao inicializar ImGui LWJGL: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void initializeFields() {
        config = new RatoAddonsConfigSimple();

        // Carregar configurações customizadas
        CustomConfigManager.loadConfig();
        CustomConfigManager.syncWithOldConfig();
    }

    private void registerEventHandlers() {
        MinecraftForge.EVENT_BUS.register(new ESP());
        MinecraftForge.EVENT_BUS.register(new InventoryPIP());
        MinecraftForge.EVENT_BUS.register(Freecam.getInstance());
        MinecraftForge.EVENT_BUS.register(new StaffCheckDetection());
        MinecraftForge.EVENT_BUS.register(WaypointPathfinder.getInstance());
        MinecraftForge.EVENT_BUS.register(WaypointCommands.getInstance());

        // Registrar eventos do foraging
        MinecraftForge.EVENT_BUS.register(ForagingMacro.getInstance());
        MinecraftForge.EVENT_BUS.register(new ForagingCommands());
        MinecraftForge.EVENT_BUS.register(new ForagingOverlay());

        // Registrar sistema de marcação de árvores
        MinecraftForge.EVENT_BUS.register(new TreeMarkerCommands());

        // Registrar comando de teste do pathfinding 3D
        MinecraftForge.EVENT_BUS.register(new PathfindingTestCommand());

        // Registrar comando de pathfinding simplificado
        MinecraftForge.EVENT_BUS.register(new SimplePathCommand());

        // Registrar comando principal do RatoClient no ClientCommandHandler
        ClientCommandHandler.instance.registerCommand(new RatoClientCommand());

        // Registrar outros comandos no ClientCommandHandler
        ClientCommandHandler.instance.registerCommand(new TreeMarkerCommands());
        ClientCommandHandler.instance.registerCommand(new PathfindingTestCommand());
        ClientCommandHandler.instance.registerCommand(new SimplePathCommand());

        // Registrar sistema de parada de emergência
        MinecraftForge.EVENT_BUS.register(EmergencyStop.getInstance());

        MinecraftForge.EVENT_BUS.register(new AdvancedStaffChecks());
        MinecraftForge.EVENT_BUS.register(FailsafeManager.getInstance());
        MinecraftForge.EVENT_BUS.register(new PacketHandler());

        // Registrar sistema Rift (temporariamente desabilitado)
        // MinecraftForge.EVENT_BUS.register(RiftAutofarm.getInstance());
        MinecraftForge.EVENT_BUS.register(new ModernESP());

        // Sistema de Visuals integrado ao ModernESP

        // Registrar GUI customizada
        MinecraftForge.EVENT_BUS.register(new GuiEventHandler());

        // Registrar sistema ImGui LWJGL
        MinecraftForge.EVENT_BUS.register(new RatoImGuiKeyHandler());
        if (RatoImGuiRenderer.getInstance() != null) {
            MinecraftForge.EVENT_BUS.register(RatoImGuiRenderer.getInstance());
        }

        Logger.sendMessage("§aRato Client carregado com sucesso!");
    }
}
