package com.rato.addons.features;

import com.rato.addons.config.RatoAddonsConfigSimple;
import com.rato.addons.util.RenderUtils;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.FontRenderer;
import net.minecraft.client.gui.ScaledResolution;
import net.minecraftforge.client.event.RenderGameOverlayEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;

import java.awt.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Overlay HUD para mostrar informações do Foraging Macro
 */
public class ForagingOverlay {
    
    private final Minecraft mc = Minecraft.getMinecraft();
    
    @SubscribeEvent
    public void onRenderOverlay(RenderGameOverlayEvent.Post event) {
        if (event.type != RenderGameOverlayEvent.ElementType.HOTBAR) return;
        if (!RatoAddonsConfigSimple.foragingEnabled) return;
        
        ForagingMacro macro = ForagingMacro.getInstance();
        if (!macro.isActive()) return;
        
        renderForagingHUD(macro);
    }
    
    /**
     * Renderiza o HUD do foraging macro
     */
    private void renderForagingHUD(ForagingMacro macro) {
        ScaledResolution sr = new ScaledResolution(mc);
        FontRenderer fr = mc.fontRendererObj;
        
        // Posição do HUD (canto superior direito)
        int hudX = sr.getScaledWidth() - 200;
        int hudY = 10;
        int hudWidth = 190;
        
        // Preparar informações
        List<String> lines = new ArrayList<>();
        lines.add("§6§lForaging Macro §7(Treecapitator)");
        lines.add("§7Area: §e" + macro.getCurrentArea().getDisplayName());
        lines.add("§7Status: §e" + macro.getCurrentState().getDisplayName());
        lines.add("§7Trees: §a" + macro.getTreesChopped() + " §7(1-hit)");
        
        // Adicionar informações do alvo se existir
        if (macro.getTargetTree() != null) {
            lines.add("§7Target: §e" + macro.getTargetTree().getX() + ", " +
                macro.getTargetTree().getY() + ", " + macro.getTargetTree().getZ());
        }

        // Mostrar status de quebra se aplicável
        if (macro.isActive()) {
            if (macro.getCurrentState() == ForagingMacro.ForagingState.CHOPPING) {
                lines.add("§7Mode: §cBreaking Block...");
            } else {
                lines.add("§7Mode: §eNext Tree After 1-Hit");
            }
        }
        
        // Calcular altura do HUD
        int lineHeight = 10;
        int hudHeight = lines.size() * lineHeight + 10;
        
        // Renderizar fundo com bordas arredondadas
        int backgroundColor = new Color(0, 0, 0, 120).getRGB();
        int borderColor = new Color(0, 255, 0, 180).getRGB();

        RenderUtils.renderRoundedQuad(hudX - 5, hudY - 5, hudWidth, hudHeight, 5, backgroundColor);
        // Renderizar borda simples (método renderRoundedBorder não existe)
        RenderUtils.renderRoundedQuad(hudX - 6, hudY - 6, hudWidth + 2, hudHeight + 2, 5, borderColor);
        
        // Renderizar texto
        int textY = hudY;
        for (String line : lines) {
            fr.drawStringWithShadow(line, hudX, textY, 0xFFFFFF);
            textY += lineHeight;
        }
        
        // Renderizar barra de progresso se necessário
        if (macro.getCurrentState() == ForagingMacro.ForagingState.WALKING_TO_TREE && 
            macro.getTargetTree() != null) {
            renderProgressBar(hudX, textY + 5, hudWidth - 10, macro);
        }
    }
    
    /**
     * Renderiza barra de progresso para caminhada até árvore
     */
    private void renderProgressBar(int x, int y, int width, ForagingMacro macro) {
        if (mc.thePlayer == null || macro.getTargetTree() == null) return;
        
        // Calcular progresso baseado na distância
        double currentDistance = mc.thePlayer.getPositionVector().distanceTo(
            new net.minecraft.util.Vec3(macro.getTargetTree()));
        double maxDistance = 30.0; // Distância máxima de escaneamento
        
        double progress = Math.max(0, Math.min(1, 1.0 - (currentDistance / maxDistance)));
        
        // Renderizar fundo da barra
        int barBackground = new Color(50, 50, 50, 180).getRGB();
        RenderUtils.renderRoundedQuad(x, y, width, 6, 2, barBackground);

        // Renderizar progresso
        int barColor = new Color(0, 255, 0, 200).getRGB();
        int progressWidth = (int) (width * progress);
        if (progressWidth > 0) {
            RenderUtils.renderRoundedQuad(x, y, progressWidth, 6, 2, barColor);
        }
        
        // Renderizar texto da distância
        String distanceText = String.format("%.1fm", currentDistance);
        mc.fontRendererObj.drawStringWithShadow(distanceText, x + width + 5, y - 1, 0xFFFFFF);
    }
    
    /**
     * Renderiza indicador de árvore alvo no mundo
     */
    @SubscribeEvent
    public void onRenderWorld(net.minecraftforge.client.event.RenderWorldLastEvent event) {
        if (!RatoAddonsConfigSimple.foragingEnabled) return;
        
        ForagingMacro macro = ForagingMacro.getInstance();
        if (!macro.isActive() || macro.getTargetTree() == null) return;
        
        renderTreeHighlight(macro.getTargetTree(), event.partialTicks);
    }
    
    /**
     * Renderiza destaque da árvore alvo
     */
    private void renderTreeHighlight(net.minecraft.util.BlockPos treePos, float partialTicks) {
        if (mc.thePlayer == null) return;
        
        // Posição do player para cálculo de offset
        double playerX = mc.thePlayer.lastTickPosX + (mc.thePlayer.posX - mc.thePlayer.lastTickPosX) * partialTicks;
        double playerY = mc.thePlayer.lastTickPosY + (mc.thePlayer.posY - mc.thePlayer.lastTickPosY) * partialTicks;
        double playerZ = mc.thePlayer.lastTickPosZ + (mc.thePlayer.posZ - mc.thePlayer.lastTickPosZ) * partialTicks;
        
        // Posição da árvore
        double treeX = treePos.getX() - playerX;
        double treeY = treePos.getY() - playerY;
        double treeZ = treePos.getZ() - playerZ;
        
        // Configurar OpenGL
        org.lwjgl.opengl.GL11.glPushMatrix();
        org.lwjgl.opengl.GL11.glTranslated(treeX, treeY, treeZ);
        org.lwjgl.opengl.GL11.glDisable(org.lwjgl.opengl.GL11.GL_TEXTURE_2D);
        org.lwjgl.opengl.GL11.glDisable(org.lwjgl.opengl.GL11.GL_DEPTH_TEST);
        org.lwjgl.opengl.GL11.glEnable(org.lwjgl.opengl.GL11.GL_BLEND);
        org.lwjgl.opengl.GL11.glBlendFunc(org.lwjgl.opengl.GL11.GL_SRC_ALPHA, org.lwjgl.opengl.GL11.GL_ONE_MINUS_SRC_ALPHA);
        org.lwjgl.opengl.GL11.glLineWidth(3.0f);
        
        // Renderizar outline do bloco
        org.lwjgl.opengl.GL11.glColor4f(1.0f, 0.0f, 0.0f, 0.8f);
        renderBlockOutline();
        
        // Renderizar preenchimento semi-transparente
        org.lwjgl.opengl.GL11.glColor4f(1.0f, 0.0f, 0.0f, 0.3f);
        renderBlockFill();
        
        // Restaurar OpenGL
        org.lwjgl.opengl.GL11.glEnable(org.lwjgl.opengl.GL11.GL_DEPTH_TEST);
        org.lwjgl.opengl.GL11.glEnable(org.lwjgl.opengl.GL11.GL_TEXTURE_2D);
        org.lwjgl.opengl.GL11.glDisable(org.lwjgl.opengl.GL11.GL_BLEND);
        org.lwjgl.opengl.GL11.glPopMatrix();
    }
    
    /**
     * Renderiza outline de um bloco
     */
    private void renderBlockOutline() {
        org.lwjgl.opengl.GL11.glBegin(org.lwjgl.opengl.GL11.GL_LINES);
        
        // Arestas inferiores
        org.lwjgl.opengl.GL11.glVertex3d(0, 0, 0);
        org.lwjgl.opengl.GL11.glVertex3d(1, 0, 0);
        org.lwjgl.opengl.GL11.glVertex3d(1, 0, 0);
        org.lwjgl.opengl.GL11.glVertex3d(1, 0, 1);
        org.lwjgl.opengl.GL11.glVertex3d(1, 0, 1);
        org.lwjgl.opengl.GL11.glVertex3d(0, 0, 1);
        org.lwjgl.opengl.GL11.glVertex3d(0, 0, 1);
        org.lwjgl.opengl.GL11.glVertex3d(0, 0, 0);
        
        // Arestas superiores
        org.lwjgl.opengl.GL11.glVertex3d(0, 1, 0);
        org.lwjgl.opengl.GL11.glVertex3d(1, 1, 0);
        org.lwjgl.opengl.GL11.glVertex3d(1, 1, 0);
        org.lwjgl.opengl.GL11.glVertex3d(1, 1, 1);
        org.lwjgl.opengl.GL11.glVertex3d(1, 1, 1);
        org.lwjgl.opengl.GL11.glVertex3d(0, 1, 1);
        org.lwjgl.opengl.GL11.glVertex3d(0, 1, 1);
        org.lwjgl.opengl.GL11.glVertex3d(0, 1, 0);
        
        // Arestas verticais
        org.lwjgl.opengl.GL11.glVertex3d(0, 0, 0);
        org.lwjgl.opengl.GL11.glVertex3d(0, 1, 0);
        org.lwjgl.opengl.GL11.glVertex3d(1, 0, 0);
        org.lwjgl.opengl.GL11.glVertex3d(1, 1, 0);
        org.lwjgl.opengl.GL11.glVertex3d(1, 0, 1);
        org.lwjgl.opengl.GL11.glVertex3d(1, 1, 1);
        org.lwjgl.opengl.GL11.glVertex3d(0, 0, 1);
        org.lwjgl.opengl.GL11.glVertex3d(0, 1, 1);
        
        org.lwjgl.opengl.GL11.glEnd();
    }
    
    /**
     * Renderiza preenchimento de um bloco
     */
    private void renderBlockFill() {
        org.lwjgl.opengl.GL11.glBegin(org.lwjgl.opengl.GL11.GL_QUADS);
        
        // Face inferior
        org.lwjgl.opengl.GL11.glVertex3d(0, 0, 0);
        org.lwjgl.opengl.GL11.glVertex3d(1, 0, 0);
        org.lwjgl.opengl.GL11.glVertex3d(1, 0, 1);
        org.lwjgl.opengl.GL11.glVertex3d(0, 0, 1);
        
        // Face superior
        org.lwjgl.opengl.GL11.glVertex3d(0, 1, 0);
        org.lwjgl.opengl.GL11.glVertex3d(0, 1, 1);
        org.lwjgl.opengl.GL11.glVertex3d(1, 1, 1);
        org.lwjgl.opengl.GL11.glVertex3d(1, 1, 0);
        
        // Faces laterais
        org.lwjgl.opengl.GL11.glVertex3d(0, 0, 0);
        org.lwjgl.opengl.GL11.glVertex3d(0, 1, 0);
        org.lwjgl.opengl.GL11.glVertex3d(1, 1, 0);
        org.lwjgl.opengl.GL11.glVertex3d(1, 0, 0);
        
        org.lwjgl.opengl.GL11.glVertex3d(1, 0, 0);
        org.lwjgl.opengl.GL11.glVertex3d(1, 1, 0);
        org.lwjgl.opengl.GL11.glVertex3d(1, 1, 1);
        org.lwjgl.opengl.GL11.glVertex3d(1, 0, 1);
        
        org.lwjgl.opengl.GL11.glVertex3d(1, 0, 1);
        org.lwjgl.opengl.GL11.glVertex3d(1, 1, 1);
        org.lwjgl.opengl.GL11.glVertex3d(0, 1, 1);
        org.lwjgl.opengl.GL11.glVertex3d(0, 0, 1);
        
        org.lwjgl.opengl.GL11.glVertex3d(0, 0, 1);
        org.lwjgl.opengl.GL11.glVertex3d(0, 1, 1);
        org.lwjgl.opengl.GL11.glVertex3d(0, 1, 0);
        org.lwjgl.opengl.GL11.glVertex3d(0, 0, 0);
        
        org.lwjgl.opengl.GL11.glEnd();
    }
}
