package com.rato.addons.pathfinding.examples;

import net.minecraft.client.Minecraft;
import net.minecraft.util.Vec3;
import net.minecraft.util.BlockPos;
import com.rato.addons.pathfinding.*;
import com.rato.addons.pathfinding.analysis.*;
import com.rato.addons.pathfinding.planning.*;
import com.rato.addons.pathfinding.movement.*;
import com.rato.addons.util.Logger;

import java.util.List;

/**
 * Exemplo de uso do sistema de pathfinding 3D para foraging
 * Demonstra como usar cada componente individualmente e em conjunto
 */
public class ForagingPathfindingExample {
    
    private final Minecraft mc = Minecraft.getMinecraft();
    
    /**
     * Exemplo 1: Mapeamento básico do entorno
     */
    public void exemploMapeamento3D() {
        Logger.sendMessage("§6=== EXEMPLO: Mapeamento 3D ===");
        
        if (mc.thePlayer == null) {
            Logger.sendMessage("§cJogador não disponível!");
            return;
        }
        
        // Criar mapper
        Grid3DMapper mapper = new Grid3DMapper();
        
        // Mapear entorno do jogador
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        Grid3DMapper.MappedGrid3D grid = mapper.mapSurroundings(playerPos, 15, 10);
        
        if (grid != null) {
            Logger.sendMessage("§aGrid mapeado com sucesso!");
            Logger.sendMessage("§7- Total de células: " + grid.cells.size());
            Logger.sendMessage("§7- Posições caminháveis: " + grid.walkablePositions.size());
            Logger.sendMessage("§7- Blocos de madeira: " + grid.treeLogPositions.size());
            
            // Mostrar estatísticas por tipo
            int solidBlocks = 0, airBlocks = 0, waterBlocks = 0, dangerousBlocks = 0;
            for (Grid3DMapper.GridCell cell : grid.cells.values()) {
                switch (cell.type) {
                    case SOLID: solidBlocks++; break;
                    case AIR: airBlocks++; break;
                    case WATER: waterBlocks++; break;
                    case DANGEROUS: dangerousBlocks++; break;
                }
            }
            
            Logger.sendMessage("§7- Blocos sólidos: " + solidBlocks);
            Logger.sendMessage("§7- Blocos de ar: " + airBlocks);
            Logger.sendMessage("§7- Blocos de água: " + waterBlocks);
            Logger.sendMessage("§7- Blocos perigosos: " + dangerousBlocks);
        } else {
            Logger.sendMessage("§cFalha no mapeamento!");
        }
    }
    
    /**
     * Exemplo 2: Detecção inteligente de árvores
     */
    public void exemploDeteccaoArvores() {
        Logger.sendMessage("§6=== EXEMPLO: Detecção de Árvores ===");
        
        if (mc.thePlayer == null) {
            Logger.sendMessage("§cJogador não disponível!");
            return;
        }
        
        // Mapear ambiente primeiro
        Grid3DMapper mapper = new Grid3DMapper();
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        Grid3DMapper.MappedGrid3D grid = mapper.mapSurroundings(playerPos, 20, 12);
        
        if (grid == null) {
            Logger.sendMessage("§cFalha no mapeamento!");
            return;
        }
        
        // Detectar árvores
        TreeDetector3D detector = new TreeDetector3D();
        List<TreeDetector3D.TreeAnalysis> trees = detector.detectTrees(grid, playerPos);
        
        Logger.sendMessage("§aEncontradas " + trees.size() + " árvores válidas:");
        
        for (int i = 0; i < Math.min(5, trees.size()); i++) {
            TreeDetector3D.TreeAnalysis tree = trees.get(i);
            Logger.sendMessage("§7" + (i + 1) + ". " + tree.treeType.displayName + 
                             " - Prioridade: " + String.format("%.2f", tree.priority));
            Logger.sendMessage("§7   Base: " + tree.basePosition);
            Logger.sendMessage("§7   Logs: " + tree.logBlocks.size() + 
                             ", Folhas: " + tree.leafBlocks.size());
            Logger.sendMessage("§7   Posição ótima: " + 
                             String.format("%.1f, %.1f, %.1f", 
                             tree.optimalStandingPosition.xCoord,
                             tree.optimalStandingPosition.yCoord,
                             tree.optimalStandingPosition.zCoord));
            Logger.sendMessage("§7   Acessibilidade: " + String.format("%.2f", tree.accessibility));
        }
    }
    
    /**
     * Exemplo 3: Planejamento de caminho com A*
     */
    public void exemploPathfinding() {
        Logger.sendMessage("§6=== EXEMPLO: Pathfinding A* ===");
        
        if (mc.thePlayer == null) {
            Logger.sendMessage("§cJogador não disponível!");
            return;
        }
        
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        
        // Mapear e construir grid de pathfinding
        Grid3DMapper mapper = new Grid3DMapper();
        Grid3DMapper.MappedGrid3D grid = mapper.mapSurroundings(playerPos, 15, 10);
        
        if (grid == null) {
            Logger.sendMessage("§cFalha no mapeamento!");
            return;
        }
        
        PathfindingGrid3D pathGrid = new PathfindingGrid3D();
        PathfindingGrid3D.BuiltPathfindingGrid builtGrid = pathGrid.buildPathfindingGrid(grid);
        
        if (builtGrid == null) {
            Logger.sendMessage("§cFalha na construção do grid de pathfinding!");
            return;
        }
        
        // Definir destino (10 blocos à frente)
        Vec3 goal = playerPos.addVector(10, 0, 0);
        
        // Executar A*
        AStar3DPathfinder pathfinder = new AStar3DPathfinder();
        AStar3DPathfinder.PathfindingResult result = pathfinder.findPath(playerPos, goal, builtGrid);
        
        if (result.pathFound) {
            Logger.sendMessage("§aCaminho encontrado!");
            Logger.sendMessage("§7- Waypoints: " + result.path.size());
            Logger.sendMessage("§7- Custo total: " + String.format("%.2f", result.totalCost));
            Logger.sendMessage("§7- Nós explorados: " + result.nodesExplored);
            Logger.sendMessage("§7- Tempo de computação: " + result.computationTime + "ms");
            
            // Mostrar primeiros waypoints
            Logger.sendMessage("§7Primeiros waypoints:");
            for (int i = 0; i < Math.min(3, result.path.size()); i++) {
                Vec3 waypoint = result.path.get(i);
                Logger.sendMessage("§7  " + (i + 1) + ". " + 
                                 String.format("%.1f, %.1f, %.1f", 
                                 waypoint.xCoord, waypoint.yCoord, waypoint.zCoord));
            }
        } else {
            Logger.sendMessage("§cCaminho não encontrado: " + result.failureReason);
            Logger.sendMessage("§7Nós explorados: " + result.nodesExplored);
            Logger.sendMessage("§7Tempo: " + result.computationTime + "ms");
        }
    }
    
    /**
     * Exemplo 4: Sistema completo integrado
     */
    public void exemploSistemaCompleto() {
        Logger.sendMessage("§6=== EXEMPLO: Sistema Completo ===");
        
        if (mc.thePlayer == null) {
            Logger.sendMessage("§cJogador não disponível!");
            return;
        }
        
        // Criar sistema completo
        ForagingPathfindingSystem system = new ForagingPathfindingSystem();
        
        // Configurar callbacks
        system.setOnTreeReached(() -> {
            Logger.sendMessage("§a✓ Árvore alcançada! Pronto para quebrar.");
        });
        
        system.setOnNoTreesFound(() -> {
            Logger.sendMessage("§eNenhuma árvore encontrada na área.");
        });
        
        system.setOnPathfindingFailed(() -> {
            Logger.sendMessage("§cFalha no pathfinding.");
        });
        
        // Iniciar sistema
        boolean success = system.startForagingPathfinding();
        
        if (success) {
            Logger.sendMessage("§aSistema de pathfinding iniciado!");
            Logger.sendMessage("§7Estado: " + system.getCurrentState().displayName);
            
            // Simular alguns updates
            for (int i = 0; i < 5; i++) {
                system.update();
                try {
                    Thread.sleep(100); // Simular delay entre ticks
                } catch (InterruptedException e) {
                    break;
                }
            }
            
            // Mostrar estatísticas
            Logger.sendMessage("§7=== Estatísticas do Sistema ===");
            Logger.sendMessage(system.getSystemStats());
            
            // Parar sistema
            system.stopForagingPathfinding();
        } else {
            Logger.sendMessage("§cFalha ao iniciar sistema!");
        }
    }
    
    /**
     * Exemplo 5: Pathfinding para múltiplas árvores
     */
    public void exemploMultiplasArvores() {
        Logger.sendMessage("§6=== EXEMPLO: Múltiplas Árvores ===");
        
        if (mc.thePlayer == null) {
            Logger.sendMessage("§cJogador não disponível!");
            return;
        }
        
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        
        // Mapear ambiente
        Grid3DMapper mapper = new Grid3DMapper();
        Grid3DMapper.MappedGrid3D grid = mapper.mapSurroundings(playerPos, 25, 15);
        
        if (grid == null) {
            Logger.sendMessage("§cFalha no mapeamento!");
            return;
        }
        
        // Detectar árvores
        TreeDetector3D detector = new TreeDetector3D();
        List<TreeDetector3D.TreeAnalysis> trees = detector.detectTrees(grid, playerPos);
        
        if (trees.size() < 2) {
            Logger.sendMessage("§cPrecisa de pelo menos 2 árvores para este exemplo!");
            return;
        }
        
        // Construir grid de pathfinding
        PathfindingGrid3D pathGrid = new PathfindingGrid3D();
        PathfindingGrid3D.BuiltPathfindingGrid builtGrid = pathGrid.buildPathfindingGrid(grid);
        
        if (builtGrid == null) {
            Logger.sendMessage("§cFalha na construção do grid!");
            return;
        }
        
        // Selecionar primeiras 3 árvores
        List<Vec3> treePositions = trees.subList(0, Math.min(3, trees.size()))
            .stream()
            .map(tree -> tree.optimalStandingPosition)
            .collect(java.util.stream.Collectors.toList());
        
        // Planejar rota otimizada
        AStar3DPathfinder pathfinder = new AStar3DPathfinder();
        AStar3DPathfinder.PathfindingResult result = pathfinder.findMultiTargetPath(playerPos, treePositions, builtGrid);
        
        if (result.pathFound) {
            Logger.sendMessage("§aRota para múltiplas árvores encontrada!");
            Logger.sendMessage("§7- Total de waypoints: " + result.path.size());
            Logger.sendMessage("§7- Custo total: " + String.format("%.2f", result.totalCost));
            Logger.sendMessage("§7- Árvores na rota: " + treePositions.size());
            Logger.sendMessage("§7- Tempo de computação: " + result.computationTime + "ms");
        } else {
            Logger.sendMessage("§cFalha ao planejar rota: " + result.failureReason);
        }
    }
    
    /**
     * Executa todos os exemplos
     */
    public void executarTodosExemplos() {
        Logger.sendMessage("§a=== INICIANDO EXEMPLOS DE PATHFINDING 3D ===");
        
        try {
            exemploMapeamento3D();
            Thread.sleep(1000);
            
            exemploDeteccaoArvores();
            Thread.sleep(1000);
            
            exemploPathfinding();
            Thread.sleep(1000);
            
            exemploMultiplasArvores();
            Thread.sleep(1000);
            
            exemploSistemaCompleto();
            
        } catch (InterruptedException e) {
            Logger.sendMessage("§cExemplos interrompidos!");
        }
        
        Logger.sendMessage("§a=== EXEMPLOS CONCLUÍDOS ===");
    }
}
