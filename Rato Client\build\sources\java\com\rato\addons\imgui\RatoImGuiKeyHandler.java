package com.rato.addons.imgui;

import com.rato.addons.util.Logger;
import net.minecraft.client.settings.KeyBinding;
import net.minecraftforge.fml.client.registry.ClientRegistry;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.InputEvent;
import org.lwjgl.input.Keyboard;

/**
 * <PERSON>les keyboard input for Rato ImGui LWJGL Interface
 */
public class RatoImGuiKeyHandler {

    // Key bindings
    private static KeyBinding toggleInterfaceKey;
    private static KeyBinding panicKey;

    // State tracking
    private static boolean initialized = false;
    private static long lastInsertPress = 0;

    /**
     * Initialize the key handler
     */
    public static void initialize() {
        if (initialized) {
            return;
        }

        try {
            // Create key bindings
            toggleInterfaceKey = new KeyBinding(
                    "key.rato.imgui.toggle",
                    Keyboard.KEY_INSERT,
                    "key.categories.rato");

            panicKey = new KeyBinding(
                    "key.rato.imgui.panic",
                    Keyboard.KEY_END,
                    "key.categories.rato");

            // Register key bindings
            ClientRegistry.registerKeyBinding(toggleInterfaceKey);
            ClientRegistry.registerKeyBinding(panicKey);

            initialized = true;
            Logger.sendLog("✅ Rato ImGui Key Handler: Sistema de teclas inicializado!");
            Logger.sendLog("🔑 INSERT = Alternar Interface | END = Pânico (Desativar tudo)");

        } catch (Exception e) {
            Logger.sendLog("❌ Rato ImGui Key Handler: Erro na inicialização: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Handle key input events
     */
    @SubscribeEvent
    public void onKeyInput(InputEvent.KeyInputEvent event) {
        if (!initialized) {
            return;
        }

        try {
            // Handle toggle interface key
            if (toggleInterfaceKey.isPressed()) {
                handleToggleInterface();
            }

            // Handle panic key
            if (panicKey.isPressed()) {
                handlePanicKey();
            }

            // Direct keyboard handling for INSERT key (backup)
            if (Keyboard.isKeyDown(Keyboard.KEY_INSERT)) {
                handleDirectInsertKey();
            }

            // Check if ESC or inventory key is pressed while menu is open
            if (RatoImGuiRenderer.isVisible()) {
                if (Keyboard.isKeyDown(Keyboard.KEY_ESCAPE) ||
                        Keyboard.isKeyDown(Keyboard.KEY_E) ||
                        Keyboard.isKeyDown(Keyboard.KEY_TAB)) {
                    RatoImGuiRenderer.toggle();
                    Logger.sendLog("🔒 Menu fechado por tecla de sistema");
                }
            }

        } catch (Exception e) {
            Logger.sendLog("❌ Rato ImGui Key Handler: Erro no processamento de teclas: " + e.getMessage());
        }
    }

    /**
     * Handle toggle interface key press
     */
    private void handleToggleInterface() {
        try {
            if (RatoImGuiRenderer.getInstance() != null) {
                RatoImGuiRenderer.toggle();
                Logger.sendLog("🔄 Rato ImGui: Interface alternada");
            } else {
                Logger.sendLog("⚠️ Rato ImGui: Renderer não inicializado! Tentando inicializar...");

                // Try to initialize renderer
                try {
                    RatoImGuiRenderer.initialize();
                    Logger.sendLog("✅ Rato ImGui: Renderer inicializado com sucesso!");
                    RatoImGuiRenderer.toggle();
                } catch (Exception e) {
                    Logger.sendLog("❌ Rato ImGui: Falha ao inicializar renderer: " + e.getMessage());
                }
            }

        } catch (Exception e) {
            Logger.sendLog("❌ Rato ImGui: Erro ao alternar interface: " + e.getMessage());
        }
    }

    /**
     * Handle panic key press (disable all features)
     */
    private void handlePanicKey() {
        try {
            Logger.sendLog("🚨 Rato ImGui: PÂNICO ATIVADO! Desativando todas as funcionalidades...");

            // Close interface if open
            if (RatoImGuiRenderer.isVisible()) {
                RatoImGuiRenderer.toggle();
            }

            // Note: In a real implementation, you would disable all mod features here
            // For now, we just log the panic activation

            Logger.sendLog("✅ Rato ImGui: Modo pânico executado!");

        } catch (Exception e) {
            Logger.sendLog("❌ Rato ImGui: Erro no modo pânico: " + e.getMessage());
        }
    }

    /**
     * Handle direct INSERT key press (backup method)
     */
    private void handleDirectInsertKey() {
        // This is a backup method in case the KeyBinding doesn't work

        long currentTime = System.currentTimeMillis();

        // Debounce the key press (prevent spam)
        if (currentTime - lastInsertPress > 200) {
            lastInsertPress = currentTime;

            // Log current status
            boolean isVisible = RatoImGuiRenderer.isVisible();
            Logger.sendLog("🔍 Rato ImGui: Interface " + (isVisible ? "visível" : "oculta"));
        }
    }

    /**
     * Get the current status of key bindings
     */
    public static String getKeyBindingStatus() {
        if (!initialized) {
            return "❌ Sistema de teclas não inicializado";
        }

        StringBuilder status = new StringBuilder();
        status.append("🔑 Teclas Rato ImGui LWJGL:\n");
        status.append("  INSERT: Alternar Interface\n");
        status.append("  END: Modo Pânico\n");
        status.append("  Renderer: ").append(RatoImGuiRenderer.getInstance() != null ? "✅" : "❌");

        return status.toString();
    }

    /**
     * Check if the key handler is initialized
     */
    public static boolean isInitialized() {
        return initialized;
    }
}
