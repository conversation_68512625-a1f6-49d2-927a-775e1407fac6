package com.rato.addons.visuals.util;

import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.Tessellator;
import net.minecraft.client.renderer.WorldRenderer;
import net.minecraft.client.renderer.vertex.DefaultVertexFormats;
import net.minecraft.entity.Entity;
import net.minecraft.util.AxisAlignedBB;
import org.lwjgl.opengl.GL11;

import java.awt.*;

/**
 * Utilitários de renderização baseados no LiquidBounce
 * Métodos para desenhar ESP, boxes, outlines, glow, etc.
 */
public class RenderUtils {

    private static final Minecraft mc = Minecraft.getMinecraft();

    /**
     * Desenha uma caixa ESP ao redor da entidade
     */
    public static void drawEntityBox(Entity entity, double x, double y, double z, Color color) {
        AxisAlignedBB boundingBox = entity.getEntityBoundingBox();
        double width = boundingBox.maxX - boundingBox.minX;
        double height = boundingBox.maxY - boundingBox.minY;

        // Expandir ligeiramente
        double expand = 0.05;

        AxisAlignedBB renderBox = new AxisAlignedBB(
                x - width / 2 - expand,
                y - expand,
                z - width / 2 - expand,
                x + width / 2 + expand,
                y + height + expand,
                z + width / 2 + expand);

        // Renderizar caixa preenchida
        drawFilledBox(renderBox, new Color(color.getRed(), color.getGreen(), color.getBlue(), 50));

        // Renderizar outline
        drawBoxOutline(renderBox, new Color(color.getRed(), color.getGreen(), color.getBlue(), 150));
    }

    /**
     * Desenha apenas o outline da entidade
     */
    public static void drawEntityOutline(Entity entity, double x, double y, double z, Color color) {
        AxisAlignedBB boundingBox = entity.getEntityBoundingBox();
        double width = boundingBox.maxX - boundingBox.minX;
        double height = boundingBox.maxY - boundingBox.minY;

        AxisAlignedBB renderBox = new AxisAlignedBB(
                x - width / 2,
                y,
                z - width / 2,
                x + width / 2,
                y + height,
                z + width / 2);

        drawBoxOutline(renderBox, color);
    }

    /**
     * Desenha efeito glow na entidade (LiquidBounce style)
     */
    public static void drawEntityGlow(Entity entity, double x, double y, double z, Color color) {
        AxisAlignedBB boundingBox = entity.getEntityBoundingBox();
        double width = boundingBox.maxX - boundingBox.minX;
        double height = boundingBox.maxY - boundingBox.minY;

        // Configurar OpenGL para glow
        GlStateManager.disableDepth();
        GlStateManager.enableBlend();
        GlStateManager.blendFunc(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA);

        // Múltiplas camadas para efeito glow
        for (int i = 0; i < 4; i++) {
            double expand = 0.05 + (i * 0.03);
            float alpha = (80f - (i * 15f)) / 255f;

            AxisAlignedBB glowBox = new AxisAlignedBB(
                    x - width / 2 - expand,
                    y - expand,
                    z - width / 2 - expand,
                    x + width / 2 + expand,
                    y + height + expand,
                    z + width / 2 + expand);

            GlStateManager.color(color.getRed() / 255f, color.getGreen() / 255f,
                    color.getBlue() / 255f, alpha);
            drawFilledBox(glowBox, new Color(color.getRed(), color.getGreen(), color.getBlue(), (int) (alpha * 255)));
        }

        GlStateManager.enableDepth();
    }

    /**
     * Desenha chams (através de paredes)
     */
    public static void drawEntityChams(Entity entity, double x, double y, double z, Color color) {
        // Desabilitar depth test para ver através de paredes
        GL11.glDisable(GL11.GL_DEPTH_TEST);

        drawEntityBox(entity, x, y, z, new Color(color.getRed(), color.getGreen(), color.getBlue(), 100));

        // Reabilitar depth test
        GL11.glEnable(GL11.GL_DEPTH_TEST);

        // Desenhar versão normal por cima
        drawEntityBox(entity, x, y, z, color);
    }

    /**
     * Desenha efeito glow adicional
     */
    public static void drawEntityGlowEffect(Entity entity, double x, double y, double z, Color color, float radius) {
        AxisAlignedBB boundingBox = entity.getEntityBoundingBox();
        double width = boundingBox.maxX - boundingBox.minX;
        double height = boundingBox.maxY - boundingBox.minY;

        AxisAlignedBB glowBox = new AxisAlignedBB(
                x - width / 2 - radius,
                y - radius,
                z - width / 2 - radius,
                x + width / 2 + radius,
                y + height + radius,
                z + width / 2 + radius);

        drawFilledBox(glowBox, new Color(color.getRed(), color.getGreen(), color.getBlue(), 30));
    }

    /**
     * Desenha ESP 2D (LiquidBounce style)
     */
    public static void drawEntity2D(Entity entity, double x, double y, double z, Color color) {
        // Calcular posições 2D da entidade
        AxisAlignedBB boundingBox = entity.getEntityBoundingBox();
        double width = boundingBox.maxX - boundingBox.minX;
        double height = boundingBox.maxY - boundingBox.minY;

        // Pontos 3D da bounding box
        double[][] corners = {
                { x - width / 2, y, z - width / 2 },
                { x + width / 2, y, z - width / 2 },
                { x - width / 2, y + height, z - width / 2 },
                { x + width / 2, y + height, z - width / 2 },
                { x - width / 2, y, z + width / 2 },
                { x + width / 2, y, z + width / 2 },
                { x - width / 2, y + height, z + width / 2 },
                { x + width / 2, y + height, z + width / 2 }
        };

        // Converter para coordenadas 2D
        float minX = Float.MAX_VALUE, minY = Float.MAX_VALUE;
        float maxX = Float.MIN_VALUE, maxY = Float.MIN_VALUE;

        for (double[] corner : corners) {
            float[] screenPos = worldToScreen(corner[0], corner[1], corner[2]);
            if (screenPos != null) {
                minX = Math.min(minX, screenPos[0]);
                maxX = Math.max(maxX, screenPos[0]);
                minY = Math.min(minY, screenPos[1]);
                maxY = Math.max(maxY, screenPos[1]);
            }
        }

        // Verificar se está na tela
        if (minX < 0 || minY < 0 || maxX > mc.displayWidth || maxY > mc.displayHeight) {
            return;
        }

        // Desenhar caixa 2D
        draw2DBox(minX, minY, maxX, maxY, color);
    }

    /**
     * Converte coordenadas 3D para 2D
     */
    private static float[] worldToScreen(double x, double y, double z) {
        // Implementação simplificada de world-to-screen
        // Esta é uma versão básica, pode ser melhorada
        return null; // Por enquanto retorna null para evitar erros
    }

    /**
     * Desenha uma caixa 2D
     */
    private static void draw2DBox(float x1, float y1, float x2, float y2, Color color) {
        GlStateManager.pushMatrix();
        GlStateManager.enableBlend();
        GlStateManager.disableTexture2D();
        GlStateManager.blendFunc(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA);

        GlStateManager.color(color.getRed() / 255f, color.getGreen() / 255f,
                color.getBlue() / 255f, color.getAlpha() / 255f);

        // Desenhar outline
        GL11.glLineWidth(2.0f);
        GL11.glBegin(GL11.GL_LINE_LOOP);
        GL11.glVertex2f(x1, y1);
        GL11.glVertex2f(x2, y1);
        GL11.glVertex2f(x2, y2);
        GL11.glVertex2f(x1, y2);
        GL11.glEnd();

        GlStateManager.enableTexture2D();
        GlStateManager.disableBlend();
        GlStateManager.popMatrix();
    }

    /**
     * Desenha uma caixa preenchida
     */
    public static void drawFilledBox(AxisAlignedBB box, Color color) {
        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer worldRenderer = tessellator.getWorldRenderer();

        GlStateManager.color(color.getRed() / 255f, color.getGreen() / 255f,
                color.getBlue() / 255f, color.getAlpha() / 255f);

        worldRenderer.begin(GL11.GL_QUADS, DefaultVertexFormats.POSITION);
        addBoxVertices(worldRenderer, box);
        tessellator.draw();
    }

    /**
     * Desenha o outline de uma caixa
     */
    public static void drawBoxOutline(AxisAlignedBB box, Color color) {
        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer worldRenderer = tessellator.getWorldRenderer();

        GlStateManager.color(color.getRed() / 255f, color.getGreen() / 255f,
                color.getBlue() / 255f, color.getAlpha() / 255f);

        worldRenderer.begin(GL11.GL_LINES, DefaultVertexFormats.POSITION);
        addBoxEdges(worldRenderer, box);
        tessellator.draw();
    }

    /**
     * Adiciona vértices para faces da caixa
     */
    private static void addBoxVertices(WorldRenderer worldRenderer, AxisAlignedBB box) {
        double minX = box.minX, minY = box.minY, minZ = box.minZ;
        double maxX = box.maxX, maxY = box.maxY, maxZ = box.maxZ;

        // Face inferior
        worldRenderer.pos(minX, minY, minZ).endVertex();
        worldRenderer.pos(maxX, minY, minZ).endVertex();
        worldRenderer.pos(maxX, minY, maxZ).endVertex();
        worldRenderer.pos(minX, minY, maxZ).endVertex();

        // Face superior
        worldRenderer.pos(minX, maxY, minZ).endVertex();
        worldRenderer.pos(minX, maxY, maxZ).endVertex();
        worldRenderer.pos(maxX, maxY, maxZ).endVertex();
        worldRenderer.pos(maxX, maxY, minZ).endVertex();

        // Faces laterais
        worldRenderer.pos(minX, minY, minZ).endVertex();
        worldRenderer.pos(minX, maxY, minZ).endVertex();
        worldRenderer.pos(maxX, maxY, minZ).endVertex();
        worldRenderer.pos(maxX, minY, minZ).endVertex();

        worldRenderer.pos(minX, minY, maxZ).endVertex();
        worldRenderer.pos(maxX, minY, maxZ).endVertex();
        worldRenderer.pos(maxX, maxY, maxZ).endVertex();
        worldRenderer.pos(minX, maxY, maxZ).endVertex();

        worldRenderer.pos(minX, minY, minZ).endVertex();
        worldRenderer.pos(minX, minY, maxZ).endVertex();
        worldRenderer.pos(minX, maxY, maxZ).endVertex();
        worldRenderer.pos(minX, maxY, minZ).endVertex();

        worldRenderer.pos(maxX, minY, minZ).endVertex();
        worldRenderer.pos(maxX, maxY, minZ).endVertex();
        worldRenderer.pos(maxX, maxY, maxZ).endVertex();
        worldRenderer.pos(maxX, minY, maxZ).endVertex();
    }

    /**
     * Adiciona vértices para arestas da caixa
     */
    private static void addBoxEdges(WorldRenderer worldRenderer, AxisAlignedBB box) {
        double minX = box.minX, minY = box.minY, minZ = box.minZ;
        double maxX = box.maxX, maxY = box.maxY, maxZ = box.maxZ;

        // Arestas verticais
        worldRenderer.pos(minX, minY, minZ).endVertex();
        worldRenderer.pos(minX, maxY, minZ).endVertex();

        worldRenderer.pos(maxX, minY, minZ).endVertex();
        worldRenderer.pos(maxX, maxY, minZ).endVertex();

        worldRenderer.pos(minX, minY, maxZ).endVertex();
        worldRenderer.pos(minX, maxY, maxZ).endVertex();

        worldRenderer.pos(maxX, minY, maxZ).endVertex();
        worldRenderer.pos(maxX, maxY, maxZ).endVertex();

        // Arestas horizontais inferiores
        worldRenderer.pos(minX, minY, minZ).endVertex();
        worldRenderer.pos(maxX, minY, minZ).endVertex();

        worldRenderer.pos(maxX, minY, minZ).endVertex();
        worldRenderer.pos(maxX, minY, maxZ).endVertex();

        worldRenderer.pos(maxX, minY, maxZ).endVertex();
        worldRenderer.pos(minX, minY, maxZ).endVertex();

        worldRenderer.pos(minX, minY, maxZ).endVertex();
        worldRenderer.pos(minX, minY, minZ).endVertex();

        // Arestas horizontais superiores
        worldRenderer.pos(minX, maxY, minZ).endVertex();
        worldRenderer.pos(maxX, maxY, minZ).endVertex();

        worldRenderer.pos(maxX, maxY, minZ).endVertex();
        worldRenderer.pos(maxX, maxY, maxZ).endVertex();

        worldRenderer.pos(maxX, maxY, maxZ).endVertex();
        worldRenderer.pos(minX, maxY, maxZ).endVertex();

        worldRenderer.pos(minX, maxY, maxZ).endVertex();
        worldRenderer.pos(minX, maxY, minZ).endVertex();
    }
}
