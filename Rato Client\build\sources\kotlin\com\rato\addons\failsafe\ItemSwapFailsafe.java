package com.rato.addons.failsafe;

import com.rato.addons.config.RatoAddonsConfigSimple;
import com.rato.addons.util.Logger;
import net.minecraft.item.ItemStack;
import net.minecraft.network.Packet;
import net.minecraft.network.play.server.S2FPacketSetSlot;

import java.util.HashMap;
import java.util.Map;

public class ItemSwapFailsafe extends Failsafe {
    
    private ItemSwapState state = ItemSwapState.NONE;
    private Map<Integer, ItemStack> lastKnownSlots = new HashMap<>();
    private long lastPacketTime = 0;
    private int suspiciousChanges = 0;
    private static final long PACKET_TIMEOUT = 5000; // 5 segundos
    
    @Override
    public int getPriority() {
        return 3;
    }
    
    @Override
    public FailsafeManager.EmergencyType getType() {
        return FailsafeManager.EmergencyType.ITEM_CHANGE_CHECK;
    }
    
    @Override
    public boolean shouldSendNotification() {
        return RatoAddonsConfigSimple.discordWebhook;
    }
    
    @Override
    public boolean shouldPlaySound() {
        return RatoAddonsConfigSimple.soundAlert;
    }
    
    @Override
    public void onReceivedPacketDetection(Packet packet) {
        if (!isEnabled()) return;
        if (!(packet instanceof S2FPacketSetSlot)) return;
        
        S2FPacketSetSlot setSlotPacket = (S2FPacketSetSlot) packet;
        int slot = setSlotPacket.func_149173_d();
        ItemStack newItem = setSlotPacket.func_149174_e();
        
        // Focar apenas no hotbar (slots 36-44)
        if (slot < 36 || slot > 44) return;
        
        // Verificar se é uma mudança suspeita
        if (isSuspiciousSlotChange(slot, newItem)) {
            suspiciousChanges++;
            lastPacketTime = System.currentTimeMillis();
            
            // Se múltiplas mudanças suspeitas em pouco tempo
            if (suspiciousChanges >= 2) {
                Logger.sendMessage("§7[DEBUG] Suspicious item changes detected: " + suspiciousChanges);
                FailsafeManager.getInstance().possibleDetection(this);
                suspiciousChanges = 0; // Reset
            }
        }
        
        // Atualizar conhecimento dos slots
        lastKnownSlots.put(slot, newItem != null ? newItem.copy() : null);
        
        // Reset contador se muito tempo passou
        if (System.currentTimeMillis() - lastPacketTime > PACKET_TIMEOUT) {
            suspiciousChanges = 0;
        }
    }
    
    private boolean isSuspiciousSlotChange(int slot, ItemStack newItem) {
        ItemStack lastItem = lastKnownSlots.get(slot);
        
        // Se não temos conhecimento anterior, não é suspeito
        if (lastItem == null && newItem == null) return false;
        
        // Mudança de item completamente diferente
        if (lastItem == null && newItem != null) return true;
        if (lastItem != null && newItem == null) return true;
        
        if (lastItem != null && newItem != null) {
            // Item completamente diferente
            if (!ItemStack.areItemsEqual(lastItem, newItem)) return true;
            
            // Mudança drástica de quantidade (mais que 16 itens)
            if (Math.abs(lastItem.stackSize - newItem.stackSize) > 16) return true;
            
            // NBT diferente (encantamentos, etc.)
            if (!ItemStack.areItemStackTagsEqual(lastItem, newItem)) return true;
        }
        
        return false;
    }
    
    @Override
    public void duringFailsafeTrigger() {
        switch (state) {
            case NONE:
                FailsafeManager.getInstance().scheduleRandomDelay(300, 800);
                state = ItemSwapState.WAIT_BEFORE_START;
                break;
                
            case WAIT_BEFORE_START:
                stopMovement();
                state = ItemSwapState.LOOK_AROUND;
                FailsafeManager.getInstance().scheduleRandomDelay(200, 500);
                break;
                
            case LOOK_AROUND:
                performHumanLikeMovement();
                state = ItemSwapState.CHECK_INVENTORY;
                FailsafeManager.getInstance().scheduleRandomDelay(400, 800);
                break;
                
            case CHECK_INVENTORY:
                // Simular verificação do inventário
                performInventoryCheck();
                state = ItemSwapState.RESPOND;
                FailsafeManager.getInstance().scheduleRandomDelay(300, 600);
                break;
                
            case RESPOND:
                // Movimento de resposta humana
                performHumanResponse();
                state = ItemSwapState.END;
                FailsafeManager.getInstance().scheduleRandomDelay(500, 1000);
                break;
                
            case END:
                endOfFailsafeTrigger();
                break;
        }
    }
    
    private void performInventoryCheck() {
        // Simular movimento de verificação do inventário
        if (mc.thePlayer != null) {
            // Olhar ligeiramente para baixo (como se verificando hotbar)
            mc.thePlayer.rotationPitch += 15.0f;
            if (mc.thePlayer.rotationPitch > 90.0f) {
                mc.thePlayer.rotationPitch = 90.0f;
            }
        }
    }
    
    private void performHumanResponse() {
        // Movimento humano de resposta
        performHumanLikeMovement();
        
        // Simular troca de item (como se reorganizando)
        if (mc.thePlayer != null && mc.thePlayer.inventory != null) {
            // Simular scroll do mouse (mudança de slot)
            int currentSlot = mc.thePlayer.inventory.currentItem;
            int newSlot = (currentSlot + 1) % 9;
            mc.thePlayer.inventory.currentItem = newSlot;
            
            // Voltar ao slot original após um momento
            FailsafeManager.getInstance().scheduleDelay(200);
            mc.thePlayer.inventory.currentItem = currentSlot;
        }
    }
    
    @Override
    public void endOfFailsafeTrigger() {
        Logger.sendMessage("§a✓ Item swap check handled");
        FailsafeManager.getInstance().stopFailsafes();
        
        // Agendar retomada das atividades
        FailsafeManager.getInstance().scheduleDelay(1000);
    }
    
    @Override
    public void resetStates() {
        state = ItemSwapState.NONE;
        suspiciousChanges = 0;
        lastPacketTime = 0;
    }
    
    private enum ItemSwapState {
        NONE,
        WAIT_BEFORE_START,
        LOOK_AROUND,
        CHECK_INVENTORY,
        RESPOND,
        END
    }
}
