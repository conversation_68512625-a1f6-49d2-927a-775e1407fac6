package com.rato.addons.gui.modern;

import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.Tessellator;
import net.minecraft.client.renderer.WorldRenderer;
import net.minecraft.client.renderer.vertex.DefaultVertexFormats;
import org.lwjgl.opengl.GL11;

import java.awt.*;
import java.awt.font.FontRenderContext;
import java.awt.geom.AffineTransform;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * Modern TTF Font Renderer with beautiful typography
 * Supports custom fonts, antialiasing, and advanced text rendering
 */
public class TTFRenderer {
    private static final Map<String, TTFRenderer> fontCache = new HashMap<>();

    private final Font font;
    private final boolean antiAlias;
    private final boolean fractionalMetrics;
    private final Map<Character, CharData> charData = new HashMap<>();
    private int textureId = -1;
    private int textureWidth = 512;
    private int textureHeight = 512;

    // Character data for texture atlas
    private static class CharData {
        public float x, y, width, height;
        public float u, v, u2, v2;
        public float advance;

        public CharData(float x, float y, float width, float height, float u, float v, float u2, float v2,
                float advance) {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
            this.u = u;
            this.v = v;
            this.u2 = u2;
            this.v2 = v2;
            this.advance = advance;
        }
    }

    /**
     * Get or create a TTF renderer for the specified font
     */
    public static TTFRenderer getFont(String fontName, int size, boolean antiAlias, boolean fractionalMetrics) {
        String key = fontName + "_" + size + "_" + antiAlias + "_" + fractionalMetrics;
        TTFRenderer existing = fontCache.get(key);
        if (existing == null) {
            try {
                existing = new TTFRenderer(fontName, size, antiAlias, fractionalMetrics);
                fontCache.put(key, existing);
            } catch (Exception e) {
                System.out.println("Failed to create TTF renderer: " + e.getMessage());
                // Return a fallback renderer
                existing = createFallbackRenderer(size);
                fontCache.put(key, existing);
            }
        }
        return existing;
    }

    /**
     * Create a fallback renderer using system fonts
     */
    private static TTFRenderer createFallbackRenderer(int size) {
        try {
            return new TTFRenderer("Arial", size, false, false);
        } catch (Exception e) {
            // Ultimate fallback - this should never fail
            return new TTFRenderer("SansSerif", size, false, false);
        }
    }

    /**
     * Get popular modern fonts
     */
    public static TTFRenderer getInterFont(int size) {
        return getFont("Inter", size, true, true);
    }

    public static TTFRenderer getRobotoFont(int size) {
        return getFont("Roboto", size, true, true);
    }

    public static TTFRenderer getSegoeUIFont(int size) {
        return getFont("Segoe UI", size, true, true);
    }

    public static TTFRenderer getSFProFont(int size) {
        return getFont("SF Pro Display", size, true, true);
    }

    private TTFRenderer(String fontName, int size, boolean antiAlias, boolean fractionalMetrics) {
        this.antiAlias = antiAlias;
        this.fractionalMetrics = fractionalMetrics;

        Font loadedFont = null;

        // Try to load custom font from resources
        try {
            InputStream fontStream = getClass().getResourceAsStream(
                    "/assets/ratoaddons/fonts/" + fontName.toLowerCase().replace(" ", "_") + ".ttf");
            if (fontStream != null) {
                loadedFont = Font.createFont(Font.TRUETYPE_FONT, fontStream).deriveFont(Font.PLAIN, size);
            }
        } catch (Exception e) {
            // Fallback to system fonts
        }

        // Try system fonts if custom font failed
        if (loadedFont == null) {
            String[] fallbackFonts = { fontName, "Inter", "Segoe UI", "Roboto", "SF Pro Display", "Arial",
                    "SansSerif" };
            for (String fallback : fallbackFonts) {
                try {
                    loadedFont = new Font(fallback, Font.PLAIN, size);
                    if (loadedFont.getFamily().equals(fallback)) {
                        break;
                    }
                } catch (Exception ignored) {
                }
            }
        }

        // Ultimate fallback
        if (loadedFont == null) {
            loadedFont = new Font(Font.SANS_SERIF, Font.PLAIN, size);
        }

        this.font = loadedFont;

        try {
            generateTexture();
        } catch (Exception e) {
            System.out.println("Failed to generate texture for font: " + e.getMessage());
            e.printStackTrace();
            // Set a flag to use fallback rendering
            textureId = -1;
        }
    }

    /**
     * Generate texture atlas for all characters
     */
    private void generateTexture() {
        BufferedImage bufferedImage = new BufferedImage(textureWidth, textureHeight, BufferedImage.TYPE_INT_ARGB);
        Graphics2D graphics = bufferedImage.createGraphics();

        // Configure graphics for beautiful rendering
        graphics.setFont(font);
        graphics.setColor(Color.WHITE);

        if (antiAlias) {
            graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            graphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        }

        if (fractionalMetrics) {
            graphics.setRenderingHint(RenderingHints.KEY_FRACTIONALMETRICS, RenderingHints.VALUE_FRACTIONALMETRICS_ON);
        }

        // High quality rendering
        graphics.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        graphics.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL, RenderingHints.VALUE_STROKE_PURE);

        FontMetrics fontMetrics = graphics.getFontMetrics();
        FontRenderContext frc = graphics.getFontRenderContext();

        int currentX = 2;
        int currentY = fontMetrics.getAscent() + 2;
        int rowHeight = fontMetrics.getHeight() + 4;

        // Generate characters 32-255 (printable ASCII + extended)
        for (int i = 32; i < 256; i++) {
            char character = (char) i;
            String charString = String.valueOf(character);

            Rectangle2D bounds = font.getStringBounds(charString, frc);
            int charWidth = (int) Math.ceil(bounds.getWidth()) + 2;
            int charHeight = (int) Math.ceil(bounds.getHeight()) + 2;

            // Check if we need to move to next row
            if (currentX + charWidth >= textureWidth) {
                currentX = 2;
                currentY += rowHeight;

                // Check if we need more texture space
                if (currentY + rowHeight >= textureHeight) {
                    // Could expand texture or use multiple textures
                    break;
                }
            }

            // Draw the character
            graphics.drawString(charString, currentX, currentY);

            // Calculate texture coordinates
            float u = (float) currentX / textureWidth;
            float v = (float) (currentY - fontMetrics.getAscent()) / textureHeight;
            float u2 = (float) (currentX + charWidth) / textureWidth;
            float v2 = (float) (currentY - fontMetrics.getAscent() + charHeight) / textureHeight;

            // Store character data
            float advance = (float) bounds.getWidth();
            charData.put(character, new CharData(
                    currentX, currentY - fontMetrics.getAscent(),
                    charWidth, charHeight,
                    u, v, u2, v2, advance));

            currentX += charWidth + 2;
        }

        graphics.dispose();

        // Upload texture to OpenGL
        textureId = GL11.glGenTextures();
        GL11.glBindTexture(GL11.GL_TEXTURE_2D, textureId);

        // Set texture parameters for crisp rendering
        GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_MIN_FILTER, GL11.GL_LINEAR);
        GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_MAG_FILTER, GL11.GL_LINEAR);
        GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_WRAP_S, GL11.GL_CLAMP);
        GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_WRAP_T, GL11.GL_CLAMP);

        // Convert BufferedImage to OpenGL texture
        int[] pixels = new int[textureWidth * textureHeight];
        bufferedImage.getRGB(0, 0, textureWidth, textureHeight, pixels, 0, textureWidth);

        java.nio.ByteBuffer buffer = java.nio.ByteBuffer.allocateDirect(textureWidth * textureHeight * 4);
        for (int pixel : pixels) {
            buffer.put((byte) ((pixel >> 16) & 0xFF)); // Red
            buffer.put((byte) ((pixel >> 8) & 0xFF)); // Green
            buffer.put((byte) (pixel & 0xFF)); // Blue
            buffer.put((byte) ((pixel >> 24) & 0xFF)); // Alpha
        }
        ((java.nio.Buffer) buffer).flip(); // Java 8 compatibility fix

        GL11.glTexImage2D(GL11.GL_TEXTURE_2D, 0, GL11.GL_RGBA, textureWidth, textureHeight, 0, GL11.GL_RGBA,
                GL11.GL_UNSIGNED_BYTE, buffer);
    }

    /**
     * Draw text with modern styling
     */
    public void drawString(String text, float x, float y, Color color, boolean shadow, boolean glow) {
        if (text == null || text.isEmpty())
            return;

        // Use fallback rendering if texture failed
        if (textureId == -1) {
            drawStringFallback(text, x, y, color, shadow);
            return;
        }

        // Draw shadow first
        if (shadow) {
            drawStringInternal(text, x + 1, y + 1, new Color(0, 0, 0, color.getAlpha() / 2));
        }

        // Draw glow effect
        if (glow) {
            for (int i = 1; i <= 3; i++) {
                Color glowColor = new Color(color.getRed(), color.getGreen(), color.getBlue(),
                        color.getAlpha() / (i * 3));
                drawStringInternal(text, x - i, y, glowColor);
                drawStringInternal(text, x + i, y, glowColor);
                drawStringInternal(text, x, y - i, glowColor);
                drawStringInternal(text, x, y + i, glowColor);
            }
        }

        // Draw main text
        drawStringInternal(text, x, y, color);
    }

    /**
     * Fallback text rendering using basic OpenGL
     */
    private void drawStringFallback(String text, float x, float y, Color color, boolean shadow) {
        // This is a very basic fallback - in a real implementation you might want to
        // use
        // Minecraft's FontRenderer or another fallback method
        System.out.println("Fallback rendering: " + text + " at " + x + "," + y);
    }

    /**
     * Simple text drawing without effects
     */
    public void drawString(String text, float x, float y, Color color) {
        drawString(text, x, y, color, false, false);
    }

    /**
     * Internal text rendering method
     */
    private void drawStringInternal(String text, float x, float y, Color color) {
        GlStateManager.enableTexture2D();
        GlStateManager.enableBlend();
        GlStateManager.blendFunc(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA);

        GL11.glBindTexture(GL11.GL_TEXTURE_2D, textureId);
        GL11.glColor4f(color.getRed() / 255.0f, color.getGreen() / 255.0f, color.getBlue() / 255.0f,
                color.getAlpha() / 255.0f);

        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer worldRenderer = tessellator.getWorldRenderer();
        worldRenderer.begin(GL11.GL_QUADS, DefaultVertexFormats.POSITION_TEX);

        float currentX = x;

        for (char character : text.toCharArray()) {
            CharData data = charData.get(character);
            if (data != null) {
                float x1 = currentX;
                float y1 = y;
                float x2 = currentX + data.width;
                float y2 = y + data.height;

                worldRenderer.pos(x1, y2, 0).tex(data.u, data.v2).endVertex();
                worldRenderer.pos(x2, y2, 0).tex(data.u2, data.v2).endVertex();
                worldRenderer.pos(x2, y1, 0).tex(data.u2, data.v).endVertex();
                worldRenderer.pos(x1, y1, 0).tex(data.u, data.v).endVertex();

                currentX += data.advance;
            }
        }

        tessellator.draw();
        GlStateManager.disableBlend();
    }

    /**
     * Get the width of a string
     */
    public float getStringWidth(String text) {
        if (text == null || text.isEmpty())
            return 0;

        float width = 0;
        for (char character : text.toCharArray()) {
            CharData data = charData.get(character);
            if (data != null) {
                width += data.advance;
            }
        }
        return width;
    }

    /**
     * Get the height of the font
     */
    public float getFontHeight() {
        return font.getSize();
    }

    /**
     * Draw centered text
     */
    public void drawCenteredString(String text, float x, float y, Color color, boolean shadow, boolean glow) {
        float width = getStringWidth(text);
        drawString(text, x - width / 2, y, color, shadow, glow);
    }

    /**
     * Draw right-aligned text
     */
    public void drawRightAlignedString(String text, float x, float y, Color color, boolean shadow, boolean glow) {
        float width = getStringWidth(text);
        drawString(text, x - width, y, color, shadow, glow);
    }

    /**
     * Cleanup resources
     */
    public void cleanup() {
        if (textureId != -1) {
            GL11.glDeleteTextures(textureId);
            textureId = -1;
        }
    }

    /**
     * Cleanup all cached fonts
     */
    public static void cleanupAll() {
        for (TTFRenderer renderer : fontCache.values()) {
            renderer.cleanup();
        }
        fontCache.clear();
    }
}
