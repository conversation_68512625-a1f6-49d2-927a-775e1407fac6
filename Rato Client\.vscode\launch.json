{"version": "0.2.0", "configurations": [{"type": "java", "name": "Minecraft Client", "request": "launch", "cwd": "${workspaceFolder}/run", "console": "internalConsole", "stopOnEntry": false, "mainClass": "net.fabricmc.devlaunchinjector.Main", "vmArgs": "\"-Dfabric.dli.config=C:\\Users\\<USER>\\Desktop\\MightyMiner-master\\MightyMiner-master\\.gradle\\loom-cache\\launch.cfg\" \"-Dfabric.dli.env=client\" \"-Dfabric.dli.main=net.minecraft.launchwrapper.Launch\"", "args": "", "env": {}, "projectName": ""}, {"type": "java", "name": "Minecraft Server", "request": "launch", "cwd": "${workspaceFolder}/run", "console": "internalConsole", "stopOnEntry": false, "mainClass": "net.fabricmc.devlaunchinjector.Main", "vmArgs": "\"-Dfabric.dli.config=C:\\Users\\<USER>\\Desktop\\MightyMiner-master\\MightyMiner-master\\.gradle\\loom-cache\\launch.cfg\" \"-Dfabric.dli.env=server\" \"-Dfabric.dli.main=net.minecraft.launchwrapper.Launch\"", "args": "\"nogui\"", "env": {}, "projectName": ""}]}