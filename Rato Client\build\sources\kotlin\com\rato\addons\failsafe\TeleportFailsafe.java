package com.rato.addons.failsafe;

import com.rato.addons.config.RatoAddonsConfigSimple;
import com.rato.addons.util.Logger;
import net.minecraft.network.Packet;
import net.minecraft.network.play.server.S08PacketPlayerPosLook;
import net.minecraft.util.Vec3;

public class TeleportFailsafe extends Failsafe {

    private static TeleportFailsafe instance;

    public static TeleportFailsafe getInstance() {
        if (instance == null) {
            instance = new TeleportFailsafe();
        }
        return instance;
    }

    private TeleportState state = TeleportState.NONE;
    private Vec3 lastPosition = null;
    
    @Override
    public int getPriority() {
        return 4;
    }
    
    @Override
    public FailsafeManager.EmergencyType getType() {
        return FailsafeManager.EmergencyType.TELEPORT_CHECK;
    }
    
    @Override
    public boolean shouldSendNotification() {
        return RatoAddonsConfigSimple.discordWebhook;
    }
    
    @Override
    public boolean shouldPlaySound() {
        return RatoAddonsConfigSimple.soundAlert;
    }
    
    @Override
    public void onReceivedPacketDetection(Packet packet) {
        if (!isEnabled()) return;

        if (packet instanceof S08PacketPlayerPosLook) {
            S08PacketPlayerPosLook posLookPacket = (S08PacketPlayerPosLook) packet;

            double newX = posLookPacket.getX();
            double newY = posLookPacket.getY();
            double newZ = posLookPacket.getZ();

            Vec3 newPosition = new Vec3(newX, newY, newZ);

            if (lastPosition != null) {
                double distance = lastPosition.distanceTo(newPosition);

                if (isSuspiciousTeleport(distance, newPosition)) {
                    // DETECÇÃO INSTANTÂNEA - mostrar mensagem como no exemplo
                    Logger.sendMessage("§c§lTeleport Failsafe §7» §fPossible check detected!");
                    FailsafeManager.getInstance().possibleDetection(this);
                }
            }

            lastPosition = newPosition;
        }
    }
    
    private boolean isSuspiciousTeleport(double distance, Vec3 newPosition) {
        // Teleporte muito grande
        if (distance > 10.0) {
            return true;
        }
        
        // Coordenadas "perfeitas" (números redondos)
        if (isPerfectCoordinates(newPosition) && distance > 5.0) {
            return true;
        }
        
        return false;
    }
    
    private boolean isPerfectCoordinates(Vec3 pos) {
        double x = pos.xCoord;
        double y = pos.yCoord;
        double z = pos.zCoord;
        
        boolean xPerfect = (x == Math.floor(x)) || (Math.abs(x - Math.floor(x) - 0.5) < 0.1);
        boolean yPerfect = (y == Math.floor(y)) || (Math.abs(y - Math.floor(y) - 0.5) < 0.1);
        boolean zPerfect = (z == Math.floor(z)) || (Math.abs(z - Math.floor(z) - 0.5) < 0.1);
        
        return xPerfect && yPerfect && zPerfect;
    }
    
    @Override
    public void duringFailsafeTrigger() {
        switch (state) {
            case NONE:
                FailsafeManager.getInstance().scheduleRandomDelay(300, 700);
                state = TeleportState.WAIT_BEFORE_START;
                break;
                
            case WAIT_BEFORE_START:
                stopMovement();
                state = TeleportState.LOOK_AROUND;
                FailsafeManager.getInstance().scheduleRandomDelay(400, 800);
                break;
                
            case LOOK_AROUND:
                performHumanLikeMovement();
                state = TeleportState.RESPOND;
                FailsafeManager.getInstance().scheduleRandomDelay(500, 900);
                break;
                
            case RESPOND:
                performTeleportResponse();
                state = TeleportState.END;
                FailsafeManager.getInstance().scheduleRandomDelay(600, 1200);
                break;
                
            case END:
                endOfFailsafeTrigger();
                break;
        }
    }
    
    private void performTeleportResponse() {
        // Movimento natural após teleporte
        performHumanLikeMovement();
        
        // Simular "confusão" após teleporte
        if (mc.thePlayer != null) {
            float confusedYaw = (float) ((Math.random() - 0.5) * 90); // -45 a +45 graus
            mc.thePlayer.rotationYaw += confusedYaw;
        }
    }
    
    @Override
    public void endOfFailsafeTrigger() {
        FailsafeManager.getInstance().stopFailsafes();
        FailsafeManager.getInstance().scheduleDelay(1000);
    }
    
    @Override
    public void resetStates() {
        state = TeleportState.NONE;
    }
    
    private enum TeleportState {
        NONE,
        WAIT_BEFORE_START,
        LOOK_AROUND,
        RESPOND,
        END
    }
}
