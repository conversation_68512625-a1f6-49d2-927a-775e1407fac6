package com.rato.addons.pathfinding.professional;

import com.rato.addons.util.Logger;
import net.minecraft.client.Minecraft;
import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;

import java.util.List;

/**
 * Gerenciador central do sistema de pathfinding profissional
 */
public class PathfindingManager {
    
    private static PathfindingManager instance;
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Componentes do sistema
    private ProfessionalPathfinder pathfinder;
    private MovementController movementController;
    private MucifexPathRenderer pathRenderer;
    private PathOptimizer pathOptimizer;
    
    // Estado do sistema
    private boolean isActive = false;
    private boolean debugMode = false;
    private boolean renderingEnabled = true;
    
    // Configurações
    private float movementSpeed = 1.0f;
    private float rotationSmoothness = 0.7f;
    
    // Estatísticas
    private long pathfindingStartTime;
    private int totalNodesExplored;
    private int pathRecalculations;
    private Vec3 currentDestination;
    
    private PathfindingManager() {
        initializeComponents();
        MinecraftForge.EVENT_BUS.register(this);
    }
    
    public static PathfindingManager getInstance() {
        if (instance == null) {
            instance = new PathfindingManager();
        }
        return instance;
    }
    
    /**
     * Inicializa os componentes do sistema
     */
    private void initializeComponents() {
        pathfinder = new ProfessionalPathfinder();
        movementController = new MovementController();
        pathRenderer = new MucifexPathRenderer();
        pathOptimizer = new PathOptimizer();
        
        // Configurar callbacks
        pathfinder.setOnPathFound(this::onPathFound);
        pathfinder.setOnPathNotFound(this::onPathNotFound);
        pathfinder.setOnStuckDetected(this::onStuckDetected);
        
        movementController.setOnDestinationReached(this::onDestinationReached);
        movementController.setOnMovementFailed(this::onMovementFailed);
    }
    
    /**
     * Inicia pathfinding para destino específico
     */
    public boolean startPathfinding(Vec3 destination) {
        if (mc.thePlayer == null || mc.theWorld == null) {
            return false;
        }
        
        if (isActive) {
            stopPathfinding();
        }
        
        currentDestination = destination;
        pathfindingStartTime = System.currentTimeMillis();
        totalNodesExplored = 0;
        pathRecalculations = 0;
        
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        
        if (debugMode) {
            Logger.sendMessage("§7[DEBUG] Iniciando pathfinding de " + 
                formatVec3(playerPos) + " para " + formatVec3(destination));
        }
        
        // Iniciar pathfinding em thread separada
        new Thread(() -> {
            List<PathNode> path = pathfinder.findPath(playerPos, destination);
            
            if (path != null && !path.isEmpty()) {
                // Otimizar caminho
                path = pathOptimizer.optimizePath(path);
                
                // Configurar movimento
                movementController.setPath(path);
                movementController.setMovementSpeed(movementSpeed);
                movementController.setRotationSmoothness(rotationSmoothness);
                
                // Configurar renderização
                if (renderingEnabled) {
                    pathRenderer.setPath(path);
                    pathRenderer.setEnabled(true);
                }
                
                isActive = true;
                totalNodesExplored = pathfinder.getNodesExplored();
                
                if (debugMode) {
                    Logger.sendMessage("§a[DEBUG] Caminho encontrado! Nós: " + path.size() + 
                        ", Explorados: " + totalNodesExplored);
                }
            } else {
                Logger.sendMessage("§c[Rato Client] Não foi possível encontrar um caminho para o destino!");
                if (debugMode) {
                    Logger.sendMessage("§7[DEBUG] Nós explorados: " + pathfinder.getNodesExplored());
                }
            }
        }).start();
        
        return true;
    }
    
    /**
     * Para o pathfinding atual
     */
    public void stopPathfinding() {
        isActive = false;
        
        if (movementController != null) {
            movementController.stop();
        }
        
        if (pathRenderer != null) {
            pathRenderer.setEnabled(false);
        }
        
        if (pathfinder != null) {
            pathfinder.cancel();
        }
        
        currentDestination = null;
        
        if (debugMode) {
            Logger.sendMessage("§7[DEBUG] Pathfinding parado");
        }
    }
    
    /**
     * Atualização principal (chamada a cada tick)
     */
    @SubscribeEvent
    public void onTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.START || !isActive) {
            return;
        }
        
        if (mc.thePlayer == null || mc.theWorld == null) {
            stopPathfinding();
            return;
        }
        
        // Atualizar movimento
        movementController.update();
        
        // Verificar se precisa recalcular caminho
        if (shouldRecalculatePath()) {
            recalculatePath();
        }
    }
    
    /**
     * Verifica se deve recalcular o caminho
     */
    private boolean shouldRecalculatePath() {
        if (currentDestination == null) return false;
        
        // Recalcular a cada 5 segundos ou se detectar mudanças significativas no ambiente
        long timeSinceStart = System.currentTimeMillis() - pathfindingStartTime;
        return timeSinceStart > 5000 && (timeSinceStart % 5000) < 50; // A cada 5 segundos
    }
    
    /**
     * Recalcula o caminho atual
     */
    private void recalculatePath() {
        if (!isActive || currentDestination == null) return;
        
        pathRecalculations++;
        
        if (debugMode) {
            Logger.sendMessage("§7[DEBUG] Recalculando caminho... (" + pathRecalculations + ")");
        }
        
        Vec3 currentPos = mc.thePlayer.getPositionVector();
        
        new Thread(() -> {
            List<PathNode> newPath = pathfinder.findPath(currentPos, currentDestination);
            
            if (newPath != null && !newPath.isEmpty()) {
                newPath = pathOptimizer.optimizePath(newPath);
                movementController.updatePath(newPath);
                
                if (renderingEnabled) {
                    pathRenderer.setPath(newPath);
                }
                
                if (debugMode) {
                    Logger.sendMessage("§a[DEBUG] Caminho recalculado! Novos nós: " + newPath.size());
                }
            }
        }).start();
    }
    
    // Callbacks do sistema
    private void onPathFound(List<PathNode> path) {
        if (debugMode) {
            Logger.sendMessage("§a[DEBUG] Caminho encontrado pelo pathfinder");
        }
    }
    
    private void onPathNotFound() {
        Logger.sendMessage("§c[Rato Client] Caminho não encontrado!");
        stopPathfinding();
    }
    
    private void onStuckDetected() {
        if (debugMode) {
            Logger.sendMessage("§e[DEBUG] Player preso detectado, recalculando...");
        }
        recalculatePath();
    }
    
    private void onDestinationReached() {
        Logger.sendMessage("§a[Rato Client] Destino alcançado!");
        stopPathfinding();
    }
    
    private void onMovementFailed() {
        if (debugMode) {
            Logger.sendMessage("§c[DEBUG] Falha no movimento, tentando recalcular...");
        }
        recalculatePath();
    }
    
    // Getters e Setters
    public boolean isActive() { return isActive; }
    public void setDebugMode(boolean debug) { this.debugMode = debug; }
    public boolean isDebugMode() { return debugMode; }
    public void setRenderingEnabled(boolean enabled) { 
        this.renderingEnabled = enabled;
        if (pathRenderer != null) {
            pathRenderer.setEnabled(enabled && isActive);
        }
    }
    public boolean isRenderingEnabled() { return renderingEnabled; }
    public void setMovementSpeed(float speed) { this.movementSpeed = speed; }
    public float getMovementSpeed() { return movementSpeed; }
    public void setRotationSmoothness(float smoothness) { this.rotationSmoothness = smoothness; }
    public float getRotationSmoothness() { return rotationSmoothness; }
    
    /**
     * Retorna status detalhado do sistema
     */
    public String getDetailedStatus() {
        if (!isActive) return "Inativo";
        
        long elapsed = System.currentTimeMillis() - pathfindingStartTime;
        return String.format("Ativo há %ds, Nós: %d, Recálculos: %d", 
            elapsed / 1000, totalNodesExplored, pathRecalculations);
    }
    
    /**
     * Retorna informações de debug
     */
    public String getDebugInfo() {
        StringBuilder info = new StringBuilder();
        info.append("§7Status: ").append(isActive ? "§aAtivo" : "§cInativo").append("\n");
        info.append("§7Destino: ").append(currentDestination != null ? formatVec3(currentDestination) : "§cNenhum").append("\n");
        info.append("§7Nós explorados: §f").append(totalNodesExplored).append("\n");
        info.append("§7Recálculos: §f").append(pathRecalculations).append("\n");
        info.append("§7Velocidade: §f").append(movementSpeed).append("\n");
        info.append("§7Suavidade: §f").append(rotationSmoothness).append("\n");
        info.append("§7Renderização: ").append(renderingEnabled ? "§aAtiva" : "§cInativa").append("\n");

        // Informações de terreno se ativo
        if (isActive && mc.thePlayer != null) {
            Vec3 playerPos = mc.thePlayer.getPositionVector();
            TerrainAnalyzer.TerrainInfo currentTerrain = pathfinder.getTerrainAnalyzer().analyzePosition(new BlockPos(playerPos));
            info.append("§7Terreno atual: §f").append(currentTerrain.type.name()).append("\n");
            info.append("§7Custo movimento: §f").append(String.format("%.1f", currentTerrain.movementCost)).append("\n");
            info.append("§7Opções movimento: §f").append(currentTerrain.pathOptions.size());
        }

        return info.toString();
    }

    /**
     * Limpa cache de análise de terreno
     */
    public void clearTerrainCache() {
        if (pathfinder != null) {
            pathfinder.clearTerrainCache();
        }
    }
    
    private String formatVec3(Vec3 vec) {
        return String.format("%.1f, %.1f, %.1f", vec.xCoord, vec.yCoord, vec.zCoord);
    }
}
