commonProperties
	fabric.development=true
	fabric.remapClasspathFile=C:\Users\<USER>\Desktop\Rato Client\Rato Client\.gradle\loom-cache\remapClasspath.txt
	log4j.configurationFile=C:\Users\<USER>\Desktop\Rato Client\Rato Client\.gradle\loom-cache\log4j.xml
	log4j2.formatMsgNoLookups=true
	net.minecraftforge.gradle.GradleStart.srg.srg-mcp=C:\Users\<USER>\.gradle\caches\polyfrost-loom\1.8.9\de.oceanlabs.mcp.mcp_stable.1_8_9.22-1.8.9-forge-1.8.9-11.15.1.2318-1.8.9\mappings-srg-named.srg
	mixin.env.remapRefMap=true
	fabric.log.disableAnsi=false
clientProperties
	java.library.path=C:\Users\<USER>\.gradle\caches\polyfrost-loom\1.8.9\natives
	org.lwjgl.librarypath=C:\Users\<USER>\.gradle\caches\polyfrost-loom\1.8.9\natives
	mixin.debug=true
	asmhelper.verbose=true
clientArgs
	--assetIndex
	1.8.9-1.8
	--assetsDir
	C:\Users\<USER>\.gradle\caches\polyfrost-loom\assets
	--tweakClass
	net.minecraftforge.fml.common.launcher.FMLTweaker
	-Ddevauth.enabled
	true
serverArgs
	--tweakClass
	net.minecraftforge.fml.common.launcher.FMLServerTweaker
commonArgs
	--accessToken
	undefined
	--mixin
	mixins.ratoaddons.json