package com.rato.addons.visuals.glow;

import com.rato.addons.config.RatoAddonsConfigSimple;
import net.minecraft.client.Minecraft;
import net.minecraft.entity.Entity;
import net.minecraft.entity.monster.EntityZombie;
import net.minecraft.entity.player.EntityPlayer;

import java.awt.*;
import java.util.List;

/**
 * Renderizador de Glow baseado no LiquidBounce
 * Aplica efeito de brilho nas entidades
 */
public class GlowRenderer {

    private static final Minecraft mc = Minecraft.getMinecraft();

    /**
     * Renderiza efeito glow para todas as entidades
     */
    public void render(float partialTicks) {
        if (mc.theWorld == null || mc.thePlayer == null)
            return;

        List<Entity> entities = mc.theWorld.loadedEntityList;

        for (Entity entity : entities) {
            if (!shouldRenderGlow(entity))
                continue;

            // Aplicar efeito glow
            applyGlowEffect(entity);
        }
    }

    /**
     * Verifica se deve aplicar glow na entidade
     */
    private boolean shouldRenderGlow(Entity entity) {
        if (entity == mc.thePlayer)
            return false;
        if (entity.isDead)
            return false;

        // Verificar tipos habilitados
        if (entity instanceof EntityZombie && RatoAddonsConfigSimple.visualsGlowZombies) {
            return true;
        }

        if (entity instanceof EntityPlayer && RatoAddonsConfigSimple.visualsGlowPlayers) {
            return true;
        }

        return false;
    }

    /**
     * Aplica efeito glow na entidade (Minecraft 1.8.9 compatible)
     */
    private void applyGlowEffect(Entity entity) {
        // Obter cor do glow
        Color glowColor = getGlowColor(entity);

        // Para Minecraft 1.8.9, usamos renderização customizada ao invés de setGlowing
        // O efeito glow será renderizado através do ESP renderer
        // Esta implementação é compatível com versões antigas
    }

    /**
     * Obtém a cor do glow para a entidade
     */
    private Color getGlowColor(Entity entity) {
        if (entity instanceof EntityZombie) {
            return new Color(
                    RatoAddonsConfigSimple.visualsZombieGlowColor.getRed(),
                    RatoAddonsConfigSimple.visualsZombieGlowColor.getGreen(),
                    RatoAddonsConfigSimple.visualsZombieGlowColor.getBlue(),
                    255);
        } else if (entity instanceof EntityPlayer) {
            return new Color(
                    RatoAddonsConfigSimple.visualsPlayerGlowColor.getRed(),
                    RatoAddonsConfigSimple.visualsPlayerGlowColor.getGreen(),
                    RatoAddonsConfigSimple.visualsPlayerGlowColor.getBlue(),
                    255);
        }

        return new Color(255, 255, 255, 255);
    }

    /**
     * Remove efeito glow de todas as entidades (Minecraft 1.8.9 compatible)
     */
    public void clearGlow() {
        if (mc.theWorld == null)
            return;

        // Para Minecraft 1.8.9, não há método setGlowing
        // O glow é controlado através do sistema de renderização customizado
        // Esta implementação é compatível com versões antigas
    }
}
