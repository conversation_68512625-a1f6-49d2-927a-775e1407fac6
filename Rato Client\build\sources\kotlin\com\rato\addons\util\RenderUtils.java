package com.rato.addons.util;

import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.FontRenderer;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.Tessellator;
import net.minecraft.client.renderer.WorldRenderer;
import net.minecraft.client.renderer.vertex.DefaultVertexFormats;
import net.minecraft.util.AxisAlignedBB;
import org.lwjgl.opengl.GL11;

public class RenderUtils {

    /**
     * Renderiza um retângulo com bordas arredondadas
     * 
     * @param x       Coordenada X do canto superior esquerdo
     * @param y       Coordenada Y do canto superior esquerdo
     * @param width   Largura do retângulo
     * @param height  Altura do retângulo
     * @param radius  Raio do arredondamento dos cantos
     * @param samples Quantidade de amostras para suavidade (recomendado: 8-16)
     * @param color   Cor no formato ARGB (0xAARRGGBB)
     */
    public static void renderRoundedQuad(float x, float y, float width, float height, float radius, int samples,
            int color) {
        renderRoundedQuadInternal(x, y, width, height, radius, samples, color);
    }

    /**
     * Método interno que faz a renderização real do retângulo arredondado
     */
    private static void renderRoundedQuadInternal(float x, float y, float width, float height, float radius,
            int samples, int color) {
        // Preparar estados OpenGL
        GlStateManager.disableTexture2D();
        GlStateManager.enableBlend();
        GlStateManager.tryBlendFuncSeparate(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA, GL11.GL_ONE, GL11.GL_ZERO);

        // Extrair componentes de cor
        float alpha = ((color >> 24) & 0xFF) / 255.0F;
        float red = ((color >> 16) & 0xFF) / 255.0F;
        float green = ((color >> 8) & 0xFF) / 255.0F;
        float blue = (color & 0xFF) / 255.0F;
        GlStateManager.color(red, green, blue, alpha);

        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer buffer = tessellator.getWorldRenderer();

        // Limitar o raio para não exceder metade da menor dimensão
        float maxRadius = Math.min(width, height) / 2.0F;
        radius = Math.min(radius, maxRadius);

        // Renderizar retângulo central (sem cantos)
        buffer.begin(GL11.GL_QUADS, DefaultVertexFormats.POSITION);

        // Retângulo horizontal central
        buffer.pos(x + radius, y, 0).endVertex();
        buffer.pos(x + radius, y + height, 0).endVertex();
        buffer.pos(x + width - radius, y + height, 0).endVertex();
        buffer.pos(x + width - radius, y, 0).endVertex();

        tessellator.draw();

        // Retângulos verticais laterais
        buffer.begin(GL11.GL_QUADS, DefaultVertexFormats.POSITION);

        buffer.pos(x, y + radius, 0).endVertex();
        buffer.pos(x, y + height - radius, 0).endVertex();
        buffer.pos(x + width, y + height - radius, 0).endVertex();
        buffer.pos(x + width, y + radius, 0).endVertex();

        tessellator.draw();

        // Renderizar cantos arredondados usando TRIANGLE_FAN
        // Canto superior esquerdo
        renderCorner(x + radius, y + radius, radius, 180, 270, samples);

        // Canto superior direito
        renderCorner(x + width - radius, y + radius, radius, 270, 360, samples);

        // Canto inferior direito
        renderCorner(x + width - radius, y + height - radius, radius, 0, 90, samples);

        // Canto inferior esquerdo
        renderCorner(x + radius, y + height - radius, radius, 90, 180, samples);

        // Restaurar estados OpenGL
        GlStateManager.enableTexture2D();
        GlStateManager.disableBlend();
        GlStateManager.color(1.0F, 1.0F, 1.0F, 1.0F);
    }

    /**
     * Renderiza um canto arredondado usando TRIANGLE_FAN
     */
    private static void renderCorner(float centerX, float centerY, float radius, float startAngle, float endAngle,
            int samples) {
        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer buffer = tessellator.getWorldRenderer();

        buffer.begin(GL11.GL_TRIANGLE_FAN, DefaultVertexFormats.POSITION);

        // Centro do arco
        buffer.pos(centerX, centerY, 0).endVertex();

        // Pontos do arco (interpolação em 90°)
        for (int i = 0; i <= samples; i++) {
            float angle = (float) Math.toRadians(startAngle + (endAngle - startAngle) * i / samples);
            float x = centerX + radius * (float) Math.cos(angle);
            float y = centerY + radius * (float) Math.sin(angle);
            buffer.pos(x, y, 0).endVertex();
        }

        tessellator.draw();
    }

    /**
     * Método de conveniência para renderizar com samples padrão (12)
     */
    public static void renderRoundedQuad(float x, float y, float width, float height, float radius, int color) {
        renderRoundedQuad(x, y, width, height, radius, 12, color);
    }

    /**
     * Desenha uma bounding box com outline
     */
    public static void drawOutlinedBoundingBox(AxisAlignedBB bb, int color, float lineWidth) {
        GlStateManager.pushMatrix();
        GlStateManager.disableTexture2D();
        GlStateManager.enableBlend();
        GlStateManager.tryBlendFuncSeparate(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA, GL11.GL_ONE, GL11.GL_ZERO);
        GlStateManager.disableDepth();

        GL11.glLineWidth(lineWidth);

        float alpha = ((color >> 24) & 0xFF) / 255.0F;
        float red = ((color >> 16) & 0xFF) / 255.0F;
        float green = ((color >> 8) & 0xFF) / 255.0F;
        float blue = (color & 0xFF) / 255.0F;
        GlStateManager.color(red, green, blue, alpha);

        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer buffer = tessellator.getWorldRenderer();

        buffer.begin(GL11.GL_LINES, DefaultVertexFormats.POSITION);

        // Bottom face
        buffer.pos(bb.minX, bb.minY, bb.minZ).endVertex();
        buffer.pos(bb.maxX, bb.minY, bb.minZ).endVertex();
        buffer.pos(bb.maxX, bb.minY, bb.minZ).endVertex();
        buffer.pos(bb.maxX, bb.minY, bb.maxZ).endVertex();
        buffer.pos(bb.maxX, bb.minY, bb.maxZ).endVertex();
        buffer.pos(bb.minX, bb.minY, bb.maxZ).endVertex();
        buffer.pos(bb.minX, bb.minY, bb.maxZ).endVertex();
        buffer.pos(bb.minX, bb.minY, bb.minZ).endVertex();

        // Top face
        buffer.pos(bb.minX, bb.maxY, bb.minZ).endVertex();
        buffer.pos(bb.maxX, bb.maxY, bb.minZ).endVertex();
        buffer.pos(bb.maxX, bb.maxY, bb.minZ).endVertex();
        buffer.pos(bb.maxX, bb.maxY, bb.maxZ).endVertex();
        buffer.pos(bb.maxX, bb.maxY, bb.maxZ).endVertex();
        buffer.pos(bb.minX, bb.maxY, bb.maxZ).endVertex();
        buffer.pos(bb.minX, bb.maxY, bb.maxZ).endVertex();
        buffer.pos(bb.minX, bb.maxY, bb.minZ).endVertex();

        // Vertical edges
        buffer.pos(bb.minX, bb.minY, bb.minZ).endVertex();
        buffer.pos(bb.minX, bb.maxY, bb.minZ).endVertex();
        buffer.pos(bb.maxX, bb.minY, bb.minZ).endVertex();
        buffer.pos(bb.maxX, bb.maxY, bb.minZ).endVertex();
        buffer.pos(bb.maxX, bb.minY, bb.maxZ).endVertex();
        buffer.pos(bb.maxX, bb.maxY, bb.maxZ).endVertex();
        buffer.pos(bb.minX, bb.minY, bb.maxZ).endVertex();
        buffer.pos(bb.minX, bb.maxY, bb.maxZ).endVertex();

        tessellator.draw();

        GlStateManager.enableDepth();
        GlStateManager.enableTexture2D();
        GlStateManager.disableBlend();
        GlStateManager.color(1.0F, 1.0F, 1.0F, 1.0F);
        GlStateManager.popMatrix();
    }

    /**
     * Desenha uma bounding box preenchida
     */
    public static void drawFilledBoundingBox(AxisAlignedBB bb, int color) {
        GlStateManager.pushMatrix();
        GlStateManager.disableTexture2D();
        GlStateManager.enableBlend();
        GlStateManager.tryBlendFuncSeparate(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA, GL11.GL_ONE, GL11.GL_ZERO);
        GlStateManager.disableDepth();

        float alpha = ((color >> 24) & 0xFF) / 255.0F;
        float red = ((color >> 16) & 0xFF) / 255.0F;
        float green = ((color >> 8) & 0xFF) / 255.0F;
        float blue = (color & 0xFF) / 255.0F;
        GlStateManager.color(red, green, blue, alpha);

        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer buffer = tessellator.getWorldRenderer();

        buffer.begin(GL11.GL_QUADS, DefaultVertexFormats.POSITION);

        // Bottom face
        buffer.pos(bb.minX, bb.minY, bb.minZ).endVertex();
        buffer.pos(bb.maxX, bb.minY, bb.minZ).endVertex();
        buffer.pos(bb.maxX, bb.minY, bb.maxZ).endVertex();
        buffer.pos(bb.minX, bb.minY, bb.maxZ).endVertex();

        // Top face
        buffer.pos(bb.minX, bb.maxY, bb.minZ).endVertex();
        buffer.pos(bb.minX, bb.maxY, bb.maxZ).endVertex();
        buffer.pos(bb.maxX, bb.maxY, bb.maxZ).endVertex();
        buffer.pos(bb.maxX, bb.maxY, bb.minZ).endVertex();

        tessellator.draw();

        GlStateManager.enableDepth();
        GlStateManager.enableTexture2D();
        GlStateManager.disableBlend();
        GlStateManager.color(1.0F, 1.0F, 1.0F, 1.0F);
        GlStateManager.popMatrix();
    }

    /**
     * Desenha um nametag 3D no mundo
     */
    public static void drawNameTag(String text, double x, double y, double z, int color) {
        Minecraft mc = Minecraft.getMinecraft();
        FontRenderer fontRenderer = mc.fontRendererObj;

        GlStateManager.pushMatrix();
        GlStateManager.translate(x, y, z);
        GlStateManager.rotate(-mc.getRenderManager().playerViewY, 0.0F, 1.0F, 0.0F);
        GlStateManager.rotate(mc.getRenderManager().playerViewX, 1.0F, 0.0F, 0.0F);
        GlStateManager.scale(-0.025F, -0.025F, 0.025F);

        GlStateManager.disableDepth();
        GlStateManager.enableBlend();
        GlStateManager.tryBlendFuncSeparate(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA, GL11.GL_ONE, GL11.GL_ZERO);

        int textWidth = fontRenderer.getStringWidth(text);
        int textHeight = fontRenderer.FONT_HEIGHT;

        // Desenhar fundo
        GlStateManager.disableTexture2D();
        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer buffer = tessellator.getWorldRenderer();

        buffer.begin(GL11.GL_QUADS, DefaultVertexFormats.POSITION_COLOR);
        buffer.pos(-textWidth / 2 - 2, -2, 0).color(0, 0, 0, 64).endVertex();
        buffer.pos(-textWidth / 2 - 2, textHeight + 2, 0).color(0, 0, 0, 64).endVertex();
        buffer.pos(textWidth / 2 + 2, textHeight + 2, 0).color(0, 0, 0, 64).endVertex();
        buffer.pos(textWidth / 2 + 2, -2, 0).color(0, 0, 0, 64).endVertex();
        tessellator.draw();

        // Desenhar texto
        GlStateManager.enableTexture2D();
        fontRenderer.drawString(text, -textWidth / 2, 0, color);

        GlStateManager.enableDepth();
        GlStateManager.disableBlend();
        GlStateManager.color(1.0F, 1.0F, 1.0F, 1.0F);
        GlStateManager.popMatrix();
    }

    /**
     * Desenha uma linha 3D entre dois pontos
     */
    public static void drawLine(double x1, double y1, double z1, double x2, double y2, double z2, int color,
            float lineWidth) {
        GlStateManager.pushMatrix();
        GlStateManager.disableTexture2D();
        GlStateManager.enableBlend();
        GlStateManager.tryBlendFuncSeparate(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA, GL11.GL_ONE, GL11.GL_ZERO);
        GlStateManager.disableDepth();

        GL11.glLineWidth(lineWidth);

        float alpha = ((color >> 24) & 0xFF) / 255.0F;
        float red = ((color >> 16) & 0xFF) / 255.0F;
        float green = ((color >> 8) & 0xFF) / 255.0F;
        float blue = (color & 0xFF) / 255.0F;
        GlStateManager.color(red, green, blue, alpha);

        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer buffer = tessellator.getWorldRenderer();

        buffer.begin(GL11.GL_LINES, DefaultVertexFormats.POSITION);
        buffer.pos(x1, y1, z1).endVertex();
        buffer.pos(x2, y2, z2).endVertex();
        tessellator.draw();

        GlStateManager.enableDepth();
        GlStateManager.enableTexture2D();
        GlStateManager.disableBlend();
        GlStateManager.color(1.0F, 1.0F, 1.0F, 1.0F);
        GlStateManager.popMatrix();
    }
}
