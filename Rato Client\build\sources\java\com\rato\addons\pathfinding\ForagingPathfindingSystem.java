package com.rato.addons.pathfinding;

import net.minecraft.client.Minecraft;
import net.minecraft.util.Vec3;
import net.minecraft.util.BlockPos;
import com.rato.addons.pathfinding.analysis.*;
import com.rato.addons.pathfinding.planning.*;
import com.rato.addons.pathfinding.movement.*;
import com.rato.addons.util.Logger;
import com.rato.addons.config.RatoAddonsConfigSimple;

import java.util.*;

/**
 * Sistema principal de pathfinding 3D para macro de foraging
 * Integra todos os componentes: mapeamento, detecção, planejamento e execução
 */
public class ForagingPathfindingSystem {

    private final Minecraft mc = Minecraft.getMinecraft();

    // Componentes do sistema
    private final Grid3DMapper gridMapper;
    private final TreeDetector3D treeDetector;
    private final PathfindingGrid3D pathfindingGrid;
    private final AStar3DPathfinder aStarPathfinder;
    private final PathExecutor pathExecutor;

    // Estado do sistema
    private boolean isActive = false;
    private Grid3DMapper.MappedGrid3D currentGrid = null;
    private PathfindingGrid3D.BuiltPathfindingGrid currentPathfindingGrid = null;
    private List<TreeDetector3D.TreeAnalysis> detectedTrees = new ArrayList<>();
    private TreeDetector3D.TreeAnalysis currentTarget = null;

    // Configurações (obtidas do config)
    private int scanRadius = RatoAddonsConfigSimple.foraging3DScanRadius;
    private int scanHeight = RatoAddonsConfigSimple.foraging3DScanHeight;
    private long lastGridUpdate = 0;
    private static final long GRID_UPDATE_INTERVAL = 5000; // 5 segundos
    private static final long TREE_SCAN_INTERVAL = 2000; // 2 segundos
    private long lastTreeScan = 0;

    // Callbacks
    private Runnable onTreeReached = null;
    private Runnable onNoTreesFound = null;
    private Runnable onPathfindingFailed = null;

    /**
     * Estado do sistema de pathfinding
     */
    public enum SystemState {
        IDLE("Idle"),
        MAPPING_ENVIRONMENT("Mapping environment"),
        DETECTING_TREES("Detecting trees"),
        PLANNING_PATH("Planning path"),
        EXECUTING_PATH("Executing path"),
        TARGET_REACHED("Target reached"),
        ERROR("Error occurred");

        public final String displayName;

        SystemState(String displayName) {
            this.displayName = displayName;
        }
    }

    private SystemState currentState = SystemState.IDLE;

    /**
     * Construtor
     */
    public ForagingPathfindingSystem() {
        this.gridMapper = new Grid3DMapper();
        this.treeDetector = new TreeDetector3D();
        this.pathfindingGrid = new PathfindingGrid3D();
        this.aStarPathfinder = new AStar3DPathfinder();
        this.pathExecutor = new PathExecutor();

        // Configurar callbacks do executor
        setupPathExecutorCallbacks();
    }

    /**
     * Configura callbacks do executor de caminho
     */
    private void setupPathExecutorCallbacks() {
        pathExecutor.setOnPathComplete(() -> {
            Logger.sendMessage("§aChegou ao alvo! Iniciando quebra da árvore...");
            currentState = SystemState.TARGET_REACHED;
            if (onTreeReached != null) {
                onTreeReached.run();
            }
        });

        pathExecutor.setOnPathFailed(() -> {
            Logger.sendMessage("§cFalha na execução do caminho. Replanejando...");
            handlePathExecutionFailure();
        });

        pathExecutor.setOnStuckDetected(() -> {
            Logger.sendMessage("§eJogador preso detectado. Remapeando ambiente...");
            remapEnvironment();
        });
    }

    /**
     * Inicia o sistema de pathfinding para foraging
     */
    public boolean startForagingPathfinding() {
        if (isActive) {
            Logger.sendMessage("§cSistema de pathfinding já está ativo!");
            return false;
        }

        if (mc.thePlayer == null || mc.theWorld == null) {
            Logger.sendMessage("§cJogador ou mundo não disponível!");
            return false;
        }

        isActive = true;
        currentState = SystemState.MAPPING_ENVIRONMENT;

        Logger.sendMessage("§aIniciando sistema de pathfinding 3D para foraging...");

        // Iniciar ciclo de pathfinding
        return startPathfindingCycle();
    }

    /**
     * Para o sistema de pathfinding
     */
    public void stopForagingPathfinding() {
        if (!isActive)
            return;

        isActive = false;
        currentState = SystemState.IDLE;

        // Parar execução de caminho
        pathExecutor.stopExecution();

        // Limpar dados
        currentGrid = null;
        currentPathfindingGrid = null;
        detectedTrees.clear();
        currentTarget = null;

        Logger.sendMessage("§7Sistema de pathfinding parado");
    }

    /**
     * Atualiza o sistema (chamado a cada tick)
     */
    public void update() {
        if (!isActive || mc.thePlayer == null || mc.theWorld == null) {
            return;
        }

        // Atualizar executor de caminho
        pathExecutor.update();

        // Verificar se precisa atualizar grid
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastGridUpdate > GRID_UPDATE_INTERVAL) {
            remapEnvironment();
        }

        // Verificar se precisa escanear árvores novamente
        if (currentTime - lastTreeScan > TREE_SCAN_INTERVAL &&
                currentState != SystemState.EXECUTING_PATH) {
            scanForTrees();
        }

        // Lógica específica do estado atual
        updateStateLogic();
    }

    /**
     * Inicia ciclo completo de pathfinding
     */
    private boolean startPathfindingCycle() {
        // Fase 1: Mapear ambiente
        if (!mapEnvironment()) {
            Logger.sendMessage("§cFalha no mapeamento do ambiente!");
            return false;
        }

        // Fase 2: Detectar árvores
        if (!scanForTrees()) {
            Logger.sendMessage("§cNenhuma árvore válida encontrada!");
            if (onNoTreesFound != null) {
                onNoTreesFound.run();
            }
            return false;
        }

        // Fase 3: Selecionar melhor árvore
        if (!selectBestTree()) {
            Logger.sendMessage("§cNão foi possível selecionar árvore alvo!");
            return false;
        }

        // Fase 4: Planejar caminho
        if (!planPathToTarget()) {
            Logger.sendMessage("§cFalha no planejamento do caminho!");
            if (onPathfindingFailed != null) {
                onPathfindingFailed.run();
            }
            return false;
        }

        return true;
    }

    /**
     * Mapeia o ambiente ao redor do jogador
     */
    private boolean mapEnvironment() {
        currentState = SystemState.MAPPING_ENVIRONMENT;
        Logger.sendMessage("§7Mapeando ambiente 3D (raio: " + scanRadius + ", altura: " + scanHeight + ")...");

        Vec3 playerPos = mc.thePlayer.getPositionVector();
        currentGrid = gridMapper.mapSurroundings(playerPos, scanRadius, scanHeight);

        if (currentGrid == null) {
            return false;
        }

        // Construir grid de pathfinding
        currentPathfindingGrid = pathfindingGrid.buildPathfindingGrid(currentGrid);

        if (currentPathfindingGrid == null) {
            return false;
        }

        lastGridUpdate = System.currentTimeMillis();

        Logger.sendMessage("§aAmbiente mapeado: " + currentGrid.cells.size() + " células, " +
                currentGrid.walkablePositions.size() + " posições caminháveis");

        return true;
    }

    /**
     * Escaneia por árvores válidas
     */
    private boolean scanForTrees() {
        if (currentGrid == null) {
            return false;
        }

        currentState = SystemState.DETECTING_TREES;
        Logger.sendMessage("§7Detectando árvores válidas...");

        Vec3 playerPos = mc.thePlayer.getPositionVector();
        detectedTrees = treeDetector.detectTrees(currentGrid, playerPos);

        lastTreeScan = System.currentTimeMillis();

        if (detectedTrees.isEmpty()) {
            Logger.sendMessage("§eNenhuma árvore válida encontrada na área");
            return false;
        }

        Logger.sendMessage("§aEncontradas " + detectedTrees.size() + " árvores válidas");

        // Log das melhores árvores
        for (int i = 0; i < Math.min(3, detectedTrees.size()); i++) {
            TreeDetector3D.TreeAnalysis tree = detectedTrees.get(i);
            Logger.sendMessage("§7  " + (i + 1) + ". " + tree.treeType.displayName +
                    " - Prioridade: " + String.format("%.2f", tree.priority) +
                    " - Dist: " + String.format("%.1f",
                            mc.thePlayer.getPositionVector().distanceTo(tree.optimalStandingPosition))
                    + "m");
        }

        return true;
    }

    /**
     * Seleciona a melhor árvore para quebrar
     */
    private boolean selectBestTree() {
        if (detectedTrees.isEmpty()) {
            return false;
        }

        // Selecionar árvore com maior prioridade
        currentTarget = detectedTrees.get(0);

        Logger.sendMessage("§aÁrvore selecionada: " + currentTarget.treeType.displayName +
                " em " + String.format("%.1f, %.1f, %.1f",
                        currentTarget.optimalStandingPosition.xCoord,
                        currentTarget.optimalStandingPosition.yCoord,
                        currentTarget.optimalStandingPosition.zCoord));

        return true;
    }

    /**
     * Planeja caminho para o alvo
     */
    private boolean planPathToTarget() {
        if (currentTarget == null || currentPathfindingGrid == null) {
            return false;
        }

        currentState = SystemState.PLANNING_PATH;
        Logger.sendMessage("§7Planejando caminho para árvore alvo...");

        Vec3 playerPos = mc.thePlayer.getPositionVector();
        Vec3 targetPos = currentTarget.optimalStandingPosition;

        AStar3DPathfinder.PathfindingResult result = aStarPathfinder.findPath(playerPos, targetPos,
                currentPathfindingGrid);

        if (!result.pathFound) {
            Logger.sendMessage("§cFalha no pathfinding: " + result.failureReason);
            return false;
        }

        Logger.sendMessage("§aCaminho encontrado: " + result.path.size() + " waypoints, " +
                "custo: " + String.format("%.1f", result.totalCost) +
                ", tempo: " + result.computationTime + "ms");

        // Iniciar execução do caminho
        currentState = SystemState.EXECUTING_PATH;
        return pathExecutor.startPathExecution(result);
    }

    /**
     * Atualiza lógica específica do estado
     */
    private void updateStateLogic() {
        switch (currentState) {
            case TARGET_REACHED:
                // Aguardar callback externo para quebrar árvore
                break;

            case ERROR:
                // Tentar recuperar
                if (System.currentTimeMillis() - lastGridUpdate > 3000) {
                    startPathfindingCycle();
                }
                break;
        }
    }

    /**
     * Lida com falha na execução do caminho
     */
    private void handlePathExecutionFailure() {
        currentState = SystemState.ERROR;

        // Tentar replanejar com árvore diferente
        if (detectedTrees.size() > 1) {
            detectedTrees.remove(0); // Remover árvore atual
            if (selectBestTree() && planPathToTarget()) {
                return; // Sucesso no replanejamento
            }
        }

        // Falha total - remapear ambiente
        remapEnvironment();
    }

    /**
     * Força remapeamento do ambiente
     */
    private void remapEnvironment() {
        Logger.sendMessage("§7Remapeando ambiente...");
        startPathfindingCycle();
    }

    /**
     * Marca árvore atual como quebrada e procura próxima
     */
    public void markCurrentTreeAsBroken() {
        if (currentTarget != null) {
            Logger.sendMessage("§aÁrvore quebrada! Procurando próxima árvore...");

            // Remover árvore atual da lista
            detectedTrees.removeIf(tree -> tree.basePosition.equals(currentTarget.basePosition));
            currentTarget = null;

            // Procurar próxima árvore
            if (!detectedTrees.isEmpty()) {
                if (selectBestTree() && planPathToTarget()) {
                    return;
                }
            }

            // Escanear por novas árvores
            scanForTrees();
            if (!detectedTrees.isEmpty() && selectBestTree()) {
                planPathToTarget();
            } else {
                Logger.sendMessage("§eNenhuma árvore disponível. Aguardando...");
                currentState = SystemState.IDLE;
            }
        }
    }

    /**
     * Força busca por nova árvore
     */
    public void findNextTree() {
        if (isActive) {
            markCurrentTreeAsBroken();
        }
    }

    // Getters
    public boolean isActive() {
        return isActive;
    }

    public SystemState getCurrentState() {
        return currentState;
    }

    public TreeDetector3D.TreeAnalysis getCurrentTarget() {
        return currentTarget;
    }

    public List<TreeDetector3D.TreeAnalysis> getDetectedTrees() {
        return new ArrayList<>(detectedTrees);
    }

    public PathExecutor getPathExecutor() {
        return pathExecutor;
    }

    // Configurações
    public void setScanRadius(int radius) {
        this.scanRadius = Math.max(5, Math.min(25, radius));
    }

    public void setScanHeight(int height) {
        this.scanHeight = Math.max(5, Math.min(15, height));
    }

    // Callbacks
    public void setOnTreeReached(Runnable callback) {
        this.onTreeReached = callback;
    }

    public void setOnNoTreesFound(Runnable callback) {
        this.onNoTreesFound = callback;
    }

    public void setOnPathfindingFailed(Runnable callback) {
        this.onPathfindingFailed = callback;
    }

    /**
     * Obtém estatísticas do sistema
     */
    public String getSystemStats() {
        if (!isActive)
            return "Sistema inativo";

        StringBuilder stats = new StringBuilder();
        stats.append("Estado: ").append(currentState.displayName).append("\n");

        if (currentGrid != null) {
            stats.append("Grid: ").append(currentGrid.cells.size()).append(" células\n");
            stats.append("Posições caminháveis: ").append(currentGrid.walkablePositions.size()).append("\n");
        }

        stats.append("Árvores detectadas: ").append(detectedTrees.size()).append("\n");

        if (pathExecutor.isExecuting()) {
            stats.append("Progresso: ").append(String.format("%.1f", pathExecutor.getProgressPercentage()))
                    .append("%\n");
            stats.append("Waypoint: ").append(pathExecutor.getCurrentWaypointIndex()).append("/")
                    .append(pathExecutor.getTotalWaypoints());
        }

        return stats.toString();
    }
}
