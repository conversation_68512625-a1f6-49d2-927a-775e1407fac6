package com.rato.addons.features;

import com.rato.addons.util.Logger;
import net.minecraft.client.Minecraft;
import net.minecraftforge.client.event.ClientChatReceivedEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.InputEvent;
import org.lwjgl.input.Keyboard;

/**
 * Sistema de comandos para o Foraging Macro
 */
public class ForagingCommands {
    
    private final Minecraft mc = Minecraft.getMinecraft();
    
    @SubscribeEvent
    public void onKeyInput(InputEvent.KeyInputEvent event) {
        if (!Keyboard.getEventKeyState()) return;

        // F7: Mostrar status do foraging
        if (Keyboard.isKeyDown(Keyboard.KEY_F7)) {
            showForagingStatus();
        }

        // F8: Mudar área de foraging
        if (Keyboard.isKeyDown(Keyboard.KEY_F8)) {
            cycleForagingArea();
        }
    }
    
    @SubscribeEvent
    public void onChatReceived(ClientChatReceivedEvent event) {
        String message = event.message.getUnformattedText().toLowerCase();
        
        // Comandos de chat para foraging
        if (message.startsWith(".foraging ") || message.startsWith(".fg ")) {
            event.setCanceled(true); // Cancelar mensagem para não aparecer no chat
            
            String[] parts = message.split(" ");
            if (parts.length < 2) {
                showForagingHelp();
                return;
            }
            
            String command = parts[1].toLowerCase();
            
            switch (command) {
                case "start":
                    ForagingMacro.getInstance().startForaging();
                    break;
                    
                case "stop":
                    ForagingMacro.getInstance().stopForaging();
                    break;
                    
                case "toggle":
                    ForagingMacro.getInstance().toggleForaging();
                    break;
                    
                case "status":
                    showForagingStatus();
                    break;
                    
                case "area":
                    if (parts.length >= 3) {
                        setForagingArea(parts[2]);
                    } else {
                        showAreaOptions();
                    }
                    break;
                    
                case "stats":
                    showForagingStats();
                    break;

                case "mode":
                    if (parts.length >= 3) {
                        setForagingMode(parts[2]);
                    } else {
                        showModeOptions();
                    }
                    break;

                case "help":
                case "h":
                default:
                    showForagingHelp();
                    break;
            }
        }
    }
    
    /**
     * Mostra o status atual do foraging macro
     */
    private void showForagingStatus() {
        ForagingMacro macro = ForagingMacro.getInstance();
        
        Logger.sendMessage("§6=== FORAGING MACRO STATUS ===");
        Logger.sendMessage("§7Status: " + (macro.isActive() ? "§aAtivo" : "§cInativo"));
        Logger.sendMessage("§7Área: §e" + macro.getCurrentArea().getDisplayName());
        Logger.sendMessage("§7Estado: §e" + macro.getCurrentState().getDisplayName());
        Logger.sendMessage("§7Árvores cortadas: §e" + macro.getTreesChopped());
        
        if (macro.getTargetTree() != null) {
            Logger.sendMessage("§7Alvo atual: §e" + macro.getTargetTree().getX() + ", " + 
                macro.getTargetTree().getY() + ", " + macro.getTargetTree().getZ());
        }
    }
    
    /**
     * Alterna entre as áreas de foraging
     */
    private void cycleForagingArea() {
        if (ForagingMacro.getInstance().isActive()) {
            Logger.sendMessage("§cPare o macro antes de mudar a área!");
            return;
        }
        
        ForagingMacro.ForagingArea[] areas = ForagingMacro.ForagingArea.values();
        ForagingMacro.ForagingArea currentArea = ForagingMacro.getInstance().getCurrentArea();
        
        // Encontrar próxima área
        int currentIndex = 0;
        for (int i = 0; i < areas.length; i++) {
            if (areas[i] == currentArea) {
                currentIndex = i;
                break;
            }
        }
        
        int nextIndex = (currentIndex + 1) % areas.length;
        ForagingMacro.getInstance().setForagingArea(areas[nextIndex]);
    }
    
    /**
     * Define a área de foraging por nome
     */
    private void setForagingArea(String areaName) {
        if (ForagingMacro.getInstance().isActive()) {
            Logger.sendMessage("§cPare o macro antes de mudar a área!");
            return;
        }
        
        ForagingMacro.ForagingArea area = null;
        
        switch (areaName.toLowerCase()) {
            case "darkoak":
            case "dark_oak":
            case "dark":
                area = ForagingMacro.ForagingArea.DARK_OAK;
                break;
            case "birch":
                area = ForagingMacro.ForagingArea.BIRCH;
                break;
            case "spruce":
                area = ForagingMacro.ForagingArea.SPRUCE;
                break;
            case "acacia":
                area = ForagingMacro.ForagingArea.ACACIA;
                break;
            case "jungle":
                area = ForagingMacro.ForagingArea.JUNGLE;
                break;
            default:
                Logger.sendMessage("§cÁrea inválida! Use: darkoak, birch, spruce, acacia, jungle");
                return;
        }
        
        ForagingMacro.getInstance().setForagingArea(area);
    }
    
    /**
     * Mostra as opções de área disponíveis
     */
    private void showAreaOptions() {
        Logger.sendMessage("§6=== ÁREAS DE FORAGING ===");
        for (ForagingMacro.ForagingArea area : ForagingMacro.ForagingArea.values()) {
            String current = (area == ForagingMacro.getInstance().getCurrentArea()) ? " §a(Atual)" : "";
            Logger.sendMessage("§7- §e" + area.getDisplayName() + current);
        }
        Logger.sendMessage("§7Use: §e.foraging area <nome>");
    }
    
    /**
     * Mostra estatísticas detalhadas
     */
    private void showForagingStats() {
        ForagingMacro macro = ForagingMacro.getInstance();
        
        Logger.sendMessage("§6=== ESTATÍSTICAS DE FORAGING ===");
        Logger.sendMessage("§7Árvores cortadas: §e" + macro.getTreesChopped());
        Logger.sendMessage("§7Área atual: §e" + macro.getCurrentArea().getDisplayName());
        
        // Calcular estatísticas de tempo se ativo
        if (macro.isActive()) {
            // TODO: Adicionar cálculos de tempo e taxa
            Logger.sendMessage("§7Status: §aAtivo");
        } else {
            Logger.sendMessage("§7Status: §cInativo");
        }
    }

    /**
     * NOVO: Define o modo de foraging (automático ou marcação)
     */
    private void setForagingMode(String mode) {
        ForagingMacro macro = ForagingMacro.getInstance();

        switch (mode.toLowerCase()) {
            case "auto":
            case "automatic":
                macro.setTreeMode(false);
                Logger.sendMessage("§a✓ Modo automático ativado!");
                break;

            case "mark":
            case "marked":
            case "manual":
                macro.setTreeMode(true);
                Logger.sendMessage("§a✓ Modo de marcação manual ativado!");
                Logger.sendMessage("§7Use /trees mark para marcar árvores");
                break;

            case "toggle":
                macro.toggleTreeMode();
                break;

            default:
                Logger.sendMessage("§cModo inválido! Use: auto, mark, ou toggle");
                showModeOptions();
                break;
        }
    }

    /**
     * NOVO: Mostra opções de modo
     */
    private void showModeOptions() {
        ForagingMacro macro = ForagingMacro.getInstance();
        String currentMode = macro.isUsingMarkedTrees() ? "§eMarcação Manual" : "§eAutomático";

        Logger.sendMessage("§6=== MODOS DE FORAGING ===");
        Logger.sendMessage("§7Modo atual: " + currentMode);
        Logger.sendMessage("");
        Logger.sendMessage("§f.foraging mode auto §7- Busca automática de árvores");
        Logger.sendMessage("§f.foraging mode mark §7- Usar árvores marcadas manualmente");
        Logger.sendMessage("§f.foraging mode toggle §7- Alternar entre modos");
        Logger.sendMessage("");
        Logger.sendMessage("§7Para marcar árvores: §f/trees mark");
        Logger.sendMessage("§7Para listar árvores: §f/trees list");
    }

    /**
     * Mostra ajuda dos comandos de foraging
     */
    private void showForagingHelp() {
        Logger.sendMessage("§6=== COMANDOS DE FORAGING ===");
        Logger.sendMessage("§7.foraging start §f- Iniciar macro");
        Logger.sendMessage("§7.foraging stop §f- Parar macro");
        Logger.sendMessage("§7.foraging toggle §f- Alternar macro");
        Logger.sendMessage("§7.foraging status §f- Ver status");
        Logger.sendMessage("§7.foraging area <nome> §f- Mudar área");
        Logger.sendMessage("§7.foraging mode <tipo> §f- Mudar modo (auto/mark)");
        Logger.sendMessage("§7.foraging stats §f- Ver estatísticas");
        Logger.sendMessage("§7.foraging help §f- Mostrar ajuda");
        Logger.sendMessage("");
        Logger.sendMessage("§6=== TECLAS DE ATALHO ===");
        Logger.sendMessage("§7F7 §f- Status");
        Logger.sendMessage("§7F8 §f- Mudar área");
        Logger.sendMessage("");
        Logger.sendMessage("§6=== CONTROLE NO MENU ===");
        Logger.sendMessage("§7Use os botões no OneConfig > Foraging > Control");
        Logger.sendMessage("");
        Logger.sendMessage("§6=== ÁREAS DISPONÍVEIS ===");
        Logger.sendMessage("§7darkoak, birch, spruce, acacia, jungle");
        Logger.sendMessage("");
        Logger.sendMessage("§6=== SISTEMA DE MARCAÇÃO ===");
        Logger.sendMessage("§7/trees mark §f- Marcar árvore (mire na madeira)");
        Logger.sendMessage("§7/trees list §f- Listar árvores marcadas");
        Logger.sendMessage("§7/trees clear §f- Limpar árvores marcadas");
        Logger.sendMessage("§7/trees region <nome> §f- Mudar região");
    }
}
