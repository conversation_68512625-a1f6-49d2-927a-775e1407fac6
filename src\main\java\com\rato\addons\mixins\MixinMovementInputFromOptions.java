package com.rato.addons.mixins;

import com.rato.addons.imgui.RatoImGuiRenderer;
import net.minecraft.util.MovementInputFromOptions;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

/**
 * Mixin para bloquear movimento quando o menu está aberto
 */
@Mixin(MovementInputFromOptions.class)
public class MixinMovementInputFromOptions {

    @Inject(method = "updatePlayerMoveState", at = @At("HEAD"), cancellable = true)
    private void blockMovementWhenMenuOpen(CallbackInfo ci) {
        // Se o menu estiver visível, bloquear todos os movimentos
        if (RatoImGuiRenderer.getInstance() != null && RatoImGuiRenderer.getInstance().isVisible()) {
            MovementInputFromOptions input = (MovementInputFromOptions) (Object) this;
            input.moveStrafe = 0.0F;
            input.moveForward = 0.0F;
            input.jump = false;
            input.sneak = false;
            ci.cancel();
        }
    }
}
