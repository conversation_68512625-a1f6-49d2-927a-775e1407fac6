package com.rato.addons.commands;

import com.rato.addons.config.RatoAddonsConfigSimple;
import com.rato.addons.pathfinding.movement.BaritoneMovementExecutor;
import com.rato.addons.util.Logger;
import net.minecraft.client.Minecraft;
import net.minecraft.command.CommandBase;
import net.minecraft.command.CommandException;
import net.minecraft.command.ICommandSender;
import net.minecraft.entity.player.EntityPlayer;

/**
 * Comando para debug do sistema de pathfinding e câmera
 */
public class PathfindingDebugCommand extends CommandBase {
    
    private final Minecraft mc = Minecraft.getMinecraft();

    @Override
    public String getCommandName() {
        return "pathdebug";
    }

    @Override
    public String getCommandUsage(ICommandSender sender) {
        return "/pathdebug [speed|camera|config] - Mostra informações de debug do pathfinding";
    }

    @Override
    public void processCommand(ICommandSender sender, String[] args) throws CommandException {
        if (!(sender instanceof EntityPlayer)) {
            Logger.sendMessage("§cEste comando só pode ser usado por jogadores!");
            return;
        }
        
        if (args.length == 0) {
            showGeneralDebug();
            return;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "speed":
                showSpeedDebug();
                break;
            case "camera":
                showCameraDebug();
                break;
            case "config":
                showConfigDebug();
                break;
            default:
                Logger.sendMessage("§cUso: /pathdebug [speed|camera|config]");
                break;
        }
    }
    
    private void showGeneralDebug() {
        Logger.sendMessage("§7§l=== Pathfinding Debug ===");
        
        // Informações do jogador
        if (mc.thePlayer != null) {
            double motionX = mc.thePlayer.motionX;
            double motionZ = mc.thePlayer.motionZ;
            double speed = Math.sqrt(motionX * motionX + motionZ * motionZ) * 20.0; // blocos/segundo
            
            Logger.sendMessage("§7Velocidade atual: §e" + String.format("%.2f", speed) + " §7blocos/segundo");
            Logger.sendMessage("§7Posição: §e" + String.format("%.1f, %.1f, %.1f", 
                    mc.thePlayer.posX, mc.thePlayer.posY, mc.thePlayer.posZ));
            Logger.sendMessage("§7Rotação: §e" + String.format("%.1f°, %.1f°", 
                    mc.thePlayer.rotationYaw, mc.thePlayer.rotationPitch));
        }
        
        // Status do pathfinding
        com.rato.addons.pathfinding.movement.MovementManager manager =
                com.rato.addons.pathfinding.movement.MovementManager.getInstance();
        if (manager != null) {
            boolean isActive = manager.isExecuting();
            Logger.sendMessage("§7Pathfinding ativo: " + (isActive ? "§aAtivo" : "§cInativo"));
            if (isActive && manager.getExecutor() != null) {
                Logger.sendMessage(manager.getExecutor().getCameraDebugInfo());
            }
        }
    }
    
    private void showSpeedDebug() {
        Logger.sendMessage("§7§l=== Speed Detection Debug ===");
        
        if (mc.thePlayer != null) {
            double motionX = mc.thePlayer.motionX;
            double motionZ = mc.thePlayer.motionZ;
            double speed = Math.sqrt(motionX * motionX + motionZ * motionZ) * 20.0;
            
            String speedLevel;
            if (speed > 2.0) {
                speedLevel = "§cVery High (Speed II+)";
            } else if (speed > 1.0) {
                speedLevel = "§6High (Speed I)";
            } else if (speed > 0.3) {
                speedLevel = "§aNormal";
            } else {
                speedLevel = "§7Low/Stopped";
            }
            
            Logger.sendMessage("§7Velocidade detectada: " + speedLevel);
            Logger.sendMessage("§7Valor exato: §e" + String.format("%.4f", speed) + " §7blocos/segundo");
            Logger.sendMessage("§7Motion X: §e" + String.format("%.4f", motionX));
            Logger.sendMessage("§7Motion Z: §e" + String.format("%.4f", motionZ));
            
            // Efeitos de poção
            try {
                if (mc.thePlayer.isPotionActive(net.minecraft.potion.Potion.moveSpeed)) {
                    int amplifier = mc.thePlayer.getActivePotionEffect(net.minecraft.potion.Potion.moveSpeed).getAmplifier();
                    Logger.sendMessage("§7Efeito Speed: §eNível " + (amplifier + 1));
                } else {
                    Logger.sendMessage("§7Efeito Speed: §cNenhum");
                }
            } catch (Exception e) {
                Logger.sendMessage("§7Efeito Speed: §cErro ao verificar");
            }
        }
    }
    
    private void showCameraDebug() {
        Logger.sendMessage("§7§l=== Camera System Debug ===");
        
        com.rato.addons.pathfinding.movement.MovementManager manager =
                com.rato.addons.pathfinding.movement.MovementManager.getInstance();
        if (manager != null && manager.isExecuting() && manager.getExecutor() != null) {
            Logger.sendMessage(manager.getExecutor().getCameraDebugInfo());
        } else {
            Logger.sendMessage("§cPathfinding não está ativo");
        }
        
        Logger.sendMessage("§7Auto Speed Detection: " + 
                (RatoAddonsConfigSimple.pathfindingAutoSpeedDetection ? "§aEnabled" : "§cDisabled"));
    }
    
    private void showConfigDebug() {
        Logger.sendMessage("§7§l=== Pathfinding Config Debug ===");
        Logger.sendMessage("§7Rotation Speed: §e" + RatoAddonsConfigSimple.pathfindingRotationSpeed);
        Logger.sendMessage("§7Smoothness: §e" + RatoAddonsConfigSimple.pathfindingSmoothness);
        Logger.sendMessage("§7High Speed Smoothness: §e" + RatoAddonsConfigSimple.pathfindingHighSpeedSmoothness);
        Logger.sendMessage("§7Auto Speed Detection: " + 
                (RatoAddonsConfigSimple.pathfindingAutoSpeedDetection ? "§aEnabled" : "§cDisabled"));
        Logger.sendMessage("§7Human Movement: " + 
                (RatoAddonsConfigSimple.pathfindingHumanMovement ? "§aEnabled" : "§cDisabled"));
        Logger.sendMessage("§7Natural Movement: " + 
                (RatoAddonsConfigSimple.pathfindingNaturalMovement ? "§aEnabled" : "§cDisabled"));
    }

    @Override
    public boolean canCommandSenderUseCommand(ICommandSender sender) {
        return true;
    }
}
