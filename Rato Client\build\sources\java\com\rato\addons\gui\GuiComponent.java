package com.rato.addons.gui;

import com.rato.addons.config.CustomConfigManager;
import net.minecraft.client.gui.Gui;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.FontRenderer;

/**
 * Classe base para componentes da GUI customizada
 */
public abstract class GuiComponent extends Gui {
    
    protected static final int COMPONENT_HEIGHT = 25;
    protected static final int TOGGLE_SIZE = 20;
    protected static final int SLIDER_HEIGHT = 20;
    protected static final int COLOR_SIZE = 20;
    
    // Cores
    protected static final int BACKGROUND_COLOR = 0x80202020;
    protected static final int ENABLED_COLOR = 0xFF00FF88;
    protected static final int DISABLED_COLOR = 0xFF404040;
    protected static final int HOVER_COLOR = 0x4000FF88;
    protected static final int TEXT_COLOR = 0xFFFFFFFF;
    protected static final int SLIDER_TRACK_COLOR = 0xFF404040;
    protected static final int SLIDER_THUMB_COLOR = 0xFF00FF88;
    
    protected String name;
    protected String category;
    protected String configKey;
    
    public GuiComponent(String name, String category, String configKey) {
        this.name = name;
        this.category = category;
        this.configKey = configKey;
    }
    
    public abstract void draw(int x, int y, int width, int mouseX, int mouseY);
    public abstract boolean mouseClicked(int x, int y, int mouseX, int mouseY, int mouseButton);
    public abstract int getHeight();
    
    protected FontRenderer getFontRenderer() {
        return Minecraft.getMinecraft().fontRendererObj;
    }
    
    protected boolean isMouseOver(int mouseX, int mouseY, int x, int y, int width, int height) {
        return mouseX >= x && mouseX <= x + width && mouseY >= y && mouseY <= y + height;
    }
    
    /**
     * Componente Toggle (Switch)
     */
    public static class ToggleComponent extends GuiComponent {
        
        public ToggleComponent(String name, String category, String configKey) {
            super(name, category, configKey);
        }
        
        @Override
        public void draw(int x, int y, int width, int mouseX, int mouseY) {
            FontRenderer fr = getFontRenderer();
            
            // Background
            boolean isHovered = isMouseOver(mouseX, mouseY, x, y, width, getHeight());
            if (isHovered) {
                drawRect(x, y, x + width, y + getHeight(), HOVER_COLOR);
            }
            
            // Text
            drawString(fr, name, x + 5, y + 8, TEXT_COLOR);
            
            // Toggle switch
            boolean enabled = CustomConfigManager.getBoolean(category, configKey);
            int toggleX = x + width - TOGGLE_SIZE - 5;
            int toggleY = y + (getHeight() - TOGGLE_SIZE) / 2;
            
            int toggleColor = enabled ? ENABLED_COLOR : DISABLED_COLOR;
            drawRect(toggleX, toggleY, toggleX + TOGGLE_SIZE, toggleY + TOGGLE_SIZE, toggleColor);
            
            // Toggle indicator
            if (enabled) {
                drawRect(toggleX + 2, toggleY + 2, toggleX + TOGGLE_SIZE - 2, toggleY + TOGGLE_SIZE - 2, 0xFF000000);
            }
        }
        
        @Override
        public boolean mouseClicked(int x, int y, int mouseX, int mouseY, int mouseButton) {
            if (isMouseOver(mouseX, mouseY, x, y, x + 200, getHeight())) { // Approximate width
                boolean currentValue = CustomConfigManager.getBoolean(category, configKey);
                CustomConfigManager.setBoolean(category, configKey, !currentValue);
                return true;
            }
            return false;
        }
        
        @Override
        public int getHeight() {
            return COMPONENT_HEIGHT;
        }
    }
    
    /**
     * Componente Slider
     */
    public static class SliderComponent extends GuiComponent {
        private float minValue;
        private float maxValue;
        private boolean isDragging = false;
        
        public SliderComponent(String name, String category, String configKey, float minValue, float maxValue) {
            super(name, category, configKey);
            this.minValue = minValue;
            this.maxValue = maxValue;
        }
        
        public SliderComponent(String name, String category, String configKey, int minValue, int maxValue) {
            this(name, category, configKey, (float) minValue, (float) maxValue);
        }
        
        @Override
        public void draw(int x, int y, int width, int mouseX, int mouseY) {
            FontRenderer fr = getFontRenderer();
            
            // Background
            boolean isHovered = isMouseOver(mouseX, mouseY, x, y, width, getHeight());
            if (isHovered) {
                drawRect(x, y, x + width, y + getHeight(), HOVER_COLOR);
            }
            
            // Text and value
            float currentValue = CustomConfigManager.getFloat(category, configKey);
            String valueText = String.format("%.2f", currentValue);
            if (maxValue <= 100 && minValue >= 0 && (maxValue - minValue) <= 100) {
                // Likely an integer slider
                valueText = String.valueOf((int) currentValue);
            }
            
            String displayText = name + ": " + valueText;
            drawString(fr, displayText, x + 5, y + 2, TEXT_COLOR);
            
            // Slider track
            int sliderY = y + 15;
            int sliderWidth = width - 10;
            drawRect(x + 5, sliderY, x + 5 + sliderWidth, sliderY + 5, SLIDER_TRACK_COLOR);
            
            // Slider thumb
            float percentage = (currentValue - minValue) / (maxValue - minValue);
            int thumbX = (int) (x + 5 + percentage * sliderWidth - 3);
            drawRect(thumbX, sliderY - 2, thumbX + 6, sliderY + 7, SLIDER_THUMB_COLOR);
        }
        
        @Override
        public boolean mouseClicked(int x, int y, int mouseX, int mouseY, int mouseButton) {
            int sliderY = y + 15;
            int sliderWidth = x + (200 - 10); // Approximate width
            
            if (isMouseOver(mouseX, mouseY, x + 5, sliderY - 2, sliderWidth, 9)) {
                isDragging = true;
                updateValue(mouseX, x + 5, sliderWidth);
                return true;
            }
            return false;
        }
        
        public void mouseDragged(int mouseX, int x, int sliderWidth) {
            if (isDragging) {
                updateValue(mouseX, x + 5, sliderWidth);
            }
        }
        
        public void mouseReleased() {
            isDragging = false;
        }
        
        private void updateValue(int mouseX, int sliderX, int sliderWidth) {
            float percentage = Math.max(0, Math.min(1, (float) (mouseX - sliderX) / sliderWidth));
            float newValue = minValue + percentage * (maxValue - minValue);
            
            // Round to reasonable precision
            if (maxValue <= 100 && minValue >= 0 && (maxValue - minValue) <= 100) {
                newValue = Math.round(newValue);
                CustomConfigManager.setInt(category, configKey, (int) newValue);
            } else {
                newValue = Math.round(newValue * 100f) / 100f;
                CustomConfigManager.setFloat(category, configKey, newValue);
            }
        }
        
        @Override
        public int getHeight() {
            return COMPONENT_HEIGHT;
        }
    }
    
    /**
     * Componente Color Picker (simplificado)
     */
    public static class ColorComponent extends GuiComponent {
        
        public ColorComponent(String name, String category, String configKey) {
            super(name, category, configKey);
        }
        
        @Override
        public void draw(int x, int y, int width, int mouseX, int mouseY) {
            FontRenderer fr = getFontRenderer();
            
            // Background
            boolean isHovered = isMouseOver(mouseX, mouseY, x, y, width, getHeight());
            if (isHovered) {
                drawRect(x, y, x + width, y + getHeight(), HOVER_COLOR);
            }
            
            // Text
            drawString(fr, name, x + 5, y + 8, TEXT_COLOR);
            
            // Color preview
            int color = CustomConfigManager.getInt(category, configKey);
            int colorX = x + width - COLOR_SIZE - 5;
            int colorY = y + (getHeight() - COLOR_SIZE) / 2;
            
            // Color border
            drawRect(colorX - 1, colorY - 1, colorX + COLOR_SIZE + 1, colorY + COLOR_SIZE + 1, 0xFFFFFFFF);
            // Color fill
            drawRect(colorX, colorY, colorX + COLOR_SIZE, colorY + COLOR_SIZE, color | 0xFF000000);
        }
        
        @Override
        public boolean mouseClicked(int x, int y, int mouseX, int mouseY, int mouseButton) {
            if (isMouseOver(mouseX, mouseY, x, y, x + 200, getHeight())) { // Approximate width
                // Simple color cycling for now - could be expanded to full color picker
                int currentColor = CustomConfigManager.getInt(category, configKey);
                int[] presetColors = {
                    0xFF00FF00, // Green
                    0xFFFF0000, // Red
                    0xFF0000FF, // Blue
                    0xFFFFFF00, // Yellow
                    0xFFFF00FF, // Magenta
                    0xFF00FFFF, // Cyan
                    0xFFFFFFFF, // White
                    0xFF808080  // Gray
                };
                
                // Find current color and cycle to next
                int currentIndex = 0;
                for (int i = 0; i < presetColors.length; i++) {
                    if ((currentColor & 0x00FFFFFF) == (presetColors[i] & 0x00FFFFFF)) {
                        currentIndex = i;
                        break;
                    }
                }
                
                int nextIndex = (currentIndex + 1) % presetColors.length;
                int alpha = currentColor & 0xFF000000;
                CustomConfigManager.setInt(category, configKey, presetColors[nextIndex] | alpha);
                return true;
            }
            return false;
        }
        
        @Override
        public int getHeight() {
            return COMPONENT_HEIGHT;
        }
    }
}
