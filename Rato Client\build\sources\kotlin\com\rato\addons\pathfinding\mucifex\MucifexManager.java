package com.rato.addons.pathfinding.mucifex;

import com.rato.addons.pathfinding.professional.MovementController;
import com.rato.addons.pathfinding.professional.PathNode;
import com.rato.addons.pathfinding.professional.MucifexPathRenderer;
import com.rato.addons.util.Logger;
import net.minecraft.client.Minecraft;
import net.minecraft.util.Vec3;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Gerenciador do sistema de pathfinding Mucifex
 * Sistema otimizado com execução assíncrona e detecção inteligente
 */
public class MucifexManager {
    
    private static MucifexManager instance;
    private final Minecraft mc = Minecraft.getMinecraft();
    
    // Componentes do sistema
    private final MucifexPathfinder pathfinder;
    private final MovementController movementController;
    private final MucifexPathRenderer pathRenderer;
    private final ExecutorService pathfindingExecutor;
    
    // Estado do sistema
    private boolean isActive = false;
    private boolean isCalculating = false;
    private boolean debugMode = false;
    private boolean renderingEnabled = true;
    
    // Configurações
    private float movementSpeed = 1.0f;
    private float rotationSmoothness = 0.9f;
    
    // Estatísticas
    private long pathfindingStartTime;
    private int pathCalculations = 0;
    private Vec3 currentDestination;
    private List<PathNode> currentPath;
    
    // Controle de recálculo
    private long lastRecalculation = 0;
    private static final long RECALCULATION_COOLDOWN = 3000; // 3 segundos
    
    private MucifexManager() {
        pathfinder = new MucifexPathfinder();
        movementController = new MovementController();
        pathRenderer = new MucifexPathRenderer();
        pathfindingExecutor = Executors.newSingleThreadExecutor(r -> {
            Thread t = new Thread(r, "Mucifex-Pathfinding");
            t.setDaemon(true);
            return t;
        });
        
        setupCallbacks();
        MinecraftForge.EVENT_BUS.register(this);
    }
    
    public static MucifexManager getInstance() {
        if (instance == null) {
            instance = new MucifexManager();
        }
        return instance;
    }
    
    /**
     * Configura callbacks do sistema
     */
    private void setupCallbacks() {
        movementController.setOnDestinationReached(() -> {
            Logger.sendMessage("§a[Mucifex] Destino alcançado!");
            stopPathfinding();
        });
        
        movementController.setOnMovementFailed(() -> {
            if (debugMode) {
                Logger.sendMessage("§e[Mucifex] Falha no movimento, recalculando...");
            }
            scheduleRecalculation();
        });
    }
    
    /**
     * Inicia pathfinding para destino específico
     */
    public boolean startPathfinding(Vec3 destination) {
        if (mc.thePlayer == null || mc.theWorld == null) {
            return false;
        }
        
        if (isActive) {
            stopPathfinding();
        }
        
        currentDestination = destination;
        pathfindingStartTime = System.currentTimeMillis();
        pathCalculations = 0;
        
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        double distance = playerPos.distanceTo(destination);
        
        if (debugMode) {
            Logger.sendMessage("§6[Mucifex] Iniciando pathfinding otimizado...");
            Logger.sendMessage("§7De: " + formatVec3(playerPos));
            Logger.sendMessage("§7Para: " + formatVec3(destination));
            Logger.sendMessage("§7Distância: " + String.format("%.1f", distance) + " blocos");
        }
        
        // Configurar movimento
        movementController.setMovementSpeed(movementSpeed);
        movementController.setRotationSmoothness(rotationSmoothness);
        
        // Iniciar cálculo assíncrono
        calculatePathAsync(playerPos, destination);
        
        isActive = true;
        Logger.sendMessage("§a[Mucifex] Sistema iniciado! Calculando caminho...");
        
        return true;
    }
    
    /**
     * Calcula caminho de forma assíncrona
     */
    private void calculatePathAsync(Vec3 start, Vec3 goal) {
        if (isCalculating) return;
        
        isCalculating = true;
        pathCalculations++;
        
        CompletableFuture.supplyAsync(() -> {
            try {
                if (debugMode) {
                    Logger.sendMessage("§7[Mucifex] Calculando caminho... (" + pathCalculations + ")");
                }
                
                long startTime = System.currentTimeMillis();
                List<PathNode> path = pathfinder.findPath(start, goal);
                long calculationTime = System.currentTimeMillis() - startTime;
                
                if (debugMode && path != null) {
                    Logger.sendMessage("§a[Mucifex] Caminho calculado em " + calculationTime + "ms: " + 
                        path.size() + " nós");
                }
                
                return path;
                
            } catch (Exception e) {
                Logger.sendMessage("§c[Mucifex] Erro no cálculo: " + e.getMessage());
                return null;
            }
        }, pathfindingExecutor).thenAccept(this::onPathCalculated);
    }
    
    /**
     * Callback quando caminho é calculado
     */
    private void onPathCalculated(List<PathNode> path) {
        isCalculating = false;
        
        if (!isActive) return;
        
        if (path != null && !path.isEmpty()) {
            currentPath = path;
            
            // Configurar movimento
            movementController.setPath(path);
            
            // Configurar renderização
            if (renderingEnabled) {
                pathRenderer.setPath(path);
                pathRenderer.setEnabled(true);
            }
            
            if (debugMode) {
                Logger.sendMessage("§a[Mucifex] Iniciando execução do caminho!");
            }
            
        } else {
            Logger.sendMessage("§c[Mucifex] Não foi possível encontrar um caminho!");
            stopPathfinding();
        }
    }
    
    /**
     * Agenda recálculo do caminho
     */
    private void scheduleRecalculation() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastRecalculation < RECALCULATION_COOLDOWN) {
            return; // Cooldown ativo
        }
        
        lastRecalculation = currentTime;
        
        if (isActive && currentDestination != null && !isCalculating) {
            Vec3 currentPos = mc.thePlayer.getPositionVector();
            
            if (debugMode) {
                Logger.sendMessage("§7[Mucifex] Recalculando caminho...");
            }
            
            calculatePathAsync(currentPos, currentDestination);
        }
    }
    
    /**
     * Para o pathfinding atual
     */
    public void stopPathfinding() {
        isActive = false;
        isCalculating = false;
        
        movementController.stop();
        
        if (pathRenderer != null) {
            pathRenderer.setEnabled(false);
        }
        
        currentPath = null;
        currentDestination = null;
        
        if (debugMode) {
            Logger.sendMessage("§7[Mucifex] Sistema parado");
        }
    }
    
    /**
     * Atualização principal (chamada a cada tick)
     */
    @SubscribeEvent
    public void onTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.START || !isActive) {
            return;
        }
        
        if (mc.thePlayer == null || mc.theWorld == null) {
            stopPathfinding();
            return;
        }
        
        // Atualizar movimento
        movementController.update();
        
        // Verificar se precisa recalcular periodicamente
        if (shouldPeriodicRecalculation()) {
            scheduleRecalculation();
        }
    }
    
    /**
     * Verifica se deve fazer recálculo periódico
     */
    private boolean shouldPeriodicRecalculation() {
        if (currentDestination == null || isCalculating) return false;
        
        long timeSinceStart = System.currentTimeMillis() - pathfindingStartTime;
        long timeSinceLastRecalc = System.currentTimeMillis() - lastRecalculation;
        
        // Recalcular a cada 10 segundos se estiver ativo há mais de 15 segundos
        return timeSinceStart > 15000 && timeSinceLastRecalc > 10000;
    }
    
    // Getters e Setters
    public boolean isActive() { return isActive; }
    public boolean isCalculating() { return isCalculating; }
    public void setDebugMode(boolean debug) { this.debugMode = debug; }
    public boolean isDebugMode() { return debugMode; }
    
    public void setRenderingEnabled(boolean enabled) { 
        this.renderingEnabled = enabled;
        if (pathRenderer != null) {
            pathRenderer.setEnabled(enabled && isActive);
        }
    }
    public boolean isRenderingEnabled() { return renderingEnabled; }
    
    public void setMovementSpeed(float speed) { this.movementSpeed = speed; }
    public float getMovementSpeed() { return movementSpeed; }
    
    public void setRotationSmoothness(float smoothness) { this.rotationSmoothness = smoothness; }
    public float getRotationSmoothness() { return rotationSmoothness; }
    
    /**
     * Retorna status detalhado do sistema
     */
    public String getDetailedStatus() {
        if (!isActive) return "Inativo";
        
        long elapsed = System.currentTimeMillis() - pathfindingStartTime;
        StringBuilder status = new StringBuilder();
        
        status.append("Ativo há ").append(elapsed / 1000).append("s");
        status.append(", Cálculos: ").append(pathCalculations);
        
        if (currentPath != null) {
            status.append(", Nós: ").append(currentPath.size());
            float progress = movementController.getProgress();
            status.append(" (").append(String.format("%.1f", progress * 100)).append("%)");
        }
        
        if (isCalculating) {
            status.append(", Calculando");
        }
        
        return status.toString();
    }
    
    /**
     * Retorna informações de debug
     */
    public String getDebugInfo() {
        StringBuilder info = new StringBuilder();
        info.append("§7Status: ").append(isActive ? "§aAtivo" : "§cInativo").append("\n");
        info.append("§7Destino: ").append(currentDestination != null ? formatVec3(currentDestination) : "§cNenhum").append("\n");
        info.append("§7Cálculos realizados: §f").append(pathCalculations).append("\n");
        info.append("§7Nós no caminho: §f").append(currentPath != null ? currentPath.size() : 0).append("\n");
        info.append("§7Calculando: ").append(isCalculating ? "§aSim" : "§cNão").append("\n");
        info.append("§7Velocidade: §f").append(movementSpeed).append("\n");
        info.append("§7Suavidade: §f").append(rotationSmoothness).append("\n");
        info.append("§7Renderização: ").append(renderingEnabled ? "§aAtiva" : "§cInativa");
        
        if (isActive) {
            long elapsed = System.currentTimeMillis() - pathfindingStartTime;
            info.append("\n§7Tempo ativo: §f").append(elapsed / 1000).append("s");
            
            long timeSinceRecalc = System.currentTimeMillis() - lastRecalculation;
            info.append("\n§7Último recálculo: §f").append(timeSinceRecalc / 1000).append("s atrás");
        }
        
        return info.toString();
    }
    
    /**
     * Obtém estatísticas de performance
     */
    public String getPerformanceStats() {
        if (!isActive) return "Sistema inativo";
        
        long elapsed = Math.max(1, System.currentTimeMillis() - pathfindingStartTime);
        double calculationsPerSecond = pathCalculations / (elapsed / 1000.0);
        
        StringBuilder stats = new StringBuilder();
        stats.append("Cálculos/s: ").append(String.format("%.2f", calculationsPerSecond));
        
        if (currentPath != null) {
            double nodesPerSecond = currentPath.size() / (elapsed / 1000.0);
            stats.append(", Nós/s: ").append(String.format("%.2f", nodesPerSecond));
        }
        
        return stats.toString();
    }
    
    /**
     * Força recálculo imediato
     */
    public void forceRecalculation() {
        if (isActive && currentDestination != null) {
            lastRecalculation = 0; // Reset cooldown
            scheduleRecalculation();
        }
    }
    
    /**
     * Limpa recursos
     */
    public void shutdown() {
        stopPathfinding();
        pathfindingExecutor.shutdown();
    }
    
    private String formatVec3(Vec3 vec) {
        return String.format("%.1f, %.1f, %.1f", vec.xCoord, vec.yCoord, vec.zCoord);
    }
}
