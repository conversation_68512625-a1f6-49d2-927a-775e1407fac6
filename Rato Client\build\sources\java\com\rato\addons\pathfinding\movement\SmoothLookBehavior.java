package com.rato.addons.pathfinding.movement;

import com.rato.addons.pathfinding.BaritoneStylePathfinder;
import net.minecraft.client.Minecraft;
import net.minecraft.util.MathHelper;
import net.minecraft.util.Vec3;

import java.util.List;
import java.util.Random;

/**
 * Sistema de rotação suave baseado no Baritone LookBehavior
 * Simula movimento de mouse real e usa lookahead para suavidade
 */
public class SmoothLookBehavior {
    
    private final Minecraft mc = Minecraft.getMinecraft();
    private final Random random = new Random();
    
    // Configurações baseadas no Baritone
    private static final double LOOKAHEAD_DISTANCE = 3.0; // Olhar 3 blocos à frente
    private static final float RANDOM_OFFSET_RANGE = 0.5f; // Pequena variação aleatória
    private static final double MIN_ANGLE_CHANGE = 0.1; // Mudança mínima para aplicar
    
    // Estado atual
    private float currentYaw;
    private float currentPitch;
    private boolean initialized = false;
    
    // Offsets aleatórios para movimento natural
    private float randomYawOffset = 0.0f;
    private float randomPitchOffset = 0.0f;
    private int randomUpdateCounter = 0;
    
    /**
     * Inicializa o sistema com rotação atual do player
     */
    public void initialize() {
        if (mc.thePlayer != null) {
            currentYaw = mc.thePlayer.rotationYaw;
            currentPitch = mc.thePlayer.rotationPitch;
            initialized = true;
        }
    }
    
    /**
     * Atualiza rotação usando lookahead e movimento de mouse simulado
     */
    public void updateRotation(List<BaritoneStylePathfinder.BaritoneNode> path, int currentNodeIndex) {
        if (!initialized || mc.thePlayer == null || path == null || currentNodeIndex >= path.size()) {
            return;
        }
        
        // Atualizar offsets aleatórios periodicamente
        updateRandomOffsets();
        
        // Calcular ponto de lookahead
        Vec3 lookTarget = calculateLookaheadTarget(path, currentNodeIndex);
        if (lookTarget == null) return;
        
        // Calcular rotação desejada
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        float[] targetRotation = calculateRotationToTarget(playerPos, lookTarget);
        
        // Aplicar offsets aleatórios para movimento natural
        targetRotation[0] += randomYawOffset;
        targetRotation[1] += randomPitchOffset;
        
        // Aplicar movimento de mouse simulado
        float newYaw = calculateMouseMove(currentYaw, targetRotation[0]);
        float newPitch = calculateMouseMove(currentPitch, targetRotation[1]);
        
        // Limitar pitch
        newPitch = MathHelper.clamp_float(newPitch, -90.0f, 90.0f);
        
        // Aplicar apenas se mudança for significativa
        if (Math.abs(newYaw - currentYaw) > MIN_ANGLE_CHANGE || 
            Math.abs(newPitch - currentPitch) > MIN_ANGLE_CHANGE) {
            
            currentYaw = newYaw;
            currentPitch = newPitch;
            
            // Aplicar ao player
            mc.thePlayer.rotationYaw = currentYaw;
            mc.thePlayer.rotationPitch = currentPitch;
        }
    }
    
    /**
     * Calcula ponto de lookahead no caminho
     */
    private Vec3 calculateLookaheadTarget(List<BaritoneStylePathfinder.BaritoneNode> path, int currentNodeIndex) {
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        
        // Procurar node à distância de lookahead
        for (int i = currentNodeIndex; i < path.size(); i++) {
            BaritoneStylePathfinder.BaritoneNode node = path.get(i);
            Vec3 nodePos = new Vec3(node.position.getX() + 0.5, node.position.getY() + 0.5, node.position.getZ() + 0.5);
            
            double distance = playerPos.distanceTo(nodePos);
            
            // Se encontrou node na distância de lookahead, usar ele
            if (distance >= LOOKAHEAD_DISTANCE) {
                return nodePos;
            }
        }
        
        // Se não encontrou, usar último node do caminho
        if (!path.isEmpty()) {
            BaritoneStylePathfinder.BaritoneNode lastNode = path.get(path.size() - 1);
            return new Vec3(lastNode.position.getX() + 0.5, lastNode.position.getY() + 0.5, lastNode.position.getZ() + 0.5);
        }
        
        return null;
    }
    
    /**
     * Calcula rotação para um target
     */
    private float[] calculateRotationToTarget(Vec3 from, Vec3 to) {
        double deltaX = to.xCoord - from.xCoord;
        double deltaY = to.yCoord - (from.yCoord + mc.thePlayer.getEyeHeight());
        double deltaZ = to.zCoord - from.zCoord;
        
        double horizontalDistance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
        
        float yaw = (float) (Math.atan2(deltaZ, deltaX) * 180.0 / Math.PI) - 90.0f;
        float pitch = (float) -(Math.atan2(deltaY, horizontalDistance) * 180.0 / Math.PI);
        
        // Normalizar yaw
        while (yaw > 180.0f) yaw -= 360.0f;
        while (yaw < -180.0f) yaw += 360.0f;
        
        return new float[]{yaw, pitch};
    }
    
    /**
     * Simula movimento de mouse real (baseado no Baritone)
     */
    private float calculateMouseMove(float current, float target) {
        float delta = MathHelper.wrapAngleTo180_float(target - current);
        
        // Converter ângulo para movimento de mouse
        double deltaPx = angleToMouse(delta);
        
        // Converter movimento de mouse de volta para ângulo
        return current + mouseToAngle(deltaPx);
    }
    
    /**
     * Converte ângulo para movimento de mouse (baseado no Baritone)
     */
    private double angleToMouse(float angleDelta) {
        float minAngleChange = mouseToAngle(1.0);
        return Math.round(angleDelta / minAngleChange);
    }
    
    /**
     * Converte movimento de mouse para ângulo (baseado no Baritone)
     */
    private float mouseToAngle(double mouseDelta) {
        // Usar sensibilidade do mouse do player
        float sensitivity = mc.gameSettings.mouseSensitivity;
        double f = sensitivity * 0.6 + 0.2;
        return (float) (mouseDelta * f * f * f * 8.0) * 0.15f;
    }
    
    /**
     * Atualiza offsets aleatórios para movimento natural
     */
    private void updateRandomOffsets() {
        randomUpdateCounter++;
        
        // Atualizar a cada 10 ticks (0.5 segundos)
        if (randomUpdateCounter >= 10) {
            randomUpdateCounter = 0;
            
            // Pequenos offsets aleatórios para movimento natural
            randomYawOffset = (float) ((random.nextDouble() - 0.5) * RANDOM_OFFSET_RANGE);
            randomPitchOffset = (float) ((random.nextDouble() - 0.5) * RANDOM_OFFSET_RANGE * 0.5); // Pitch menos variável
            
            // Adicionar variação extra ocasionalmente
            double extraRandom = random.nextDouble() - 0.5;
            if (Math.abs(extraRandom) < 0.1) {
                extraRandom *= 2; // Amplificar pequenas variações
            }
            randomYawOffset += extraRandom * 0.2f;
        }
    }
    
    /**
     * Força rotação para um target específico (para situações especiais)
     */
    public void forceRotationToTarget(Vec3 target) {
        if (!initialized || mc.thePlayer == null) return;
        
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        float[] targetRotation = calculateRotationToTarget(playerPos, target);
        
        currentYaw = targetRotation[0];
        currentPitch = targetRotation[1];
        
        mc.thePlayer.rotationYaw = currentYaw;
        mc.thePlayer.rotationPitch = currentPitch;
    }
    
    /**
     * Verifica se está olhando aproximadamente para o target
     */
    public boolean isLookingAt(Vec3 target, float tolerance) {
        if (!initialized || mc.thePlayer == null) return false;
        
        Vec3 playerPos = mc.thePlayer.getPositionVector();
        float[] targetRotation = calculateRotationToTarget(playerPos, target);
        
        float yawDiff = Math.abs(MathHelper.wrapAngleTo180_float(targetRotation[0] - currentYaw));
        float pitchDiff = Math.abs(targetRotation[1] - currentPitch);
        
        return yawDiff <= tolerance && pitchDiff <= tolerance;
    }
    
    /**
     * Reset do sistema
     */
    public void reset() {
        initialized = false;
        randomYawOffset = 0.0f;
        randomPitchOffset = 0.0f;
        randomUpdateCounter = 0;
    }
}
